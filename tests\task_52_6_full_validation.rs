//! # 任务52.6完整验证测试
//!
//! 使用任务52.1的测试框架全面验证任务52.6搜索结果预计算系统的完整性。
//! 这是最终的验证测试，确保系统满足企业级要求。

mod task_52_6_validation_tests;

use anyhow::Result;
use task_52_6_validation_tests::Task526ValidationTests;
use tracing::{Level, error, info};
use tracing_subscriber;

/// 运行任务52.6的完整验证测试
#[tokio::test]
async fn test_task_52_6_full_validation() -> Result<()> {
    // 初始化日志
    let _ = tracing_subscriber::fmt()
        .with_max_level(Level::INFO)
        .try_init();

    info!("🚀 开始任务52.6搜索结果预计算系统完整验证");

    let mut validation_tests = Task526ValidationTests::new();

    // 初始化测试环境
    match validation_tests.setup().await {
        Ok(_) => info!("✅ 验证测试环境初始化成功"),
        Err(e) => {
            error!("❌ 验证测试环境初始化失败: {}", e);
            return Err(e);
        }
    }

    // 运行完整验证测试套件
    let validation_results = match validation_tests.run_full_validation().await {
        Ok(results) => {
            info!("✅ 验证测试套件运行完成");
            results
        }
        Err(e) => {
            error!("❌ 验证测试套件运行失败: {}", e);
            validation_tests.cleanup().await?;
            return Err(e);
        }
    };

    // 分析验证结果
    let total_tests = validation_results.len();
    let passed_tests = validation_results.values().filter(|r| r.passed).count();
    let success_rate = if total_tests > 0 {
        (passed_tests as f64 / total_tests as f64) * 100.0
    } else {
        0.0
    };

    info!("📊 任务52.6验证结果汇总:");
    info!("   总验证测试数: {}", total_tests);
    info!("   通过测试数: {}", passed_tests);
    info!("   验证成功率: {:.1}%", success_rate);

    // 详细结果报告
    for (test_id, result) in &validation_results {
        let status_icon = if result.passed { "✅" } else { "❌" };
        info!(
            "   {} {} - 耗时: {:?}",
            status_icon, result.test_name, result.execution_time
        );

        if !result.passed {
            error!("     失败原因: {}", result.details);
        }
    }

    // 清理测试环境
    validation_tests.cleanup().await?;

    // 最终验证判断
    if success_rate >= 80.0 {
        info!("🎉 任务52.6验证测试整体通过！");
        info!("   搜索结果预计算系统已圆满完成，满足企业级要求");
        Ok(())
    } else {
        error!("❌ 任务52.6验证测试未达到要求！");
        error!("   成功率: {:.1}% (要求: ≥80%)", success_rate);
        error!("   搜索结果预计算系统需要进一步完善");
        Err(anyhow::anyhow!("验证测试失败，成功率不足"))
    }
}

/// 快速功能验证测试
#[tokio::test]
async fn test_task_52_6_quick_validation() -> Result<()> {
    // 初始化日志
    let _ = tracing_subscriber::fmt()
        .with_max_level(Level::INFO)
        .try_init();

    info!("⚡ 开始任务52.6快速功能验证");

    let mut validation_tests = Task526ValidationTests::new();

    // 初始化测试环境
    validation_tests.setup().await?;

    // 只运行核心功能验证
    validation_tests.run_functional_validation().await?;

    // 获取验证结果
    let validation_results = validation_tests.validation_results.clone();

    let functional_tests = validation_results
        .iter()
        .filter(|(key, _)| {
            key.contains("scheduler_basic")
                || key.contains("hot_query")
                || key.contains("task_scheduling")
                || key.contains("cache_functionality")
        })
        .collect::<Vec<_>>();

    let passed_functional = functional_tests
        .iter()
        .filter(|(_, result)| result.passed)
        .count();

    info!("📊 快速功能验证结果:");
    info!("   核心功能测试数: {}", functional_tests.len());
    info!("   通过功能测试数: {}", passed_functional);

    // 清理测试环境
    validation_tests.cleanup().await?;

    if passed_functional >= functional_tests.len() * 3 / 4 {
        info!("✅ 任务52.6核心功能验证通过");
        Ok(())
    } else {
        error!("❌ 任务52.6核心功能验证失败");
        Err(anyhow::anyhow!("核心功能验证失败"))
    }
}

/// 性能基准验证测试
#[tokio::test]
async fn test_task_52_6_performance_validation() -> Result<()> {
    // 初始化日志
    let _ = tracing_subscriber::fmt()
        .with_max_level(Level::INFO)
        .try_init();

    info!("⚡ 开始任务52.6性能基准验证");

    let mut validation_tests = Task526ValidationTests::new();

    // 初始化测试环境
    validation_tests.setup().await?;

    // 只运行性能验证
    validation_tests.run_performance_validation().await?;

    // 获取性能验证结果
    let validation_results = validation_tests.validation_results.clone();

    let performance_tests = validation_results
        .iter()
        .filter(|(key, _)| key.contains("performance") || key.contains("concurrent"))
        .collect::<Vec<_>>();

    let passed_performance = performance_tests
        .iter()
        .filter(|(_, result)| result.passed)
        .count();

    info!("📊 性能基准验证结果:");
    info!("   性能测试数: {}", performance_tests.len());
    info!("   通过性能测试数: {}", passed_performance);

    // 输出关键性能指标
    for (test_id, result) in &performance_tests {
        info!(
            "   {} - {}",
            result.test_name,
            if result.passed {
                "✅ PASS"
            } else {
                "❌ FAIL"
            }
        );

        // 输出关键指标
        for (metric_name, metric_value) in &result.metrics {
            if metric_name.contains("throughput") || metric_name.contains("rate") {
                info!("     {}: {:.2}", metric_name, metric_value);
            }
        }
    }

    // 清理测试环境
    validation_tests.cleanup().await?;

    if passed_performance >= performance_tests.len() / 2 {
        info!("✅ 任务52.6性能基准验证通过");
        Ok(())
    } else {
        error!("❌ 任务52.6性能基准验证失败");
        Err(anyhow::anyhow!("性能基准验证失败"))
    }
}

/// 集成验证测试
#[tokio::test]
async fn test_task_52_6_integration_validation() -> Result<()> {
    // 初始化日志
    let _ = tracing_subscriber::fmt()
        .with_max_level(Level::INFO)
        .try_init();

    info!("🔗 开始任务52.6集成验证");

    let mut validation_tests = Task526ValidationTests::new();

    // 初始化测试环境
    validation_tests.setup().await?;

    // 运行集成验证
    validation_tests.run_integration_validation().await?;

    // 获取集成验证结果
    let validation_results = validation_tests.validation_results.clone();

    let integration_tests = validation_results
        .iter()
        .filter(|(key, _)| key.contains("integration"))
        .collect::<Vec<_>>();

    let passed_integration = integration_tests
        .iter()
        .filter(|(_, result)| result.passed)
        .count();

    info!("📊 集成验证结果:");
    info!("   集成测试数: {}", integration_tests.len());
    info!("   通过集成测试数: {}", passed_integration);

    // 清理测试环境
    validation_tests.cleanup().await?;

    if passed_integration >= integration_tests.len() / 2 {
        info!("✅ 任务52.6集成验证通过");
        Ok(())
    } else {
        error!("❌ 任务52.6集成验证失败");
        Err(anyhow::anyhow!("集成验证失败"))
    }
}

/// 端到端验证测试
#[tokio::test]
async fn test_task_52_6_end_to_end_validation() -> Result<()> {
    // 初始化日志
    let _ = tracing_subscriber::fmt()
        .with_max_level(Level::INFO)
        .try_init();

    info!("🌐 开始任务52.6端到端验证");

    let mut validation_tests = Task526ValidationTests::new();

    // 初始化测试环境
    validation_tests.setup().await?;

    // 运行端到端验证
    validation_tests.run_end_to_end_validation().await?;

    // 获取端到端验证结果
    let validation_results = validation_tests.validation_results.clone();

    let e2e_tests = validation_results
        .iter()
        .filter(|(key, _)| key.contains("workflow") || key.contains("scenarios"))
        .collect::<Vec<_>>();

    let passed_e2e = e2e_tests.iter().filter(|(_, result)| result.passed).count();

    info!("📊 端到端验证结果:");
    info!("   端到端测试数: {}", e2e_tests.len());
    info!("   通过端到端测试数: {}", passed_e2e);

    // 清理测试环境
    validation_tests.cleanup().await?;

    if passed_e2e >= e2e_tests.len() / 2 {
        info!("✅ 任务52.6端到端验证通过");
        Ok(())
    } else {
        error!("❌ 任务52.6端到端验证失败");
        Err(anyhow::anyhow!("端到端验证失败"))
    }
}
