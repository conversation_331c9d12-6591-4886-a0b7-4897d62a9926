//! # 性能监控系统测试
//!
//! 测试任务52.7开发的性能监控和指标收集系统的功能

use std::collections::HashMap;
use std::time::Duration;
use tokio::time::sleep;

use app_common::middleware::{
    AlertLevel, AlertRule, IntegratedMonitoringConfig, IntegratedMonitoringSystem,
    RequestEndMetrics, create_enterprise_monitoring_system,
};

/// 测试集成监控系统的基本功能
#[tokio::test]
async fn test_integrated_monitoring_system_basic() {
    // 创建企业级监控系统
    let monitoring_system = create_enterprise_monitoring_system().expect("创建监控系统失败");

    // 获取健康状态报告
    let health_report = monitoring_system.get_health_report();
    assert!(health_report.system_enabled);
    assert_eq!(health_report.total_http_requests, 0);
    assert_eq!(health_report.total_search_requests, 0);
    assert_eq!(health_report.total_queue_tasks, 0);

    println!("✅ 集成监控系统基本功能测试通过");
}

/// 测试HTTP性能监控
#[tokio::test]
async fn test_http_performance_monitoring() {
    let monitoring_system = create_enterprise_monitoring_system().expect("创建监控系统失败");

    let performance_metrics = monitoring_system.performance_metrics();

    // 模拟HTTP请求
    performance_metrics.record_request_start();
    sleep(Duration::from_millis(100)).await;
    let request_end_metrics = RequestEndMetrics {
        method: "GET",
        path: "/api/test",
        status_code: axum::http::StatusCode::OK,
        duration: Duration::from_millis(100),
        headers: None,
        request_size: None,
        response_size: None,
    };
    performance_metrics.record_request_end(request_end_metrics);

    let stats = performance_metrics.get_stats();
    assert_eq!(stats.total_requests, 1);
    assert_eq!(stats.active_connections, 0);

    println!("✅ HTTP性能监控测试通过");
}

/// 测试搜索性能监控
#[tokio::test]
async fn test_search_performance_monitoring() {
    let monitoring_system = create_enterprise_monitoring_system().expect("创建监控系统失败");

    let search_metrics = monitoring_system.search_metrics();

    // 模拟搜索请求
    let mut search_request = search_metrics.start_search_request(
        "test query".to_string(),
        "full_text".to_string(),
        None,
    );

    sleep(Duration::from_millis(50)).await;

    search_request.result_count = 10;
    search_request.cache_hit = true;
    search_request.quality_score = Some(0.9);

    search_metrics.finish_search_request(search_request);

    let stats = search_metrics.get_performance_stats();
    assert_eq!(stats.total_searches, 1);
    assert_eq!(stats.cache_hits, 1);
    assert_eq!(stats.cache_misses, 0);
    assert_eq!(stats.cache_hit_ratio, 1.0);

    println!("✅ 搜索性能监控测试通过");
}

/// 测试数据库性能监控
#[tokio::test]
async fn test_database_performance_monitoring() {
    let monitoring_system = create_enterprise_monitoring_system().expect("创建监控系统失败");

    let database_metrics = monitoring_system.database_metrics();

    // 模拟数据库查询
    let mut db_query = database_metrics.start_database_query(
        "SELECT".to_string(),
        Some("users".to_string()),
        Some("SELECT * FROM users WHERE id = ?".to_string()),
    );

    sleep(Duration::from_millis(30)).await;

    db_query.affected_rows = Some(1);
    db_query.used_index = Some(true);

    database_metrics.finish_database_query(db_query);

    // 更新连接池统计
    database_metrics.update_connection_pool_stats(5, 10, 20);

    println!("✅ 数据库性能监控测试通过");
}

/// 测试队列性能监控
#[tokio::test]
async fn test_queue_performance_monitoring() {
    let monitoring_system = create_enterprise_monitoring_system().expect("创建监控系统失败");

    let queue_metrics = monitoring_system.queue_metrics();

    // 模拟队列任务
    let mut queue_task = queue_metrics.start_queue_task(
        "email_queue".to_string(),
        "send_email".to_string(),
        "task_123".to_string(),
    );

    sleep(Duration::from_millis(200)).await;

    queue_task.priority = Some(1);
    queue_task.retry_count = 0;
    queue_task.task_size_bytes = Some(1024);

    queue_metrics.finish_queue_task(queue_task, true, None);

    // 更新队列长度
    queue_metrics.update_queue_length("email_queue".to_string(), 5);

    let stats = queue_metrics.get_performance_stats();
    assert_eq!(stats.total_tasks, 1);
    assert_eq!(stats.successful_tasks, 1);
    assert_eq!(stats.failed_tasks, 0);
    assert_eq!(stats.overall_success_rate, 1.0);

    println!("✅ 队列性能监控测试通过");
}

/// 测试告警管理系统
#[tokio::test]
async fn test_alert_management_system() {
    let monitoring_system = create_enterprise_monitoring_system().expect("创建监控系统失败");

    let alert_manager = monitoring_system.alert_manager();

    // 添加自定义告警规则
    let custom_rule = AlertRule {
        id: "test_metric_high".to_string(),
        name: "测试指标过高".to_string(),
        description: "测试指标超过阈值".to_string(),
        metric_name: "test_metric".to_string(),
        level: AlertLevel::Warning,
        threshold: 100.0,
        operator: ">".to_string(),
        duration_seconds: 60,
        enabled: true,
        labels: HashMap::new(),
        message_template: "测试指标过高: {current_value} > {threshold}".to_string(),
    };

    alert_manager.add_rule(custom_rule);

    // 评估指标并触发告警
    let mut labels = HashMap::new();
    labels.insert("service".to_string(), "test".to_string());

    alert_manager.evaluate_metric("test_metric", 150.0, &labels);

    let active_alerts = alert_manager.get_active_alerts();
    assert_eq!(active_alerts.len(), 1);
    assert_eq!(active_alerts[0].level, AlertLevel::Warning);

    // 解决告警
    alert_manager.evaluate_metric("test_metric", 50.0, &labels);

    let stats = alert_manager.get_alert_stats();
    assert!(stats.total_alerts > 0);

    println!("✅ 告警管理系统测试通过");
}

/// 测试监控系统维护功能
#[tokio::test]
async fn test_monitoring_system_maintenance() {
    let monitoring_system = create_enterprise_monitoring_system().expect("创建监控系统失败");

    // 执行维护任务
    monitoring_system.perform_maintenance().await;

    println!("✅ 监控系统维护功能测试通过");
}

/// 测试性能指标评估和告警触发
#[tokio::test]
async fn test_metric_evaluation_and_alerting() {
    let monitoring_system = create_enterprise_monitoring_system().expect("创建监控系统失败");

    let mut labels = HashMap::new();
    labels.insert("endpoint".to_string(), "/api/test".to_string());

    // 测试HTTP响应时间告警
    monitoring_system.evaluate_metric("http_request_duration_seconds", 3.0, &labels);

    // 测试数据库连接池使用率告警
    labels.clear();
    labels.insert("database".to_string(), "main".to_string());
    monitoring_system.evaluate_metric("database_connection_pool_usage_ratio", 0.9, &labels);

    // 测试搜索缓存命中率告警
    labels.clear();
    labels.insert("cache_type".to_string(), "search".to_string());
    monitoring_system.evaluate_metric("search_cache_hit_ratio", 0.6, &labels);

    // 测试队列长度告警
    labels.clear();
    labels.insert("queue".to_string(), "email".to_string());
    monitoring_system.evaluate_metric("queue_length", 150.0, &labels);

    let alert_stats = monitoring_system.alert_manager().get_alert_stats();
    println!("活跃告警数: {}", alert_stats.active_alerts_count);
    println!("总告警数: {}", alert_stats.total_alerts);

    println!("✅ 指标评估和告警触发测试通过");
}

/// 测试监控系统配置
#[tokio::test]
async fn test_monitoring_system_configuration() {
    // 测试默认配置
    let default_config = IntegratedMonitoringConfig::default();
    assert!(default_config.enable_monitoring);
    assert_eq!(default_config.prometheus_bind_address, "0.0.0.0:9090");

    // 测试企业级配置
    let enterprise_config = IntegratedMonitoringSystem::create_enterprise_config();
    assert!(enterprise_config.enable_monitoring);
    assert!(
        enterprise_config
            .performance_config
            .enable_prometheus_metrics
    );
    assert!(
        enterprise_config
            .search_metrics_config
            .enable_search_metrics
    );
    assert!(
        enterprise_config
            .database_metrics_config
            .enable_database_metrics
    );
    assert!(enterprise_config.queue_metrics_config.enable_queue_metrics);
    assert!(enterprise_config.alert_manager_config.enable_alerting);

    println!("✅ 监控系统配置测试通过");
}

/// 综合性能监控测试
#[tokio::test]
async fn test_comprehensive_performance_monitoring() {
    println!("🚀 开始综合性能监控测试...");

    let monitoring_system = create_enterprise_monitoring_system().expect("创建监控系统失败");

    // 模拟一系列操作
    for i in 0..10 {
        // HTTP请求
        let performance_metrics = monitoring_system.performance_metrics();
        performance_metrics.record_request_start();
        sleep(Duration::from_millis(50 + i * 10)).await;
        let path = format!("/api/test/{}", i);
        let request_end_metrics = RequestEndMetrics {
            method: "GET",
            path: &path,
            status_code: axum::http::StatusCode::OK,
            duration: Duration::from_millis(50 + i * 10),
            headers: None,
            request_size: None,
            response_size: None,
        };
        performance_metrics.record_request_end(request_end_metrics);

        // 搜索请求
        let search_metrics = monitoring_system.search_metrics();
        let mut search_request = search_metrics.start_search_request(
            format!("query {}", i),
            "full_text".to_string(),
            None,
        );
        sleep(Duration::from_millis(30)).await;
        search_request.result_count = (5 + i) as usize;
        search_request.cache_hit = i % 2 == 0;
        search_request.quality_score = Some(0.8 + (i as f64) * 0.01);
        search_metrics.finish_search_request(search_request);

        // 数据库查询
        let database_metrics = monitoring_system.database_metrics();
        let db_query = database_metrics.start_database_query(
            "SELECT".to_string(),
            Some("users".to_string()),
            None,
        );
        sleep(Duration::from_millis(20)).await;
        database_metrics.finish_database_query(db_query);

        // 队列任务
        let queue_metrics = monitoring_system.queue_metrics();
        let queue_task = queue_metrics.start_queue_task(
            "test_queue".to_string(),
            "test_task".to_string(),
            format!("task_{}", i),
        );
        sleep(Duration::from_millis(100)).await;
        queue_metrics.finish_queue_task(queue_task, i % 8 != 7, None); // 偶尔失败
    }

    // 获取最终报告
    let health_report = monitoring_system.get_health_report();
    println!("📊 监控系统健康报告:");
    println!("  - HTTP请求总数: {}", health_report.total_http_requests);
    println!("  - 搜索请求总数: {}", health_report.total_search_requests);
    println!("  - 队列任务总数: {}", health_report.total_queue_tasks);
    println!(
        "  - 搜索缓存命中率: {:.2}%",
        health_report.search_cache_hit_ratio * 100.0
    );
    println!(
        "  - 队列成功率: {:.2}%",
        health_report.queue_success_rate * 100.0
    );
    println!("  - 活跃告警数: {}", health_report.active_alerts);

    // 验证结果
    assert_eq!(health_report.total_http_requests, 10);
    assert_eq!(health_report.total_search_requests, 10);
    assert_eq!(health_report.total_queue_tasks, 10);
    assert!(health_report.search_cache_hit_ratio >= 0.4); // 至少40%命中率
    assert!(health_report.queue_success_rate >= 0.8); // 至少80%成功率

    println!(
        "✅ 综合性能监控测试通过 - 成功率: {:.1}%",
        health_report.queue_success_rate * 100.0
    );
}
