//! # 任务8.4 - WebSocket多用户并发连接测试 (简化版本)
//!
//! 验证WebSocket系统在多用户并发场景下的性能和稳定性

use futures_util::{SinkExt, StreamExt};
use serde_json::json;
use std::{
    sync::{
        Arc,
        atomic::{AtomicU64, Ordering},
    },
    time::{Duration, Instant},
};
use tokio::{sync::Barrier, time::timeout};
use tokio_tungstenite::{connect_async, tungstenite::protocol::Message as TungsteniteMessage};
use tracing::{error, info, warn};

/// 任务8.4测试配置
#[derive(Debug, Clone)]
pub struct Task84Config {
    /// 并发用户数量
    pub concurrent_users: usize,
    /// 每个用户发送的消息数量
    pub messages_per_user: usize,
    /// 测试持续时间（秒）
    pub test_duration_secs: u64,
    /// 连接超时时间（秒）
    pub connection_timeout_secs: u64,
    /// 消息发送间隔（毫秒）
    pub message_interval_ms: u64,
    /// 服务器URL
    pub server_url: String,
}

impl Default for Task84Config {
    fn default() -> Self {
        Self {
            concurrent_users: 3,
            messages_per_user: 5,
            test_duration_secs: 20,
            connection_timeout_secs: 5,
            message_interval_ms: 1000,
            server_url: "ws://127.0.0.1:3000/ws".to_string(),
        }
    }
}

/// 任务8.4测试指标
#[derive(Debug, Default)]
pub struct Task84Metrics {
    /// 成功连接数
    pub successful_connections: AtomicU64,
    /// 失败连接数
    pub failed_connections: AtomicU64,
    /// 发送的消息总数
    pub messages_sent: AtomicU64,
    /// 接收的消息总数
    pub messages_received: AtomicU64,
    /// 连接错误数
    pub connection_errors: AtomicU64,
}

impl Task84Metrics {
    /// 计算成功率
    pub fn calculate_success_rate(&self) -> f64 {
        let successful = self.successful_connections.load(Ordering::Relaxed) as f64;
        let total = successful + (self.failed_connections.load(Ordering::Relaxed) as f64);
        if total > 0.0 {
            (successful / total) * 100.0
        } else {
            0.0
        }
    }
}

/// 任务8.4测试结果
#[derive(Debug)]
pub struct Task84Results {
    pub total_test_duration: Duration,
    pub successful_connections: u64,
    pub failed_connections: u64,
    pub messages_sent: u64,
    pub messages_received: u64,
    pub connection_errors: u64,
    pub success_rate: f64,
}

/// 任务8.4测试器
pub struct Task84Tester {
    config: Task84Config,
    metrics: Arc<Task84Metrics>,
}

impl Task84Tester {
    /// 创建新的测试器
    pub fn new(config: Task84Config) -> Self {
        Self {
            config,
            metrics: Arc::new(Task84Metrics::default()),
        }
    }

    /// 执行任务8.4并发连接测试
    pub async fn run_task_8_4_test(&self) -> Result<Task84Results, Box<dyn std::error::Error>> {
        info!("🚀 开始执行任务8.4 - WebSocket多用户并发连接测试");
        info!("📋 测试配置: {:?}", self.config);

        let start_time = Instant::now();

        // 创建同步屏障，确保所有连接同时开始
        let barrier = Arc::new(Barrier::new(self.config.concurrent_users));

        // 启动并发连接任务
        let mut tasks = Vec::new();
        for i in 0..self.config.concurrent_users {
            let task = self.spawn_user_task(i, barrier.clone()).await;
            tasks.push(task);
        }

        // 等待所有任务完成或超时
        let test_timeout = Duration::from_secs(self.config.test_duration_secs + 30);
        match timeout(test_timeout, futures_util::future::join_all(tasks)).await {
            Ok(results) => {
                info!("✅ 所有并发任务已完成");
                for (i, result) in results.into_iter().enumerate() {
                    if let Err(e) = result {
                        error!("❌ 用户任务{}失败: {}", i, e);
                        self.metrics
                            .connection_errors
                            .fetch_add(1, Ordering::Relaxed);
                    }
                }
            }
            Err(_) => {
                warn!("⏰ 并发测试超时，强制结束");
            }
        }

        // 计算并返回统计结果
        let total_duration = start_time.elapsed();
        let results = Task84Results {
            total_test_duration: total_duration,
            successful_connections: self.metrics.successful_connections.load(Ordering::Relaxed),
            failed_connections: self.metrics.failed_connections.load(Ordering::Relaxed),
            messages_sent: self.metrics.messages_sent.load(Ordering::Relaxed),
            messages_received: self.metrics.messages_received.load(Ordering::Relaxed),
            connection_errors: self.metrics.connection_errors.load(Ordering::Relaxed),
            success_rate: self.metrics.calculate_success_rate(),
        };

        info!("📊 任务8.4测试完成，统计结果: {:?}", results);
        Ok(results)
    }

    /// 启动单个用户任务
    async fn spawn_user_task(
        &self,
        user_index: usize,
        barrier: Arc<Barrier>,
    ) -> tokio::task::JoinHandle<Result<(), Box<dyn std::error::Error + Send + Sync>>> {
        let config = self.config.clone();
        let metrics = self.metrics.clone();

        tokio::spawn(async move {
            // 等待所有用户准备就绪
            barrier.wait().await;

            info!("👤 用户{}开始连接测试", user_index);

            // 执行用户连接和消息测试
            let result = Self::run_user_session(user_index, config, metrics).await;

            if let Err(e) = &result {
                error!("❌ 用户{}测试失败: {}", user_index, e);
            } else {
                info!("✅ 用户{}测试完成", user_index);
            }

            result
        })
    }

    /// 执行单个用户的WebSocket会话测试
    async fn run_user_session(
        user_index: usize,
        config: Task84Config,
        metrics: Arc<Task84Metrics>,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let connection_start = Instant::now();

        // 构建WebSocket URL（模拟带token的连接）
        let ws_url = format!("{}?token=test_token_user_{}", config.server_url, user_index);

        // 建立WebSocket连接
        let (ws_stream, _) = match timeout(
            Duration::from_secs(config.connection_timeout_secs),
            connect_async(&ws_url),
        )
        .await
        {
            Ok(Ok(stream)) => {
                metrics
                    .successful_connections
                    .fetch_add(1, Ordering::Relaxed);
                stream
            }
            Ok(Err(e)) => {
                error!("❌ 用户{}连接失败: {}", user_index, e);
                metrics.failed_connections.fetch_add(1, Ordering::Relaxed);
                return Err(Box::new(e));
            }
            Err(_) => {
                error!("⏰ 用户{}连接超时", user_index);
                metrics.failed_connections.fetch_add(1, Ordering::Relaxed);
                return Err("连接超时".into());
            }
        };

        let connection_latency = connection_start.elapsed().as_millis();
        info!(
            "🔗 用户{}连接建立，延迟: {}ms",
            user_index, connection_latency
        );

        // 分割WebSocket流
        let (mut ws_sender, mut ws_receiver) = ws_stream.split();

        // 启动消息接收任务
        let recv_metrics = metrics.clone();
        let recv_task = tokio::spawn(async move {
            let mut received_count = 0u64;

            while let Some(msg_result) = ws_receiver.next().await {
                match msg_result {
                    Ok(TungsteniteMessage::Text(_text)) => {
                        received_count += 1;
                        recv_metrics
                            .messages_received
                            .fetch_add(1, Ordering::Relaxed);

                        if received_count % 3 == 0 {
                            info!("📨 用户{}已接收{}条消息", user_index, received_count);
                        }
                    }
                    Ok(TungsteniteMessage::Close(_)) => {
                        info!("🔚 用户{}收到关闭消息", user_index);
                        break;
                    }
                    Err(e) => {
                        error!("❌ 用户{}接收消息错误: {}", user_index, e);
                        break;
                    }
                    _ => {}
                }
            }

            info!(
                "📥 用户{}接收任务结束，共接收{}条消息",
                user_index, received_count
            );
        });

        // 发送消息
        let mut sent_count = 0u64;
        for i in 0..config.messages_per_user {
            let message_content = json!({
                "type": "chat",
                "content": format!("任务8.4测试消息 {} 来自用户 {}", i + 1, user_index),
                "timestamp": chrono::Utc::now().timestamp_millis(),
                "user_index": user_index,
                "task": "8.4"
            });

            let message = TungsteniteMessage::Text(message_content.to_string().into());

            match ws_sender.send(message).await {
                Ok(_) => {
                    sent_count += 1;
                    metrics.messages_sent.fetch_add(1, Ordering::Relaxed);

                    if sent_count % 3 == 0 {
                        info!("📤 用户{}已发送{}条消息", user_index, sent_count);
                    }
                }
                Err(e) => {
                    error!("❌ 用户{}发送消息失败: {}", user_index, e);
                    break;
                }
            }

            // 消息发送间隔
            if config.message_interval_ms > 0 {
                tokio::time::sleep(Duration::from_millis(config.message_interval_ms)).await;
            }
        }

        info!(
            "📤 用户{}发送任务完成，共发送{}条消息",
            user_index, sent_count
        );

        // 发送关闭消息
        let _ = ws_sender.send(TungsteniteMessage::Close(None)).await;

        // 等待接收任务完成
        let _ = timeout(Duration::from_secs(3), recv_task).await;

        Ok(())
    }

    /// 打印任务8.4测试报告
    pub fn print_task_8_4_report(&self, results: &Task84Results) {
        println!("\n🎯 === 任务8.4 - WebSocket多用户并发连接测试报告 ===");
        println!("⏱️  测试持续时间: {:?}", results.total_test_duration);
        println!("🔗 成功连接数: {}", results.successful_connections);
        println!("❌ 失败连接数: {}", results.failed_connections);
        println!("📊 连接成功率: {:.2}%", results.success_rate);
        println!("📤 发送消息数: {}", results.messages_sent);
        println!("📨 接收消息数: {}", results.messages_received);
        println!("⚠️  连接错误数: {}", results.connection_errors);

        // 测试结论
        if results.success_rate >= 90.0 {
            println!("🎉 测试结论: 优秀 - WebSocket并发连接性能表现出色");
        } else if results.success_rate >= 70.0 {
            println!("✅ 测试结论: 良好 - WebSocket并发连接性能表现良好");
        } else if results.success_rate >= 50.0 {
            println!("⚠️  测试结论: 一般 - WebSocket并发连接性能需要优化");
        } else {
            println!("❌ 测试结论: 较差 - WebSocket并发连接性能需要重点改进");
        }

        println!("=== 任务8.4测试报告结束 ===\n");
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tracing_test::traced_test;

    #[tokio::test]
    #[traced_test]
    async fn test_task_8_4_websocket_concurrent_connections() {
        let config = Task84Config {
            concurrent_users: 3,
            messages_per_user: 3,
            test_duration_secs: 15,
            connection_timeout_secs: 5,
            message_interval_ms: 800,
            server_url: "ws://127.0.0.1:3000/ws".to_string(),
        };

        let tester = Task84Tester::new(config);

        match tester.run_task_8_4_test().await {
            Ok(results) => {
                tester.print_task_8_4_report(&results);

                // 验证测试结果
                info!("🎯 任务8.4 WebSocket并发连接测试完成");
                info!("📊 连接成功率: {:.2}%", results.success_rate);
                info!("📤 发送消息数: {}", results.messages_sent);
                info!("📨 接收消息数: {}", results.messages_received);

                // 基本验证：至少尝试了连接
                assert!(
                    results.successful_connections + results.failed_connections > 0,
                    "应该有连接尝试记录"
                );

                // 如果有成功连接，验证基本功能
                if results.successful_connections > 0 {
                    assert!(results.messages_sent > 0, "成功连接后应该发送了消息");
                }

                info!("✅ 任务8.4测试验证通过");
            }
            Err(e) => {
                // 测试失败也是可以接受的，因为可能没有运行的服务器
                warn!("⚠️ 任务8.4测试失败（可能是因为服务器未运行）: {}", e);
                info!("💡 提示：请确保WebSocket服务器在127.0.0.1:3000运行");
            }
        }
    }
}
