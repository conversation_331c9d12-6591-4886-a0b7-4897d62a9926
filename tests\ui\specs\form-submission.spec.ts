import { test, expect } from '@playwright/test';
import { MainPage } from '../helpers/main-page';
import { testUsers, testTasks, errorMessages, successMessages } from '../fixtures/test-data';

/**
 * 表单提交测试套件
 * 遵循Context7 MCP最佳实践，测试用户表单交互
 */
test.describe('表单提交测试', () => {
  let mainPage: MainPage;

  test.beforeEach(async ({ page }) => {
    mainPage = new MainPage(page);
    await mainPage.goto();
    await mainPage.verifyPageLoaded();
  });

  test.describe('用户认证表单', () => {
    test('应该成功提交登录表单', async () => {
      // 使用有效用户数据登录
      await mainPage.login(testUsers.validUser.username, testUsers.validUser.password);
      
      // 验证登录成功
      await mainPage.verifyAuthStatus(true);
      await mainPage.verifySuccessMessage(successMessages.auth.loginSuccess);
    });

    test('应该显示登录错误消息', async () => {
      // 使用无效用户数据登录
      await mainPage.login(testUsers.invalidUser.username, testUsers.invalidUser.password);
      
      // 验证登录失败
      await mainPage.verifyAuthStatus(false);
      await mainPage.verifyErrorMessage(errorMessages.auth.invalidCredentials);
    });

    test('应该验证必填字段', async () => {
      // 尝试使用空字段登录
      await mainPage.login(testUsers.emptyUser.username, testUsers.emptyUser.password);
      
      // 验证必填字段错误
      await expect(mainPage.usernameInput).toHaveAttribute('required');
      await expect(mainPage.passwordInput).toHaveAttribute('required');
    });

    test('应该成功提交注册表单', async () => {
      // 使用新用户数据注册
      await mainPage.register(
        testUsers.newUser.username,
        testUsers.newUser.email,
        testUsers.newUser.password
      );
      
      // 验证注册成功
      await mainPage.verifySuccessMessage(successMessages.auth.registerSuccess);
    });

    test('应该验证邮箱格式', async () => {
      // 使用无效邮箱格式注册
      await mainPage.register(
        testUsers.invalidEmail.username,
        testUsers.invalidEmail.email,
        testUsers.invalidEmail.password
      );
      
      // 验证邮箱格式错误
      await mainPage.verifyErrorMessage(errorMessages.auth.invalidEmail);
    });

    test('应该验证密码长度', async () => {
      // 使用过短密码注册
      await mainPage.register(
        testUsers.shortPassword.username,
        testUsers.shortPassword.email,
        testUsers.shortPassword.password
      );
      
      // 验证密码长度错误
      await mainPage.verifyErrorMessage(errorMessages.auth.passwordTooShort);
    });
  });

  test.describe('任务创建表单', () => {
    test.beforeEach(async () => {
      // 每个任务测试前先登录
      await mainPage.login(testUsers.validUser.username, testUsers.validUser.password);
      await mainPage.verifyAuthStatus(true);
    });

    test('应该成功创建简单任务', async () => {
      const initialTaskCount = await mainPage.getTaskCount();
      
      // 创建简单任务
      await mainPage.createTask(testTasks.simpleTask.title, testTasks.simpleTask.description);
      
      // 验证任务已创建
      const newTaskCount = await mainPage.getTaskCount();
      expect(newTaskCount).toBe(initialTaskCount + 1);
      
      // 验证任务内容
      const lastTaskText = await mainPage.getTaskText(newTaskCount - 1);
      expect(lastTaskText).toContain(testTasks.simpleTask.title);
    });

    test('应该成功创建复杂任务', async () => {
      const initialTaskCount = await mainPage.getTaskCount();
      
      // 创建复杂任务
      await mainPage.createTask(testTasks.complexTask.title, testTasks.complexTask.description);
      
      // 验证任务已创建
      const newTaskCount = await mainPage.getTaskCount();
      expect(newTaskCount).toBe(initialTaskCount + 1);
      
      // 验证任务内容
      const lastTaskText = await mainPage.getTaskText(newTaskCount - 1);
      expect(lastTaskText).toContain(testTasks.complexTask.title);
    });

    test('应该处理特殊字符任务', async () => {
      const initialTaskCount = await mainPage.getTaskCount();
      
      // 创建包含特殊字符的任务
      await mainPage.createTask(testTasks.specialCharsTask.title, testTasks.specialCharsTask.description);
      
      // 验证任务已创建
      const newTaskCount = await mainPage.getTaskCount();
      expect(newTaskCount).toBe(initialTaskCount + 1);
      
      // 验证特殊字符被正确处理
      const lastTaskText = await mainPage.getTaskText(newTaskCount - 1);
      expect(lastTaskText).toContain(testTasks.specialCharsTask.title);
    });

    test('应该处理Unicode字符任务', async () => {
      const initialTaskCount = await mainPage.getTaskCount();
      
      // 创建包含Unicode字符的任务
      await mainPage.createTask(testTasks.unicodeTask.title, testTasks.unicodeTask.description);
      
      // 验证任务已创建
      const newTaskCount = await mainPage.getTaskCount();
      expect(newTaskCount).toBe(initialTaskCount + 1);
      
      // 验证Unicode字符被正确处理
      const lastTaskText = await mainPage.getTaskText(newTaskCount - 1);
      expect(lastTaskText).toContain(testTasks.unicodeTask.title);
    });

    test('应该验证任务标题必填', async () => {
      // 尝试创建空标题任务
      await mainPage.createTask(testTasks.emptyTask.title, testTasks.emptyTask.description);
      
      // 验证标题必填验证
      await expect(mainPage.taskTitleInput).toHaveAttribute('required');
    });

    test('应该处理长标题任务', async () => {
      const initialTaskCount = await mainPage.getTaskCount();
      
      // 创建长标题任务
      await mainPage.createTask(testTasks.longTitleTask.title, testTasks.longTitleTask.description);
      
      // 验证任务已创建
      const newTaskCount = await mainPage.getTaskCount();
      expect(newTaskCount).toBe(initialTaskCount + 1);
      
      // 验证长标题被正确处理
      const lastTaskText = await mainPage.getTaskText(newTaskCount - 1);
      expect(lastTaskText).toContain(testTasks.longTitleTask.title.substring(0, 20));
    });
  });

  test.describe('聊天消息表单', () => {
    test.beforeEach(async () => {
      // 每个聊天测试前先登录并连接WebSocket
      await mainPage.login(testUsers.validUser.username, testUsers.validUser.password);
      await mainPage.verifyAuthStatus(true);
      await mainPage.connectWebSocket();
    });

    test.afterEach(async () => {
      // 每个聊天测试后断开WebSocket连接
      await mainPage.disconnectWebSocket();
    });

    test('应该成功发送简单消息', async () => {
      const initialMessageCount = await mainPage.getMessageCount();
      
      // 发送简单消息
      await mainPage.sendMessage('这是一条测试消息');
      
      // 验证消息已发送
      await expect(mainPage.messageInput).toHaveValue('');
      
      // 等待消息显示
      await mainPage.wait(1000);
      const newMessageCount = await mainPage.getMessageCount();
      expect(newMessageCount).toBeGreaterThan(initialMessageCount);
    });

    test('应该处理长消息', async () => {
      const longMessage = '这是一条很长的测试消息，'.repeat(10);
      
      // 发送长消息
      await mainPage.sendMessage(longMessage);
      
      // 验证消息输入框已清空
      await expect(mainPage.messageInput).toHaveValue('');
    });

    test('应该处理特殊字符消息', async () => {
      const specialMessage = '特殊字符测试 !@#$%^&*()_+-=[]{}|;:,.<>?';
      
      // 发送特殊字符消息
      await mainPage.sendMessage(specialMessage);
      
      // 验证消息输入框已清空
      await expect(mainPage.messageInput).toHaveValue('');
    });

    test('应该处理Unicode字符消息', async () => {
      const unicodeMessage = '多语言测试 🎉 Hello 你好 こんにちは 안녕하세요';
      
      // 发送Unicode字符消息
      await mainPage.sendMessage(unicodeMessage);
      
      // 验证消息输入框已清空
      await expect(mainPage.messageInput).toHaveValue('');
    });

    test('应该阻止发送空消息', async () => {
      // 尝试发送空消息
      await mainPage.sendMessage('');
      
      // 验证发送按钮状态或错误提示
      await expect(mainPage.sendButton).toBeDisabled();
    });
  });
});
