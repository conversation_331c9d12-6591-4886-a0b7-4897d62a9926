# 模块化领域驱动设计 (Modular DDD) 结合整洁架构的Axum 项目企业级架构迁移方案

**文档版本**: 1.0
**日期**: 2025年7月8日
**作者**: (资深 Rust 后端工程师)

## 1. 当前架构分析报告

在进行迁移之前，我们首先对当前项目架构进行一次全面的分析。

### 1.1. 目录结构与代码组织

当前项目采用了一种经典的、以技术职责为核心的分层架构，主要代码位于 `src/app/` 目录下，并按 `controller`, `service`, `repository`, `model`, `middleware` 等目录进行划分。

- **`src/main.rs`**: 应用入口，职责单一清晰。
- **`src/startup.rs`**: 负责应用的组装，包括数据库、日志、路由和中间件，是应用初始化的核心。
- **`src/routes.rs`**: 路由定义文件，但存在将所有路由（API、WebSocket、性能、健康检查等）集中在一个巨大函数 `create_routes` 中的问题，随着功能增多，该文件会变得越来越难以维护。
- **`src/app/{controller, service, repository}`**: 核心的C-S-R分层，逻辑清晰。
- **`src/app/model`**: 存放 DTOs，但与 `migration` 中的实体（Entity）存在一定的概念重叠和潜在的定义分散问题。
- **`src/error.rs`**: 统一的错误处理，是良好的实践。

### 1.2. 优点

- **关注点分离 (SoC)**: 成功地将数据访问 (Repository)、业务逻辑 (Service) 和 HTTP 处理 (Controller) 分离开来，降低了代码的耦合度。
- **学习友好**: 对于初学者来说，这种直观的分层易于理解一个请求的完整生命周期。
- **功能完备**: 项目已经具备了企业级应用所需的诸多高级功能，如 JWT 认证、WebSocket、性能监控、错误恢复、中间件体系等，基础非常扎实。
- **统一错误处理**: `AppError` 的设计使得错误处理规范化。

### 1.3. 缺点和技术债务

- **可扩展性瓶颈**:
    - **巨型 `routes.rs`**: 所有路由集中在一个文件，违反了“开闭原则”。每增加一个新功能模块，都需要修改这个文件，容易引发冲突和错误。
    - **平铺的 `controller`, `service`**: `controller` 和 `service` 目录下的文件是按功能平铺的。当业务领域增多（例如，未来增加“支付”、“社交”等模块），这些目录会迅速膨胀，变得难以管理。
- **领域模型不清晰**:
    - 当前架构更偏向于“数据驱动”而非“领域驱动”。业务逻辑主要体现在 `service` 层，但没有一个明确的、受保护的“领域层 (Domain)”来封装最核心的业务规则和实体。
    - `app/model` 和 `migration/src/..._entity.rs` 共同定义了应用的“数据形状”，但核心的业务不变量和行为没有被强制约束。
- **模块化不足**:
    - 整个应用是一个大的单体 (Monolith) Crate。虽然功能上解耦，但在物理上没有分离。这使得按业务领域进行团队分工、独立测试和部署变得困难。
    - 缺乏一个 `common` 或 `shared` Crate 来存放跨层共享的代码（如 `AppError`、通用工具函数），可能导致潜在的循环依赖问题。

## 2. 目标架构设计方案 (方案2)

我们将采用 **模块化领域驱动设计 (Modular DDD) 结合整洁架构** 的思想，对项目进行重构。结合 Rust 社区构建大型企业级应用的黄金标准。

### 2.1. 核心理念

- **业务为王**: 从按“技术职责”（controller, service）分层，转向按“业务领域”（`user`, `chat`, `task`）进行垂直切分。
- **内外分离**: 严格区分代表核心业务逻辑的“内部”代码（领域层、应用层）和与外部世界交互的“外部”代码（基础设施层、表示层）。
- **依赖倒置**: 外部依赖内部，实现细节依赖抽象。

### 2.2. 目标目录结构

```
axum-tutorial/
├── Cargo.toml             # [workspace] 定义所有成员
├── crates/                # 存放所有业务逻辑和基础设施的库
│   ├── app_common/        # 【新增】公共模块: 错误处理、共享DTO、工具函数等
│   │   ├── Cargo.toml
│   │   └── src/
│   │       ├── error.rs
│   │       ├── lib.rs
│   │       └── utils.rs
│   ├── app_domain/        # 【新增】领域层: 核心业务模型、实体和规则
│   │   ├── Cargo.toml
│   │   └── src/
│   │       ├── lib.rs
│   │       ├── user/
│   │       ├── task/
│   │       └── chat/
│   ├── app_application/   # 【新增】应用层: 业务用例实现 (原 Service 层)
│   │   ├── Cargo.toml
│   │   └── src/
│   │       ├── lib.rs
│   │       ├── user_service.rs
│   │       ├── task_service.rs
│   │       └── chat_service.rs
│   └── app_infrastructure/# 【新增】基础设施层: 数据库、缓存等具体实现 (原 Repository 层)
│       ├── Cargo.toml
│       └── src/
│           ├── lib.rs
│           ├── user_repository.rs
│           ├── task_repository.rs
│           └── message_repository.rs
├── server/                # 【重命名】主应用 (原 src/ 目录内容)
│   ├── Cargo.toml
│   └── src/
│       ├── main.rs        # 程序入口 (保持简洁)
│       ├── startup.rs     # 应用启动、中间件和路由组装
│       ├── config.rs      # 配置加载与管理
│       ├── state.rs       # 共享应用状态 (如数据库连接池)
│       └── routes/        # 【重构】按业务模块组织的路由/控制器
│           ├── mod.rs
│           ├── auth.rs
│           ├── task.rs
│           ├── message.rs
│           ├── performance.rs
│           └── websocket.rs
└── migration/             # 数据库迁移 (保持不变)
```

### 2.3. 依赖关系图

```mermaid
graph TD
    subgraph "Workspace"
        S(server) -->|uses| AA(app_application);
        S -->|uses| AI(app_infrastructure);
        S -->|uses| AC(app_common);

        AA -->|uses| AD(app_domain);
        AA -->|defines traits, uses| AI;
        AA -->|uses| AC;

        AI -->|uses| AD;
        AI -->|uses| AC;

        AD -->|uses| AC;
    end
```

- **`server`**: 负责启动、配置和路由，是所有层的“组装车间”。
- **`app_application`**: 编排领域对象和基础设施接口，完成业务用例。
- **`app_infrastructure`**: 实现应用层定义的接口，与数据库等外部系统交互。
- **`app_domain`**: 包含纯粹的业务实体和规则，不依赖任何其他层。
- **`app_common`**: 被所有层依赖，提供共享的基础功能。

## 3. 技术选型和依赖管理策略

- **Cargo Workspace**: 我们将充分利用 Cargo Workspace，将 `server`, `app_common`, `app_domain`, `app_application`, `app_infrastructure` 和 `migration` 全部定义为 workspace 的成员。这有助于统一编译和依赖管理。
- **依赖版本**: 严格按照要求，保持 `Cargo.toml` 中所有依赖版本不变。
- **Crate 间依赖**: 在每个 Crate 的 `Cargo.toml` 中明确定义其对 workspace 内其他 Crate 的依赖。例如，`app_application/Cargo.toml` 会包含 `app_domain = { path = "../app_domain" }`。

## 4. 验证细节与渐进式迁移策略

为确保迁移过程平稳且功能无损，我们将采取以下策略：

1.  **创建新结构**: 首先创建新的目录结构 (`server/`, `crates/app_*`) 和对应的 `Cargo.toml` 文件。
2.  **迁移 `app_common`**: 将 `src/error.rs` 和 `src/app/utils` 的部分内容移动到 `crates/app_common`。编译通过。
3.  **迁移 `app_domain`**: 将 `migration/src/*_entity.rs` 中的实体定义（不含 `sea_orm` 宏）和相关的业务逻辑移动到 `crates/app_domain`。编译通过。
4.  **迁移 `app_infrastructure`**: 将 `src/app/repository` 移动到 `crates/app_infrastructure`。修改代码以适应新的 `app_domain` 和 `app_common`。编译通过。
5.  **迁移 `app_application`**: 将 `src/app/service` 移动到 `crates/app_application`。修改代码以调用新的 `app_infrastructure` 和 `app_domain`。编译通过。
6.  **迁移 `server`**:
    *   将 `src/` 下的 `main.rs`, `startup.rs`, `config.rs` 移动到 `server/src/`。
    *   将 `src/app/controller` 和 `src/routes.rs` 的逻辑合并重构到 `server/src/routes/` 下的各个模块文件中。
    *   将 `src/app/middleware` 移动到 `server/src/middleware` (或 `app_common`，取决于其通用性)。
7.  **最终编译与测试**: 在根目录运行 `cargo check` 和 `cargo test`，确保整个 workspace 编译无误，且所有测试通过。
8.  **清理旧文件**: 验证无误后，删除原 `src/` 目录下的已迁移文件。

## 5. 风险评估和缓解措施

- **风险**: 路径和依赖关系错误导致大量编译失败。
  - **缓解**: 采用小步快跑、频繁编译的策略。每迁移一个模块，就立即运行 `cargo check` 解决编译错误，而不是等到最后。
- **风险**: 逻辑在迁移过程中丢失或被错误修改。
  - **缓解**: 严格遵循文件迁移映射表，不做任何逻辑功能的修改。迁移完成后，依赖现有的测试套件 (`cargo test`) 进行回归测试，确保功能一致性。
- **风险**: `async-trait` 和 `dyn Trait` 问题。
  - **缓解**: 在 Crate 之间传递 Trait 对象时，确保 `async-trait` 被正确使用。新架构下，接口（Trait）主要在 `app_application` 中定义，由 `app_infrastructure` 实现，`server` 中进行注入，这条路径上的 `async-trait` 使用需要特别注意。

## 6. 后续维护和扩展指南

- **添加新功能**:
    1.  **领域层**: 在 `app_domain` 中添加或修改实体和业务规则。
    2.  **应用层**: 在 `app_application` 中添加新的 `Service` 函数，编排领域逻辑。
    3.  **基础设施层**: 如果需要，在 `app_infrastructure` 中实现新的数据访问逻辑。
    4.  **表示层**: 在 `server/src/routes/` 下创建或修改对应的路由文件，添加新的 handler，并将其注册到 `server/src/startup.rs` 中。
- **团队协作**: 不同团队可以并行在不同的业务模块 Crate 中工作，只要接口（Trait）定义稳定，就可以减少互相干扰。

## 7. 迁移实施的详细提示词 (Prompt for Gemini)

这是最关键的部分。你可以将以下内容作为一个巨大的提示词，分步或一次性地提供给 Gemini CLI 来执行整个迁移过程。

```markdown
你好，现在请你作为一名资深 Rust 工程师，严格按照我们讨论的“方案：增强型模块化分层架构”来重构当前的 Axum 项目。请严格遵守以下步骤，每一步操作后都通过 `cargo check` 确保编译通过。

**全局要求:**
- **保持依赖不变**: 不要修改任何 `Cargo.toml` 文件中的依赖版本。
- **保持功能不变**: 迁移的目标是架构重构，不要添加、删除或修改任何业务逻辑。
- **小步验证**: 每完成一个文件或模块的创建/移动，就进行一次编译检查。

---

### **第零步：环境清理**

首先，删除 `target` 目录，以确保我们从一个干净的状态开始编译。

**操作:**
`rm -rf ./target`

---

### **第一步：创建新的目录结构和 `Cargo.toml` 文件**

1.  在项目根目录创建 `server` 目录和 `crates` 目录。
2.  在 `crates` 目录下，创建 `app_common`, `app_domain`, `app_application`, `app_infrastructure` 四个目录。
3.  为所有新创建的目录生成基础的 `src/lib.rs` 和 `Cargo.toml` 文件。

**操作:**

1.  创建目录：
    `mkdir server crates crates/app_common crates/app_application crates/app_domain crates/app_infrastructure`

2.  为 `server` 创建 `Cargo.toml` 并写入内容:
    `touch server/Cargo.toml`
    ```toml
    [package]
    name = "server"
    version = "0.1.0"
    edition = "2024"

    [dependencies]
    axum-tutorial = { path = "..", features = ["testing"] }
    app_common = { path = "../crates/app_common" }
    app_application = { path = "../crates/app_application" }
    app_infrastructure = { path = "../crates/app_infrastructure" }

    anyhow = "1.0"
    tokio = { version = "1", features = ["full"] }
    dotenvy = "0.15"
    ```

3.  为 `app_common` 创建 `Cargo.toml` 并写入内容:
    `touch crates/app_common/Cargo.toml`
    ```toml
    [package]
    name = "app_common"
    version = "0.1.0"
    edition = "2024"

    [dependencies]
    anyhow = "1.0"
    axum = { version = "0.8.4", features = ["ws", "macros"] }
    http = "1.1"
    hyper = "1.4"
    serde = { version = "1.0", features = ["derive"] }
    serde_json = "1.0"
    thiserror = "1.0"
    tracing = "0.1"
    jsonwebtoken = "9.3"
    uuid = { version = "1.11", features = ["v4", "serde"] }
    chrono = { version = "0.4.41", features = ["serde"] }
    validator = { version = "0.18", features = ["derive"] }
    ```

4.  为 `app_domain` 创建 `Cargo.toml` 并写入内容:
    `touch crates/app_domain/Cargo.toml`
    ```toml
    [package]
    name = "app_domain"
    version = "0.1.0"
    edition = "2024"

    [dependencies]
    serde = { version = "1.0", features = ["derive"] }
    uuid = { version = "1.11", features = ["v4", "serde"] }
    chrono = { version = "0.4.41", features = ["serde"] }
    app_common = { path = "../app_common" }
    ```

5.  为 `app_application` 创建 `Cargo.toml` 并写入内容:
    `touch crates/app_application/Cargo.toml`
    ```toml
    [package]
    name = "app_application"
    version = "0.1.0"
    edition = "2024"

    [dependencies]
    app_domain = { path = "../app_domain" }
    app_common = { path = "../app_common" }
    async-trait = "0.1"
    uuid = { version = "1.11", features = ["v4", "serde"] }
    argon2 = "0.5"
    rand_core = "0.6"
    jsonwebtoken = "9.3"
    tracing = "0.1"
    ```

6.  为 `app_infrastructure` 创建 `Cargo.toml` 并写入内容:
    `touch crates/app_infrastructure/Cargo.toml`
    ```toml
    [package]
    name = "app_infrastructure"
    version = "0.1.0"
    edition = "2024"

    [dependencies]
    app_domain = { path = "../app_domain" }
    app_common = { path = "../app_common" }
    app_application = { path = "../app_application" }
    sea-orm = { version = "1.1.12", features = ["sqlx-sqlite", "runtime-tokio-native-tls"] }
    async-trait = "0.1"
    tracing = "0.1"
    ```

7.  创建所有 `src` 目录和 `lib.rs`/`main.rs` 文件:
    `mkdir server/src crates/app_common/src crates/app_application/src crates/app_domain/src crates/app_infrastructure/src`
    `touch server/src/main.rs crates/app_common/src/lib.rs crates/app_application/src/lib.rs crates/app_domain/src/lib.rs crates/app_infrastructure/src/lib.rs`

8.  **修改根 `Cargo.toml`**: 更新 workspace 成员，并将 `[package]` 修改为 `[lib]`。
    ```toml
    [workspace]
    members = [
        ".",
        "server",
        "migration",
        "crates/app_common",
        "crates/app_domain",
        "crates/app_application",
        "crates/app_infrastructure"
    ]

    [lib]
    name = "axum_tutorial"
    path = "src/lib.rs"

    [dependencies]
    # ... 保持所有依赖不变 ...
    ```

9.  **验证**:
    `cargo check --all`

---

### **第二步：迁移文件**

逐个将旧 `src` 目录下的文件移动到新的目录结构中。

**操作:**

1.  **移动 `config.rs`, `startup.rs` 到 `server/src/`**:
    `mv src/config.rs server/src/config.rs`
    `mv src/startup.rs server/src/startup.rs`

2.  **移动 `error.rs` 到 `crates/app_common/src/`**:
    `mv src/error.rs crates/app_common/src/error.rs`
    在 `crates/app_common/src/lib.rs` 中添加: `pub mod error;`

3.  **移动 `app/utils` 到 `crates/app_common/src/`**:
    `mv src/app/utils crates/app_common/src/utils`
    在 `crates/app_common/src/lib.rs` 中添加: `pub mod utils;`

4.  **迁移 `Domain` 层**:
    - 将 `src/app/model` 移动到 `crates/app_domain/src/dto`。
    - 将 `migration/src/*_entity.rs` 文件中的 `struct` 定义复制到 `crates/app_domain/src/` 下对应的模块中。

5.  **迁移 `Infrastructure` 层**:
    - 将 `src/app/repository` 移动到 `crates/app_infrastructure/src/repository`。
    - 修改代码以依赖 `app_domain` 和 `app_common`。

6.  **迁移 `Application` 层**:
    - 将 `src/app/service` 移动到 `crates/app_application/src/service`。
    - 在 `app_application/src/lib.rs` 中定义业务服务的 Trait。

7.  **重构 `Server` (表示层)**:
    - 将 `src/app/controller` 的内容按业务领域拆分并移动到 `server/src/routes/`。
    - 重写 `server/src/routes/mod.rs` 组合路由。
    - 修改 `server/src/startup.rs` 调用新路由。
    - 将 `src/app/middleware` 移动到 `server/src/middleware`。

---

### **第三步：更新代码和依赖路径**

根据新的 Crate 结构，修复所有失效的 `use` 语句和模块引用。

**核心原则:**
- 在 `server` 中，`use crate::...` 指向 `server` 内部。要引用其他 Crate，使用 `use app_common::...`。
- 在 `crates/*` 中，`use crate::...` 指向其自身 Crate 内部。

**操作:**
1.  打开所有被移动的文件。
2.  修改 `use` 语句。
3.  **反复运行 `cargo check --all`**，根据编译器提示逐一修复路径错误。

---

### **第四步：最终验证和清理**

1.  当 `cargo check --all` 没有任何错误后，运行测试套件：
    `cargo test --all`
2.  确保所有测试都通过。
3.  手动启动服务器并测试所有 API 端点。
4.  确认一切正常后，删除旧的 `src` 目录下的已迁移文件。
