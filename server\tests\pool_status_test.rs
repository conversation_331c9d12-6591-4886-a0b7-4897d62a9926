/// 连接池状态监控集成测试
///
/// 测试数据库和缓存连接池状态监控处理器的功能
/// 验证API响应格式、数据完整性和性能指标
use axum::{extract::State, response::IntoResponse};
use axum_server::{
    AppConfig, ServiceContainerTrait,
    dependency_injection::{DefaultServiceContainerBuilder, ServiceContainerBuilder},
    routes::AppState,
    routes::handlers::{cache, database_health},
};
use sea_orm::Database;
use serde_json::Value;
use std::sync::Arc;

/// 测试数据库连接池状态监控处理器
#[tokio::test]
async fn test_database_pool_status_handler() {
    // 创建测试配置
    let config = AppConfig::default();

    // 创建内存数据库连接
    let database = Arc::new(
        Database::connect("sqlite::memory:")
            .await
            .expect("数据库连接失败"),
    );

    // 创建服务容器
    let container = DefaultServiceContainerBuilder::new()
        .with_config(config.clone())
        .with_database(database.clone())
        .with_all_services()
        .build()
        .expect("服务容器构建失败");

    // 创建应用状态
    let app_state = AppState {
        user_service: container.get_user_service(),
        task_service: container.get_task_service(),
        chat_service: container.get_chat_service(),
        resilient_chat_service: None,
        websocket_service: container.get_websocket_service(),
        db: container.get_database(),
        jwt_secret: config.jwt_secret.clone(),
    };

    // 直接调用处理器函数
    let result = database_health::get_database_pool_status(State(app_state)).await;

    // 验证结果
    assert!(result.is_ok(), "数据库连接池状态处理器应成功返回");

    let response = result.unwrap();
    let json: Value = serde_json::from_str(&serde_json::to_string(&response.0).unwrap()).unwrap();

    // 验证响应结构
    assert!(
        json["success"].as_bool().unwrap_or(false),
        "响应应标记为成功"
    );
    assert!(json["message"].as_str().is_some(), "响应应包含消息字段");
    assert!(json["data"].is_object(), "响应应包含数据对象");

    let data = &json["data"];

    // 验证连接池基本指标
    assert!(
        data["total_connections"].as_u64().is_some(),
        "应包含总连接数"
    );
    assert!(
        data["active_connections"].as_u64().is_some(),
        "应包含活跃连接数"
    );
    assert!(
        data["idle_connections"].as_u64().is_some(),
        "应包含空闲连接数"
    );
    assert!(
        data["max_connections"].as_u64().is_some(),
        "应包含最大连接数"
    );
    assert!(
        data["min_connections"].as_u64().is_some(),
        "应包含最小连接数"
    );

    // 验证性能指标
    assert!(
        data["acquire_success_count"].as_u64().is_some(),
        "应包含获取成功次数"
    );
    assert!(
        data["acquire_failure_count"].as_u64().is_some(),
        "应包含获取失败次数"
    );
    assert!(
        data["avg_acquire_time_ms"].as_f64().is_some(),
        "应包含平均获取时间"
    );
    assert!(data["success_rate"].as_f64().is_some(), "应包含成功率");
    assert!(
        data["pool_utilization"].as_f64().is_some(),
        "应包含连接池利用率"
    );

    // 验证健康状态
    assert!(data["is_healthy"].as_bool().is_some(), "应包含健康状态");
    assert!(
        data["last_health_check"].as_str().is_some(),
        "应包含最后健康检查时间"
    );

    // 验证配置信息
    let config = &data["config"];
    assert!(config.is_object(), "应包含配置对象");
    assert!(
        config["max_connections"].as_u64().is_some(),
        "配置应包含最大连接数"
    );
    assert!(
        config["min_connections"].as_u64().is_some(),
        "配置应包含最小连接数"
    );
    assert!(
        config["connect_timeout_secs"].as_u64().is_some(),
        "配置应包含连接超时时间"
    );

    // 验证数据逻辑一致性
    let total = data["total_connections"].as_u64().unwrap();
    let active = data["active_connections"].as_u64().unwrap();
    let idle = data["idle_connections"].as_u64().unwrap();
    assert_eq!(total, active + idle, "总连接数应等于活跃连接数加空闲连接数");

    let success_rate = data["success_rate"].as_f64().unwrap();
    assert!(
        success_rate >= 0.0 && success_rate <= 1.0,
        "成功率应在0-1之间"
    );

    println!("✅ 数据库连接池状态监控处理器测试通过");
}

/// 测试缓存连接池状态监控处理器
#[tokio::test]
async fn test_cache_pool_status_handler() {
    // 创建测试配置
    let config = AppConfig::default();

    // 创建内存数据库连接
    let database = Arc::new(
        Database::connect("sqlite::memory:")
            .await
            .expect("数据库连接失败"),
    );

    // 创建服务容器
    let container = DefaultServiceContainerBuilder::new()
        .with_config(config.clone())
        .with_database(database.clone())
        .with_all_services()
        .build()
        .expect("服务容器构建失败");

    // 创建应用状态
    let app_state = AppState {
        user_service: container.get_user_service(),
        task_service: container.get_task_service(),
        chat_service: container.get_chat_service(),
        resilient_chat_service: None,
        websocket_service: container.get_websocket_service(),
        db: container.get_database(),
        jwt_secret: config.jwt_secret.clone(),
    };

    // 直接调用处理器函数
    let result = cache::get_cache_pool_status(State(app_state)).await;

    // 验证结果 - 这个函数返回impl IntoResponse，我们需要转换
    let response = result.into_response();

    // 验证状态码
    assert_eq!(
        response.status(),
        200,
        "缓存连接池状态处理器应返回200状态码"
    );

    println!("✅ 缓存连接池状态监控处理器测试通过");
}

/// 测试连接池状态监控处理器的性能
#[tokio::test]
async fn test_pool_status_handler_performance() {
    // 创建测试配置
    let config = AppConfig::default();

    // 创建内存数据库连接
    let database = Arc::new(
        Database::connect("sqlite::memory:")
            .await
            .expect("数据库连接失败"),
    );

    // 创建服务容器
    let container = DefaultServiceContainerBuilder::new()
        .with_config(config.clone())
        .with_database(database.clone())
        .with_all_services()
        .build()
        .expect("服务容器构建失败");

    // 创建应用状态
    let app_state = AppState {
        user_service: container.get_user_service(),
        task_service: container.get_task_service(),
        chat_service: container.get_chat_service(),
        resilient_chat_service: None,
        websocket_service: container.get_websocket_service(),
        db: container.get_database(),
        jwt_secret: config.jwt_secret.clone(),
    };

    // 测试数据库连接池状态处理器性能
    let start = std::time::Instant::now();
    let result = database_health::get_database_pool_status(State(app_state.clone())).await;
    let db_duration = start.elapsed();

    assert!(result.is_ok(), "数据库连接池状态处理器应成功");
    assert!(
        db_duration.as_millis() < 100,
        "数据库连接池状态查询应在100ms内完成"
    );

    // 测试缓存连接池状态处理器性能
    let start = std::time::Instant::now();
    let _result = cache::get_cache_pool_status(State(app_state)).await;
    let cache_duration = start.elapsed();

    assert!(
        cache_duration.as_millis() < 100,
        "缓存连接池状态查询应在100ms内完成"
    );

    println!("✅ 连接池状态监控处理器性能测试通过");
    println!("   - 数据库连接池状态查询耗时: {:?}", db_duration);
    println!("   - 缓存连接池状态查询耗时: {:?}", cache_duration);
}
