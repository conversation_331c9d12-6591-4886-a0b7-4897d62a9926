{"testTasks": [{"id": "task-001", "title": "测试任务1", "description": "这是一个用于测试的任务", "status": "pending", "priority": "high", "created_by": "test-user-001", "created_at": "2025-01-01T00:00:00Z", "updated_at": "2025-01-01T00:00:00Z", "due_date": "2025-01-15T23:59:59Z"}, {"id": "task-002", "title": "测试任务2", "description": "另一个测试任务", "status": "in_progress", "priority": "medium", "created_by": "test-user-001", "created_at": "2025-01-02T00:00:00Z", "updated_at": "2025-01-02T12:00:00Z", "due_date": "2025-01-20T23:59:59Z"}, {"id": "task-003", "title": "已完成任务", "description": "这是一个已完成的任务", "status": "completed", "priority": "low", "created_by": "test-user-001", "created_at": "2025-01-01T00:00:00Z", "updated_at": "2025-01-03T15:30:00Z", "due_date": "2025-01-10T23:59:59Z"}], "taskTemplates": {"validTask": {"title": "新建测试任务", "description": "通过E2E测试创建的任务", "priority": "medium", "due_date": "2025-02-01T23:59:59Z"}, "invalidTasks": [{"title": "", "description": "标题为空的任务", "priority": "medium", "expectedError": "任务标题不能为空"}, {"title": "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa", "description": "标题过长的任务", "priority": "medium", "expectedError": "任务标题长度不能超过255个字符"}, {"title": "有效标题", "description": "这是一个非常长的描述，用于测试描述字段的长度限制。这个描述会重复很多次以达到超过2000个字符的长度限制。这是一个非常长的描述，用于测试描述字段的长度限制。这个描述会重复很多次以达到超过2000个字符的长度限制。这是一个非常长的描述，用于测试描述字段的长度限制。这个描述会重复很多次以达到超过2000个字符的长度限制。这是一个非常长的描述，用于测试描述字段的长度限制。这个描述会重复很多次以达到超过2000个字符的长度限制。这是一个非常长的描述，用于测试描述字段的长度限制。这个描述会重复很多次以达到超过2000个字符的长度限制。这是一个非常长的描述，用于测试描述字段的长度限制。这个描述会重复很多次以达到超过2000个字符的长度限制。这是一个非常长的描述，用于测试描述字段的长度限制。这个描述会重复很多次以达到超过2000个字符的长度限制。这是一个非常长的描述，用于测试描述字段的长度限制。这个描述会重复很多次以达到超过2000个字符的长度限制。这是一个非常长的描述，用于测试描述字段的长度限制。这个描述会重复很多次以达到超过2000个字符的长度限制。这是一个非常长的描述，用于测试描述字段的长度限制。这个描述会重复很多次以达到超过2000个字符的长度限制。这是一个非常长的描述，用于测试描述字段的长度限制。这个描述会重复很多次以达到超过2000个字符的长度限制。这是一个非常长的描述，用于测试描述字段的长度限制。这个描述会重复很多次以达到超过2000个字符的长度限制。这是一个非常长的描述，用于测试描述字段的长度限制。这个描述会重复很多次以达到超过2000个字符的长度限制。这是一个非常长的描述，用于测试描述字段的长度限制。这个描述会重复很多次以达到超过2000个字符的长度限制。这是一个非常长的描述，用于测试描述字段的长度限制。这个描述会重复很多次以达到超过2000个字符的长度限制。这是一个非常长的描述，用于测试描述字段的长度限制。这个描述会重复很多次以达到超过2000个字符的长度限制。这是一个非常长的描述，用于测试描述字段的长度限制。这个描述会重复很多次以达到超过2000个字符的长度限制。这是一个非常长的描述，用于测试描述字段的长度限制。这个描述会重复很多次以达到超过2000个字符的长度限制。这是一个非常长的描述，用于测试描述字段的长度限制。这个描述会重复很多次以达到超过2000个字符的长度限制。这是一个非常长的描述，用于测试描述字段的长度限制。这个描述会重复很多次以达到超过2000个字符的长度限制。", "priority": "medium", "expectedError": "任务描述长度不能超过2000个字符"}, {"title": "有效标题", "description": "有效描述", "priority": "invalid_priority", "expectedError": "无效的优先级"}]}, "updateTemplates": {"statusUpdates": [{"from": "pending", "to": "in_progress", "description": "开始处理任务"}, {"from": "in_progress", "to": "completed", "description": "任务已完成"}, {"from": "pending", "to": "cancelled", "description": "取消任务"}], "priorityUpdates": [{"from": "low", "to": "high", "reason": "紧急情况"}, {"from": "high", "to": "medium", "reason": "优先级调整"}]}}