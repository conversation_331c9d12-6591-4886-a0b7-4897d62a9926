/**
 * WebSocket UI调试测试
 * 
 * 专门调试UI状态更新问题
 */

const { chromium } = require('playwright');

async function runWebSocketUIDebugTest() {
    console.log('🔍 开始WebSocket UI调试测试...');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 2000 // 减慢操作速度以便观察
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        console.log(`[浏览器-${msg.type()}] ${msg.text()}`);
    });
    
    try {
        // 导航和登录
        await page.goto('http://127.0.0.1:3000');
        await page.waitForLoadState('networkidle');
        
        await page.click('#loginTab');
        await page.fill('#loginUsername', 'testuser456');
        await page.fill('#loginPassword', 'password123');
        await page.click('#loginForm button[type="submit"]');
        await page.waitForSelector('#currentUser:has-text("testuser456")', { timeout: 10000 });
        
        console.log('✅ 登录成功');
        
        // 获取元素
        const connectBtn = page.locator('#wsConnectBtn');
        const disconnectBtn = page.locator('#wsDisconnectBtn');
        const connectionStatus = page.locator('#connectionStatus');
        const messageInput = page.locator('#messageInput');
        const sendBtn = page.locator('#sendBtn');
        
        // 检查初始状态
        console.log('\n🔍 检查初始状态:');
        console.log(`连接按钮启用: ${await connectBtn.isEnabled()}`);
        console.log(`断开按钮启用: ${await disconnectBtn.isEnabled()}`);
        console.log(`消息输入框启用: ${await messageInput.isEnabled()}`);
        console.log(`发送按钮启用: ${await sendBtn.isEnabled()}`);
        console.log(`连接状态: ${await connectionStatus.textContent()}`);
        
        // 连接WebSocket
        console.log('\n📡 连接WebSocket...');
        await connectBtn.click();
        await page.waitForTimeout(3000);
        
        console.log('\n🔍 检查连接后状态:');
        console.log(`连接按钮启用: ${await connectBtn.isEnabled()}`);
        console.log(`断开按钮启用: ${await disconnectBtn.isEnabled()}`);
        console.log(`消息输入框启用: ${await messageInput.isEnabled()}`);
        console.log(`发送按钮启用: ${await sendBtn.isEnabled()}`);
        console.log(`连接状态: ${await connectionStatus.textContent()}`);
        
        // 如果消息输入框启用，尝试发送消息
        if (await messageInput.isEnabled()) {
            console.log('\n📝 尝试发送消息...');
            await messageInput.fill('UI调试测试消息');
            await sendBtn.click();
            await page.waitForTimeout(1000);
            
            const chatMessages = page.locator('#chatMessages');
            const messageExists = await chatMessages.locator(':has-text("UI调试测试消息")').count() > 0;
            console.log(`消息发送成功: ${messageExists}`);
        } else {
            console.log('❌ 消息输入框被禁用，无法发送消息');
            
            // 检查DOM元素的实际状态
            const inputElement = await page.$('#messageInput');
            const inputDisabled = await inputElement.getAttribute('disabled');
            console.log(`输入框disabled属性: ${inputDisabled}`);
            
            // 检查JavaScript变量状态
            const jsState = await page.evaluate(() => {
                return {
                    isConnected: window.isConnected,
                    socket: window.socket ? 'exists' : 'null',
                    socketReadyState: window.socket ? window.socket.readyState : 'N/A'
                };
            });
            console.log('JavaScript状态:', jsState);
        }
        
        // 断开连接
        console.log('\n📡 断开WebSocket...');
        await disconnectBtn.click();
        await page.waitForTimeout(2000);
        
        console.log('\n🔍 检查断开后状态:');
        console.log(`连接按钮启用: ${await connectBtn.isEnabled()}`);
        console.log(`断开按钮启用: ${await disconnectBtn.isEnabled()}`);
        console.log(`消息输入框启用: ${await messageInput.isEnabled()}`);
        console.log(`发送按钮启用: ${await sendBtn.isEnabled()}`);
        console.log(`连接状态: ${await connectionStatus.textContent()}`);
        
        // 再次连接
        console.log('\n📡 再次连接WebSocket...');
        await connectBtn.click();
        await page.waitForTimeout(3000);
        
        console.log('\n🔍 检查再次连接后状态:');
        console.log(`连接按钮启用: ${await connectBtn.isEnabled()}`);
        console.log(`断开按钮启用: ${await disconnectBtn.isEnabled()}`);
        console.log(`消息输入框启用: ${await messageInput.isEnabled()}`);
        console.log(`发送按钮启用: ${await sendBtn.isEnabled()}`);
        console.log(`连接状态: ${await connectionStatus.textContent()}`);
        
        // 检查chatElements对象
        const chatElementsState = await page.evaluate(() => {
            return {
                messageInputExists: !!window.chatElements?.messageInput,
                messageInputDisabled: window.chatElements?.messageInput?.disabled,
                sendBtnExists: !!window.chatElements?.sendBtn,
                sendBtnDisabled: window.chatElements?.sendBtn?.disabled,
                connectionStatusText: window.chatElements?.connectionStatus?.textContent
            };
        });
        console.log('\nchatElements状态:', chatElementsState);
        
    } catch (error) {
        console.error('❌ 测试执行失败:', error);
    } finally {
        await browser.close();
        console.log('🏁 UI调试测试完成');
    }
}

// 运行测试
if (require.main === module) {
    runWebSocketUIDebugTest().catch(console.error);
}

module.exports = { runWebSocketUIDebugTest };
