# Development Preferences
- Windows 10 x86 system with Rust 2024 Edition and Axum 0.8.4 framework at D:\ceshi\ceshi\axum-tutorial
- Mandatory Context7 MCP verification before writing code or fixing errors
- Enterprise-grade code quality: TDD approach, >95% test coverage with cargo llvm-cov HTML reports, SOLID/DRY principles
- Use package managers (cargo update/outdated) instead of manual config editing
- Prefer simpler/incremental solutions before complex ones

# Development Workflow
- MCP tool verification before writing code
- Rust/Clippy warnings workflow: identify type → Context7 MCP documentation queries → TDD solutions → str-replace-editor modifications → test verification
- Error analysis: compile verification → error classification → prioritized fixing → quality validation

# Project Specifics
- Enterprise-level million-concurrent chat application backend using Axum framework
- Performance targets: >100k QPS throughput, million-concurrent scalability
- SeaORM with native-tls backend

# Testing
- TDD approach with mandatory unit/integration tests for all functions/modules
- >95% code coverage target using cargo llvm-cov HTML reports
- E2E testing with Playwright MCP
- Validation: cargo check/clippy/fmt

