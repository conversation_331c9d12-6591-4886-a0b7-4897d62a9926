//! # 搜索结果预计算系统测试
//!
//! 测试搜索结果预计算系统的核心功能，包括：
//! - 热门搜索词分析和识别
//! - 预计算任务调度和执行
//! - 搜索结果预生成和缓存
//! - 缓存预热和更新机制
//! - 性能监控和统计

use app_application::{HotQueryStats, PrecomputeScheduler, PrecomputeSchedulerConfig};
use app_domain::entities::search_task::{
    PrecomputeScheduleStrategy, PrecomputeTask, PrecomputeTaskType, SearchTaskPriority,
};
use app_infrastructure::cache::{
    CacheConfig, MultiTierCacheService, PrecomputeCache, PrecomputeCacheConfig,
    PrecomputeTaskStatus, PrecomputedResult, create_multi_tier_cache_service,
};
use chrono::{DateTime, Utc};
use serde_json::json;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::time::{Duration, sleep};
use uuid::Uuid;

/// 测试预计算调度器的基本功能
#[tokio::test]
async fn test_precompute_scheduler_basic_functionality() {
    // 创建调度器配置
    let config = PrecomputeSchedulerConfig {
        max_concurrent_tasks: 5,
        hot_query_analysis_interval: 1, // 1秒用于测试
        precompute_cache_ttl: 300,
        min_search_frequency: 3,
        max_hot_queries: 10,
        task_timeout_seconds: 30,
        stats_retention_days: 7,
    };

    // 创建预计算调度器
    let scheduler = PrecomputeScheduler::new(config);

    // 测试调度器启动
    assert!(scheduler.start().await.is_ok());

    // 模拟搜索查询统计更新
    let user_id = Uuid::new_v4();
    assert!(
        scheduler
            .update_search_stats("rust programming", 150, user_id)
            .await
            .is_ok()
    );
    assert!(
        scheduler
            .update_search_stats("rust programming", 120, user_id)
            .await
            .is_ok()
    );
    assert!(
        scheduler
            .update_search_stats("rust programming", 180, user_id)
            .await
            .is_ok()
    );
    assert!(
        scheduler
            .update_search_stats("axum framework", 200, user_id)
            .await
            .is_ok()
    );

    // 等待一段时间让统计数据处理
    sleep(Duration::from_millis(100)).await;

    // 获取热门搜索词
    let hot_queries = scheduler.get_hot_queries(5).await;
    assert!(!hot_queries.is_empty());

    // 验证热门搜索词包含我们添加的查询
    let rust_query = hot_queries.iter().find(|q| q.query == "rust programming");
    assert!(rust_query.is_some());

    let rust_stats = rust_query.unwrap();
    assert_eq!(rust_stats.frequency, 3);
    assert!(rust_stats.avg_response_time_ms > 0.0);

    // 获取调度器统计信息
    let stats = scheduler.get_stats().await;
    assert!(stats.hot_queries_count > 0);

    // 测试调度器停止
    assert!(scheduler.stop().await.is_ok());
}

/// 测试预计算任务调度
#[tokio::test]
async fn test_precompute_task_scheduling() {
    let config = PrecomputeSchedulerConfig::default();
    let scheduler = PrecomputeScheduler::new(config);

    // 启动调度器
    assert!(scheduler.start().await.is_ok());

    // 创建预计算任务
    let task = PrecomputeTask::new(
        PrecomputeTaskType::ResultPregeneration,
        PrecomputeScheduleStrategy::EventDriven,
        Utc::now(),
    )
    .with_target_query("test query".to_string())
    .with_priority(SearchTaskPriority::High)
    .with_param("test_param", "test_value");

    // 调度任务
    let task_id = scheduler.schedule_task(task).await;
    assert!(task_id.is_ok());

    // 等待任务处理
    sleep(Duration::from_millis(200)).await;

    // 验证统计信息更新
    let stats = scheduler.get_stats().await;
    assert!(stats.total_tasks > 0);

    // 停止调度器
    assert!(scheduler.stop().await.is_ok());
}

/// 测试预计算缓存功能
#[tokio::test]
async fn test_precompute_cache_functionality() {
    // 创建缓存配置
    let cache_config = CacheConfig::default();
    let cache_service = create_multi_tier_cache_service(cache_config).await;

    // 如果缓存服务创建失败，跳过测试
    if cache_service.is_err() {
        println!("跳过预计算缓存测试：无法连接到缓存服务");
        return;
    }

    let cache_service = cache_service.unwrap();
    let precompute_cache_config = PrecomputeCacheConfig::default();
    let precompute_cache = PrecomputeCache::new(cache_service, precompute_cache_config);

    // 创建预计算结果
    let mut metadata = HashMap::new();
    metadata.insert("source".to_string(), "test".to_string());

    let precomputed_result = PrecomputedResult {
        query: "test query".to_string(),
        result_data: json!({
            "messages": [
                {"id": 1, "content": "Test message 1"},
                {"id": 2, "content": "Test message 2"}
            ]
        }),
        total_count: 2,
        computed_at: Utc::now(),
        expires_at: Utc::now() + chrono::Duration::hours(1),
        frequency: 15,
        avg_response_time_ms: 120.5,
        cache_hits: 0,
        version: 1,
        metadata,
    };

    // 测试缓存预计算结果
    let cache_result = precompute_cache
        .cache_precomputed_result(&precomputed_result)
        .await;
    assert!(cache_result.is_ok());

    // 测试获取预计算结果
    let retrieved_result = precompute_cache.get_precomputed_result("test query").await;
    assert!(retrieved_result.is_ok());

    let result = retrieved_result.unwrap();
    assert!(result.is_some());

    let cached_result = result.unwrap();
    assert_eq!(cached_result.query, "test query");
    assert_eq!(cached_result.total_count, 2);
    assert_eq!(cached_result.frequency, 15);

    // 测试任务状态缓存
    let task_id = Uuid::new_v4();
    let task_status = PrecomputeTaskStatus {
        task_id,
        status: "processing".to_string(),
        progress: 50,
        started_at: Some(Utc::now()),
        estimated_completion: Some(Utc::now() + chrono::Duration::minutes(5)),
        error_message: None,
        execution_stats: None,
    };

    // 缓存任务状态
    let status_cache_result = precompute_cache.cache_task_status(&task_status).await;
    assert!(status_cache_result.is_ok());

    // 获取任务状态
    let retrieved_status = precompute_cache.get_task_status(task_id).await;
    assert!(retrieved_status.is_ok());

    let status = retrieved_status.unwrap();
    assert!(status.is_some());

    let cached_status = status.unwrap();
    assert_eq!(cached_status.task_id, task_id);
    assert_eq!(cached_status.status, "processing");
    assert_eq!(cached_status.progress, 50);

    // 测试缓存失效
    let invalidation_result = precompute_cache
        .invalidate_precomputed_result("test query")
        .await;
    assert!(invalidation_result.is_ok());

    // 验证缓存已失效
    let after_invalidation = precompute_cache.get_precomputed_result("test query").await;
    assert!(after_invalidation.is_ok());
    assert!(after_invalidation.unwrap().is_none());

    // 获取缓存统计信息
    let cache_stats = precompute_cache.get_cache_stats().await;
    assert!(cache_stats.total_entries > 0);
    assert!(cache_stats.invalidation_count > 0);
}

/// 测试热门搜索词识别和预计算任务创建
#[tokio::test]
async fn test_hot_query_identification() {
    let config = PrecomputeSchedulerConfig {
        min_search_frequency: 2, // 降低阈值用于测试
        hot_query_analysis_interval: 1,
        ..Default::default()
    };

    let scheduler = PrecomputeScheduler::new(config);
    assert!(scheduler.start().await.is_ok());

    let user_id = Uuid::new_v4();

    // 模拟多次搜索同一个查询
    for _ in 0..5 {
        assert!(
            scheduler
                .update_search_stats("popular query", 100, user_id)
                .await
                .is_ok()
        );
    }

    // 等待热门搜索词分析
    sleep(Duration::from_millis(1500)).await;

    // 获取热门搜索词
    let hot_queries = scheduler.get_hot_queries(10).await;
    let popular_query = hot_queries.iter().find(|q| q.query == "popular query");

    assert!(popular_query.is_some());
    let query_stats = popular_query.unwrap();
    assert_eq!(query_stats.frequency, 5);
    assert!(query_stats.precomputed); // 应该已经被标记为预计算

    // 验证统计信息
    let stats = scheduler.get_stats().await;
    assert!(stats.total_tasks > 0); // 应该有预计算任务被创建

    assert!(scheduler.stop().await.is_ok());
}

/// 测试预计算任务的不同类型执行
#[tokio::test]
async fn test_different_precompute_task_types() {
    let config = PrecomputeSchedulerConfig::default();
    let scheduler = PrecomputeScheduler::new(config);

    assert!(scheduler.start().await.is_ok());

    // 测试不同类型的预计算任务
    let task_types = vec![
        PrecomputeTaskType::HotQueryAnalysis,
        PrecomputeTaskType::ResultPregeneration,
        PrecomputeTaskType::CacheWarmup,
        PrecomputeTaskType::StatisticsUpdate,
        PrecomputeTaskType::ExpiredDataCleanup,
    ];

    for task_type in task_types {
        let task = PrecomputeTask::new(
            task_type.clone(),
            PrecomputeScheduleStrategy::EventDriven,
            Utc::now(),
        )
        .with_priority(SearchTaskPriority::Normal);

        let task_id = scheduler.schedule_task(task).await;
        assert!(
            task_id.is_ok(),
            "Failed to schedule task type: {:?}",
            task_type
        );
    }

    // 等待任务执行
    sleep(Duration::from_millis(1000)).await;

    // 验证任务执行统计
    let stats = scheduler.get_stats().await;
    assert!(stats.total_tasks >= 5);

    assert!(scheduler.stop().await.is_ok());
}

/// 测试预计算系统的性能监控
#[tokio::test]
async fn test_precompute_performance_monitoring() {
    let config = PrecomputeSchedulerConfig::default();
    let scheduler = PrecomputeScheduler::new(config);

    assert!(scheduler.start().await.is_ok());

    // 创建多个预计算任务
    for i in 0..3 {
        let task = PrecomputeTask::new(
            PrecomputeTaskType::ResultPregeneration,
            PrecomputeScheduleStrategy::EventDriven,
            Utc::now(),
        )
        .with_target_query(format!("test query {}", i))
        .with_priority(SearchTaskPriority::Normal);

        assert!(scheduler.schedule_task(task).await.is_ok());
    }

    // 等待任务执行
    sleep(Duration::from_millis(800)).await;

    // 检查性能统计
    let stats = scheduler.get_stats().await;
    assert!(stats.total_tasks >= 3);
    assert!(stats.avg_task_execution_time_ms >= 0.0);

    // 验证热门搜索词统计
    let user_id = Uuid::new_v4();
    for i in 0..3 {
        assert!(
            scheduler
                .update_search_stats(
                    &format!("performance test {}", i),
                    100 + (i as u64) * 10,
                    user_id
                )
                .await
                .is_ok()
        );
    }

    let hot_queries = scheduler.get_hot_queries(5).await;
    assert!(!hot_queries.is_empty());

    // 验证每个查询的统计信息
    for query_stats in &hot_queries {
        assert!(!query_stats.query.is_empty());
        assert!(query_stats.frequency > 0);
        assert!(query_stats.avg_response_time_ms > 0.0);
    }

    assert!(scheduler.stop().await.is_ok());
}
