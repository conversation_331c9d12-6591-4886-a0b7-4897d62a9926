/**
 * 消息搜索API简单测试
 * 专注于测试searchMessages方法的核心功能
 */

// 简单的mock设置
global.fetch = jest.fn();
global.console = { log: jest.fn(), warn: jest.fn(), error: jest.fn() };
global.localStorage = { getItem: jest.fn(() => 'test-token') };

// Mock auth模块
jest.mock('../static/js/modules/auth.js', () => ({
    getAuthToken: () => 'test-token',
    isAuthenticated: () => true
}));

describe('消息搜索API测试', () => {
    let chatAPI;

    beforeAll(() => {
        // 动态导入模块
        const apiModule = require('../static/js/modules/api.js');
        chatAPI = apiModule.chatAPI;
    });

    beforeEach(() => {
        jest.clearAllMocks();
        fetch.mockClear();
        
        // 清理缓存
        if (chatAPI && chatAPI.clearSearchCache) {
            chatAPI.clearSearchCache();
        }
    });

    describe('参数验证', () => {
        test('空关键词应该抛出错误', async () => {
            await expect(chatAPI.searchMessages({}))
                .rejects.toThrow('搜索关键词不能为空');
        });

        test('空白关键词应该抛出错误', async () => {
            await expect(chatAPI.searchMessages({ keyword: '   ' }))
                .rejects.toThrow('搜索关键词不能为空白字符');
        });

        test('过长关键词应该抛出错误', async () => {
            const longKeyword = 'a'.repeat(101);
            await expect(chatAPI.searchMessages({ keyword: longKeyword }))
                .rejects.toThrow('搜索关键词长度不能超过100个字符');
        });
    });

    describe('基本搜索功能', () => {
        test('成功搜索消息', async () => {
            const mockResponse = {
                success: true,
                data: {
                    messages: [{
                        id: 'msg-1',
                        content: '测试消息',
                        sender_username: '用户1',
                        room_name: '全局聊天室',
                        created_at: '2025-07-23T10:00:00Z',
                        message_type: 'text'
                    }],
                    total_count: 1,
                    page: 1,
                    limit: 20,
                    keyword: '测试'
                }
            };

            fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => mockResponse
            });

            const result = await chatAPI.searchMessages({ keyword: '测试' });

            expect(fetch).toHaveBeenCalledWith(
                expect.stringContaining('/api/messages/search'),
                expect.objectContaining({
                    method: 'GET',
                    headers: expect.objectContaining({
                        'Authorization': 'Bearer test-token'
                    })
                })
            );

            expect(result).toEqual(mockResponse);
        });

        test('处理所有搜索参数', async () => {
            const mockResponse = {
                success: true,
                data: {
                    messages: [],
                    total_count: 0,
                    page: 2,
                    limit: 10,
                    keyword: '高级搜索'
                }
            };

            fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => mockResponse
            });

            const searchParams = {
                keyword: '高级搜索',
                message_type: 'text',
                sender: 'user-123',
                room_id: 'room-456',
                start_date: '2025-07-20T00:00:00Z',
                end_date: '2025-07-23T23:59:59Z',
                page: 2,
                limit: 10
            };

            await chatAPI.searchMessages(searchParams);

            expect(fetch).toHaveBeenCalledWith(
                expect.stringContaining('keyword=%E9%AB%98%E7%BA%A7%E6%90%9C%E7%B4%A2'),
                expect.any(Object)
            );
            expect(fetch).toHaveBeenCalledWith(
                expect.stringContaining('message_type=text'),
                expect.any(Object)
            );
            expect(fetch).toHaveBeenCalledWith(
                expect.stringContaining('page=2&limit=10'),
                expect.any(Object)
            );
        });
    });

    describe('缓存功能', () => {
        test('缓存搜索结果', async () => {
            const mockResponse = {
                success: true,
                data: {
                    messages: [{ id: 'msg-1', content: '缓存测试' }],
                    total_count: 1,
                    page: 1,
                    limit: 20,
                    keyword: '缓存'
                }
            };

            fetch.mockResolvedValue({
                ok: true,
                status: 200,
                json: async () => mockResponse
            });

            // 第一次调用
            await chatAPI.searchMessages({ keyword: '缓存' });
            expect(fetch).toHaveBeenCalledTimes(1);

            // 第二次调用应该使用缓存
            await chatAPI.searchMessages({ keyword: '缓存' });
            expect(fetch).toHaveBeenCalledTimes(1); // 仍然是1次
        });

        test('禁用缓存时不使用缓存', async () => {
            const mockResponse = {
                success: true,
                data: {
                    messages: [],
                    total_count: 0,
                    page: 1,
                    limit: 20,
                    keyword: '无缓存'
                }
            };

            fetch.mockResolvedValue({
                ok: true,
                status: 200,
                json: async () => mockResponse
            });

            // 两次调用都禁用缓存
            await chatAPI.searchMessages({ keyword: '无缓存' }, { useCache: false });
            await chatAPI.searchMessages({ keyword: '无缓存' }, { useCache: false });

            expect(fetch).toHaveBeenCalledTimes(2);
        });
    });

    describe('错误处理', () => {
        test('处理网络错误', async () => {
            fetch.mockRejectedValueOnce(new Error('网络连接失败'));

            await expect(chatAPI.searchMessages({ keyword: '网络错误' }))
                .rejects.toThrow('网络连接失败');
        });

        test('处理HTTP错误状态', async () => {
            fetch.mockResolvedValueOnce({
                ok: false,
                status: 500,
                statusText: 'Internal Server Error',
                json: async () => ({
                    success: false,
                    error: '服务器内部错误'
                })
            });

            await expect(chatAPI.searchMessages({ keyword: '服务器错误' }))
                .rejects.toThrow();
        });
    });

    describe('分页支持', () => {
        test('支持分页参数', async () => {
            const mockResponse = {
                success: true,
                data: {
                    messages: [],
                    total_count: 100,
                    page: 3,
                    limit: 15,
                    keyword: '分页测试'
                }
            };

            fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => mockResponse
            });

            await chatAPI.searchMessages({
                keyword: '分页测试',
                page: 3,
                limit: 15
            });

            expect(fetch).toHaveBeenCalledWith(
                expect.stringContaining('page=3&limit=15'),
                expect.any(Object)
            );
        });

        test('使用默认分页参数', async () => {
            const mockResponse = {
                success: true,
                data: {
                    messages: [],
                    total_count: 0,
                    page: 1,
                    limit: 20,
                    keyword: '默认分页'
                }
            };

            fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => mockResponse
            });

            await chatAPI.searchMessages({ keyword: '默认分页' });

            expect(fetch).toHaveBeenCalledWith(
                expect.stringContaining('page=1&limit=20'),
                expect.any(Object)
            );
        });
    });
});
