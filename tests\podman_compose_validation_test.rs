// Podman Compose配置验证测试
// 验证podman-compose.yml文件的正确性和最佳实践合规性
// 基于Context7 MCP最佳实践和TDD开发模式

use serde_yaml::{self, Value};
use std::collections::HashMap;
use std::fs;
use std::path::Path;

#[cfg(test)]
mod podman_compose_validation_tests {
    use super::*;

    /// 测试podman-compose.yml文件是否存在
    #[test]
    fn test_podman_compose_file_exists() {
        let compose_file_path = "podman-compose.yml";
        assert!(
            Path::new(compose_file_path).exists(),
            "podman-compose.yml文件不存在"
        );
    }

    /// 测试YAML文件格式是否正确
    #[test]
    fn test_yaml_format_validity() {
        let compose_content =
            fs::read_to_string("podman-compose.yml").expect("无法读取podman-compose.yml文件");

        let parsed_yaml: Result<Value, _> = serde_yaml::from_str(&compose_content);
        assert!(parsed_yaml.is_ok(), "YAML格式无效: {:?}", parsed_yaml.err());
    }

    /// 测试必需的服务是否存在
    #[test]
    fn test_required_services_exist() {
        let compose_content =
            fs::read_to_string("podman-compose.yml").expect("无法读取podman-compose.yml文件");

        let yaml: Value = serde_yaml::from_str(&compose_content).expect("YAML解析失败");

        let services = yaml["services"]
            .as_mapping()
            .expect("services部分不存在或格式错误");

        // 验证必需的服务
        let required_services = vec!["postgres", "dragonflydb"];
        for service in required_services {
            assert!(
                services.contains_key(&Value::String(service.to_string())),
                "必需的服务 '{}' 不存在",
                service
            );
        }
    }

    /// 测试PostgreSQL配置
    #[test]
    fn test_postgresql_configuration() {
        let compose_content =
            fs::read_to_string("podman-compose.yml").expect("无法读取podman-compose.yml文件");

        let yaml: Value = serde_yaml::from_str(&compose_content).expect("YAML解析失败");

        let postgres = &yaml["services"]["postgres"];

        // 验证镜像版本
        let image = postgres["image"].as_str().expect("PostgreSQL镜像配置缺失");
        assert!(
            image.contains("postgres:17"),
            "PostgreSQL版本应为17，当前: {}",
            image
        );

        // 验证环境变量
        let env = postgres["environment"]
            .as_mapping()
            .expect("PostgreSQL环境变量配置缺失");

        assert!(env.contains_key(&Value::String("POSTGRES_DB".to_string())));
        assert!(env.contains_key(&Value::String("POSTGRES_USER".to_string())));
        assert!(env.contains_key(&Value::String("POSTGRES_PASSWORD".to_string())));

        // 验证端口映射
        let ports = postgres["ports"]
            .as_sequence()
            .expect("PostgreSQL端口配置缺失");
        assert!(
            ports
                .iter()
                .any(|p| p.as_str().unwrap_or("").contains("5432:5432")),
            "PostgreSQL端口5432映射缺失"
        );

        // 验证健康检查
        assert!(
            postgres["healthcheck"].is_mapping(),
            "PostgreSQL健康检查配置缺失"
        );
    }

    /// 测试DragonflyDB配置
    #[test]
    fn test_dragonflydb_configuration() {
        let compose_content =
            fs::read_to_string("podman-compose.yml").expect("无法读取podman-compose.yml文件");

        let yaml: Value = serde_yaml::from_str(&compose_content).expect("YAML解析失败");

        let dragonflydb = &yaml["services"]["dragonflydb"];

        // 验证镜像
        let image = dragonflydb["image"]
            .as_str()
            .expect("DragonflyDB镜像配置缺失");
        assert!(
            image.contains("dragonflydb/dragonfly"),
            "DragonflyDB镜像配置错误: {}",
            image
        );

        // 验证端口映射
        let ports = dragonflydb["ports"]
            .as_sequence()
            .expect("DragonflyDB端口配置缺失");
        assert!(
            ports
                .iter()
                .any(|p| p.as_str().unwrap_or("").contains("6379:6379")),
            "DragonflyDB端口6379映射缺失"
        );

        // 验证启动命令
        let command = dragonflydb["command"]
            .as_str()
            .expect("DragonflyDB启动命令缺失");
        assert!(
            command.contains("dragonfly"),
            "DragonflyDB启动命令应包含'dragonfly'"
        );

        // 验证健康检查
        assert!(
            dragonflydb["healthcheck"].is_mapping(),
            "DragonflyDB健康检查配置缺失"
        );
    }

    /// 测试网络配置
    #[test]
    fn test_network_configuration() {
        let compose_content =
            fs::read_to_string("podman-compose.yml").expect("无法读取podman-compose.yml文件");

        let yaml: Value = serde_yaml::from_str(&compose_content).expect("YAML解析失败");

        // 验证网络定义
        let networks = yaml["networks"].as_mapping().expect("网络配置缺失");

        assert!(
            networks.contains_key(&Value::String("axum_network".to_string())),
            "axum_network网络定义缺失"
        );

        // 验证服务网络配置
        let services = yaml["services"].as_mapping().expect("services部分不存在");

        for (service_name, service_config) in services {
            let networks = service_config["networks"].as_sequence();
            if let Some(nets) = networks {
                assert!(
                    nets.iter()
                        .any(|n| n.as_str().unwrap_or("") == "axum_network"),
                    "服务 {} 未连接到axum_network",
                    service_name.as_str().unwrap_or("unknown")
                );
            }
        }
    }

    /// 测试数据卷配置
    #[test]
    fn test_volume_configuration() {
        let compose_content =
            fs::read_to_string("podman-compose.yml").expect("无法读取podman-compose.yml文件");

        let yaml: Value = serde_yaml::from_str(&compose_content).expect("YAML解析失败");

        // 验证数据卷定义
        let volumes = yaml["volumes"].as_mapping().expect("数据卷配置缺失");

        let required_volumes = vec!["postgres_17_data", "dragonflydb_data"];

        for volume in required_volumes {
            assert!(
                volumes.contains_key(&Value::String(volume.to_string())),
                "必需的数据卷 '{}' 不存在",
                volume
            );
        }
    }

    /// 测试资源限制配置
    #[test]
    fn test_resource_limits() {
        let compose_content =
            fs::read_to_string("podman-compose.yml").expect("无法读取podman-compose.yml文件");

        let yaml: Value = serde_yaml::from_str(&compose_content).expect("YAML解析失败");

        let services = yaml["services"].as_mapping().expect("services部分不存在");

        // 验证主要服务的资源限制
        let critical_services = vec!["postgres", "dragonflydb"];

        for service_name in critical_services {
            let service = &services[&Value::String(service_name.to_string())];
            let deploy = service["deploy"].as_mapping();

            if let Some(deploy_config) = deploy {
                let resources = deploy_config.get(&Value::String("resources".to_string()));
                assert!(
                    resources.is_some(),
                    "服务 {} 缺少资源限制配置",
                    service_name
                );
            }
        }
    }

    /// 测试安全配置
    #[test]
    fn test_security_configuration() {
        let compose_content =
            fs::read_to_string("podman-compose.yml").expect("无法读取podman-compose.yml文件");

        let yaml: Value = serde_yaml::from_str(&compose_content).expect("YAML解析失败");

        let services = yaml["services"].as_mapping().expect("services部分不存在");

        // 验证PostgreSQL密码配置
        let postgres = &services[&Value::String("postgres".to_string())];
        let pg_env = postgres["environment"]
            .as_mapping()
            .expect("PostgreSQL环境变量缺失");

        let pg_password = pg_env.get(&Value::String("POSTGRES_PASSWORD".to_string()));
        assert!(pg_password.is_some(), "PostgreSQL密码配置缺失");

        // 验证DragonflyDB密码配置
        let dragonflydb = &services[&Value::String("dragonflydb".to_string())];
        let df_command = dragonflydb["command"]
            .as_str()
            .expect("DragonflyDB命令缺失");

        assert!(
            df_command.contains("--requirepass="),
            "DragonflyDB密码配置缺失"
        );
    }

    /// 测试监控配置
    #[test]
    fn test_monitoring_configuration() {
        let compose_content =
            fs::read_to_string("podman-compose.yml").expect("无法读取podman-compose.yml文件");

        let yaml: Value = serde_yaml::from_str(&compose_content).expect("YAML解析失败");

        let services = yaml["services"].as_mapping().expect("services部分不存在");

        // 验证监控服务存在
        let monitoring_services = vec!["prometheus", "grafana"];

        for service in monitoring_services {
            assert!(
                services.contains_key(&Value::String(service.to_string())),
                "监控服务 '{}' 不存在",
                service
            );
        }
    }

    /// 测试配置文件依赖
    #[test]
    fn test_configuration_file_dependencies() {
        // 验证PostgreSQL配置文件
        assert!(
            Path::new("config/postgresql.conf").exists(),
            "PostgreSQL配置文件不存在"
        );

        assert!(
            Path::new("config/pg_hba.conf").exists(),
            "PostgreSQL HBA配置文件不存在"
        );

        // 验证初始化脚本
        assert!(
            Path::new("scripts/init-db.sql").exists(),
            "数据库初始化脚本不存在"
        );

        // 验证监控配置
        assert!(
            Path::new("monitoring/prometheus.yml").exists(),
            "Prometheus配置文件不存在"
        );
    }
}

/// 辅助函数：验证YAML配置的完整性
pub fn validate_compose_configuration() -> Result<(), String> {
    let compose_content = fs::read_to_string("podman-compose.yml")
        .map_err(|e| format!("无法读取podman-compose.yml: {}", e))?;

    let _yaml: Value =
        serde_yaml::from_str(&compose_content).map_err(|e| format!("YAML解析失败: {}", e))?;

    println!("✅ podman-compose.yml配置验证通过");
    Ok(())
}
