//! # WebSocket实时监控面板测试
//!
//! 测试WebSocket实时监控面板的功能，包括统计数据获取、实时更新、图表展示等

use axum::http::StatusCode;
use serde_json::{Value, json};
use std::time::Duration;
use tokio::time::sleep;

use crate::test_utils::{TestClient, create_test_app};

/// 测试WebSocket统计信息API端点
#[tokio::test]
async fn test_websocket_stats_endpoint() {
    let app = create_test_app().await;
    let client = TestClient::new(app);

    // 发送GET请求获取WebSocket统计信息
    let response = client.get("/api/websocket/stats").send().await;

    assert_eq!(response.status(), StatusCode::OK);

    let stats: Value = response.json().await;

    // 验证响应结构
    assert!(stats.get("connection_stats").is_some());
    assert!(stats.get("message_stats").is_some());
    assert!(stats.get("timestamp").is_some());

    // 验证连接统计字段
    let connection_stats = &stats["connection_stats"];
    assert!(connection_stats.get("active_connections").is_some());
    assert!(connection_stats.get("total_connections").is_some());
    assert!(connection_stats.get("unique_users").is_some());
    assert!(connection_stats.get("connections_by_type").is_some());

    // 验证消息统计字段
    let message_stats = &stats["message_stats"];
    assert!(message_stats.get("total_sent").is_some());
    assert!(message_stats.get("total_received").is_some());
    assert!(message_stats.get("messages_by_type").is_some());
    assert!(message_stats.get("avg_message_size").is_some());
}

/// 测试WebSocket连接信息API端点
#[tokio::test]
async fn test_websocket_connections_endpoint() {
    let app = create_test_app().await;
    let client = TestClient::new(app);

    let response = client.get("/api/websocket/connections").send().await;

    assert_eq!(response.status(), StatusCode::OK);

    let connections: Value = response.json().await;

    // 验证响应结构
    assert!(connections.get("active_connections").is_some());
    assert!(connections.get("total_connections_today").is_some());
    assert!(connections.get("connections_by_type").is_some());
    assert!(connections.get("connection_quality").is_some());
    assert!(connections.get("timestamp").is_some());
}

/// 测试WebSocket性能指标API端点
#[tokio::test]
async fn test_websocket_metrics_endpoint() {
    let app = create_test_app().await;
    let client = TestClient::new(app);

    let response = client.get("/api/websocket/metrics").send().await;

    assert_eq!(response.status(), StatusCode::OK);

    let metrics: Value = response.json().await;

    // 验证响应结构
    assert!(metrics.get("performance").is_some());
    assert!(metrics.get("reliability").is_some());
    assert!(metrics.get("resource_usage").is_some());
    assert!(metrics.get("error_statistics").is_some());

    // 验证性能指标字段
    let performance = &metrics["performance"];
    assert!(performance.get("message_throughput_per_second").is_some());
    assert!(performance.get("average_latency_ms").is_some());
    assert!(performance.get("p95_latency_ms").is_some());
    assert!(performance.get("p99_latency_ms").is_some());
}

/// 测试WebSocket稳定性状态API端点
#[tokio::test]
async fn test_websocket_stability_endpoint() {
    let app = create_test_app().await;
    let client = TestClient::new(app);

    let response = client.get("/api/websocket/stability").send().await;

    assert_eq!(response.status(), StatusCode::OK);

    let stability: Value = response.json().await;

    // 验证响应结构
    assert!(stability.get("stability").is_some());
    assert!(stability.get("heartbeat").is_some());
    assert!(stability.get("reconnection").is_some());
    assert!(stability.get("timestamp").is_some());

    // 验证稳定性指标
    let stability_metrics = &stability["stability"];
    assert!(stability_metrics.get("total_connections").is_some());
    assert!(stability_metrics.get("active_connections").is_some());
    assert!(stability_metrics.get("healthy_connections").is_some());
    assert!(stability_metrics.get("health_rate").is_some());
}

/// 测试实时数据更新功能
#[tokio::test]
async fn test_realtime_data_updates() {
    let app = create_test_app().await;
    let client = TestClient::new(app);

    // 获取初始统计数据
    let initial_response = client.get("/api/websocket/stats").send().await;

    let initial_stats: Value = initial_response.json().await;
    let initial_timestamp = initial_stats["timestamp"].as_str().unwrap();

    // 等待一段时间
    sleep(Duration::from_millis(100)).await;

    // 再次获取统计数据
    let updated_response = client.get("/api/websocket/stats").send().await;

    let updated_stats: Value = updated_response.json().await;
    let updated_timestamp = updated_stats["timestamp"].as_str().unwrap();

    // 验证时间戳已更新
    assert_ne!(initial_timestamp, updated_timestamp);
}

/// 测试监控面板HTML页面
#[tokio::test]
async fn test_monitoring_dashboard_page() {
    let app = create_test_app().await;
    let client = TestClient::new(app);

    let response = client.get("/websocket-stats.html").send().await;

    assert_eq!(response.status(), StatusCode::OK);

    let content_type = response
        .headers()
        .get("content-type")
        .unwrap()
        .to_str()
        .unwrap();

    assert!(content_type.contains("text/html"));
}

/// 测试错误处理
#[tokio::test]
async fn test_error_handling() {
    let app = create_test_app().await;
    let client = TestClient::new(app);

    // 测试不存在的端点
    let response = client.get("/api/websocket/nonexistent").send().await;

    assert_eq!(response.status(), StatusCode::NOT_FOUND);
}

/// 测试数据格式验证
#[tokio::test]
async fn test_data_format_validation() {
    let app = create_test_app().await;
    let client = TestClient::new(app);

    let response = client.get("/api/websocket/stats").send().await;

    let stats: Value = response.json().await;

    // 验证数值类型
    let connection_stats = &stats["connection_stats"];
    assert!(connection_stats["active_connections"].is_u64());
    assert!(connection_stats["total_connections"].is_u64());
    assert!(connection_stats["unique_users"].is_u64());

    let message_stats = &stats["message_stats"];
    assert!(message_stats["total_sent"].is_u64());
    assert!(message_stats["total_received"].is_u64());
    assert!(message_stats["avg_message_size"].is_f64());
}

/// 测试并发访问
#[tokio::test]
async fn test_concurrent_access() {
    let app = create_test_app().await;
    let client = TestClient::new(app);

    // 创建多个并发请求
    let mut handles = vec![];

    for _ in 0..10 {
        let client_clone = client.clone();
        let handle = tokio::spawn(async move {
            let response = client_clone.get("/api/websocket/stats").send().await;

            assert_eq!(response.status(), StatusCode::OK);
            response.json::<Value>().await
        });
        handles.push(handle);
    }

    // 等待所有请求完成
    for handle in handles {
        let stats = handle.await.unwrap();
        assert!(stats.get("connection_stats").is_some());
        assert!(stats.get("message_stats").is_some());
    }
}
