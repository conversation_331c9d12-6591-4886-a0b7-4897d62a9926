//! # 任务23：错误处理机制测试
//!
//! 基于Context7 MCP最佳实践，验证：
//! - thiserror错误定义
//! - anyhow错误上下文传播
//! - 统一错误响应格式
//! - 错误日志记录
//! - 错误恢复策略

use anyhow::{Context, Result as AnyhowResult};
use app_common::{
    error::AppError,
    utils::{
        ErrorCategory, ErrorContext, ErrorHandlerTool as <PERSON><PERSON>r<PERSON><PERSON><PERSON>,
        ErrorRecoveryTool as ErrorRecoveryManager, ErrorSeverity, RecoveryStrategy,
    },
};
use axum::{
    Json, Router,
    body::Body,
    extract::Request,
    http::{Method, StatusCode, Uri},
    response::Response,
    routing::{get, post},
};
use serde_json::{Value, json};
use std::time::Duration;
use tokio::time::sleep;
use tower::ServiceExt;
use uuid::Uuid;

/// 测试thiserror错误定义
#[tokio::test]
async fn test_thiserror_error_definition() {
    // 测试任务未找到错误
    let task_id = Uuid::new_v4();
    let error = AppError::TaskNotFound(task_id);

    // 验证错误消息格式
    let error_message = error.to_string();
    assert!(error_message.contains(&task_id.to_string()));
    assert!(error_message.contains("未找到ID为"));

    // 测试用户已存在错误
    let username = "testuser";
    let error = AppError::UserAlreadyExists(username.to_string());
    let error_message = error.to_string();
    assert!(error_message.contains(username));
    assert!(error_message.contains("已存在"));

    println!("✅ thiserror错误定义测试通过");
}

/// 测试anyhow错误上下文传播
#[tokio::test]
async fn test_anyhow_context_propagation() {
    // 模拟一个可能失败的操作
    fn failing_operation() -> AnyhowResult<String> {
        Err(anyhow::anyhow!("底层操作失败"))
    }

    // 使用Context添加上下文信息
    let result = failing_operation()
        .context("执行用户注册操作时")
        .context("处理HTTP请求时");

    match result {
        Err(error) => {
            let error_chain = format!("{:#}", error);
            assert!(error_chain.contains("处理HTTP请求时"));
            assert!(error_chain.contains("执行用户注册操作时"));
            assert!(error_chain.contains("底层操作失败"));
        }
        Ok(_) => panic!("期望错误但得到成功结果"),
    }

    // 测试转换为AppError
    let app_error = AppError::External(anyhow::anyhow("外部服务错误").context("调用用户服务时"));
    let error_message = app_error.to_string();
    assert!(error_message.contains("调用用户服务时"));

    println!("✅ anyhow错误上下文传播测试通过");
}

/// 测试错误处理器
#[tokio::test]
async fn test_error_handler() {
    // 测试不同类型的错误处理
    let test_cases = vec![
        (
            AppError::TaskNotFound(Uuid::new_v4()),
            StatusCode::NOT_FOUND,
            ErrorCategory::UserInput,
            ErrorSeverity::Low,
        ),
        (
            AppError::Forbidden("权限不足".to_string()),
            StatusCode::FORBIDDEN,
            ErrorCategory::Authentication,
            ErrorSeverity::Medium,
        ),
        (
            AppError::DatabaseError("连接失败".to_string()),
            StatusCode::INTERNAL_SERVER_ERROR,
            ErrorCategory::Database,
            ErrorSeverity::High,
        ),
        (
            AppError::RateLimited,
            StatusCode::TOO_MANY_REQUESTS,
            ErrorCategory::System,
            ErrorSeverity::Medium,
        ),
    ];

    for (error, expected_status, expected_category, expected_severity) in test_cases {
        let (status_code, context) = ErrorHandler::handle_error(&error);

        assert_eq!(status_code, expected_status);
        assert_eq!(context.category, expected_category);
        assert_eq!(context.severity, expected_severity);
        assert!(!context.error_id.is_empty());
        assert!(!context.user_message.is_empty());
    }

    println!("✅ 错误处理器测试通过");
}

/// 测试错误恢复策略
#[tokio::test]
async fn test_error_recovery_strategies() {
    let test_cases = vec![
        (AppError::Timeout, "Retry"),
        (AppError::ServiceUnavailable, "Retry"),
        (AppError::RateLimited, "Retry"),
        (AppError::InvalidCredentials, "FailFast"),
        (AppError::Forbidden("test".to_string()), "FailFast"),
    ];

    for (error, expected_strategy_type) in test_cases {
        let strategy = ErrorRecoveryManager::get_recovery_strategy(&error);

        match (&strategy, expected_strategy_type) {
            (RecoveryStrategy::Retry { .. }, "Retry") => {
                // 验证重试策略
                if let RecoveryStrategy::Retry {
                    max_attempts,
                    delay_ms,
                } = strategy
                {
                    assert!(max_attempts > 0);
                    assert!(delay_ms > 0);
                }
            }
            (RecoveryStrategy::FailFast, "FailFast") => {
                // 验证快速失败策略
            }
            (RecoveryStrategy::Fallback, "Fallback") => {
                // 验证降级策略
            }
            _ => panic!(
                "错误恢复策略不匹配: 期望 {}, 得到 {:?}",
                expected_strategy_type, strategy
            ),
        }
    }

    println!("✅ 错误恢复策略测试通过");
}

/// 测试错误上下文创建
#[tokio::test]
async fn test_error_context_creation() {
    let context = ErrorContext::new(ErrorCategory::UserInput, ErrorSeverity::Low, "测试错误消息")
        .with_technical_details("技术详细信息")
        .with_suggested_action("建议的解决方案")
        .with_request_id("req-123");

    assert_eq!(context.category, ErrorCategory::UserInput);
    assert_eq!(context.severity, ErrorSeverity::Low);
    assert_eq!(context.user_message, "测试错误消息");
    assert_eq!(context.technical_details, Some("技术详细信息".to_string()));
    assert_eq!(context.suggested_action, Some("建议的解决方案".to_string()));
    assert_eq!(context.request_id, Some("req-123".to_string()));
    assert!(!context.error_id.is_empty());

    println!("✅ 错误上下文创建测试通过");
}

/// 测试HTTP错误响应
#[tokio::test]
async fn test_http_error_responses() {
    // 创建测试路由
    async fn test_handler() -> Result<Json<Value>, AppError> {
        Err(AppError::TaskNotFound(Uuid::new_v4()))
    }

    let app = Router::new().route("/test", get(test_handler));

    // 发送测试请求
    let request = Request::builder()
        .method(Method::GET)
        .uri("/test")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();

    // 验证响应状态码
    assert_eq!(response.status(), StatusCode::NOT_FOUND);

    // 验证响应体包含错误信息
    let body = axum::body::to_bytes(response.into_body(), usize::MAX)
        .await
        .unwrap();
    let response_json: Value = serde_json::from_slice(&body).unwrap();

    assert_eq!(response_json["success"], false);
    assert!(response_json["error"].is_object());
    assert!(response_json["error"]["code"].is_string());
    assert!(response_json["error"]["message"].is_string());

    println!("✅ HTTP错误响应测试通过");
}

/// 测试日志记录
#[tokio::test]
async fn test_error_logging() {
    // 测试错误上下文日志记录
    let context = ErrorContext::new(
        ErrorCategory::Database,
        ErrorSeverity::High,
        "数据库连接失败",
    );

    // 验证上下文包含必要信息
    assert_eq!(context.category, ErrorCategory::Database);
    assert_eq!(context.severity, ErrorSeverity::High);
    assert!(!context.error_id.is_empty());

    println!("✅ 错误日志记录测试通过");
}

/// 集成测试：完整的错误处理流程
#[tokio::test]
async fn test_complete_error_handling_flow() {
    // 模拟一个复杂的错误场景
    async fn complex_operation() -> Result<String, AppError> {
        // 模拟数据库操作失败
        let db_result: AnyhowResult<String> = Err(anyhow::anyhow!("数据库连接超时"));

        let result = db_result
            .context("执行用户查询操作")
            .context("处理用户登录请求")
            .map_err(AppError::External)?;

        Ok(result)
    }

    // 执行操作并处理错误
    match complex_operation().await {
        Ok(_) => panic!("期望错误但得到成功结果"),
        Err(error) => {
            // 使用错误处理器处理错误
            let (status_code, context) = ErrorHandler::handle_error(&error);

            // 验证错误处理结果
            assert_eq!(status_code, StatusCode::BAD_GATEWAY);
            assert_eq!(context.category, ErrorCategory::ExternalService);
            assert_eq!(context.severity, ErrorSeverity::Medium);

            // 验证错误消息包含上下文信息
            assert!(context.technical_details.is_some());
            let technical_details = context.technical_details.unwrap();
            assert!(technical_details.contains("处理用户登录请求"));

            // 获取恢复策略
            let recovery_strategy = ErrorRecoveryManager::get_recovery_strategy(&error);
            assert!(matches!(recovery_strategy, RecoveryStrategy::FailFast));
        }
    }

    println!("✅ 完整错误处理流程测试通过");
}

/// 性能测试：错误处理性能
#[tokio::test]
async fn test_error_handling_performance() {
    let start = std::time::Instant::now();

    // 创建大量错误并处理
    for _ in 0..1000 {
        let error = AppError::TaskNotFound(Uuid::new_v4());
        let (_status, _context) = ErrorHandler::handle_error(&error);
    }

    let duration = start.elapsed();

    // 验证性能要求（1000个错误处理应该在100ms内完成）
    assert!(
        duration < Duration::from_millis(100),
        "错误处理性能不达标: {:?}",
        duration
    );

    println!("✅ 错误处理性能测试通过 (耗时: {:?})", duration);
}
