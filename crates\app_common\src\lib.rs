//! # App Common
//!
//! 公共模块，提供跨层共享的基础功能：
//! - 错误处理
//! - 共享DTO
//! - 工具函数
//! - 通用类型定义
//! - 中间件组件
//! - 日志记录系统
//! - 异步trait优化

// ============================================================================
// 模块声明 - 按功能分组
// ============================================================================

// 核心功能模块
pub mod adapters;
pub mod compatibility;
pub mod config;
pub mod error;
pub mod utils;

// 基础设施模块
pub mod logging;
pub mod middleware;
pub mod monitoring;

// 性能优化模块
pub mod async_trait_optimization;

// 测试配置模块
#[cfg(any(test, feature = "testing"))]
pub mod test_config;

// ============================================================================
// 核心类型重新导出 - 按使用频率排序
// ============================================================================

// 最常用的错误处理类型
pub use error::{AppError, Result};

// 认证和权限控制类型（现在从middleware和utils模块导出）
pub use middleware::{
    AuthenticatedUser, Permission, UnifiedPermissionChecker, UserRole,
    create_default_permission_checker, create_permission_middleware_state,
    inject_authenticated_user, inject_permission_state, require_admin, require_delete_permission,
    require_manager, require_write_permission,
};
pub use utils::{AuthService, Claims, JwtError, JwtUtils, jwt_utils::ExtendedClaims};

// 配置相关类型
pub use config::{AppConfig, ConfigError, ConfigResult, DatabasePoolConfig, ServerConfig};

// 工具函数和服务 - 按功能分组
pub use utils::{
    // 数据转换相关
    BatchConverter,
    // 资源管理相关
    ConnectionMetrics,
    ConversionResult,
    DataConverter,
    DatabasePoolManager,
    EntityToActiveModel,
    EnumConverter,
    // 错误处理相关
    ErrorHandler,
    ErrorRecoveryConfig,
    ErrorRecoveryManager,
    ErrorResponseBuilder,
    MemoryManager,
    MemoryMetrics,
    ModelToEntity,
    PoolMetrics,
    RecoveryStatus,
    TokenExtractionMethod,
    WebSocketPoolManager,
};

// 异步trait优化相关
pub use async_trait_optimization::{
    AsyncTraitOptimizer, AsyncTraitOptimizerFactory, AsyncTraitPerformanceTester,
    DefaultAsyncTraitOptimizer,
};

// ============================================================================
// 外部依赖重新导出 - 仅导出必要的依赖
// ============================================================================

// 核心异步支持
pub use async_trait;

// 序列化支持
pub use serde;
pub use serde_json;

// 时间处理
pub use chrono;

// 唯一标识符
pub use uuid;

// 验证支持
pub use validator;

// 日志支持
pub use tracing;

// 错误处理
pub use anyhow;
