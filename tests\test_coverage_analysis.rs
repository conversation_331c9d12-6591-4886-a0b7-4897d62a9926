//! # 测试覆盖率分析和重构
//!
//! 根据Context7 MCP最佳实践，分析项目测试覆盖率并重构重复的测试代码模式
//! 遵循TDD方法：先写测试，再实现功能

use std::collections::HashMap;
use std::fs;
use std::path::Path;

/// 测试覆盖率分析器
#[derive(Debug, <PERSON>lone)]
pub struct TestCoverageAnalyzer {
    /// 测试文件路径映射
    pub test_files: HashMap<String, Vec<String>>,
    /// 重复测试模式统计
    pub duplicate_patterns: HashMap<String, u32>,
    /// 覆盖率统计
    pub coverage_stats: CoverageStats,
}

/// 覆盖率统计信息
#[derive(Debu<PERSON>, <PERSON><PERSON>, Default)]
pub struct CoverageStats {
    /// 总测试数量
    pub total_tests: u32,
    /// 单元测试数量
    pub unit_tests: u32,
    /// 集成测试数量
    pub integration_tests: u32,
    /// 文档测试数量
    pub doc_tests: u32,
    /// 重复测试模式数量
    pub duplicate_patterns: u32,
    /// 测试覆盖率百分比
    pub coverage_percentage: f64,
}

impl Default for TestCoverageAnalyzer {
    fn default() -> Self {
        Self::new()
    }
}

impl TestCoverageAnalyzer {
    /// 创建新的测试覆盖率分析器
    pub fn new() -> Self {
        Self {
            test_files: HashMap::new(),
            duplicate_patterns: HashMap::new(),
            coverage_stats: CoverageStats::default(),
        }
    }

    /// 扫描项目中的所有测试文件
    pub fn scan_test_files(
        &mut self,
        project_root: &str,
    ) -> Result<(), Box<dyn std::error::Error>> {
        self.scan_directory(project_root)?;
        self.analyze_coverage();
        Ok(())
    }

    /// 递归扫描目录中的测试文件
    fn scan_directory(&mut self, dir_path: &str) -> Result<(), Box<dyn std::error::Error>> {
        let path = Path::new(dir_path);
        if !path.exists() {
            return Ok(());
        }

        for entry in fs::read_dir(path)? {
            let entry = entry?;
            let file_path = entry.path();

            if file_path.is_dir() {
                // 递归扫描子目录
                if let Some(dir_name) = file_path.file_name() {
                    if dir_name != "target" && dir_name != ".git" {
                        self.scan_directory(&file_path.to_string_lossy())?;
                    }
                }
            } else if file_path.extension().is_some_and(|ext| ext == "rs") {
                self.analyze_test_file(&file_path)?;
            }
        }
        Ok(())
    }

    /// 分析单个测试文件
    fn analyze_test_file(&mut self, file_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
        let content = fs::read_to_string(file_path)?;
        let file_path_str = file_path.to_string_lossy().to_string();

        // 检查是否是测试文件
        if self.is_test_file(&content, &file_path_str) {
            let test_functions = self.extract_test_functions(&content);
            self.test_files
                .insert(file_path_str.clone(), test_functions.clone());

            // 分析重复模式
            self.analyze_duplicate_patterns(&content, &test_functions);

            // 更新统计信息
            self.update_coverage_stats(&file_path_str, &test_functions);
        }
        Ok(())
    }

    /// 检查文件是否是测试文件
    pub fn is_test_file(&self, content: &str, file_path: &str) -> bool {
        // 检查文件路径是否包含测试相关目录
        let is_test_dir = file_path.contains("/tests/")
            || file_path.contains("\\tests\\")
            || file_path.starts_with("tests/")
            || file_path.starts_with("tests\\");

        // 检查文件内容是否包含测试相关标记
        let has_test_attr = content.contains("#[test]") || content.contains("#[tokio::test]");
        let has_cfg_test = content.contains("#[cfg(test)]");
        let has_test_mod = content.contains("mod test") || content.contains("mod tests");

        is_test_dir || has_test_attr || has_cfg_test || has_test_mod
    }

    /// 提取测试函数名称
    pub fn extract_test_functions(&self, content: &str) -> Vec<String> {
        let mut test_functions = Vec::new();
        let lines: Vec<&str> = content.lines().collect();

        for (i, line) in lines.iter().enumerate() {
            if line.trim().starts_with("#[test]") || line.trim().starts_with("#[tokio::test]") {
                // 查找下一行的函数定义
                if let Some(next_line) = lines.get(i + 1) {
                    if let Some(fn_name) = self.extract_function_name(next_line) {
                        test_functions.push(fn_name);
                    }
                }
            }
        }

        test_functions
    }

    /// 从函数定义行提取函数名
    pub fn extract_function_name(&self, line: &str) -> Option<String> {
        let trimmed = line.trim();
        if trimmed.starts_with("fn ") || trimmed.starts_with("async fn ") {
            let parts: Vec<&str> = trimmed.split_whitespace().collect();
            // 对于 "fn name(" 或 "async fn name("
            let fn_index = if trimmed.starts_with("async") { 2 } else { 1 };
            if parts.len() > fn_index {
                let fn_name = parts[fn_index].split('(').next()?;
                return Some(fn_name.to_string());
            }
        }
        None
    }

    /// 分析重复的测试模式
    pub fn analyze_duplicate_patterns(&mut self, content: &str, test_functions: &[String]) {
        // 分析常见的测试模式
        for test_fn in test_functions {
            // 检查测试函数名模式
            if test_fn.starts_with("test_") {
                let pattern = self.extract_test_pattern(test_fn);
                *self.duplicate_patterns.entry(pattern).or_insert(0) += 1;
            }
        }

        // 分析测试代码中的重复模式
        self.analyze_code_patterns(content);
    }

    /// 提取测试模式
    pub fn extract_test_pattern(&self, test_fn: &str) -> String {
        // 提取测试函数的模式，例如 test_create_*, test_validate_*, 等
        let parts: Vec<&str> = test_fn.split('_').collect();
        if parts.len() >= 3 {
            format!("{}_{}_*", parts[0], parts[1])
        } else {
            test_fn.to_string()
        }
    }

    /// 分析代码中的重复模式
    fn analyze_code_patterns(&mut self, content: &str) {
        // 检查常见的重复代码模式
        let patterns = [
            "assert_eq!",
            "assert!",
            "expect(",
            "unwrap()",
            "create_test_",
            "setup_test_",
            "mock_",
        ];

        for pattern in &patterns {
            let count = content.matches(pattern).count() as u32;
            if count > 1 {
                *self
                    .duplicate_patterns
                    .entry(pattern.to_string())
                    .or_insert(0) += count;
            }
        }
    }

    /// 更新覆盖率统计
    pub fn update_coverage_stats(&mut self, file_path: &str, test_functions: &[String]) {
        self.coverage_stats.total_tests += test_functions.len() as u32;

        // 根据文件路径和内容分类测试类型
        let is_integration_test = file_path.contains("/tests/")
            || file_path.contains("\\tests\\")
            || file_path.starts_with("tests/")
            || file_path.starts_with("tests\\");

        if is_integration_test {
            self.coverage_stats.integration_tests += test_functions.len() as u32;
        } else {
            self.coverage_stats.unit_tests += test_functions.len() as u32;
        }
    }

    /// 分析整体覆盖率
    pub fn analyze_coverage(&mut self) {
        // 计算重复模式数量
        self.coverage_stats.duplicate_patterns = self.duplicate_patterns.len() as u32;

        // 简单的覆盖率计算（基于测试文件数量）
        let total_files = self.test_files.len() as f64;
        if total_files > 0.0 {
            self.coverage_stats.coverage_percentage = (total_files / 10.0) * 100.0; // 假设目标是10个测试文件
            if self.coverage_stats.coverage_percentage > 100.0 {
                self.coverage_stats.coverage_percentage = 100.0;
            }
        }
    }

    /// 获取覆盖率统计信息
    pub fn get_coverage_stats(&self) -> &CoverageStats {
        &self.coverage_stats
    }

    /// 获取可变的覆盖率统计信息（用于测试）
    #[allow(dead_code)]
    pub fn get_coverage_stats_mut(&mut self) -> &mut CoverageStats {
        &mut self.coverage_stats
    }

    /// 获取重复模式报告
    #[allow(dead_code)]
    pub fn get_duplicate_patterns(&self) -> &HashMap<String, u32> {
        &self.duplicate_patterns
    }

    /// 生成覆盖率报告
    pub fn generate_report(&self) -> String {
        let mut report = String::new();

        report.push_str("# 测试覆盖率分析报告\n\n");

        // 基本统计信息
        report.push_str("## 基本统计\n");
        report.push_str(&format!(
            "- 总测试数量: {}\n",
            self.coverage_stats.total_tests
        ));
        report.push_str(&format!("- 单元测试: {}\n", self.coverage_stats.unit_tests));
        report.push_str(&format!(
            "- 集成测试: {}\n",
            self.coverage_stats.integration_tests
        ));
        report.push_str(&format!("- 文档测试: {}\n", self.coverage_stats.doc_tests));
        report.push_str(&format!(
            "- 覆盖率: {:.1}%\n\n",
            self.coverage_stats.coverage_percentage
        ));

        // 重复模式分析
        report.push_str("## 重复模式分析\n");
        if self.duplicate_patterns.is_empty() {
            report.push_str("✅ 未发现明显的重复测试模式\n\n");
        } else {
            for (pattern, count) in &self.duplicate_patterns {
                if *count > 2 {
                    report.push_str(&format!("⚠️  模式 '{pattern}' 出现 {count} 次\n"));
                }
            }
            report.push('\n');
        }

        // 测试文件列表
        report.push_str("## 测试文件列表\n");
        for (file_path, test_functions) in &self.test_files {
            report.push_str(&format!("### {file_path}\n"));
            for test_fn in test_functions {
                report.push_str(&format!("- {test_fn}\n"));
            }
            report.push('\n');
        }

        report
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_coverage_analyzer_creation() {
        let analyzer = TestCoverageAnalyzer::new();
        assert_eq!(analyzer.test_files.len(), 0);
        assert_eq!(analyzer.duplicate_patterns.len(), 0);
        assert_eq!(analyzer.coverage_stats.total_tests, 0);
    }

    #[test]
    fn test_is_test_file_detection() {
        let analyzer = TestCoverageAnalyzer::new();

        // 测试包含 #[test] 的文件
        let test_content = r#"
            #[test]
            fn test_something() {
                assert_eq!(1, 1);
            }
        "#;
        assert!(analyzer.is_test_file(test_content, "src/lib.rs"));

        // 测试包含 #[cfg(test)] 的文件
        let cfg_test_content = r#"
            #[cfg(test)]
            mod tests {
                #[test]
                fn test_something() {}
            }
        "#;
        assert!(analyzer.is_test_file(cfg_test_content, "src/lib.rs"));

        // 测试路径包含 tests 目录的文件
        assert!(analyzer.is_test_file("fn main() {}", "tests/integration_test.rs"));

        // 测试普通文件
        assert!(!analyzer.is_test_file("fn main() {}", "src/main.rs"));
    }

    #[test]
    fn test_extract_function_name() {
        let analyzer = TestCoverageAnalyzer::new();

        assert_eq!(
            analyzer.extract_function_name("fn test_something() {"),
            Some("test_something".to_string())
        );

        assert_eq!(
            analyzer.extract_function_name("    fn test_with_spaces() -> Result<(), Error> {"),
            Some("test_with_spaces".to_string())
        );

        assert_eq!(analyzer.extract_function_name("not a function"), None);
    }

    #[test]
    fn test_extract_test_pattern() {
        let analyzer = TestCoverageAnalyzer::new();

        assert_eq!(
            analyzer.extract_test_pattern("test_create_user"),
            "test_create_*"
        );

        assert_eq!(
            analyzer.extract_test_pattern("test_validate_input"),
            "test_validate_*"
        );

        assert_eq!(analyzer.extract_test_pattern("test_simple"), "test_simple");
    }

    #[test]
    fn test_extract_test_functions() {
        let analyzer = TestCoverageAnalyzer::new();

        let content = r#"
            #[test]
            fn test_first() {
                assert!(true);
            }

            #[tokio::test]
            async fn test_async() {
                assert!(true);
            }

            fn not_a_test() {
                // This should not be included
            }
        "#;

        let functions = analyzer.extract_test_functions(content);
        assert_eq!(functions.len(), 2);
        assert!(functions.contains(&"test_first".to_string()));
        assert!(functions.contains(&"test_async".to_string()));
        assert!(!functions.contains(&"not_a_test".to_string()));
    }

    #[test]
    fn test_analyze_code_patterns() {
        let mut analyzer = TestCoverageAnalyzer::new();

        let content = r#"
            #[test]
            fn test_multiple_asserts() {
                assert_eq!(1, 1);
                assert_eq!(2, 2);
                assert!(true);
                assert!(false);
                let result = something().expect("should work");
                let other = other_thing().unwrap();
            }
        "#;

        analyzer.analyze_code_patterns(content);

        // 检查是否检测到重复模式
        assert!(analyzer.duplicate_patterns.contains_key("assert_eq!"));
        assert!(analyzer.duplicate_patterns.contains_key("assert!"));
        assert_eq!(*analyzer.duplicate_patterns.get("assert_eq!").unwrap(), 2);
        assert_eq!(*analyzer.duplicate_patterns.get("assert!").unwrap(), 2);
    }

    #[test]
    fn test_coverage_stats_calculation() {
        let mut analyzer = TestCoverageAnalyzer::new();

        // 模拟添加一些测试文件
        analyzer.test_files.insert(
            "tests/integration_test.rs".to_string(),
            vec!["test_1".to_string(), "test_2".to_string()],
        );
        analyzer
            .test_files
            .insert("src/lib.rs".to_string(), vec!["test_unit".to_string()]);

        // 更新统计信息
        analyzer.update_coverage_stats(
            "tests/integration_test.rs",
            &["test_1".to_string(), "test_2".to_string()],
        );
        analyzer.update_coverage_stats("src/lib.rs", &["test_unit".to_string()]);
        analyzer.analyze_coverage();

        let stats = analyzer.get_coverage_stats();
        assert_eq!(stats.total_tests, 3);
        assert_eq!(stats.integration_tests, 2);
        assert_eq!(stats.unit_tests, 1);
    }

    #[test]
    fn test_generate_report() {
        let mut analyzer = TestCoverageAnalyzer::new();

        // 添加一些测试数据
        analyzer
            .test_files
            .insert("test_file.rs".to_string(), vec!["test_example".to_string()]);
        analyzer
            .duplicate_patterns
            .insert("test_create_*".to_string(), 5);
        analyzer.coverage_stats.total_tests = 10;
        analyzer.coverage_stats.unit_tests = 8;
        analyzer.coverage_stats.integration_tests = 2;
        analyzer.coverage_stats.coverage_percentage = 85.0;

        let report = analyzer.generate_report();

        // 验证报告内容
        assert!(report.contains("# 测试覆盖率分析报告"));
        assert!(report.contains("总测试数量: 10"));
        assert!(report.contains("单元测试: 8"));
        assert!(report.contains("集成测试: 2"));
        assert!(report.contains("覆盖率: 85.0%"));
        assert!(report.contains("test_create_*"));
        assert!(report.contains("test_file.rs"));
        assert!(report.contains("test_example"));
    }
}
