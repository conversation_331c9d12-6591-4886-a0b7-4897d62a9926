//! # 查询构建器
//!
//! 提供动态查询构建功能，支持复杂的查询条件组合和类型安全的查询构建。
//!
//! ## 设计原则
//!
//! ### 1. 类型安全 (Type Safety)
//! - 使用强类型约束确保查询的正确性
//! - 编译期检查查询条件的有效性
//! - 避免SQL注入和类型错误
//!
//! ### 2. 灵活性 (Flexibility)
//! - 支持动态查询条件组合
//! - 可扩展的查询操作符
//! - 支持复杂的嵌套查询
//!
//! ### 3. 性能优化 (Performance)
//! - 查询条件预编译和缓存
//! - 索引友好的查询生成
//! - 避免N+1查询问题
//!
//! ### 4. 易用性 (Usability)
//! - 流畅的API设计
//! - 直观的方法链调用
//! - 丰富的查询条件支持

use crate::repositories::repository_traits::{
    ComparisonOperator, FilterCondition, FilterValue, LogicalOperator, PaginationParams,
    QueryFilter, SortOrder,
};

use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 动态查询构建器
///
/// 提供类型安全的动态查询构建功能，支持复杂的查询条件组合。
/// 使用构建器模式，提供流畅的API接口。
#[derive(Debug, Clone)]
pub struct QueryBuilder {
    /// 查询条件列表
    conditions: Vec<FilterCondition>,
    /// 逻辑操作符
    logical_operator: LogicalOperator,
    /// 排序条件
    sort_conditions: Vec<SortCondition>,
    /// 分页参数
    pagination: Option<PaginationParams>,
    /// 查询字段限制
    select_fields: Option<Vec<String>>,
    /// 连接查询配置
    #[allow(dead_code)]
    joins: Vec<JoinConfig>,
}

/// 排序条件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SortCondition {
    /// 排序字段
    pub field: String,
    /// 排序方向
    pub order: SortOrder,
}

/// 连接查询配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JoinConfig {
    /// 连接表名
    pub table: String,
    /// 连接类型
    pub join_type: JoinType,
    /// 连接条件
    pub on_condition: String,
}

/// 连接类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum JoinType {
    /// 内连接
    Inner,
    /// 左连接
    Left,
    /// 右连接
    Right,
    /// 全连接
    Full,
}

impl Default for QueryBuilder {
    fn default() -> Self {
        Self::new()
    }
}

impl QueryBuilder {
    /// 创建新的查询构建器
    ///
    /// # 返回
    /// 新的查询构建器实例
    pub fn new() -> Self {
        Self {
            conditions: Vec::new(),
            logical_operator: LogicalOperator::And,
            sort_conditions: Vec::new(),
            pagination: None,
            select_fields: None,
            joins: Vec::new(),
        }
    }

    /// 添加等于条件
    ///
    /// # 参数
    /// - `field`: 字段名
    /// - `value`: 比较值
    ///
    /// # 返回
    /// 更新后的查询构建器
    pub fn eq<T: Into<FilterValue>>(mut self, field: &str, value: T) -> Self {
        self.conditions.push(FilterCondition {
            field: field.to_string(),
            operator: ComparisonOperator::Eq,
            value: value.into(),
        });
        self
    }

    /// 添加不等于条件
    ///
    /// # 参数
    /// - `field`: 字段名
    /// - `value`: 比较值
    ///
    /// # 返回
    /// 更新后的查询构建器
    pub fn ne<T: Into<FilterValue>>(mut self, field: &str, value: T) -> Self {
        self.conditions.push(FilterCondition {
            field: field.to_string(),
            operator: ComparisonOperator::Ne,
            value: value.into(),
        });
        self
    }

    /// 添加大于条件
    ///
    /// # 参数
    /// - `field`: 字段名
    /// - `value`: 比较值
    ///
    /// # 返回
    /// 更新后的查询构建器
    pub fn gt<T: Into<FilterValue>>(mut self, field: &str, value: T) -> Self {
        self.conditions.push(FilterCondition {
            field: field.to_string(),
            operator: ComparisonOperator::Gt,
            value: value.into(),
        });
        self
    }

    /// 添加大于等于条件
    ///
    /// # 参数
    /// - `field`: 字段名
    /// - `value`: 比较值
    ///
    /// # 返回
    /// 更新后的查询构建器
    pub fn gte<T: Into<FilterValue>>(mut self, field: &str, value: T) -> Self {
        self.conditions.push(FilterCondition {
            field: field.to_string(),
            operator: ComparisonOperator::Gte,
            value: value.into(),
        });
        self
    }

    /// 添加小于条件
    ///
    /// # 参数
    /// - `field`: 字段名
    /// - `value`: 比较值
    ///
    /// # 返回
    /// 更新后的查询构建器
    pub fn lt<T: Into<FilterValue>>(mut self, field: &str, value: T) -> Self {
        self.conditions.push(FilterCondition {
            field: field.to_string(),
            operator: ComparisonOperator::Lt,
            value: value.into(),
        });
        self
    }

    /// 添加小于等于条件
    ///
    /// # 参数
    /// - `field`: 字段名
    /// - `value`: 比较值
    ///
    /// # 返回
    /// 更新后的查询构建器
    pub fn lte<T: Into<FilterValue>>(mut self, field: &str, value: T) -> Self {
        self.conditions.push(FilterCondition {
            field: field.to_string(),
            operator: ComparisonOperator::Lte,
            value: value.into(),
        });
        self
    }

    /// 添加包含条件（LIKE）
    ///
    /// # 参数
    /// - `field`: 字段名
    /// - `value`: 包含的字符串
    ///
    /// # 返回
    /// 更新后的查询构建器
    pub fn contains(mut self, field: &str, value: &str) -> Self {
        self.conditions.push(FilterCondition {
            field: field.to_string(),
            operator: ComparisonOperator::Contains,
            value: FilterValue::String(value.to_string()),
        });
        self
    }

    /// 添加开始于条件
    ///
    /// # 参数
    /// - `field`: 字段名
    /// - `value`: 开始字符串
    ///
    /// # 返回
    /// 更新后的查询构建器
    pub fn starts_with(mut self, field: &str, value: &str) -> Self {
        self.conditions.push(FilterCondition {
            field: field.to_string(),
            operator: ComparisonOperator::StartsWith,
            value: FilterValue::String(value.to_string()),
        });
        self
    }

    /// 添加结束于条件
    ///
    /// # 参数
    /// - `field`: 字段名
    /// - `value`: 结束字符串
    ///
    /// # 返回
    /// 更新后的查询构建器
    pub fn ends_with(mut self, field: &str, value: &str) -> Self {
        self.conditions.push(FilterCondition {
            field: field.to_string(),
            operator: ComparisonOperator::EndsWith,
            value: FilterValue::String(value.to_string()),
        });
        self
    }

    /// 添加IN条件
    ///
    /// # 参数
    /// - `field`: 字段名
    /// - `values`: 值列表
    ///
    /// # 返回
    /// 更新后的查询构建器
    pub fn in_list<T: Into<FilterValue>>(mut self, field: &str, values: T) -> Self {
        self.conditions.push(FilterCondition {
            field: field.to_string(),
            operator: ComparisonOperator::In,
            value: values.into(),
        });
        self
    }

    /// 添加NOT IN条件
    ///
    /// # 参数
    /// - `field`: 字段名
    /// - `values`: 值列表
    ///
    /// # 返回
    /// 更新后的查询构建器
    pub fn not_in_list<T: Into<FilterValue>>(mut self, field: &str, values: T) -> Self {
        self.conditions.push(FilterCondition {
            field: field.to_string(),
            operator: ComparisonOperator::NotIn,
            value: values.into(),
        });
        self
    }

    /// 添加IS NULL条件
    ///
    /// # 参数
    /// - `field`: 字段名
    ///
    /// # 返回
    /// 更新后的查询构建器
    pub fn is_null(mut self, field: &str) -> Self {
        self.conditions.push(FilterCondition {
            field: field.to_string(),
            operator: ComparisonOperator::IsNull,
            value: FilterValue::Boolean(true), // 占位值
        });
        self
    }

    /// 添加IS NOT NULL条件
    ///
    /// # 参数
    /// - `field`: 字段名
    ///
    /// # 返回
    /// 更新后的查询构建器
    pub fn is_not_null(mut self, field: &str) -> Self {
        self.conditions.push(FilterCondition {
            field: field.to_string(),
            operator: ComparisonOperator::IsNotNull,
            value: FilterValue::Boolean(true), // 占位值
        });
        self
    }

    /// 设置逻辑操作符为AND
    ///
    /// # 返回
    /// 更新后的查询构建器
    pub fn and(mut self) -> Self {
        self.logical_operator = LogicalOperator::And;
        self
    }

    /// 设置逻辑操作符为OR
    ///
    /// # 返回
    /// 更新后的查询构建器
    pub fn or(mut self) -> Self {
        self.logical_operator = LogicalOperator::Or;
        self
    }

    /// 添加排序条件
    ///
    /// # 参数
    /// - `field`: 排序字段
    /// - `order`: 排序方向
    ///
    /// # 返回
    /// 更新后的查询构建器
    pub fn order_by(mut self, field: &str, order: SortOrder) -> Self {
        self.sort_conditions.push(SortCondition {
            field: field.to_string(),
            order,
        });
        self
    }

    /// 添加升序排序
    ///
    /// # 参数
    /// - `field`: 排序字段
    ///
    /// # 返回
    /// 更新后的查询构建器
    pub fn order_asc(self, field: &str) -> Self {
        self.order_by(field, SortOrder::Asc)
    }

    /// 添加降序排序
    ///
    /// # 参数
    /// - `field`: 排序字段
    ///
    /// # 返回
    /// 更新后的查询构建器
    pub fn order_desc(self, field: &str) -> Self {
        self.order_by(field, SortOrder::Desc)
    }

    /// 设置分页参数
    ///
    /// # 参数
    /// - `pagination`: 分页参数
    ///
    /// # 返回
    /// 更新后的查询构建器
    pub fn paginate(mut self, pagination: PaginationParams) -> Self {
        self.pagination = Some(pagination);
        self
    }

    /// 设置查询字段
    ///
    /// # 参数
    /// - `fields`: 字段列表
    ///
    /// # 返回
    /// 更新后的查询构建器
    pub fn select(mut self, fields: Vec<String>) -> Self {
        self.select_fields = Some(fields);
        self
    }

    /// 构建查询过滤器
    ///
    /// # 返回
    /// 查询过滤器
    pub fn build_filter(self) -> QueryFilter {
        QueryFilter {
            conditions: self.conditions,
            operator: self.logical_operator,
        }
    }

    /// 获取分页参数
    ///
    /// # 返回
    /// 分页参数（如果设置）
    pub fn get_pagination(&self) -> Option<&PaginationParams> {
        self.pagination.as_ref()
    }

    /// 获取排序条件
    ///
    /// # 返回
    /// 排序条件列表
    pub fn get_sort_conditions(&self) -> &[SortCondition] {
        &self.sort_conditions
    }

    /// 获取查询字段
    ///
    /// # 返回
    /// 查询字段列表（如果设置）
    pub fn get_select_fields(&self) -> Option<&[String]> {
        self.select_fields.as_deref()
    }
}

/// FilterValue的From实现，支持常用类型的自动转换
impl From<String> for FilterValue {
    fn from(value: String) -> Self {
        FilterValue::String(value)
    }
}

impl From<&str> for FilterValue {
    fn from(value: &str) -> Self {
        FilterValue::String(value.to_string())
    }
}

impl From<i64> for FilterValue {
    fn from(value: i64) -> Self {
        FilterValue::Integer(value)
    }
}

impl From<i32> for FilterValue {
    fn from(value: i32) -> Self {
        FilterValue::Integer(value as i64)
    }
}

impl From<f64> for FilterValue {
    fn from(value: f64) -> Self {
        FilterValue::Float(value)
    }
}

impl From<f32> for FilterValue {
    fn from(value: f32) -> Self {
        FilterValue::Float(value as f64)
    }
}

impl From<bool> for FilterValue {
    fn from(value: bool) -> Self {
        FilterValue::Boolean(value)
    }
}

impl From<Uuid> for FilterValue {
    fn from(value: Uuid) -> Self {
        FilterValue::Uuid(value)
    }
}

impl From<Vec<String>> for FilterValue {
    fn from(value: Vec<String>) -> Self {
        FilterValue::StringList(value)
    }
}

impl From<Vec<i64>> for FilterValue {
    fn from(value: Vec<i64>) -> Self {
        FilterValue::IntegerList(value)
    }
}

impl From<Vec<Uuid>> for FilterValue {
    fn from(value: Vec<Uuid>) -> Self {
        FilterValue::UuidList(value)
    }
}
