//! 认证系统性能基准测试
//!
//! 本文件包含认证系统的性能基准测试，用于评估：
//! - JWT token创建和验证性能
//! - 权限检查性能
//! - 缓存机制效果
//! - 高并发场景下的性能表现
//!
//! 使用Criterion进行基准测试，支持HTML报告生成

use criterion::{BenchmarkId, Criterion, Throughput, criterion_group, criterion_main};

use tokio::runtime::Runtime;

// 导入认证相关模块
use app_common::{
    middleware::permission_middleware::UnifiedPermissionChecker, utils::jwt_utils::JwtUtils,
};
use app_interfaces::auth::{Permission, UserRole};

// 测试常量
const BENCHMARK_JWT_SECRET: &str = "benchmark_jwt_secret_key_for_performance_testing_2024_edition";
const BENCHMARK_USER_ID: &str = "550e8400-e29b-41d4-a716-446655440000";
const BENCHMARK_USERNAME: &str = "benchmark_user";

/// 创建基准测试用的JWT工具实例
fn create_benchmark_jwt_utils() -> JwtUtils {
    JwtUtils::new(BENCHMARK_JWT_SECRET.to_string())
}

/// 创建基准测试用的权限检查器
fn create_benchmark_permission_checker() -> UnifiedPermissionChecker {
    UnifiedPermissionChecker::new()
}

/// JWT Token创建性能基准测试
fn bench_jwt_token_creation(c: &mut Criterion) {
    let _rt = Runtime::new().unwrap();
    let jwt_utils = create_benchmark_jwt_utils();

    let mut group = c.benchmark_group("jwt_token_creation");
    group.throughput(Throughput::Elements(1));

    // 测试不同角色的token创建性能
    let roles = vec![
        UserRole::Guest,
        UserRole::User,
        UserRole::Manager,
        UserRole::Admin,
    ];

    for role in roles {
        group.bench_with_input(
            BenchmarkId::new("create_token_with_role", format!("{role:?}")),
            &role,
            |b, role: &UserRole| {
                b.iter(|| {
                    std::hint::black_box(jwt_utils.create_token_with_role(
                        BENCHMARK_USER_ID,
                        BENCHMARK_USERNAME,
                        role.clone(),
                        3600,
                    ))
                })
            },
        );
    }

    group.finish();
}

/// JWT Token验证性能基准测试
fn bench_jwt_token_validation(c: &mut Criterion) {
    let _rt = Runtime::new().unwrap();
    let jwt_utils = create_benchmark_jwt_utils();

    // 预先创建不同角色的token用于验证测试
    let tokens: Vec<(UserRole, String)> = vec![
        UserRole::Guest,
        UserRole::User,
        UserRole::Manager,
        UserRole::Admin,
    ]
    .into_iter()
    .map(|role| {
        let token = jwt_utils
            .create_token_with_role(BENCHMARK_USER_ID, BENCHMARK_USERNAME, role.clone(), 3600)
            .expect("创建token失败");
        (role, token)
    })
    .collect();

    let mut group = c.benchmark_group("jwt_token_validation");
    group.throughput(Throughput::Elements(1));

    for (role, token) in &tokens {
        group.bench_with_input(
            BenchmarkId::new("validate_token", format!("{role:?}")),
            token,
            |b, token| b.iter(|| std::hint::black_box(jwt_utils.validate_token_with_role(token))),
        );
    }

    group.finish();
}

/// 权限检查性能基准测试
fn bench_permission_checking(c: &mut Criterion) {
    let _rt = Runtime::new().unwrap();
    let permission_checker = create_benchmark_permission_checker();

    let mut group = c.benchmark_group("permission_checking");
    group.throughput(Throughput::Elements(1));

    // 测试不同权限级别的检查性能
    let permissions = vec![
        Permission::Read,   // 25
        Permission::Write,  // 50
        Permission::Delete, // 75
        Permission::Admin,  // 100
    ];
    let user_roles = vec![
        UserRole::Guest,   // 25
        UserRole::User,    // 50
        UserRole::Manager, // 75
        UserRole::Admin,   // 100
    ];

    for role in &user_roles {
        for permission in &permissions {
            group.bench_with_input(
                BenchmarkId::new("check_permission", format!("{role:?}_req_{permission:?}")),
                &(role, permission),
                |b, (role, permission)| {
                    b.iter(|| {
                        std::hint::black_box(permission_checker.has_permission(role, permission))
                    })
                },
            );
        }
    }

    group.finish();
}

/// 权限缓存性能基准测试
fn bench_permission_caching(c: &mut Criterion) {
    let _rt = Runtime::new().unwrap();
    let permission_checker = create_benchmark_permission_checker();

    let mut group = c.benchmark_group("permission_caching");
    group.throughput(Throughput::Elements(1));

    // 预热缓存 - 执行一些权限检查来填充缓存
    for role in [
        &UserRole::Guest,
        &UserRole::User,
        &UserRole::Manager,
        &UserRole::Admin,
    ] {
        for permission in [
            &Permission::Read,
            &Permission::Write,
            &Permission::Delete,
            &Permission::Admin,
        ] {
            permission_checker.has_permission(role, permission);
        }
    }

    // 测试缓存命中的性能
    group.bench_function("cached_permission_check", |b| {
        b.iter(|| {
            // 这些检查应该命中缓存
            std::hint::black_box(
                permission_checker.has_permission(&UserRole::Admin, &Permission::Admin),
            );
            std::hint::black_box(
                permission_checker.has_permission(&UserRole::Manager, &Permission::Delete),
            );
            std::hint::black_box(
                permission_checker.has_permission(&UserRole::User, &Permission::Write),
            );
            std::hint::black_box(
                permission_checker.has_permission(&UserRole::Guest, &Permission::Read),
            );
        })
    });

    group.finish();
}

/// 高并发JWT验证基准测试
fn bench_concurrent_jwt_validation(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let jwt_utils = create_benchmark_jwt_utils();

    // 预先创建多个token用于并发测试
    let tokens: Vec<String> = (0..100)
        .map(|i| {
            jwt_utils
                .create_token_with_role(
                    &format!("user_{i}"),
                    &format!("username_{i}"),
                    UserRole::User.clone(),
                    3600,
                )
                .expect("创建token失败")
        })
        .collect();

    let mut group = c.benchmark_group("concurrent_jwt_validation");
    group.throughput(Throughput::Elements(100));

    group.bench_function("validate_100_tokens_concurrent", |b| {
        b.to_async(&rt).iter(|| async {
            let jwt_utils = &jwt_utils;
            let futures: Vec<_> = tokens
                .iter()
                .map(|token| async move {
                    std::hint::black_box(jwt_utils.validate_token_with_role(token))
                })
                .collect();

            // 并发执行所有验证
            futures::future::join_all(futures).await
        })
    });

    group.finish();
}

// 配置基准测试组
criterion_group!(
    benches,
    bench_jwt_token_creation,
    bench_jwt_token_validation,
    bench_permission_checking,
    bench_permission_caching,
    bench_concurrent_jwt_validation
);

criterion_main!(benches);
