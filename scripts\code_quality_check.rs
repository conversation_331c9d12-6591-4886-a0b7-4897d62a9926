use std::fs;
use std::process::{Command, Stdio};

/// 代码质量检查器
/// 运行 cargo clippy, cargo fmt, cargo llvm-cov 等工具
struct CodeQualityChecker {
    /// 检查结果
    results: Vec<(String, bool, String)>,
}

impl CodeQualityChecker {
    fn new() -> Self {
        Self {
            results: Vec::new(),
        }
    }

    /// 运行 cargo check 确保编译通过
    fn run_cargo_check(&mut self) {
        println!("🔍 运行 cargo check --workspace...");

        let output = Command::new("cargo")
            .args(&["check", "--workspace"])
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .output();

        match output {
            Ok(output) => {
                let success = output.status.success();
                let stderr = String::from_utf8_lossy(&output.stderr);

                if success {
                    println!("✅ cargo check 通过");
                    self.results.push((
                        "cargo_check".to_string(),
                        true,
                        "编译检查通过".to_string(),
                    ));
                } else {
                    println!("❌ cargo check 失败:");
                    println!("{}", stderr);
                    self.results
                        .push(("cargo_check".to_string(), false, stderr.to_string()));
                }
            }
            Err(e) => {
                println!("❌ 无法运行 cargo check: {}", e);
                self.results
                    .push(("cargo_check".to_string(), false, format!("执行失败: {}", e)));
            }
        }
    }

    /// 运行 cargo clippy 检查代码质量
    fn run_cargo_clippy(&mut self) {
        println!("🔍 运行 cargo clippy --workspace -- -D warnings...");

        let output = Command::new("cargo")
            .args(&["clippy", "--workspace", "--", "-D", "warnings"])
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .output();

        match output {
            Ok(output) => {
                let success = output.status.success();
                let stderr = String::from_utf8_lossy(&output.stderr);
                let stdout = String::from_utf8_lossy(&output.stdout);

                if success {
                    println!("✅ cargo clippy 通过 - 无警告");
                    self.results.push((
                        "cargo_clippy".to_string(),
                        true,
                        "无clippy警告".to_string(),
                    ));
                } else {
                    println!("❌ cargo clippy 发现问题:");
                    println!("{}", stderr);
                    println!("{}", stdout);
                    self.results.push((
                        "cargo_clippy".to_string(),
                        false,
                        format!("{}\n{}", stderr, stdout),
                    ));
                }
            }
            Err(e) => {
                println!("❌ 无法运行 cargo clippy: {}", e);
                self.results.push((
                    "cargo_clippy".to_string(),
                    false,
                    format!("执行失败: {}", e),
                ));
            }
        }
    }

    /// 运行 cargo fmt 检查代码格式
    fn run_cargo_fmt(&mut self) {
        println!("🔍 运行 cargo fmt --check...");

        let output = Command::new("cargo")
            .args(&["fmt", "--check"])
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .output();

        match output {
            Ok(output) => {
                let success = output.status.success();
                let stderr = String::from_utf8_lossy(&output.stderr);
                let stdout = String::from_utf8_lossy(&output.stdout);

                if success {
                    println!("✅ cargo fmt 通过 - 代码格式规范");
                    self.results
                        .push(("cargo_fmt".to_string(), true, "代码格式规范".to_string()));
                } else {
                    println!("❌ cargo fmt 发现格式问题:");
                    println!("{}", stderr);
                    println!("{}", stdout);
                    self.results.push((
                        "cargo_fmt".to_string(),
                        false,
                        format!("{}\n{}", stderr, stdout),
                    ));
                }
            }
            Err(e) => {
                println!("❌ 无法运行 cargo fmt: {}", e);
                self.results
                    .push(("cargo_fmt".to_string(), false, format!("执行失败: {}", e)));
            }
        }
    }

    /// 运行测试覆盖率检查
    fn run_coverage_check(&mut self) {
        println!("🔍 运行测试覆盖率检查...");

        // 首先检查是否安装了 cargo-llvm-cov
        let llvm_cov_check = Command::new("cargo")
            .args(&["llvm-cov", "--version"])
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .output();

        match llvm_cov_check {
            Ok(output) if output.status.success() => {
                println!("✅ cargo-llvm-cov 已安装");
                self.run_llvm_cov();
            }
            _ => {
                println!("⚠️ cargo-llvm-cov 未安装，跳过覆盖率检查");
                println!("💡 安装命令: cargo install cargo-llvm-cov");
                self.results.push((
                    "coverage".to_string(),
                    false,
                    "cargo-llvm-cov 未安装".to_string(),
                ));
            }
        }
    }

    /// 运行 cargo llvm-cov
    fn run_llvm_cov(&mut self) {
        let output = Command::new("cargo")
            .args(&["llvm-cov", "--workspace", "--html"])
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .output();

        match output {
            Ok(output) => {
                let success = output.status.success();
                let stderr = String::from_utf8_lossy(&output.stderr);
                let stdout = String::from_utf8_lossy(&output.stdout);

                if success {
                    // 尝试解析覆盖率
                    let coverage_info = self.parse_coverage_output(&stdout);
                    println!("✅ 测试覆盖率报告生成成功");
                    println!("{}", coverage_info);
                    self.results
                        .push(("coverage".to_string(), true, coverage_info));
                } else {
                    println!("❌ 测试覆盖率检查失败:");
                    println!("{}", stderr);
                    self.results
                        .push(("coverage".to_string(), false, stderr.to_string()));
                }
            }
            Err(e) => {
                println!("❌ 无法运行 cargo llvm-cov: {}", e);
                self.results
                    .push(("coverage".to_string(), false, format!("执行失败: {}", e)));
            }
        }
    }

    /// 解析覆盖率输出
    fn parse_coverage_output(&self, output: &str) -> String {
        // 简化的覆盖率解析
        if output.contains("target/llvm-cov/html") {
            "HTML覆盖率报告已生成到 target/llvm-cov/html/".to_string()
        } else {
            output.to_string()
        }
    }

    /// 检查中文注释完整性
    fn check_chinese_comments(&mut self) {
        println!("🔍 检查中文注释完整性...");

        let mut total_functions = 0;
        let mut commented_functions = 0;

        // 检查主要源码目录
        let src_dirs = vec![
            "crates/app_common/src",
            "crates/app_domain/src",
            "crates/app_application/src",
            "crates/app_infrastructure/src",
            "crates/app_interfaces/src",
        ];

        for src_dir in src_dirs {
            if let Ok((funcs, commented)) = self.count_functions_and_comments(src_dir) {
                total_functions += funcs;
                commented_functions += commented;
            }
        }

        let comment_rate = if total_functions > 0 {
            ((commented_functions as f64) / (total_functions as f64)) * 100.0
        } else {
            0.0
        };

        if comment_rate >= 80.0 {
            println!(
                "✅ 中文注释覆盖率: {:.1}% ({}/{})",
                comment_rate, commented_functions, total_functions
            );
            self.results.push((
                "chinese_comments".to_string(),
                true,
                format!("注释覆盖率: {:.1}%", comment_rate),
            ));
        } else {
            println!(
                "❌ 中文注释覆盖率不足: {:.1}% ({}/{})",
                comment_rate, commented_functions, total_functions
            );
            self.results.push((
                "chinese_comments".to_string(),
                false,
                format!("注释覆盖率不足: {:.1}%", comment_rate),
            ));
        }
    }

    /// 统计函数和注释数量
    fn count_functions_and_comments(
        &self,
        dir_path: &str,
    ) -> Result<(usize, usize), Box<dyn std::error::Error>> {
        let mut total_functions = 0;
        let mut commented_functions = 0;

        if let Ok(entries) = fs::read_dir(dir_path) {
            for entry in entries {
                if let Ok(entry) = entry {
                    let path = entry.path();
                    if path.extension().map_or(false, |ext| ext == "rs") {
                        if let Ok(content) = fs::read_to_string(&path) {
                            let (funcs, commented) = self.analyze_rust_file(&content);
                            total_functions += funcs;
                            commented_functions += commented;
                        }
                    }
                }
            }
        }

        Ok((total_functions, commented_functions))
    }

    /// 分析Rust文件中的函数和注释
    fn analyze_rust_file(&self, content: &str) -> (usize, usize) {
        let lines: Vec<&str> = content.lines().collect();
        let mut functions = 0;
        let mut commented_functions = 0;
        let mut i = 0;

        while i < lines.len() {
            let line = lines[i].trim();

            // 检查是否是函数定义
            if line.starts_with("pub fn ")
                || line.starts_with("fn ")
                || line.starts_with("async fn ")
                || line.starts_with("pub async fn ")
            {
                functions += 1;

                // 检查前面几行是否有中文注释
                let mut has_chinese_comment = false;
                for j in (0..i).rev().take(5) {
                    let prev_line = lines[j].trim();
                    if prev_line.starts_with("///") || prev_line.starts_with("//") {
                        // 检查是否包含中文字符
                        if prev_line.chars().any(|c| (c as u32) > 127) {
                            has_chinese_comment = true;
                            break;
                        }
                    } else if !prev_line.is_empty() && !prev_line.starts_with("#[") {
                        break;
                    }
                }

                if has_chinese_comment {
                    commented_functions += 1;
                }
            }
            i += 1;
        }

        (functions, commented_functions)
    }

    /// 生成质量检查报告
    fn generate_report(&self) {
        println!("\n📊 代码质量检查报告");
        println!("{}", "=".repeat(50));

        let mut passed = 0;
        let total = self.results.len();

        for (check_name, success, details) in &self.results {
            let status = if *success {
                passed += 1;
                "✅ 通过"
            } else {
                "❌ 失败"
            };

            println!("\n🔍 {}: {}", check_name, status);
            if !success || check_name == "coverage" {
                println!("   详情: {}", details);
            }
        }

        let success_rate = ((passed as f64) / (total as f64)) * 100.0;
        println!(
            "\n📈 总体通过率: {:.1}% ({}/{})",
            success_rate, passed, total
        );

        if success_rate >= 95.0 {
            println!("🎉 代码质量检查通过！");
        } else {
            println!("⚠️ 代码质量需要改进");
        }
    }

    /// 运行完整的代码质量检查
    fn run_full_check(&mut self) {
        println!("🚀 开始代码质量检查...\n");

        self.run_cargo_check();
        self.run_cargo_clippy();
        self.run_cargo_fmt();
        self.run_coverage_check();
        self.check_chinese_comments();

        self.generate_report();
    }
}

fn main() {
    let mut checker = CodeQualityChecker::new();
    checker.run_full_check();
}
