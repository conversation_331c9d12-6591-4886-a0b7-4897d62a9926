//! # JWT认证中间件集成测试
//!
//! 测试JWT认证中间件在新架构中的功能，包括：
//! - 有效token的认证
//! - 无效token的拒绝
//! - 过期token的处理
//! - AuthenticatedUser提取器的功能
//! - 统一认证系统集成测试

use app_common::{
    middleware::{AuthenticatedUser, JwtAuthState},
    utils::JwtUtils,
};
use axum::{
    Router,
    http::{StatusCode, header::AUTHORIZATION},
    middleware,
    response::Json,
    routing::get,
};
use axum_test::TestServer;
use sea_orm::prelude::Uuid;
use serde_json::{Value, json};

const TEST_JWT_SECRET: &str = "test-jwt-middleware-secret-key-for-testing";

/// 测试用的处理器，需要认证用户
async fn protected_handler(user: AuthenticatedUser) -> Json<Value> {
    Json(json!({
        "user_id": user.user_id,
        "username": user.username,
        "message": "访问成功"
    }))
}

/// 创建测试应用
fn create_test_app() -> Router {
    let jwt_auth_state = JwtAuthState::new(TEST_JWT_SECRET.to_string());

    Router::new()
        .route("/protected", get(protected_handler))
        .layer(middleware::from_fn(
            |mut req: axum::extract::Request, next: axum::middleware::Next| async move {
                // 直接将JWT密钥注入到请求扩展中
                req.extensions_mut().insert(TEST_JWT_SECRET.to_string());
                next.run(req).await
            },
        ))
        .with_state(jwt_auth_state)
}

/// 创建有效的JWT token
fn create_valid_token(user_id: &str, username: &str) -> String {
    let jwt_utils = JwtUtils::new(TEST_JWT_SECRET.to_string());
    jwt_utils.create_token(user_id, username, 1).unwrap()
}

/// 创建过期的JWT token
fn create_expired_token(user_id: &str, username: &str) -> String {
    use app_common::utils::Claims;
    use chrono::{Duration, Utc};
    use jsonwebtoken::{EncodingKey, Header, encode};

    // 创建过期的Claims
    let past_time = Utc::now() - Duration::hours(1);
    let claims = Claims {
        sub: user_id.to_string(),
        username: username.to_string(),
        iat: past_time.timestamp(),
        exp: past_time.timestamp(),
    };

    encode(
        &Header::default(),
        &claims,
        &EncodingKey::from_secret(TEST_JWT_SECRET.as_ref()),
    )
    .expect("Failed to create expired token")
}

#[tokio::test]
async fn test_valid_token_authentication() {
    let app = create_test_app();
    let server = TestServer::new(app).unwrap();

    let user_id = Uuid::new_v4();
    let username = "testuser";
    let token = create_valid_token(&user_id.to_string(), username);

    let response = server
        .get("/protected")
        .add_header(AUTHORIZATION, format!("Bearer {token}"))
        .await;

    assert_eq!(response.status_code(), StatusCode::OK);

    let body: Value = response.json();
    assert_eq!(body["user_id"], user_id.to_string());
    assert_eq!(body["username"], username);
    assert_eq!(body["message"], "访问成功");
}

#[tokio::test]
async fn test_missing_token_rejection() {
    let app = create_test_app();
    let server = TestServer::new(app).unwrap();

    let response = server.get("/protected").await;

    assert_eq!(response.status_code(), StatusCode::UNAUTHORIZED);
}

#[tokio::test]
async fn test_invalid_token_rejection() {
    let app = create_test_app();
    let server = TestServer::new(app).unwrap();

    let response = server
        .get("/protected")
        .add_header(AUTHORIZATION, "Bearer invalid-token")
        .await;

    assert_eq!(response.status_code(), StatusCode::UNAUTHORIZED);
}

#[tokio::test]
async fn test_expired_token_rejection() {
    let app = create_test_app();
    let server = TestServer::new(app).unwrap();

    let user_id = Uuid::new_v4();
    let username = "testuser";
    let expired_token = create_expired_token(&user_id.to_string(), username);

    let response = server
        .get("/protected")
        .add_header(AUTHORIZATION, format!("Bearer {expired_token}"))
        .await;

    assert_eq!(response.status_code(), StatusCode::UNAUTHORIZED);
}

#[tokio::test]
async fn test_malformed_authorization_header() {
    let app = create_test_app();
    let server = TestServer::new(app).unwrap();

    // 测试不是Bearer格式的Authorization头
    let response = server
        .get("/protected")
        .add_header(AUTHORIZATION, "Basic dGVzdDp0ZXN0")
        .await;

    assert_eq!(response.status_code(), StatusCode::UNAUTHORIZED);
}

#[tokio::test]
async fn test_empty_bearer_token() {
    let app = create_test_app();
    let server = TestServer::new(app).unwrap();

    // 测试空的Bearer token
    let response = server
        .get("/protected")
        .add_header(AUTHORIZATION, "Bearer ")
        .await;

    assert_eq!(response.status_code(), StatusCode::UNAUTHORIZED);
}

#[tokio::test]
async fn test_token_with_invalid_user_id() {
    // 这个测试需要手动创建一个包含无效user_id的token
    use app_common::utils::Claims;
    use chrono::{Duration, Utc};
    use jsonwebtoken::{EncodingKey, Header, encode};

    let app = create_test_app();
    let server = TestServer::new(app).unwrap();

    // 创建包含无效UUID的Claims
    let now = Utc::now();
    let claims = Claims {
        sub: "invalid-uuid-format".to_string(),
        username: "testuser".to_string(),
        iat: now.timestamp(),
        exp: (now + Duration::hours(1)).timestamp(),
    };

    let token = encode(
        &Header::default(),
        &claims,
        &EncodingKey::from_secret(TEST_JWT_SECRET.as_ref()),
    )
    .unwrap();

    let response = server
        .get("/protected")
        .add_header(AUTHORIZATION, format!("Bearer {token}"))
        .await;

    assert_eq!(response.status_code(), StatusCode::UNAUTHORIZED);
}

#[tokio::test]
async fn test_authenticated_user_creation() {
    let user_id = Uuid::new_v4();
    let username = "testuser".to_string();

    let auth_user = AuthenticatedUser::new(user_id, username.clone());

    assert_eq!(auth_user.user_id, user_id);
    assert_eq!(auth_user.username, username);
}

#[tokio::test]
async fn test_jwt_auth_state_creation() {
    let secret = "test-secret".to_string();
    let auth_state = JwtAuthState::new(secret.clone());

    assert_eq!(auth_state.jwt_secret, secret);
}
