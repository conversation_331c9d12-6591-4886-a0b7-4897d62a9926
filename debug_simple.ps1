# Simple debug script
$baseUrl = "http://127.0.0.1:3000"

Write-Host "Debug authentication..." -ForegroundColor Yellow

# Login
$loginBody = @{
    username = "testuser456"
    password = "password123"
} | ConvertTo-<PERSON>son

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/auth/login" -Method Post -ContentType "application/json" -Body $loginBody
    Write-Host "Login successful" -ForegroundColor Green
    
    $token = $loginResponse.data.token
    Write-Host "Token: $($token.Substring(0,30))..." -ForegroundColor Cyan
    
    # Test user endpoint
    $headers = @{ "Authorization" = "Bearer $token" }
    
    try {
        $userResponse = Invoke-RestMethod -Uri "$baseUrl/api/users/online-users" -Method Get -Headers $headers
        Write-Host "User endpoint works" -ForegroundColor Green
    } catch {
        Write-Host "User endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Test search endpoint
    try {
        $searchResponse = Invoke-RestMethod -Uri "$baseUrl/api/messages/search?keyword=test" -Method Get -Headers $headers
        Write-Host "Search endpoint works" -ForegroundColor Green
    } catch {
        Write-Host "Search endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "Login failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Debug completed" -ForegroundColor Green
