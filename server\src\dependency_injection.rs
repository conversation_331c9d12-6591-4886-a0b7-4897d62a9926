//! # 依赖注入容器模块
//!
//! 实现统一的依赖注入模式，提供服务注册、解析和生命周期管理功能
//! 基于Context7 MCP最佳实践和Rust模块系统设计

// ============================================================================
// 子模块声明
// ============================================================================

pub mod builder;
pub mod container;
pub mod registry;

// ============================================================================
// 重新导出核心类型
// ============================================================================

pub use builder::DefaultServiceContainerBuilder;
pub use container::DefaultServiceContainer;
#[allow(unused_imports)]
pub use registry::{
    ServiceProvider, ServiceRegistry, ServiceRegistryBuilder, ServiceRegistryConfig,
};

use anyhow::Result as AnyhowResult;
use std::sync::Arc;

// 导入应用服务trait
use app_application::{
    ChatApplicationService, TaskApplicationService, UserApplicationService,
    websocket_service::WebSocketApplicationService,
};

// 导入领域服务trait
use app_domain::services::{ChatDomainService, TaskDomainService, UserDomainService};

// 导入仓库trait
use app_domain::repositories::{
    ChatRepositoryContract, TaskRepositoryContract, UserRepositoryContract,
};

// 导入WebSocket服务trait
use app_domain::websocket::{
    WebSocketConnectionService, WebSocketMessageService, WebSocketStatsService,
};

use sea_orm::{DatabaseConnection, DatabaseTransaction};

/// 事务管理器接口
///
/// 提供统一的事务管理功能，支持跨服务的事务操作
#[async_trait::async_trait]
pub trait TransactionManager: Send + Sync {
    /// 开始事务
    async fn begin_transaction(&self) -> AnyhowResult<Box<dyn TransactionContext>>;
}

/// 事务上下文接口
///
/// 表示一个活跃的数据库事务
#[async_trait::async_trait]
pub trait TransactionContext: Send + Sync {
    /// 提交事务
    async fn commit(self: Box<Self>) -> AnyhowResult<()>;

    /// 回滚事务
    async fn rollback(self: Box<Self>) -> AnyhowResult<()>;

    /// 获取事务中的数据库连接
    fn get_transaction(&self) -> &DatabaseTransaction;
}

/// 依赖注入容器接口
///
/// 定义服务容器的核心功能，支持服务注册、解析和生命周期管理
pub trait ServiceContainer: Clone + Send + Sync {
    /// 获取用户应用服务
    fn get_user_service(&self) -> Arc<dyn UserApplicationService>;

    /// 获取任务应用服务
    fn get_task_service(&self) -> Arc<dyn TaskApplicationService>;

    /// 获取聊天应用服务
    fn get_chat_service(&self) -> Arc<dyn ChatApplicationService>;

    /// 获取WebSocket应用服务
    fn get_websocket_service(&self) -> Arc<dyn WebSocketApplicationService>;

    /// 获取数据库连接
    fn get_database(&self) -> Arc<DatabaseConnection>;

    /// 获取事务管理器
    fn get_transaction_manager(&self) -> Arc<dyn TransactionManager>;
}

/// 服务注册器接口
///
/// 定义服务注册的标准接口，支持不同类型的服务注册
#[allow(dead_code)]
pub trait ServiceRegistrar {
    /// 注册用户相关服务
    fn register_user_services(
        &mut self,
        user_repository: Arc<dyn UserRepositoryContract>,
        user_domain_service: Arc<dyn UserDomainService>,
    ) -> AnyhowResult<()>;

    /// 注册任务相关服务
    fn register_task_services(
        &mut self,
        task_repository: Arc<dyn TaskRepositoryContract>,
        task_domain_service: Arc<dyn TaskDomainService>,
    ) -> AnyhowResult<()>;

    /// 注册聊天相关服务
    fn register_chat_services(
        &mut self,
        chat_repository: Arc<dyn ChatRepositoryContract>,
        chat_domain_service: Arc<dyn ChatDomainService>,
    ) -> AnyhowResult<()>;

    /// 注册WebSocket相关服务
    fn register_websocket_services(
        &mut self,
        connection_service: Arc<dyn WebSocketConnectionService>,
        message_service: Arc<dyn WebSocketMessageService>,
        stats_service: Arc<dyn WebSocketStatsService>,
    ) -> AnyhowResult<()>;
}

/// 服务生命周期管理器接口
///
/// 定义服务生命周期管理的标准接口
#[allow(dead_code)]
#[async_trait::async_trait]
pub trait ServiceLifecycleManager {
    /// 初始化所有服务
    async fn initialize(&mut self) -> AnyhowResult<()>;

    /// 启动所有服务
    async fn start(&self) -> AnyhowResult<()>;

    /// 停止所有服务
    async fn stop(&self) -> AnyhowResult<()>;

    /// 健康检查
    async fn health_check(&self) -> AnyhowResult<bool>;
}

/// 服务工厂接口
///
/// 定义服务创建的标准接口，支持不同的服务创建策略
pub trait ServiceFactory {
    /// 创建用户应用服务
    fn create_user_service(
        &self,
        repository: Arc<dyn UserRepositoryContract>,
        domain_service: Arc<dyn UserDomainService>,
    ) -> AnyhowResult<Arc<dyn UserApplicationService>>;

    /// 创建任务应用服务
    fn create_task_service(
        &self,
        repository: Arc<dyn TaskRepositoryContract>,
        domain_service: Arc<dyn TaskDomainService>,
    ) -> AnyhowResult<Arc<dyn TaskApplicationService>>;

    /// 创建聊天应用服务
    fn create_chat_service(
        &self,
        repository: Arc<dyn ChatRepositoryContract>,
        domain_service: Arc<dyn ChatDomainService>,
        user_repository: Arc<dyn app_domain::repositories::UserRepositoryContract>,
    ) -> AnyhowResult<Arc<dyn ChatApplicationService>>;

    /// 创建WebSocket应用服务
    fn create_websocket_service(
        &self,
        connection_service: Arc<dyn WebSocketConnectionService>,
        message_service: Arc<dyn WebSocketMessageService>,
        stats_service: Arc<dyn WebSocketStatsService>,
    ) -> AnyhowResult<Arc<dyn WebSocketApplicationService>>;
}

/// 依赖注入构建器接口
///
/// 提供流式API来构建服务容器
#[allow(dead_code)]
pub trait ServiceContainerBuilder {
    type Container: ServiceContainer;

    /// 设置数据库连接
    fn with_database(self, database: Arc<DatabaseConnection>) -> Self;

    /// 添加用户服务
    fn with_user_service(
        self,
        repository: Arc<dyn UserRepositoryContract>,
        domain_service: Arc<dyn UserDomainService>,
    ) -> Self;

    /// 添加任务服务
    fn with_task_service(
        self,
        repository: Arc<dyn TaskRepositoryContract>,
        domain_service: Arc<dyn TaskDomainService>,
    ) -> Self;

    /// 添加聊天服务
    fn with_chat_service(
        self,
        repository: Arc<dyn ChatRepositoryContract>,
        domain_service: Arc<dyn ChatDomainService>,
    ) -> Self;

    /// 添加WebSocket服务
    fn with_websocket_service(
        self,
        connection_service: Arc<dyn WebSocketConnectionService>,
        message_service: Arc<dyn WebSocketMessageService>,
        stats_service: Arc<dyn WebSocketStatsService>,
    ) -> Self;

    /// 构建服务容器
    fn build(self) -> AnyhowResult<Self::Container>;
}

/// 服务解析器接口
///
/// 提供类型安全的服务解析功能
#[allow(dead_code)]
pub trait ServiceResolver {
    /// 解析指定类型的服务
    fn resolve<T: ?Sized + 'static>(&self) -> Option<Arc<T>>;

    /// 解析指定类型的服务（必须存在）
    fn resolve_required<T: ?Sized + 'static>(&self) -> AnyhowResult<Arc<T>>;
}

/// 服务配置接口
///
/// 定义服务配置的标准接口
#[allow(dead_code)]
pub trait ServiceConfiguration {
    /// 获取服务配置
    fn get_config(&self) -> &crate::config::AppConfig;

    /// 验证配置
    fn validate_config(&self) -> AnyhowResult<()>;

    /// 应用配置
    fn apply_config(&mut self) -> AnyhowResult<()>;
}
