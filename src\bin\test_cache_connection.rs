use fred::prelude::*;
use std::time::Duration;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔍 测试缓存连接...");

    // 加载.env文件
    if let Err(e) = dotenvy::dotenv() {
        println!("⚠️ 无法加载.env文件: {}", e);
    }

    // 从环境变量读取连接URL
    let cache_url = std::env::var("CACHE_URL").unwrap_or_else(|_| {
        "redis://:dragonfly_secure_password_2025@172.19.9.132:6379".to_string()
    });

    println!("📡 连接URL: {}", cache_url);

    // 创建Redis配置
    let config = Config::from_url(&cache_url)?;
    println!("⚙️ Redis配置创建成功");

    // 创建客户端
    let client = Builder::from_config(config).build()?;
    println!("🔧 Redis客户端创建成功");

    // 尝试连接（5秒超时，与服务器相同）
    println!("🔗 正在连接到DragonflyDB（5秒超时）...");
    let connect_result = tokio::time::timeout(Duration::from_secs(5), client.connect()).await;

    match connect_result {
        Ok(Ok(_)) => {
            println!("✅ 成功连接到DragonflyDB!");

            // 测试PING命令
            println!("📡 测试PING命令...");
            let ping_result: String = client.ping::<String>(None).await?;
            println!("🏓 PING响应: {}", ping_result);

            // 测试SET/GET命令
            println!("📝 测试SET/GET命令...");
            let _: () = client
                .set("test_key", "test_value", None, None, false)
                .await?;
            let value: String = client.get("test_key").await?;
            println!("📖 GET结果: {}", value);

            // 清理测试数据
            let _: i64 = client.del("test_key").await?;
            println!("🧹 清理测试数据完成");

            println!("🎉 DragonflyDB连接测试完全成功!");
        }
        Ok(Err(e)) => {
            println!("❌ 连接DragonflyDB失败: {}", e);
            return Err(e.into());
        }
        Err(_) => {
            println!("❌ 连接DragonflyDB超时 (5秒)");

            // 尝试诊断网络问题
            println!("🔍 开始网络诊断...");

            // 测试端口连接
            println!("📡 测试端口连接...");
            let test_result = std::process::Command::new("powershell")
                .args(&[
                    "-Command",
                    "Test-NetConnection -ComputerName 172.19.9.132 -Port 6379",
                ])
                .output();

            match test_result {
                Ok(output) => {
                    let result_str = String::from_utf8_lossy(&output.stdout);
                    if result_str.contains("TcpTestSucceeded") {
                        if result_str.contains("True") {
                            println!("✅ 端口连接测试成功");
                        } else {
                            println!("❌ 端口连接测试失败");
                        }
                    }
                    println!("📋 详细结果: {}", result_str);
                }
                Err(e) => {
                    println!("⚠️ 无法执行端口测试: {}", e);
                }
            }

            return Err("连接超时".into());
        }
    }

    Ok(())
}
