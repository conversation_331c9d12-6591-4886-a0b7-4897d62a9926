//! # 任务创建功能E2E测试
//!
//! 本模块实现任务创建功能的端到端测试，遵循Context7 MCP最佳实践：
//! - 使用清晰的函数命名（test_create_task_success、test_create_task_validation等）
//! - 详细的中文注释
//! - 完整的错误处理
//! - 遵循DRY和SOLID原则
//! - 遵循TDD（Test-Driven Development）开发模式
//! - 验证API响应格式、状态码和数据持久化

use anyhow::{Context, Result};
use serde_json::{Value, json};
use std::time::Duration;
use tokio::time::sleep;

// 导入E2E测试辅助模块
#[path = "e2e/helpers/mod.rs"]
mod helpers;

use helpers::{
    AuthHelper, E2EConfig, TaskCrudHelper, TestServer, TestTaskData, ensure_dir_exists,
    test_server::TestServerConfig,
};

/// 任务创建E2E测试套件
pub struct TaskCreateTestSuite {
    config: E2EConfig,
    test_server: TestServer,
    auth_helper: <PERSON><PERSON><PERSON><PERSON><PERSON>,
    task_crud_helper: TaskCrudHelper,
    test_user_token: Option<String>,
}

impl TaskCreateTestSuite {
    /// 创建新的测试套件实例
    pub async fn new() -> Result<Self> {
        println!("🔧 初始化任务创建E2E测试套件...");

        // 加载配置
        let config = E2EConfig::from_env().context("无法加载E2E测试配置")?;

        // 创建测试服务器
        let server_config = TestServerConfig {
            host: config.server_host.clone(),
            port: config.server_port,
            startup_timeout: 60,
            health_check_interval: 1,
            project_root: std::env::current_dir()
                .context("无法获取当前目录")?
                .to_string_lossy()
                .to_string(),
        };

        let test_server = TestServer::new(server_config);

        // 创建辅助工具
        let auth_helper = AuthHelper::new(config.clone());
        let task_crud_helper = TaskCrudHelper::new(config.clone());

        Ok(Self {
            config,
            test_server,
            auth_helper,
            task_crud_helper,
            test_user_token: None,
        })
    }

    /// 设置测试环境
    pub async fn setup_test_environment(&mut self) -> Result<()> {
        println!("🚀 设置任务创建测试环境...");

        // 1. 确保报告目录存在
        self.ensure_report_directories()?;

        // 2. 启动测试服务器
        self.start_test_server().await?;

        // 3. 配置认证流程
        self.configure_authentication().await?;

        println!("✅ 任务创建测试环境设置完成");
        Ok(())
    }

    /// 确保报告目录存在
    fn ensure_report_directories(&self) -> Result<()> {
        println!("📁 创建测试报告目录...");

        ensure_dir_exists(&self.config.report_dir)?;
        ensure_dir_exists(&self.config.screenshot_dir)?;
        ensure_dir_exists(&self.config.video_dir)?;

        println!("✅ 测试报告目录创建完成");
        Ok(())
    }

    /// 启动测试服务器
    async fn start_test_server(&mut self) -> Result<()> {
        println!("🚀 启动测试服务器...");

        self.test_server
            .start()
            .await
            .context("无法启动测试服务器")?;

        // 等待服务器完全启动
        sleep(Duration::from_secs(3)).await;

        // 验证服务器健康状态
        let health_check = self.test_server.health_check().await?;
        println!("✅ 服务器健康检查通过: {:?}", health_check);

        Ok(())
    }

    /// 配置认证流程
    async fn configure_authentication(&mut self) -> Result<()> {
        println!("🔐 配置认证流程...");

        // 1. 注册测试用户（如果不存在）
        let register_result = self
            .auth_helper
            .register_user(
                &self.config.test_username,
                &self.config.test_email,
                &self.config.test_password,
            )
            .await;

        match register_result {
            Ok(response) => {
                if response["status"].as_u64().unwrap_or(0) == 201 {
                    println!("✅ 测试用户注册成功");
                } else if response["status"].as_u64().unwrap_or(0) == 409 {
                    println!("ℹ️ 测试用户已存在，跳过注册");
                } else {
                    return Err(anyhow::anyhow!("用户注册失败: {:?}", response));
                }
            }
            Err(e) => {
                println!("⚠️ 用户注册失败，可能用户已存在: {}", e);
            }
        }

        // 2. 登录获取认证令牌
        let token = self
            .auth_helper
            .get_auth_token(&self.config.test_username, &self.config.test_password)
            .await?;

        self.test_user_token = Some(token.clone());

        // 3. 验证令牌有效性
        let verify_result = self.auth_helper.verify_token(&token).await?;
        if verify_result["status"].as_u64().unwrap_or(0) != 200 {
            return Err(anyhow::anyhow!("令牌验证失败: {:?}", verify_result));
        }

        println!("✅ 认证流程配置完成，令牌验证通过");
        Ok(())
    }

    /// 测试任务创建成功场景
    pub async fn test_create_task_success(&self) -> Result<()> {
        println!("🧪 测试任务创建成功场景...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 创建测试任务数据
        let test_task = TestTaskData::new("E2E测试任务 - 创建成功")
            .with_description("这是一个用于测试任务创建成功场景的测试任务")
            .with_priority("high");

        // 执行任务创建
        let create_result = self.task_crud_helper.create_task(token, &test_task).await?;

        // 验证响应状态码
        assert_eq!(
            create_result["status"].as_u64().unwrap_or(0),
            201,
            "任务创建应返回201状态码"
        );

        // 验证响应格式
        self.task_crud_helper
            .validate_task_response(&create_result["body"])?;

        // 验证任务数据
        let task_data = &create_result["body"]["data"];
        assert_eq!(
            task_data["title"].as_str().unwrap_or(""),
            test_task.title,
            "返回的任务标题应与请求一致"
        );
        assert_eq!(
            task_data["description"].as_str().unwrap_or(""),
            test_task.description.as_ref().unwrap(),
            "返回的任务描述应与请求一致"
        );
        assert_eq!(
            task_data["completed"].as_bool().unwrap_or(true),
            false,
            "新创建的任务应为未完成状态"
        );

        // 验证必需字段存在
        assert!(task_data["id"].is_string(), "任务ID应为字符串类型");
        assert!(
            task_data["created_at"].is_string(),
            "创建时间应为字符串类型"
        );
        assert!(
            task_data["updated_at"].is_string(),
            "更新时间应为字符串类型"
        );

        // 清理测试数据
        if let Some(task_id) = task_data["id"].as_str() {
            let _ = self.task_crud_helper.delete_task(token, task_id).await;
        }

        println!("✅ 任务创建成功场景测试通过");
        Ok(())
    }

    /// 测试任务创建验证失败场景
    pub async fn test_create_task_validation_failure(&self) -> Result<()> {
        println!("🧪 测试任务创建验证失败场景...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 测试空标题
        let empty_title_task = TestTaskData::new("").with_description("测试空标题验证");

        let empty_title_result = self
            .task_crud_helper
            .create_task(token, &empty_title_task)
            .await?;
        assert_eq!(
            empty_title_result["status"].as_u64().unwrap_or(0),
            400,
            "空标题应返回400错误"
        );

        // 测试过长标题（超过200字符）
        let long_title = "a".repeat(201);
        let long_title_task = TestTaskData::new(&long_title).with_description("测试过长标题验证");

        let long_title_result = self
            .task_crud_helper
            .create_task(token, &long_title_task)
            .await?;
        assert_eq!(
            long_title_result["status"].as_u64().unwrap_or(0),
            400,
            "过长标题应返回400错误"
        );

        println!("✅ 任务创建验证失败场景测试通过");
        Ok(())
    }

    /// 测试未认证用户创建任务
    pub async fn test_create_task_unauthorized(&self) -> Result<()> {
        println!("🧪 测试未认证用户创建任务...");

        let test_task = TestTaskData::new("未认证测试任务");

        // 使用无效令牌
        let invalid_token = "invalid_token";
        let unauthorized_result = self
            .task_crud_helper
            .create_task(invalid_token, &test_task)
            .await?;

        assert_eq!(
            unauthorized_result["status"].as_u64().unwrap_or(0),
            401,
            "未认证用户应返回401错误"
        );

        println!("✅ 未认证用户创建任务测试通过");
        Ok(())
    }

    /// 测试批量创建任务
    pub async fn test_create_multiple_tasks(&self) -> Result<()> {
        println!("🧪 测试批量创建任务...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 生成多个测试任务
        let test_tasks = TaskCrudHelper::generate_test_tasks(5);

        // 批量创建任务
        let create_results = self
            .task_crud_helper
            .create_multiple_tasks(token, &test_tasks)
            .await?;

        // 验证所有任务都创建成功
        assert_eq!(create_results.len(), 5, "应该创建5个任务");

        let mut created_task_ids = Vec::new();
        for (i, result) in create_results.iter().enumerate() {
            assert_eq!(
                result["status"].as_u64().unwrap_or(0),
                201,
                "第{}个任务创建应返回201状态码",
                i + 1
            );

            // 验证响应格式
            self.task_crud_helper
                .validate_task_response(&result["body"])?;

            // 收集任务ID用于清理
            if let Some(task_id) = result["body"]["data"]["id"].as_str() {
                created_task_ids.push(task_id.to_string());
            }
        }

        // 清理创建的任务
        for task_id in created_task_ids {
            let _ = self.task_crud_helper.delete_task(token, &task_id).await;
        }

        println!("✅ 批量创建任务测试通过");
        Ok(())
    }

    /// 测试并发创建任务
    pub async fn test_create_tasks_concurrently(&self) -> Result<()> {
        println!("🧪 测试并发创建任务...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 创建并发任务
        let mut handles = Vec::new();
        for i in 0..3 {
            let token = token.clone();
            let task_crud_helper = self.task_crud_helper.clone();
            let test_task = TestTaskData::new(&format!("并发测试任务 #{}", i + 1))
                .with_description(&format!("第{}个并发创建的测试任务", i + 1));

            let handle =
                tokio::spawn(async move { task_crud_helper.create_task(&token, &test_task).await });
            handles.push(handle);
        }

        // 等待所有任务完成
        let mut created_task_ids = Vec::new();
        for handle in handles {
            let result = handle.await??;
            assert_eq!(
                result["status"].as_u64().unwrap_or(0),
                201,
                "并发任务创建应返回201状态码"
            );

            if let Some(task_id) = result["body"]["data"]["id"].as_str() {
                created_task_ids.push(task_id.to_string());
            }
        }

        // 清理创建的任务
        for task_id in created_task_ids {
            let _ = self.task_crud_helper.delete_task(token, &task_id).await;
        }

        println!("✅ 并发创建任务测试通过");
        Ok(())
    }

    /// 测试任务创建数据持久化
    pub async fn test_create_task_data_persistence(&self) -> Result<()> {
        println!("🧪 测试任务创建数据持久化...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 创建测试任务
        let test_task = TestTaskData::new("数据持久化测试任务")
            .with_description("用于验证数据持久化的测试任务")
            .with_priority("medium");

        let create_result = self.task_crud_helper.create_task(token, &test_task).await?;
        assert_eq!(create_result["status"].as_u64().unwrap_or(0), 201);

        let task_id = create_result["body"]["data"]["id"]
            .as_str()
            .ok_or_else(|| anyhow::anyhow!("无法获取任务ID"))?;

        // 等待一段时间确保数据已持久化
        sleep(Duration::from_millis(500)).await;

        // 重新获取任务验证数据持久化
        let get_result = self
            .task_crud_helper
            .fetch_task_by_id(token, task_id)
            .await?;
        assert_eq!(get_result["status"].as_u64().unwrap_or(0), 200);

        let persisted_task = &get_result["body"]["data"];
        assert_eq!(
            persisted_task["title"].as_str().unwrap_or(""),
            test_task.title,
            "持久化的任务标题应与创建时一致"
        );
        assert_eq!(
            persisted_task["description"].as_str().unwrap_or(""),
            test_task.description.as_ref().unwrap(),
            "持久化的任务描述应与创建时一致"
        );
        assert_eq!(
            persisted_task["completed"].as_bool().unwrap_or(true),
            false,
            "持久化的任务状态应为未完成"
        );

        // 验证时间戳字段
        assert!(
            persisted_task["created_at"].is_string(),
            "创建时间应为字符串"
        );
        assert!(
            persisted_task["updated_at"].is_string(),
            "更新时间应为字符串"
        );

        // 清理测试数据
        let _ = self.task_crud_helper.delete_task(token, task_id).await;

        println!("✅ 任务创建数据持久化测试通过");
        Ok(())
    }

    /// 测试任务创建响应时间性能
    pub async fn test_create_task_performance(&self) -> Result<()> {
        println!("🧪 测试任务创建响应时间性能...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        let test_task =
            TestTaskData::new("性能测试任务").with_description("用于测试响应时间性能的任务");

        // 记录开始时间
        let start_time = std::time::Instant::now();

        // 执行任务创建
        let create_result = self.task_crud_helper.create_task(token, &test_task).await?;

        // 计算响应时间
        let response_time = start_time.elapsed();

        // 验证创建成功
        assert_eq!(create_result["status"].as_u64().unwrap_or(0), 201);

        // 验证响应时间（应小于1秒）
        assert!(
            response_time.as_millis() < 1000,
            "任务创建响应时间应小于1秒，实际: {}ms",
            response_time.as_millis()
        );

        println!("📊 任务创建响应时间: {}ms", response_time.as_millis());

        // 清理测试数据
        if let Some(task_id) = create_result["body"]["data"]["id"].as_str() {
            let _ = self.task_crud_helper.delete_task(token, task_id).await;
        }

        println!("✅ 任务创建响应时间性能测试通过");
        Ok(())
    }

    /// 清理测试环境
    pub async fn cleanup(&mut self) -> Result<()> {
        println!("🧹 清理测试环境...");

        // 清理测试任务
        if let Some(token) = &self.test_user_token {
            let _ = self.task_crud_helper.cleanup_test_tasks(token).await;
        }

        // 停止测试服务器
        self.test_server.stop()?;

        println!("✅ 测试环境清理完成");
        Ok(())
    }
}

impl Drop for TaskCreateTestSuite {
    fn drop(&mut self) {
        // 确保服务器被停止
        let _ = self.test_server.stop();
    }
}

/// 任务创建成功测试
#[tokio::test]
async fn test_task_create_success() -> Result<()> {
    let mut test_suite = TaskCreateTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_create_task_success().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 任务创建验证失败测试
#[tokio::test]
async fn test_task_create_validation_failure() -> Result<()> {
    let mut test_suite = TaskCreateTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_create_task_validation_failure().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 未认证用户创建任务测试
#[tokio::test]
async fn test_task_create_unauthorized() -> Result<()> {
    let mut test_suite = TaskCreateTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_create_task_unauthorized().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 批量任务创建测试
#[tokio::test]
async fn test_task_create_batch() -> Result<()> {
    let mut test_suite = TaskCreateTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_create_multiple_tasks().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 任务创建并发测试
#[tokio::test]
async fn test_task_create_concurrent() -> Result<()> {
    let mut test_suite = TaskCreateTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_create_tasks_concurrently().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 任务创建数据持久化测试
#[tokio::test]
async fn test_task_create_data_persistence() -> Result<()> {
    let mut test_suite = TaskCreateTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_create_task_data_persistence().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 任务创建响应时间性能测试
#[tokio::test]
async fn test_task_create_performance() -> Result<()> {
    let mut test_suite = TaskCreateTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_create_task_performance().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 主测试函数 - 运行所有任务创建测试
#[tokio::main]
async fn main() -> Result<()> {
    // 设置日志
    tracing_subscriber::fmt::init();

    println!("🚀 启动任务创建功能E2E测试套件");

    let mut test_suite = TaskCreateTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    println!("📋 开始执行任务创建测试用例...");

    // 1. 基础功能测试
    println!("1️⃣ 执行任务创建成功测试...");
    test_suite.test_create_task_success().await?;

    // 2. 验证失败测试
    println!("2️⃣ 执行任务创建验证失败测试...");
    test_suite.test_create_task_validation_failure().await?;

    // 3. 未认证用户测试
    println!("3️⃣ 执行未认证用户创建任务测试...");
    test_suite.test_create_task_unauthorized().await?;

    // 4. 批量创建测试
    println!("4️⃣ 执行批量任务创建测试...");
    test_suite.test_create_multiple_tasks().await?;

    // 5. 并发创建测试
    println!("5️⃣ 执行并发任务创建测试...");
    test_suite.test_create_tasks_concurrently().await?;

    // 6. 数据持久化测试
    println!("6️⃣ 执行数据持久化测试...");
    test_suite.test_create_task_data_persistence().await?;

    // 7. 性能测试
    println!("7️⃣ 执行响应时间性能测试...");
    test_suite.test_create_task_performance().await?;

    test_suite.cleanup().await?;

    println!("✅ 所有任务创建E2E测试完成");
    Ok(())
}
