//! # 消息搜索缓存服务
//!
//! 专门为消息搜索功能设计的多级缓存服务
//! 实现L1热门搜索缓存和L2近期搜索结果缓存，支持缓存预热和智能更新

use super::{
    build_cache_key, build_user_cache_key, cache_keys, cache_ttl,
    multi_tier::{CacheTier, MultiTierCacheService},
};
use anyhow::Result as AnyhowResult;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tracing::{debug, info};

/// 搜索查询结果数据结构
///
/// 【目的】: 存储搜索查询的结果和元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchQueryResult {
    /// 查询字符串
    pub query: String,
    /// 搜索结果（消息ID列表）
    pub message_ids: Vec<String>,
    /// 结果总数
    pub total_count: u64,
    /// 查询时间戳
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// 查询用户ID
    pub user_id: String,
    /// 搜索范围（可选：聊天室ID）
    pub room_id: Option<String>,
}

/// 搜索查询统计信息
///
/// 【目的】: 记录搜索查询的使用频率和性能指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchQueryStats {
    /// 查询字符串
    pub query: String,
    /// 查询次数
    pub query_count: u64,
    /// 最后查询时间
    pub last_queried: chrono::DateTime<chrono::Utc>,
    /// 平均响应时间（毫秒）
    pub avg_response_time_ms: f64,
    /// 结果命中率
    pub hit_rate: f64,
}

/// 用户搜索历史记录
///
/// 【目的】: 存储用户的搜索历史，用于个性化推荐
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserSearchHistory {
    /// 用户ID
    pub user_id: String,
    /// 最近搜索查询列表（按时间倒序）
    pub recent_queries: Vec<String>,
    /// 热门查询列表（按频率排序）
    pub popular_queries: Vec<String>,
    /// 最后更新时间
    pub last_updated: chrono::DateTime<chrono::Utc>,
}

/// 消息搜索缓存服务
///
/// 【目的】: 为消息搜索功能提供专门的多级缓存支持
/// 【特性】: L1热门查询缓存、L2搜索结果缓存、用户历史缓存、统计信息缓存
pub struct MessageSearchCacheService {
    /// 底层多层缓存服务
    cache_service: Arc<MultiTierCacheService>,
}

impl MessageSearchCacheService {
    /// 创建新的消息搜索缓存服务
    ///
    /// 【参数】:
    /// - cache_service: 多层缓存服务实例
    ///
    /// 【返回】: 消息搜索缓存服务实例
    pub fn new(cache_service: Arc<MultiTierCacheService>) -> Self {
        info!("🚀 创建消息搜索缓存服务");
        Self { cache_service }
    }

    /// 缓存搜索查询结果（L2缓存）
    ///
    /// 【参数】:
    /// - result: 搜索查询结果
    ///
    /// 【返回】: 操作结果
    pub async fn cache_search_result(&self, result: &SearchQueryResult) -> AnyhowResult<()> {
        let cache_key = self.build_search_result_key(
            &result.query,
            result.user_id.as_str(),
            result.room_id.as_deref(),
        );

        debug!(
            "缓存搜索结果: {} (用户: {}, 结果数: {})",
            result.query, result.user_id, result.total_count
        );

        // 使用温数据层缓存搜索结果
        self.cache_service
            .set_with_tier(
                CacheTier::Warm,
                &cache_key,
                result,
                Some(cache_ttl::RECENT_SEARCH_RESULTS),
            )
            .await?;

        // 更新搜索统计信息
        self.update_search_stats(&result.query).await?;

        // 更新用户搜索历史
        self.update_user_search_history(&result.user_id, &result.query)
            .await?;

        Ok(())
    }

    /// 获取缓存的搜索结果
    ///
    /// 【参数】:
    /// - query: 搜索查询字符串
    /// - user_id: 用户ID
    /// - room_id: 聊天室ID（可选）
    ///
    /// 【返回】: 缓存的搜索结果（如果存在）
    pub async fn get_cached_search_result(
        &self,
        query: &str,
        user_id: &str,
        room_id: Option<&str>,
    ) -> AnyhowResult<Option<SearchQueryResult>> {
        let cache_key = self.build_search_result_key(query, user_id, room_id);

        debug!("查找缓存搜索结果: {} (用户: {})", query, user_id);

        // 先从热数据层查找
        if let Ok(Some(result)) = self
            .cache_service
            .get_with_tier::<SearchQueryResult>(CacheTier::Hot, &cache_key)
            .await
        {
            debug!("从热缓存命中搜索结果: {}", query);
            return Ok(Some(result));
        }

        // 再从温数据层查找
        if let Ok(Some(result)) = self
            .cache_service
            .get_with_tier::<SearchQueryResult>(CacheTier::Warm, &cache_key)
            .await
        {
            debug!("从温缓存命中搜索结果: {}", query);

            // 如果是热门查询，提升到热数据层
            if self.is_hot_query(query).await? {
                let _ = self
                    .cache_service
                    .set_with_tier(
                        CacheTier::Hot,
                        &cache_key,
                        &result,
                        Some(cache_ttl::HOT_SEARCH_QUERIES),
                    )
                    .await;
                debug!("将热门查询提升到热缓存: {}", query);
            }

            return Ok(Some(result));
        }

        debug!("缓存未命中搜索结果: {}", query);
        Ok(None)
    }

    /// 缓存热门搜索查询（L1缓存）
    ///
    /// 【参数】:
    /// - queries: 热门查询列表
    ///
    /// 【返回】: 操作结果
    pub async fn cache_hot_queries(&self, queries: &[String]) -> AnyhowResult<()> {
        let cache_key = build_cache_key(cache_keys::HOT_SEARCH_QUERIES, "global");

        info!("缓存热门搜索查询: {} 个", queries.len());

        // 转换为Vec以满足Sized要求
        let queries_vec = queries.to_vec();

        // 使用热数据层缓存热门查询
        self.cache_service
            .set_with_tier(
                CacheTier::Hot,
                &cache_key,
                &queries_vec,
                Some(cache_ttl::HOT_SEARCH_QUERIES),
            )
            .await?;

        Ok(())
    }

    /// 获取热门搜索查询
    ///
    /// 【返回】: 热门查询列表
    pub async fn get_hot_queries(&self) -> AnyhowResult<Option<Vec<String>>> {
        let cache_key = build_cache_key(cache_keys::HOT_SEARCH_QUERIES, "global");

        debug!("获取热门搜索查询");

        self.cache_service
            .get_with_tier::<Vec<String>>(CacheTier::Hot, &cache_key)
            .await
    }

    /// 获取用户搜索历史
    ///
    /// 【参数】:
    /// - user_id: 用户ID
    ///
    /// 【返回】: 用户搜索历史
    pub async fn get_user_search_history(
        &self,
        user_id: &str,
    ) -> AnyhowResult<Option<UserSearchHistory>> {
        let cache_key = build_user_cache_key(user_id, cache_keys::USER_SEARCH_HISTORY);

        debug!("获取用户搜索历史: {}", user_id);

        self.cache_service
            .get_with_tier::<UserSearchHistory>(CacheTier::Cold, &cache_key)
            .await
    }

    /// 获取搜索查询统计信息
    ///
    /// 【参数】:
    /// - query: 查询字符串
    ///
    /// 【返回】: 查询统计信息
    pub async fn get_search_stats(&self, query: &str) -> AnyhowResult<Option<SearchQueryStats>> {
        let cache_key = build_cache_key(cache_keys::SEARCH_QUERY_STATS, query);

        debug!("获取搜索统计信息: {}", query);

        self.cache_service
            .get_with_tier::<SearchQueryStats>(CacheTier::Warm, &cache_key)
            .await
    }

    /// 预热缓存
    ///
    /// 【参数】:
    /// - hot_queries: 热门查询列表
    /// - recent_results: 近期搜索结果列表
    ///
    /// 【返回】: 操作结果
    pub async fn warm_cache(
        &self,
        hot_queries: &[String],
        recent_results: &[SearchQueryResult],
    ) -> AnyhowResult<()> {
        info!(
            "开始缓存预热: {} 个热门查询, {} 个近期结果",
            hot_queries.len(),
            recent_results.len()
        );

        // 预热热门查询
        self.cache_hot_queries(hot_queries).await?;

        // 预热近期搜索结果
        for result in recent_results {
            let _ = self.cache_search_result(result).await;
        }

        info!("缓存预热完成");
        Ok(())
    }

    /// 构建搜索结果缓存键
    ///
    /// 【参数】:
    /// - query: 查询字符串
    /// - user_id: 用户ID
    /// - room_id: 聊天室ID（可选）
    ///
    /// 【返回】: 缓存键
    fn build_search_result_key(&self, query: &str, user_id: &str, room_id: Option<&str>) -> String {
        let base_key = format!(
            "{}:{}:{}",
            cache_keys::RECENT_SEARCH_RESULTS,
            user_id,
            query
        );
        if let Some(room_id) = room_id {
            format!("{base_key}:{room_id}")
        } else {
            base_key
        }
    }

    /// 检查是否为热门查询
    ///
    /// 【参数】:
    /// - query: 查询字符串
    ///
    /// 【返回】: 是否为热门查询
    async fn is_hot_query(&self, query: &str) -> AnyhowResult<bool> {
        if let Some(hot_queries) = self.get_hot_queries().await? {
            Ok(hot_queries.contains(&query.to_string()))
        } else {
            Ok(false)
        }
    }

    /// 更新搜索统计信息
    ///
    /// 【参数】:
    /// - query: 查询字符串
    ///
    /// 【返回】: 操作结果
    async fn update_search_stats(&self, query: &str) -> AnyhowResult<()> {
        let cache_key = build_cache_key(cache_keys::SEARCH_QUERY_STATS, query);

        // 获取现有统计信息或创建新的
        let mut stats = self
            .get_search_stats(query)
            .await?
            .unwrap_or_else(|| SearchQueryStats {
                query: query.to_string(),
                query_count: 0,
                last_queried: chrono::Utc::now(),
                avg_response_time_ms: 0.0,
                hit_rate: 0.0,
            });

        // 更新统计信息
        stats.query_count += 1;
        stats.last_queried = chrono::Utc::now();

        // 缓存更新后的统计信息
        self.cache_service
            .set_with_tier(
                CacheTier::Warm,
                &cache_key,
                &stats,
                Some(cache_ttl::SEARCH_QUERY_STATS),
            )
            .await?;

        Ok(())
    }

    /// 更新用户搜索历史
    ///
    /// 【参数】:
    /// - user_id: 用户ID
    /// - query: 查询字符串
    ///
    /// 【返回】: 操作结果
    async fn update_user_search_history(&self, user_id: &str, query: &str) -> AnyhowResult<()> {
        let cache_key = build_user_cache_key(user_id, cache_keys::USER_SEARCH_HISTORY);

        // 获取现有历史记录或创建新的
        let mut history = self
            .get_user_search_history(user_id)
            .await?
            .unwrap_or_else(|| UserSearchHistory {
                user_id: user_id.to_string(),
                recent_queries: Vec::new(),
                popular_queries: Vec::new(),
                last_updated: chrono::Utc::now(),
            });

        // 更新最近查询列表
        history.recent_queries.retain(|q| q != query);
        history.recent_queries.insert(0, query.to_string());

        // 保持最近查询列表不超过20个
        if history.recent_queries.len() > 20 {
            history.recent_queries.truncate(20);
        }

        history.last_updated = chrono::Utc::now();

        // 缓存更新后的历史记录
        self.cache_service
            .set_with_tier(
                CacheTier::Cold,
                &cache_key,
                &history,
                Some(cache_ttl::USER_SEARCH_HISTORY),
            )
            .await?;

        Ok(())
    }

    /// 原始获取缓存值（用于兼容性检查）
    ///
    /// 【参数】:
    /// - key: 缓存键
    ///
    /// 【返回】: 原始缓存值或错误
    ///
    /// 【用途】: 专门用于缓存兼容性检查，不进行任何数据转换
    pub async fn get_cache_value_raw(&self, key: &str) -> AnyhowResult<String> {
        // 尝试从各个缓存层获取原始值
        if let Ok(Some(value)) = self
            .cache_service
            .get_raw_from_tier(CacheTier::Hot, key)
            .await
        {
            return Ok(value);
        }

        if let Ok(Some(value)) = self
            .cache_service
            .get_raw_from_tier(CacheTier::Warm, key)
            .await
        {
            return Ok(value);
        }

        if let Ok(Some(value)) = self
            .cache_service
            .get_raw_from_tier(CacheTier::Cold, key)
            .await
        {
            return Ok(value);
        }

        Err(anyhow::anyhow!("缓存键不存在: {}", key))
    }
}
