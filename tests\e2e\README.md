# E2E 测试目录

本目录包含Axum项目的端到端(E2E)测试，使用MCP Playwright进行自动化测试。

## 目录结构

```
tests/e2e/
├── README.md                 # 本文件
├── config/                   # 配置文件
│   ├── playwright.config.json
│   └── test.env
├── fixtures/                 # 测试数据和夹具
│   ├── users.json
│   └── tasks.json
├── helpers/                  # 测试辅助函数
│   ├── auth.rs
│   ├── api.rs
│   └── database.rs
├── specs/                    # 测试规范文件
│   ├── auth/                 # 认证相关测试
│   ├── tasks/                # 任务管理测试
│   ├── api/                  # API响应测试
│   ├── ui/                   # UI交互测试
│   └── websocket/            # WebSocket测试
└── reports/                  # 测试报告输出
    ├── html/
    └── json/

## 测试环境配置

- 测试服务器地址: 127.0.0.1:3000
- 测试用户: testuser456/password123
- 数据库: SQLite (本地测试)

## 运行测试

```bash
# 启动测试服务器
cargo run -p server

# 运行所有E2E测试
cargo test --test e2e_tests

# 运行特定模块测试
cargo test --test e2e_auth_tests
cargo test --test e2e_tasks_tests
```

## 测试覆盖率目标

- 总体覆盖率: 95%
- 用户认证: 100%
- CRUD操作: 95%
- API响应: 90%
- UI交互: 85%
- 错误处理: 90%
