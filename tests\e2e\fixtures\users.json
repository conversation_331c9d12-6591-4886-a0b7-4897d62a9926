{"testUsers": [{"id": "test-user-001", "username": "testuser456", "email": "<EMAIL>", "password": "password123", "role": "user", "created_at": "2025-01-01T00:00:00Z", "is_active": true}, {"id": "test-admin-001", "username": "testadmin", "email": "<EMAIL>", "password": "adminpass123", "role": "admin", "created_at": "2025-01-01T00:00:00Z", "is_active": true}, {"id": "test-user-002", "username": "testuser2", "email": "<EMAIL>", "password": "password456", "role": "user", "created_at": "2025-01-01T00:00:00Z", "is_active": false}], "invalidUsers": [{"username": "", "email": "<EMAIL>", "password": "password123", "expectedError": "用户名不能为空"}, {"username": "validuser", "email": "invalid-email", "password": "password123", "expectedError": "邮箱格式无效"}, {"username": "validuser", "email": "<EMAIL>", "password": "123", "expectedError": "密码长度至少6位"}, {"username": "testuser456", "email": "<EMAIL>", "password": "password123", "expectedError": "用户名已存在"}]}