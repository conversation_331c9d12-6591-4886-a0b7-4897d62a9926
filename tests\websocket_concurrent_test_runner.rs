//! # WebSocket并发测试运行器
//!
//! 整合所有WebSocket并发测试，提供统一的测试入口和报告

use serde_json::json;
use std::{
    collections::HashMap,
    time::{Duration, Instant},
};
use tracing::{error, info, warn};

mod websocket_concurrent_e2e_test;
mod websocket_concurrent_integration_test;
mod websocket_concurrent_test;
mod websocket_playwright_concurrent_test;

// 使用明确的导入避免类型歧义
use websocket_concurrent_e2e_test::*;
use websocket_concurrent_integration_test::{
    ConcurrentTestConfig as IntegrationConcurrentTestConfig,
    TestStatistics as IntegrationTestStatistics, WebSocketConcurrentTestSuite,
    WebSocketConcurrentTester as IntegrationWebSocketTester,
};
use websocket_concurrent_test::{
    ConcurrentTestConfig as NativeConcurrentTestConfig, TestStatistics as NativeTestStatistics,
    WebSocketConcurrentTester as NativeWebSocketTester,
};
use websocket_playwright_concurrent_test::*;

/// 综合测试结果
#[derive(Debug)]
pub struct ComprehensiveTestResults {
    /// 原生WebSocket测试结果
    pub native_test_results: HashMap<String, NativeTestStatistics>,
    /// 集成测试结果
    pub integration_test_results: HashMap<String, IntegrationTestStatistics>,
    /// 浏览器E2E测试结果
    pub browser_test_results: HashMap<String, BrowserTestStatistics>,
    /// Playwright测试结果
    pub playwright_test_results: HashMap<String, PlaywrightTestStatistics>,
    /// 测试执行时间
    pub test_execution_times: HashMap<String, Duration>,
    /// 测试成功/失败状态
    pub test_status: HashMap<String, bool>,
}

impl ComprehensiveTestResults {
    /// 创建新的综合测试结果
    pub fn new() -> Self {
        Self {
            native_test_results: HashMap::new(),
            integration_test_results: HashMap::new(),
            browser_test_results: HashMap::new(),
            playwright_test_results: HashMap::new(),
            test_execution_times: HashMap::new(),
            test_status: HashMap::new(),
        }
    }

    /// 添加原生测试结果
    pub fn add_native_test_result(
        &mut self,
        test_name: String,
        result: NativeTestStatistics,
        duration: Duration,
        success: bool,
    ) {
        self.native_test_results.insert(test_name.clone(), result);
        self.test_execution_times
            .insert(test_name.clone(), duration);
        self.test_status.insert(test_name, success);
    }

    /// 添加集成测试结果
    pub fn add_integration_test_result(
        &mut self,
        test_name: String,
        result: IntegrationTestStatistics,
        duration: Duration,
        success: bool,
    ) {
        self.integration_test_results
            .insert(test_name.clone(), result);
        self.test_execution_times
            .insert(test_name.clone(), duration);
        self.test_status.insert(test_name, success);
    }

    /// 添加浏览器测试结果
    pub fn add_browser_test_result(
        &mut self,
        test_name: String,
        result: BrowserTestStatistics,
        duration: Duration,
        success: bool,
    ) {
        self.browser_test_results.insert(test_name.clone(), result);
        self.test_execution_times
            .insert(test_name.clone(), duration);
        self.test_status.insert(test_name, success);
    }

    /// 添加Playwright测试结果
    pub fn add_playwright_test_result(
        &mut self,
        test_name: String,
        result: PlaywrightTestStatistics,
        duration: Duration,
        success: bool,
    ) {
        self.playwright_test_results
            .insert(test_name.clone(), result);
        self.test_execution_times
            .insert(test_name.clone(), duration);
        self.test_status.insert(test_name, success);
    }

    /// 计算总体成功率
    pub fn calculate_overall_success_rate(&self) -> f64 {
        let total_tests = self.test_status.len();
        if total_tests == 0 {
            return 0.0;
        }

        let successful_tests = self
            .test_status
            .values()
            .filter(|&&success| success)
            .count();
        ((successful_tests as f64) / (total_tests as f64)) * 100.0
    }

    /// 获取总执行时间
    pub fn get_total_execution_time(&self) -> Duration {
        self.test_execution_times.values().sum()
    }

    /// 打印综合测试报告
    pub fn print_comprehensive_report(&self) {
        println!("\n{}", "=".repeat(80));
        println!("               WebSocket并发测试综合报告");
        println!("{}", "=".repeat(80));

        // 总体统计
        println!("\n📊 总体统计:");
        println!("  总测试数量: {}", self.test_status.len());
        println!(
            "  成功测试数: {}",
            self.test_status.values().filter(|&&s| s).count()
        );
        println!(
            "  失败测试数: {}",
            self.test_status.values().filter(|&&s| !s).count()
        );
        println!(
            "  总体成功率: {:.2}%",
            self.calculate_overall_success_rate()
        );
        println!(
            "  总执行时间: {:.2}秒",
            self.get_total_execution_time().as_secs_f64()
        );

        // 原生WebSocket测试结果
        if !self.native_test_results.is_empty() {
            println!("\n🔧 原生WebSocket测试结果:");
            for (test_name, stats) in &self.native_test_results {
                let status = if *self.test_status.get(test_name).unwrap_or(&false) {
                    "✅"
                } else {
                    "❌"
                };
                let duration = self
                    .test_execution_times
                    .get(test_name)
                    .unwrap_or(&Duration::ZERO);

                println!("  {} {}", status, test_name);
                println!("    连接成功率: {:.2}%", stats.success_rate);
                println!("    消息发送数: {}", stats.total_messages_sent);
                println!("    消息接收数: {}", stats.total_messages_received);
                println!("    平均连接延迟: {:.2}ms", stats.avg_connection_latency_ms);
                println!("    执行时间: {:.2}秒", duration.as_secs_f64());
            }
        }

        // 集成测试结果
        if !self.integration_test_results.is_empty() {
            println!("\n🔗 集成测试结果:");
            for (test_name, stats) in &self.integration_test_results {
                let status = if *self.test_status.get(test_name).unwrap_or(&false) {
                    "✅"
                } else {
                    "❌"
                };
                let duration = self
                    .test_execution_times
                    .get(test_name)
                    .unwrap_or(&Duration::ZERO);

                println!("  {} {}", status, test_name);
                println!("    连接成功率: {:.2}%", stats.success_rate);
                println!("    消息发送数: {}", stats.total_messages_sent);
                println!("    重连次数: {}", stats.reconnection_count);
                println!("    执行时间: {:.2}秒", duration.as_secs_f64());
            }
        }

        // 浏览器E2E测试结果
        if !self.browser_test_results.is_empty() {
            println!("\n🌐 浏览器E2E测试结果:");
            for (test_name, stats) in &self.browser_test_results {
                let status = if *self.test_status.get(test_name).unwrap_or(&false) {
                    "✅"
                } else {
                    "❌"
                };
                let duration = self
                    .test_execution_times
                    .get(test_name)
                    .unwrap_or(&Duration::ZERO);

                println!("  {} {}", status, test_name);
                println!("    浏览器启动成功率: {:.2}%", stats.browser_success_rate);
                println!(
                    "    WebSocket连接成功率: {:.2}%",
                    stats.websocket_success_rate
                );
                println!("    平均页面加载时间: {:.2}ms", stats.avg_page_load_time_ms);
                println!("    执行时间: {:.2}秒", duration.as_secs_f64());
            }
        }

        // Playwright测试结果
        if !self.playwright_test_results.is_empty() {
            println!("\n🎭 Playwright测试结果:");
            for (test_name, stats) in &self.playwright_test_results {
                let status = if *self.test_status.get(test_name).unwrap_or(&false) {
                    "✅"
                } else {
                    "❌"
                };
                let duration = self
                    .test_execution_times
                    .get(test_name)
                    .unwrap_or(&Duration::ZERO);

                println!("  {} {}", status, test_name);
                println!("    页面创建成功率: {:.2}%", stats.page_success_rate);
                println!("    登录成功率: {:.2}%", stats.login_success_rate);
                println!(
                    "    WebSocket连接成功率: {:.2}%",
                    stats.websocket_success_rate
                );
                println!("    执行时间: {:.2}秒", duration.as_secs_f64());
            }
        }

        // 性能建议
        println!("\n💡 性能建议:");
        self.print_performance_recommendations();

        println!("\n{}", "=".repeat(80));
        println!("                    报告结束");
        println!("{}", "=".repeat(80));
    }

    /// 打印性能建议
    fn print_performance_recommendations(&self) {
        let mut recommendations = Vec::new();

        // 分析原生测试结果
        for (test_name, stats) in &self.native_test_results {
            if stats.success_rate < 90.0 {
                recommendations.push(format!(
                    "  ⚠️  {}: 连接成功率较低({:.2}%)，建议检查网络配置和服务器负载",
                    test_name, stats.success_rate
                ));
            }
            if stats.avg_connection_latency_ms > 1000.0 {
                recommendations.push(format!(
                    "  ⚠️  {}: 连接延迟较高({:.2}ms)，建议优化网络或服务器性能",
                    test_name, stats.avg_connection_latency_ms
                ));
            }
            if stats.reconnection_count > 0 {
                recommendations.push(format!(
                    "  ⚠️  {}: 发生了{}次重连，建议检查连接稳定性",
                    test_name, stats.reconnection_count
                ));
            }
        }

        // 分析浏览器测试结果
        for (test_name, stats) in &self.browser_test_results {
            if stats.avg_page_load_time_ms > 3000.0 {
                recommendations.push(format!(
                    "  ⚠️  {}: 页面加载时间较长({:.2}ms)，建议优化前端资源",
                    test_name, stats.avg_page_load_time_ms
                ));
            }
            if stats.javascript_errors > 0 {
                recommendations.push(format!(
                    "  ⚠️  {}: 发现{}个JavaScript错误，建议检查前端代码",
                    test_name, stats.javascript_errors
                ));
            }
        }

        if recommendations.is_empty() {
            println!("  ✅ 所有测试表现良好，无需特别优化");
        } else {
            for recommendation in recommendations {
                println!("{}", recommendation);
            }
        }
    }
}

/// WebSocket并发测试运行器
pub struct WebSocketConcurrentTestRunner {
    base_url: String,
    test_credentials: (String, String),
}

impl WebSocketConcurrentTestRunner {
    /// 创建新的测试运行器
    pub fn new(base_url: String, test_credentials: (String, String)) -> Self {
        Self {
            base_url,
            test_credentials,
        }
    }

    /// 运行所有并发测试
    pub async fn run_all_concurrent_tests(
        &self,
    ) -> Result<ComprehensiveTestResults, Box<dyn std::error::Error>> {
        let mut results = ComprehensiveTestResults::new();

        info!("开始执行WebSocket并发测试套件");
        let overall_start = Instant::now();

        // 1. 运行原生WebSocket测试
        info!("执行原生WebSocket并发测试...");
        if let Err(e) = self.run_native_tests(&mut results).await {
            error!("原生WebSocket测试失败: {}", e);
        }

        // 2. 运行集成测试
        info!("执行集成测试...");
        if let Err(e) = self.run_integration_tests(&mut results).await {
            error!("集成测试失败: {}", e);
        }

        // 3. 运行浏览器E2E测试
        info!("执行浏览器E2E测试...");
        if let Err(e) = self.run_browser_tests(&mut results).await {
            error!("浏览器E2E测试失败: {}", e);
        }

        // 4. 运行Playwright测试
        info!("执行Playwright测试...");
        if let Err(e) = self.run_playwright_tests(&mut results).await {
            error!("Playwright测试失败: {}", e);
        }

        let overall_duration = overall_start.elapsed();
        info!(
            "所有并发测试完成，总耗时: {:.2}秒",
            overall_duration.as_secs_f64()
        );

        Ok(results)
    }

    /// 运行原生WebSocket测试
    async fn run_native_tests(
        &self,
        results: &mut ComprehensiveTestResults,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // 轻量级测试
        let start = Instant::now();
        let config = NativeConcurrentTestConfig {
            concurrent_users: 5,
            messages_per_user: 20,
            test_duration_secs: 30,
            ..Default::default()
        };

        let tester = NativeWebSocketTester::new(
            format!("{}/ws", self.base_url.replace("http", "ws")),
            config,
        );

        match tester.run_concurrent_test().await {
            Ok(stats) => {
                let success = stats.success_rate >= 80.0;
                results.add_native_test_result(
                    "原生轻量级并发测试".to_string(),
                    stats,
                    start.elapsed(),
                    success,
                );
            }
            Err(e) => {
                error!("原生轻量级并发测试失败: {}", e);
                results
                    .test_status
                    .insert("原生轻量级并发测试".to_string(), false);
            }
        }

        Ok(())
    }

    /// 运行集成测试
    async fn run_integration_tests(
        &self,
        results: &mut ComprehensiveTestResults,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let start = Instant::now();
        let test_suite = WebSocketConcurrentTestSuite::new(self.base_url.clone());

        match test_suite.run_light_concurrent_test().await {
            Ok(stats) => {
                let success = stats.success_rate >= 80.0;
                results.add_integration_test_result(
                    "集成轻量级并发测试".to_string(),
                    stats,
                    start.elapsed(),
                    success,
                );
            }
            Err(e) => {
                error!("集成轻量级并发测试失败: {}", e);
                results
                    .test_status
                    .insert("集成轻量级并发测试".to_string(), false);
            }
        }

        Ok(())
    }

    /// 运行浏览器E2E测试
    async fn run_browser_tests(
        &self,
        results: &mut ComprehensiveTestResults,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let start = Instant::now();
        let config = BrowserTestConfig {
            concurrent_browsers: 3,
            messages_per_browser: 10,
            test_duration_secs: 30,
            ..Default::default()
        };

        let tester = WebSocketBrowserConcurrentTester::new(
            self.base_url.clone(),
            config,
            self.test_credentials.clone(),
        );

        match tester.run_browser_concurrent_test().await {
            Ok(stats) => {
                let success =
                    stats.browser_success_rate >= 80.0 && stats.websocket_success_rate >= 80.0;
                results.add_browser_test_result(
                    "浏览器E2E并发测试".to_string(),
                    stats,
                    start.elapsed(),
                    success,
                );
            }
            Err(e) => {
                error!("浏览器E2E并发测试失败: {}", e);
                results
                    .test_status
                    .insert("浏览器E2E并发测试".to_string(), false);
            }
        }

        Ok(())
    }

    /// 运行Playwright测试
    async fn run_playwright_tests(
        &self,
        results: &mut ComprehensiveTestResults,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let start = Instant::now();
        let config = PlaywrightTestConfig {
            concurrent_pages: 3,
            messages_per_page: 10,
            test_duration_secs: 30,
            ..Default::default()
        };

        let tester = WebSocketPlaywrightTester::new(
            self.base_url.clone(),
            config,
            self.test_credentials.clone(),
        );

        match tester.run_playwright_concurrent_test().await {
            Ok(stats) => {
                let success =
                    stats.page_success_rate >= 90.0 && stats.websocket_success_rate >= 90.0;
                results.add_playwright_test_result(
                    "Playwright并发测试".to_string(),
                    stats,
                    start.elapsed(),
                    success,
                );
            }
            Err(e) => {
                error!("Playwright并发测试失败: {}", e);
                results
                    .test_status
                    .insert("Playwright并发测试".to_string(), false);
            }
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tracing_test::traced_test;

    const TEST_SERVER_URL: &str = "http://127.0.0.1:3000";
    const TEST_CREDENTIALS: (&str, &str) = ("testuser456", "password123");

    #[tokio::test]
    #[traced_test]
    async fn test_comprehensive_concurrent_websockets() {
        let runner = WebSocketConcurrentTestRunner::new(
            TEST_SERVER_URL.to_string(),
            (
                TEST_CREDENTIALS.0.to_string(),
                TEST_CREDENTIALS.1.to_string(),
            ),
        );

        match runner.run_all_concurrent_tests().await {
            Ok(results) => {
                results.print_comprehensive_report();

                // 验证总体成功率
                let overall_success_rate = results.calculate_overall_success_rate();
                assert!(
                    overall_success_rate >= 75.0,
                    "总体测试成功率应该至少75%，实际: {:.2}%",
                    overall_success_rate
                );

                info!("WebSocket并发测试套件执行成功");
            }
            Err(e) => {
                panic!("WebSocket并发测试套件执行失败: {}", e);
            }
        }
    }
}

/// 主函数 - 运行WebSocket并发测试套件
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    println!("🚀 启动WebSocket并发测试套件");

    // 创建测试运行器
    let base_url = "http://127.0.0.1:3000".to_string();
    let test_credentials = ("testuser456".to_string(), "password123".to_string());
    let runner = WebSocketConcurrentTestRunner::new(base_url, test_credentials);

    // 运行完整测试套件
    let _results = runner.run_all_concurrent_tests().await?;

    println!("✅ WebSocket并发测试套件执行完成");
    Ok(())
}
