//! # 任务删除功能E2E测试
//!
//! 本模块实现任务删除功能的端到端测试，遵循Context7 MCP最佳实践：
//! - 使用清晰的函数命名（test_delete_task_success、test_delete_task_unauthorized等）
//! - 详细的中文注释
//! - 完整的错误处理
//! - 遵循DRY和SOLID原则
//! - 遵循TDD（Test-Driven Development）开发模式
//! - 验证API响应格式、状态码、权限控制和删除后的数据一致性

use anyhow::{Context, Result};
use serde_json::{Value, json};
use std::time::Duration;
use tokio::time::sleep;

// 导入E2E测试辅助模块
#[path = "e2e/helpers/mod.rs"]
mod helpers;

use helpers::{
    AuthHelper, E2EConfig, TaskCrudHelper, TestServer, TestTaskData, ensure_dir_exists,
    test_server::TestServerConfig,
};

/// 任务删除E2E测试套件
pub struct TaskDeleteTestSuite {
    config: E2EConfig,
    test_server: TestServer,
    auth_helper: Auth<PERSON>elper,
    task_crud_helper: TaskCrudHelper,
    test_user_token: Option<String>,
    test_task_ids: Vec<String>,           // 存储测试任务ID用于删除测试
    secondary_user_token: Option<String>, // 用于权限测试的第二个用户
}

impl TaskDeleteTestSuite {
    /// 创建新的测试套件实例
    pub async fn new() -> Result<Self> {
        println!("🔧 初始化任务删除E2E测试套件...");

        // 加载配置
        let config = E2EConfig::from_env().context("无法加载E2E测试配置")?;

        // 创建测试服务器
        let server_config = TestServerConfig {
            host: config.server_host.clone(),
            port: config.server_port,
            startup_timeout: 60,
            health_check_interval: 1,
            project_root: std::env::current_dir()
                .context("无法获取当前目录")?
                .to_string_lossy()
                .to_string(),
        };

        let test_server = TestServer::new(server_config);

        // 创建辅助工具
        let auth_helper = AuthHelper::new(config.clone());
        let task_crud_helper = TaskCrudHelper::new(config.clone());

        Ok(Self {
            config,
            test_server,
            auth_helper,
            task_crud_helper,
            test_user_token: None,
            test_task_ids: Vec::new(),
            secondary_user_token: None,
        })
    }

    /// 设置基本测试环境（不包含认证和测试数据）
    pub async fn setup_basic_environment(&mut self) -> Result<()> {
        println!("🚀 设置基本测试环境...");

        // 1. 确保报告目录存在
        self.ensure_report_directories()?;

        // 2. 启动测试服务器
        self.start_test_server().await?;

        println!("✅ 基本测试环境设置完成");
        Ok(())
    }

    /// 设置测试环境
    pub async fn setup_test_environment(&mut self) -> Result<()> {
        println!("🚀 设置任务删除测试环境...");

        // 1. 设置基本环境
        self.setup_basic_environment().await?;

        // 2. 配置认证流程
        self.configure_authentication().await?;

        // 3. 创建测试数据
        self.create_test_data().await?;

        println!("✅ 任务删除测试环境设置完成");
        Ok(())
    }

    /// 确保报告目录存在
    fn ensure_report_directories(&self) -> Result<()> {
        println!("📁 创建测试报告目录...");

        ensure_dir_exists(&self.config.report_dir)?;
        ensure_dir_exists(&self.config.screenshot_dir)?;
        ensure_dir_exists(&self.config.video_dir)?;

        println!("✅ 测试报告目录创建完成");
        Ok(())
    }

    /// 启动测试服务器
    async fn start_test_server(&mut self) -> Result<()> {
        println!("🚀 启动测试服务器...");

        self.test_server
            .start()
            .await
            .context("无法启动测试服务器")?;

        // 等待服务器完全启动
        sleep(Duration::from_secs(3)).await;

        // 验证服务器健康状态
        let health_check = self.test_server.health_check().await?;
        println!("✅ 服务器健康检查通过: {:?}", health_check);

        Ok(())
    }

    /// 配置认证流程
    async fn configure_authentication(&mut self) -> Result<()> {
        println!("🔐 配置认证流程...");

        // 生成唯一的测试用户名，避免冲突
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_millis();
        let unique_username = format!("delete_test_user_{}", timestamp);
        let unique_email = format!("delete_test_user_{}@example.com", timestamp);
        let secondary_username = format!("delete_test_user2_{}", timestamp);
        let secondary_email = format!("delete_test_user2_{}@example.com", timestamp);

        println!("📝 使用唯一测试用户名: {}", unique_username);

        // 1. 注册主测试用户
        let register_result = self
            .auth_helper
            .register_user(&unique_username, &unique_email, &self.config.test_password)
            .await;

        match register_result {
            Ok(response) => {
                if response["status"].as_u64().unwrap_or(0) == 201 {
                    println!("✅ 主测试用户注册成功");
                } else {
                    return Err(anyhow::anyhow!("主用户注册失败: {:?}", response));
                }
            }
            Err(e) => {
                return Err(anyhow::anyhow!("主用户注册请求失败: {}", e));
            }
        }

        // 2. 注册第二个测试用户（用于权限测试）
        let secondary_register_result = self
            .auth_helper
            .register_user(
                &secondary_username,
                &secondary_email,
                &self.config.test_password,
            )
            .await;

        match secondary_register_result {
            Ok(response) => {
                if response["status"].as_u64().unwrap_or(0) == 201 {
                    println!("✅ 第二个测试用户注册成功");
                } else {
                    return Err(anyhow::anyhow!("第二个用户注册失败: {:?}", response));
                }
            }
            Err(e) => {
                return Err(anyhow::anyhow!("第二个用户注册请求失败: {}", e));
            }
        }

        // 3. 登录获取主用户认证令牌
        let token = self
            .auth_helper
            .get_auth_token(&unique_username, &self.config.test_password)
            .await?;

        self.test_user_token = Some(token.clone());

        // 4. 登录获取第二个用户认证令牌
        let secondary_token = self
            .auth_helper
            .get_auth_token(&secondary_username, &self.config.test_password)
            .await?;

        self.secondary_user_token = Some(secondary_token);

        // 5. 验证令牌有效性（通过调用需要认证的API端点）
        let test_result = self.task_crud_helper.fetch_tasks_list(&token).await;
        match test_result {
            Ok(response) => {
                if response["status"].as_u64().unwrap_or(0) == 200 {
                    println!("✅ 令牌验证通过（通过任务列表API测试）");
                } else {
                    return Err(anyhow::anyhow!(
                        "令牌验证失败，任务列表API返回: {:?}",
                        response
                    ));
                }
            }
            Err(e) => {
                return Err(anyhow::anyhow!("令牌验证失败，无法调用任务列表API: {}", e));
            }
        }

        println!("✅ 认证流程配置完成，令牌验证通过");
        Ok(())
    }

    /// 创建测试数据
    async fn create_test_data(&mut self) -> Result<()> {
        println!("📝 创建测试数据...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 创建多个测试任务用于删除测试
        let test_tasks = vec![
            TestTaskData::new("删除测试任务 #1")
                .with_description("第一个用于删除测试的任务")
                .with_priority("high"),
            TestTaskData::new("删除测试任务 #2")
                .with_description("第二个用于删除测试的任务")
                .with_priority("medium"),
            TestTaskData::new("删除测试任务 #3")
                .with_description("第三个用于删除测试的任务")
                .with_priority("low")
                .with_completed(true),
            TestTaskData::new("删除测试任务 #4")
                .with_description("第四个用于删除测试的任务")
                .with_priority("medium"),
        ];

        for test_task in test_tasks {
            let create_result = self.task_crud_helper.create_task(token, &test_task).await?;
            if create_result["status"].as_u64().unwrap_or(0) == 201 {
                if let Some(task_id) = create_result["body"]["data"]["id"].as_str() {
                    self.test_task_ids.push(task_id.to_string());
                }
            }
        }

        println!(
            "✅ 测试数据创建完成，创建了{}个测试任务",
            self.test_task_ids.len()
        );
        Ok(())
    }

    /// 测试任务删除成功场景
    pub async fn test_delete_task_success(&self) -> Result<()> {
        println!("🧪 测试任务删除成功场景...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 使用第一个测试任务ID
        let task_id = self
            .test_task_ids
            .first()
            .ok_or_else(|| anyhow::anyhow!("没有可用的测试任务ID"))?;

        // 首先验证任务存在
        let fetch_result = self
            .task_crud_helper
            .fetch_task_by_id(token, task_id)
            .await?;
        assert_eq!(
            fetch_result["status"].as_u64().unwrap_or(0),
            200,
            "任务应该存在"
        );

        // 执行任务删除
        let delete_result = self.task_crud_helper.delete_task(token, task_id).await?;

        // 验证删除响应状态码（通常是204 No Content或200 OK）
        let status = delete_result["status"].as_u64().unwrap_or(0);
        assert!(
            status == 204 || status == 200,
            "任务删除应返回204或200状态码，实际: {}",
            status
        );

        // 验证任务已被删除 - 尝试再次获取应返回404
        let verify_result = self.task_crud_helper.fetch_task_by_id(token, task_id).await;
        match verify_result {
            Ok(response) => {
                assert_eq!(
                    response["status"].as_u64().unwrap_or(0),
                    404,
                    "删除后的任务应返回404"
                );
            }
            Err(e) => {
                // 如果是网络错误，检查是否是404响应
                let error_msg = e.to_string();
                assert!(
                    error_msg.contains("404") || error_msg.contains("Not Found"),
                    "删除后的任务应返回404错误，实际错误: {}",
                    error_msg
                );
            }
        }

        println!("✅ 任务删除成功场景测试通过");
        Ok(())
    }

    /// 测试删除不存在的任务
    pub async fn test_delete_task_not_found(&self) -> Result<()> {
        println!("🧪 测试删除不存在的任务...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 使用不存在的任务ID（有效的UUID格式）
        let non_existent_id = "00000000-0000-0000-0000-000000000000";

        // 尝试删除不存在的任务
        let delete_result = self
            .task_crud_helper
            .delete_task(token, non_existent_id)
            .await;

        // 验证响应
        match delete_result {
            Ok(response) => {
                let status = response["status"].as_u64().unwrap_or(0);
                assert_eq!(status, 404, "删除不存在的任务应返回404状态码");
            }
            Err(e) => {
                // 如果是网络错误或JSON解析错误，检查是否是404响应
                let error_msg = e.to_string();
                assert!(
                    error_msg.contains("404") || error_msg.contains("Not Found"),
                    "应该是404错误，实际错误: {}",
                    error_msg
                );
            }
        }

        println!("✅ 删除不存在的任务测试通过");
        Ok(())
    }

    /// 测试未认证用户删除任务
    pub async fn test_delete_task_unauthorized(&self) -> Result<()> {
        println!("🧪 测试未认证用户删除任务...");

        // 使用无效令牌和一个固定的任务ID（不需要真实存在）
        let invalid_token = "invalid_token";
        let dummy_task_id = "00000000-0000-0000-0000-000000000000"; // 使用固定的UUID格式

        // 尝试删除任务
        let delete_result = self
            .task_crud_helper
            .delete_task(invalid_token, dummy_task_id)
            .await;

        // 验证响应 - 应该是认证错误
        match delete_result {
            Ok(response) => {
                let status = response["status"].as_u64().unwrap_or(0);
                assert_eq!(status, 401, "未认证用户应返回401状态码");
            }
            Err(e) => {
                // 如果是网络错误或JSON解析错误，检查是否是401响应
                let error_msg = e.to_string();
                assert!(
                    error_msg.contains("401") || error_msg.contains("Unauthorized"),
                    "应该是认证错误，实际错误: {}",
                    error_msg
                );
            }
        }

        println!("✅ 未认证用户删除任务测试通过");
        Ok(())
    }

    /// 测试权限控制 - 用户只能删除自己的任务
    pub async fn test_delete_task_permission_denied(&self) -> Result<()> {
        println!("🧪 测试权限控制 - 用户只能删除自己的任务...");

        let main_token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("主用户令牌未设置"))?;

        let secondary_token = self
            .secondary_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("第二个用户令牌未设置"))?;

        // 使用主用户创建一个任务
        let test_task =
            TestTaskData::new("权限测试任务").with_description("用于测试权限控制的任务");

        let create_result = self
            .task_crud_helper
            .create_task(main_token, &test_task)
            .await?;
        let task_id = create_result["body"]["data"]["id"]
            .as_str()
            .ok_or_else(|| anyhow::anyhow!("无法获取创建的任务ID"))?;

        // 使用第二个用户的令牌尝试删除第一个用户的任务
        let delete_result = self
            .task_crud_helper
            .delete_task(secondary_token, task_id)
            .await;

        // 验证响应 - 应该是权限错误或任务不存在错误
        match delete_result {
            Ok(response) => {
                let status = response["status"].as_u64().unwrap_or(0);
                // 可能返回404（任务不存在）或403（权限不足）
                assert!(
                    status == 404 || status == 403,
                    "尝试删除他人任务应返回404或403状态码，实际: {}",
                    status
                );
            }
            Err(e) => {
                // 如果是网络错误或JSON解析错误，检查是否是权限相关错误
                let error_msg = e.to_string();
                assert!(
                    error_msg.contains("404")
                        || error_msg.contains("403")
                        || error_msg.contains("Not Found")
                        || error_msg.contains("Forbidden"),
                    "应该是权限相关错误，实际错误: {}",
                    error_msg
                );
            }
        }

        // 清理：使用主用户删除任务
        let _ = self.task_crud_helper.delete_task(main_token, task_id).await;

        println!("✅ 权限控制测试通过");
        Ok(())
    }

    /// 测试任务删除响应时间性能
    pub async fn test_delete_task_performance(&self) -> Result<()> {
        println!("🧪 测试任务删除响应时间性能...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 创建一个新的测试任务用于性能测试
        let test_task =
            TestTaskData::new("性能测试任务").with_description("用于测试删除性能的任务");

        let create_result = self.task_crud_helper.create_task(token, &test_task).await?;
        let task_id = create_result["body"]["data"]["id"]
            .as_str()
            .ok_or_else(|| anyhow::anyhow!("无法获取创建的任务ID"))?;

        // 测试删除性能
        let start_time = std::time::Instant::now();
        let delete_result = self.task_crud_helper.delete_task(token, task_id).await?;
        let response_time = start_time.elapsed();

        // 验证删除成功
        let status = delete_result["status"].as_u64().unwrap_or(0);
        assert!(status == 204 || status == 200, "删除应该成功");

        // 验证响应时间（应小于2000ms）- 考虑到测试环境的性能限制
        assert!(
            response_time.as_millis() < 2000,
            "任务删除响应时间应小于2000ms，实际: {}ms",
            response_time.as_millis()
        );

        println!("📊 任务删除响应时间: {}ms", response_time.as_millis());
        println!("✅ 任务删除响应时间性能测试通过");
        Ok(())
    }

    /// 测试重复删除同一任务
    pub async fn test_delete_task_idempotency(&self) -> Result<()> {
        println!("🧪 测试重复删除同一任务...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 创建一个新的测试任务用于此测试
        let test_task =
            TestTaskData::new("重复删除测试任务").with_description("用于测试重复删除的任务");

        let create_result = self.task_crud_helper.create_task(token, &test_task).await?;
        let task_id = create_result["body"]["data"]["id"]
            .as_str()
            .ok_or_else(|| anyhow::anyhow!("无法获取创建的任务ID"))?;

        // 第一次删除
        let first_delete_result = self.task_crud_helper.delete_task(token, task_id).await?;
        let first_status = first_delete_result["status"].as_u64().unwrap_or(0);
        assert!(
            first_status == 204 || first_status == 200,
            "第一次删除应该成功"
        );

        // 第二次删除同一任务
        let second_delete_result = self.task_crud_helper.delete_task(token, task_id).await;

        // 验证第二次删除的响应
        match second_delete_result {
            Ok(response) => {
                let status = response["status"].as_u64().unwrap_or(0);
                // 第二次删除应返回404（任务不存在）
                assert_eq!(status, 404, "第二次删除应返回404状态码");
            }
            Err(e) => {
                // 如果是网络错误，检查是否是404响应
                let error_msg = e.to_string();
                assert!(
                    error_msg.contains("404") || error_msg.contains("Not Found"),
                    "第二次删除应该是404错误，实际错误: {}",
                    error_msg
                );
            }
        }

        println!("✅ 重复删除同一任务测试通过");
        Ok(())
    }

    /// 测试删除已完成的任务
    pub async fn test_delete_completed_task(&self) -> Result<()> {
        println!("🧪 测试删除已完成的任务...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 创建一个普通任务
        let task_data =
            TestTaskData::new("删除测试任务").with_description("用于测试删除已完成任务的功能");

        let create_result = self.task_crud_helper.create_task(token, &task_data).await?;
        let task_id = create_result["body"]["data"]["id"]
            .as_str()
            .ok_or_else(|| anyhow::anyhow!("无法获取创建的任务ID"))?;

        // 更新任务状态为已完成
        let update_data = TestTaskData::new("").with_status("completed");
        let update_result = self
            .task_crud_helper
            .update_task(token, task_id, &update_data)
            .await?;
        assert_eq!(
            update_result["status"].as_u64().unwrap_or(0),
            200,
            "任务更新应该成功"
        );

        // 验证任务确实是已完成状态
        let fetch_result = self
            .task_crud_helper
            .fetch_task_by_id(token, task_id)
            .await?;
        let task_status = fetch_result["body"]["data"]["status"]
            .as_str()
            .unwrap_or("pending");
        assert_eq!(task_status, "completed", "任务状态应该是completed");

        // 删除已完成的任务
        let delete_result = self.task_crud_helper.delete_task(token, task_id).await?;

        // 验证删除成功
        let status = delete_result["status"].as_u64().unwrap_or(0);
        assert!(
            status == 204 || status == 200,
            "删除已完成任务应该成功，状态码: {}",
            status
        );

        // 验证任务已被删除
        let verify_result = self.task_crud_helper.fetch_task_by_id(token, task_id).await;
        match verify_result {
            Ok(response) => {
                assert_eq!(
                    response["status"].as_u64().unwrap_or(0),
                    404,
                    "删除后的任务应返回404"
                );
            }
            Err(e) => {
                let error_msg = e.to_string();
                assert!(
                    error_msg.contains("404") || error_msg.contains("Not Found"),
                    "删除后的任务应返回404错误，实际错误: {}",
                    error_msg
                );
            }
        }

        println!("✅ 删除已完成的任务测试通过");
        Ok(())
    }

    /// 测试删除操作对任务列表的影响
    pub async fn test_delete_task_list_consistency(&self) -> Result<()> {
        println!("🧪 测试删除操作对任务列表的影响...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 获取删除前的任务列表
        let before_list = self.task_crud_helper.fetch_tasks_list(token).await?;
        let before_count = before_list["body"]["data"]
            .as_array()
            .map(|arr| arr.len())
            .unwrap_or(0);

        // 创建一个新任务用于删除测试
        let test_task = TestTaskData::new("列表一致性测试任务")
            .with_description("用于测试删除对列表影响的任务");

        let create_result = self.task_crud_helper.create_task(token, &test_task).await?;
        let task_id = create_result["body"]["data"]["id"]
            .as_str()
            .ok_or_else(|| anyhow::anyhow!("无法获取创建的任务ID"))?;

        // 验证任务已添加到列表
        let after_create_list = self.task_crud_helper.fetch_tasks_list(token).await?;
        let after_create_count = after_create_list["body"]["data"]
            .as_array()
            .map(|arr| arr.len())
            .unwrap_or(0);

        assert_eq!(
            after_create_count,
            before_count + 1,
            "创建任务后列表数量应增加1"
        );

        // 删除任务
        let delete_result = self.task_crud_helper.delete_task(token, task_id).await?;
        assert!(
            delete_result["status"].as_u64().unwrap_or(0) == 204
                || delete_result["status"].as_u64().unwrap_or(0) == 200
        );

        // 验证任务已从列表中移除
        let after_delete_list = self.task_crud_helper.fetch_tasks_list(token).await?;
        let after_delete_count = after_delete_list["body"]["data"]
            .as_array()
            .map(|arr| arr.len())
            .unwrap_or(0);

        assert_eq!(
            after_delete_count, before_count,
            "删除任务后列表数量应恢复原状"
        );

        // 验证删除的任务不在列表中
        let tasks = after_delete_list["body"]["data"].as_array().unwrap();
        let found_deleted_task = tasks
            .iter()
            .any(|task| task["id"].as_str() == Some(task_id));

        assert!(!found_deleted_task, "删除的任务不应出现在任务列表中");

        println!("✅ 删除操作对任务列表的影响测试通过");
        Ok(())
    }

    /// 清理测试环境
    pub async fn cleanup(&mut self) -> Result<()> {
        println!("🧹 清理测试环境...");

        // 清理剩余的测试任务
        if let Some(token) = &self.test_user_token {
            for task_id in &self.test_task_ids {
                let _ = self.task_crud_helper.delete_task(token, task_id).await;
            }
        }

        // 停止测试服务器
        self.test_server.stop()?;

        println!("✅ 测试环境清理完成");
        Ok(())
    }
}

impl Drop for TaskDeleteTestSuite {
    fn drop(&mut self) {
        // 确保服务器被停止
        let _ = self.test_server.stop();
    }
}

/// 任务删除成功测试
#[tokio::test]
async fn test_delete_task_success() -> Result<()> {
    let mut test_suite = TaskDeleteTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_delete_task_success().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 删除不存在的任务测试
#[tokio::test]
async fn test_delete_task_not_found() -> Result<()> {
    let mut test_suite = TaskDeleteTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_delete_task_not_found().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 未认证用户删除任务测试
#[tokio::test]
async fn test_delete_task_unauthorized() -> Result<()> {
    let mut test_suite = TaskDeleteTestSuite::new().await?;
    // 只需要设置基本的服务器连接，不需要认证和测试数据
    test_suite.setup_basic_environment().await?;

    test_suite.test_delete_task_unauthorized().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 权限控制测试
#[tokio::test]
async fn test_delete_task_permission_denied() -> Result<()> {
    let mut test_suite = TaskDeleteTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_delete_task_permission_denied().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 任务删除性能测试
#[tokio::test]
async fn test_delete_task_performance() -> Result<()> {
    let mut test_suite = TaskDeleteTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_delete_task_performance().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 重复删除同一任务测试
#[tokio::test]
async fn test_delete_task_idempotency() -> Result<()> {
    let mut test_suite = TaskDeleteTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_delete_task_idempotency().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 删除已完成的任务测试
#[tokio::test]
async fn test_delete_completed_task() -> Result<()> {
    let mut test_suite = TaskDeleteTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_delete_completed_task().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 删除操作对任务列表的影响测试
#[tokio::test]
async fn test_delete_task_list_consistency() -> Result<()> {
    let mut test_suite = TaskDeleteTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_delete_task_list_consistency().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 主测试函数 - 运行所有任务删除测试
#[tokio::main]
async fn main() -> Result<()> {
    // 设置日志
    tracing_subscriber::fmt::init();

    println!("🚀 启动任务删除功能E2E测试套件");

    let mut test_suite = TaskDeleteTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    println!("📋 开始执行任务删除测试用例...");

    // 1. 基础功能测试
    println!("1️⃣ 执行任务删除成功测试...");
    test_suite.test_delete_task_success().await?;

    // 2. 错误场景测试
    println!("2️⃣ 执行删除不存在任务测试...");
    test_suite.test_delete_task_not_found().await?;

    // 3. 未认证用户测试
    println!("3️⃣ 执行未认证用户删除任务测试...");
    test_suite.test_delete_task_unauthorized().await?;

    // 4. 权限控制测试
    println!("4️⃣ 执行权限控制测试...");
    test_suite.test_delete_task_permission_denied().await?;

    // 5. 性能测试
    println!("5️⃣ 执行任务删除响应时间性能测试...");
    test_suite.test_delete_task_performance().await?;

    // 6. 重复删除测试
    println!("6️⃣ 执行重复删除同一任务测试...");
    test_suite.test_delete_task_idempotency().await?;

    // 7. 删除已完成任务测试
    println!("7️⃣ 执行删除已完成任务测试...");
    test_suite.test_delete_completed_task().await?;

    // 8. 列表一致性测试
    println!("8️⃣ 执行删除操作对任务列表的影响测试...");
    test_suite.test_delete_task_list_consistency().await?;

    test_suite.cleanup().await?;

    println!("✅ 所有任务删除E2E测试完成");
    Ok(())
}
