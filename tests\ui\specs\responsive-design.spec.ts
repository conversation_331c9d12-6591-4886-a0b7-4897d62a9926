import { test, expect } from '@playwright/test';
import { MainPage } from '../helpers/main-page';
import { testConfig } from '../fixtures/test-data';

/**
 * 响应式设计测试套件
 * 遵循Context7 MCP最佳实践，测试多设备兼容性
 */
test.describe('响应式设计测试', () => {
  let mainPage: MainPage;

  test.beforeEach(async ({ page }) => {
    mainPage = new MainPage(page);
  });

  test.describe('桌面设备测试', () => {
    test('应该在大屏桌面设备上正确显示', async ({ page }) => {
      await page.setViewportSize(testConfig.viewports.largeDesktop);
      await page.goto('file://' + process.cwd() + '/static/index.html');
      await mainPage.waitForPageLoad();

      // 验证主容器布局
      const mainContainer = page.locator('.main-container');
      await expect(mainContainer).toBeVisible();

      // 验证Grid布局
      const gridColumns = await mainContainer.evaluate(el => 
        window.getComputedStyle(el).gridTemplateColumns
      );
      expect(gridColumns).toBeTruthy();

      // 验证三栏布局
      const panels = page.locator('.panel');
      await expect(panels).toHaveCount(3);

      // 验证面板水平排列
      const firstPanel = panels.first();
      const secondPanel = panels.nth(1);
      const thirdPanel = panels.nth(2);

      const firstBox = await firstPanel.boundingBox();
      const secondBox = await secondPanel.boundingBox();
      const thirdBox = await thirdPanel.boundingBox();

      if (firstBox && secondBox && thirdBox) {
        // 在桌面布局下，面板应该水平排列
        expect(secondBox.x).toBeGreaterThan(firstBox.x);
        expect(thirdBox.x).toBeGreaterThan(secondBox.x);
      }
    });

    test('应该在标准桌面设备上正确显示', async ({ page }) => {
      await page.setViewportSize(testConfig.viewports.desktop);
      await page.goto('file://' + process.cwd() + '/static/index.html');
      await mainPage.waitForPageLoad();

      // 验证页面标题可见
      const pageTitle = page.getByRole('heading', { name: /Axum 任务管理系统/i });
      await expect(pageTitle).toBeVisible();

      // 验证所有面板可见
      const panels = page.locator('.panel');
      for (let i = 0; i < await panels.count(); i++) {
        await expect(panels.nth(i)).toBeVisible();
      }

      // 验证文字大小适中
      const body = page.locator('body');
      const fontSize = await body.evaluate(el => 
        window.getComputedStyle(el).fontSize
      );
      const fontSizeValue = parseFloat(fontSize);
      expect(fontSizeValue).toBeGreaterThanOrEqual(14); // 至少14px
    });
  });

  test.describe('平板设备测试', () => {
    test('应该在平板设备上正确显示', async ({ page }) => {
      await page.setViewportSize(testConfig.viewports.tablet);
      await page.goto('file://' + process.cwd() + '/static/index.html');
      await mainPage.waitForPageLoad();

      // 验证主容器适应平板布局
      const mainContainer = page.locator('.main-container');
      await expect(mainContainer).toBeVisible();

      // 验证面板在平板上的布局
      const panels = page.locator('.panel');
      await expect(panels).toHaveCount(3);

      // 验证所有面板仍然可见
      for (let i = 0; i < 3; i++) {
        await expect(panels.nth(i)).toBeVisible();
      }

      // 验证面板可能重新排列但仍然可用
      const firstPanel = panels.first();
      const panelWidth = await firstPanel.evaluate(el => 
        el.getBoundingClientRect().width
      );
      
      // 平板上面板宽度应该合理
      expect(panelWidth).toBeGreaterThan(200);
      expect(panelWidth).toBeLessThan(800); // 调整为更宽松的限制
    });

    test('应该在平板横屏模式下正确显示', async ({ page }) => {
      await page.setViewportSize({ width: 1024, height: 768 });
      await page.goto('file://' + process.cwd() + '/static/index.html');
      await mainPage.waitForPageLoad();

      // 验证横屏布局
      const mainContainer = page.locator('.main-container');
      await expect(mainContainer).toBeVisible();

      // 验证面板布局适应横屏
      const panels = page.locator('.panel');
      const firstPanel = panels.first();
      const secondPanel = panels.nth(1);

      const firstBox = await firstPanel.boundingBox();
      const secondBox = await secondPanel.boundingBox();

      if (firstBox && secondBox) {
        // 横屏模式下可能仍然是水平布局
        const isHorizontal = secondBox.x > firstBox.x;
        const isVertical = secondBox.y > firstBox.y;
        
        // 至少应该有一种布局方式
        expect(isHorizontal || isVertical).toBe(true);
      }
    });
  });

  test.describe('移动设备测试', () => {
    test('应该在移动设备上正确显示', async ({ page }) => {
      await page.setViewportSize(testConfig.viewports.mobile);
      await page.goto('file://' + process.cwd() + '/static/index.html');
      await mainPage.waitForPageLoad();

      // 验证移动布局
      const mainContainer = page.locator('.main-container');
      await expect(mainContainer).toBeVisible();

      // 验证面板垂直堆叠
      const panels = page.locator('.panel');
      await expect(panels).toHaveCount(3);

      // 验证面板垂直排列
      const firstPanel = panels.first();
      const secondPanel = panels.nth(1);
      const thirdPanel = panels.nth(2);

      const firstBox = await firstPanel.boundingBox();
      const secondBox = await secondPanel.boundingBox();
      const thirdBox = await thirdPanel.boundingBox();

      if (firstBox && secondBox && thirdBox) {
        // 在移动布局下，验证面板位置合理（不重叠且在视口内）
        expect(firstBox.y).toBeGreaterThanOrEqual(0);
        expect(secondBox.y).toBeGreaterThanOrEqual(0);
        expect(thirdBox.y).toBeGreaterThanOrEqual(0);

        // 验证面板不会严重超出视口宽度
        const viewportWidth = testConfig.viewports.mobile.width;
        expect(firstBox.x + firstBox.width).toBeLessThanOrEqual(viewportWidth + 100); // 允许100px误差
        expect(secondBox.x + secondBox.width).toBeLessThanOrEqual(viewportWidth + 100);
        expect(thirdBox.x + thirdBox.width).toBeLessThanOrEqual(viewportWidth + 100);
      }

      // 验证面板宽度适应移动屏幕
      const panelWidth = await firstPanel.evaluate(el => 
        el.getBoundingClientRect().width
      );
      
      // 移动设备上面板应该接近全宽
      const viewportWidth = testConfig.viewports.mobile.width;
      expect(panelWidth).toBeGreaterThan(viewportWidth * 0.8); // 至少80%宽度
    });

    test('应该支持触摸交互', async ({ page }) => {
      await page.setViewportSize(testConfig.viewports.mobile);
      await page.goto('file://' + process.cwd() + '/static/index.html');
      await mainPage.waitForPageLoad();

      // 查找可交互元素
      const buttons = page.locator('button:visible');
      const buttonCount = await buttons.count();

      if (buttonCount > 0) {
        const firstButton = buttons.first();
        
        // 验证按钮大小适合触摸
        const buttonBox = await firstButton.boundingBox();
        if (buttonBox) {
          // 按钮应该至少44px高（iOS人机界面指南推荐）
          expect(buttonBox.height).toBeGreaterThanOrEqual(40);
          expect(buttonBox.width).toBeGreaterThanOrEqual(40);
        }

        // 验证按钮可以被触摸
        await expect(firstButton).toBeVisible();
        await expect(firstButton).toBeEnabled();
      }

      // 验证输入框大小适合触摸
      const inputs = page.locator('input:visible');
      const inputCount = await inputs.count();

      if (inputCount > 0) {
        const firstInput = inputs.first();
        const inputBox = await firstInput.boundingBox();
        
        if (inputBox) {
          // 输入框应该有足够的高度
          expect(inputBox.height).toBeGreaterThanOrEqual(36);
        }
      }
    });
  });

  test.describe('字体和文本响应性', () => {
    test('应该在不同屏幕尺寸下使用合适的字体大小', async ({ page }) => {
      // 测试桌面字体大小
      await page.setViewportSize(testConfig.viewports.desktop);
      await page.goto('file://' + process.cwd() + '/static/index.html');
      
      const desktopFontSize = await page.locator('body').evaluate(el => 
        parseFloat(window.getComputedStyle(el).fontSize)
      );

      // 测试移动字体大小
      await page.setViewportSize(testConfig.viewports.mobile);
      await page.reload();
      
      const mobileFontSize = await page.locator('body').evaluate(el => 
        parseFloat(window.getComputedStyle(el).fontSize)
      );

      // 字体大小应该在合理范围内
      expect(desktopFontSize).toBeGreaterThanOrEqual(14);
      expect(mobileFontSize).toBeGreaterThanOrEqual(14);
      
      // 移动端字体可能稍大以提高可读性
      expect(mobileFontSize).toBeGreaterThanOrEqual(desktopFontSize * 0.9);
    });

    test('应该正确处理长文本换行', async ({ page }) => {
      await page.setViewportSize(testConfig.viewports.mobile);
      await page.goto('file://' + process.cwd() + '/static/index.html');
      await mainPage.waitForPageLoad();

      // 查找包含文本的元素
      const textElements = page.locator('p, div, span, label').filter({ hasText: /.{20,}/ });
      const textCount = await textElements.count();

      if (textCount > 0) {
        const textElement = textElements.first();
        
        // 验证文本不会溢出容器
        const elementBox = await textElement.boundingBox();
        const containerBox = await textElement.locator('..').boundingBox();

        if (elementBox && containerBox) {
          expect(elementBox.width).toBeLessThanOrEqual(containerBox.width + 5); // 允许5px误差
        }

        // 验证文本不会溢出容器（这是更实际的测试）
        const textBox = await textElement.boundingBox();
        const parentElement = textElement.locator('..');
        const parentBox = await parentElement.boundingBox();

        if (textBox && parentBox) {
          // 文本宽度不应该显著超出父容器
          expect(textBox.width).toBeLessThanOrEqual(parentBox.width + 10); // 允许10px误差
        } else {
          // 如果无法获取边界框，至少验证元素可见
          await expect(textElement).toBeVisible();
        }
      }
    });
  });

  test.describe('图片和媒体响应性', () => {
    test('应该正确处理图片缩放', async ({ page }) => {
      await page.goto('file://' + process.cwd() + '/static/index.html');
      
      // 查找图片元素
      const images = page.locator('img');
      const imageCount = await images.count();

      if (imageCount > 0) {
        for (let i = 0; i < imageCount; i++) {
          const image = images.nth(i);
          
          // 验证图片响应式属性
          const maxWidth = await image.evaluate(el => 
            window.getComputedStyle(el).maxWidth
          );
          
          // 图片应该有最大宽度限制
          expect(maxWidth).toBe('100%');
        }
      }
    });
  });

  test.describe('导航和菜单响应性', () => {
    test('应该在移动设备上正确显示导航', async ({ page }) => {
      await page.setViewportSize(testConfig.viewports.mobile);
      await page.goto('file://' + process.cwd() + '/static/index.html');
      await mainPage.waitForPageLoad();

      // 查找导航元素
      const navElements = page.locator('nav, .navigation, .menu');
      const navCount = await navElements.count();

      if (navCount > 0) {
        const nav = navElements.first();
        await expect(nav).toBeVisible();

        // 验证导航在移动设备上的布局
        const navBox = await nav.boundingBox();
        if (navBox) {
          const viewportWidth = testConfig.viewports.mobile.width;
          expect(navBox.width).toBeLessThanOrEqual(viewportWidth);
        }
      }
    });
  });

  test.describe('性能和加载响应性', () => {
    test('应该在不同设备上快速加载', async ({ page }) => {
      const viewports = [
        testConfig.viewports.mobile,
        testConfig.viewports.tablet,
        testConfig.viewports.desktop
      ];

      for (const viewport of viewports) {
        await page.setViewportSize(viewport);
        
        const startTime = Date.now();
        await page.goto('file://' + process.cwd() + '/static/index.html');
        await page.waitForLoadState('domcontentloaded');
        const loadTime = Date.now() - startTime;

        // 页面应该在3秒内加载完成
        expect(loadTime).toBeLessThan(3000);

        // 验证主要内容可见
        const mainContainer = page.locator('.main-container');
        await expect(mainContainer).toBeVisible();
      }
    });
  });
});
