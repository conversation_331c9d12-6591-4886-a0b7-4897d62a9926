//! 边缘情况测试集成测试
//!
//! 验证边缘情况测试模块的功能

use std::time::Duration;

mod edge_case_tests;
use edge_case_tests::*;

#[tokio::test]
async fn test_edge_test_config_creation() {
    let config = EdgeTestConfig::default();
    assert_eq!(config.base_url, "http://127.0.0.1:3000");
    assert_eq!(config.test_user, "testuser456");
    assert_eq!(config.test_password, "password123");
    assert_eq!(config.timeout, Duration::from_secs(30));
    assert_eq!(config.max_retries, 3);
}

#[tokio::test]
async fn test_test_stats_success_rate() {
    let mut stats = TestStats::default();
    stats.total_requests = 100;
    stats.successful_requests = 80;
    stats.failed_requests = 20;

    assert_eq!(stats.success_rate(), 80.0);
}

#[tokio::test]
async fn test_test_stats_zero_requests() {
    let stats = TestStats::default();
    assert_eq!(stats.success_rate(), 0.0);
}

#[tokio::test]
async fn test_network_interruption_test_creation() {
    let config = EdgeTestConfig::default();
    let test = NetworkInterruptionTest::new(config);

    // 测试对象创建成功
    // 注意：这里不能直接测试网络功能，因为需要服务器运行
}

#[tokio::test]
async fn test_large_data_test_creation() {
    let config = EdgeTestConfig::default();
    let test = LargeDataTest::new(config);

    // 测试对象创建成功
}

#[tokio::test]
async fn test_concurrency_stress_test_creation() {
    let config = EdgeTestConfig::default();
    let test = ConcurrencyStressTest::new(config);

    // 测试对象创建成功
}

// 注意：以下测试需要服务器运行在127.0.0.1:3000才能执行
// 在CI/CD环境中，这些测试应该被标记为集成测试

#[tokio::test]
#[ignore] // 需要服务器运行
async fn test_network_interruption_with_server() {
    let config = EdgeTestConfig::default();
    let test = NetworkInterruptionTest::new(config);

    // 只有在服务器运行时才执行此测试
    match test.run_network_interruption_test().await {
        Ok(stats) => {
            println!("网络中断测试完成，成功率: {:.2}%", stats.success_rate());
            assert!(stats.total_requests > 0);
        }
        Err(e) => {
            println!("网络中断测试失败（预期，因为服务器可能未运行）: {}", e);
        }
    }
}

#[tokio::test]
#[ignore] // 需要服务器运行
async fn test_large_data_with_server() {
    let config = EdgeTestConfig::default();
    let test = LargeDataTest::new(config);

    match test.run_large_data_test().await {
        Ok(stats) => {
            println!("大数据量测试完成，成功率: {:.2}%", stats.success_rate());
            assert!(stats.total_requests > 0);
        }
        Err(e) => {
            println!("大数据量测试失败（预期，因为服务器可能未运行）: {}", e);
        }
    }
}

#[tokio::test]
#[ignore] // 需要服务器运行
async fn test_concurrency_stress_with_server() {
    let config = EdgeTestConfig::default();
    let test = ConcurrencyStressTest::new(config);

    match test.run_concurrency_stress_test().await {
        Ok(stats) => {
            println!("高并发测试完成，成功率: {:.2}%", stats.success_rate());
            assert!(stats.total_requests > 0);
        }
        Err(e) => {
            println!("高并发测试失败（预期，因为服务器可能未运行）: {}", e);
        }
    }
}

// 性能基准测试
#[tokio::test]
async fn test_stats_calculation_performance() {
    let mut stats = TestStats::default();

    // 模拟大量请求数据
    stats.total_requests = 1000000;
    stats.successful_requests = 950000;
    stats.failed_requests = 50000;

    let start = std::time::Instant::now();
    let success_rate = stats.success_rate();
    let duration = start.elapsed();

    assert_eq!(success_rate, 95.0);
    assert!(duration < Duration::from_millis(1)); // 应该非常快
}

// 边界条件测试
#[tokio::test]
async fn test_edge_cases_boundary_conditions() {
    // 测试极大数值
    let mut stats = TestStats::default();
    stats.total_requests = usize::MAX;
    stats.successful_requests = usize::MAX - 1;
    stats.failed_requests = 1;

    let success_rate = stats.success_rate();
    assert!(success_rate > 99.0);

    // 测试零值
    let mut zero_stats = TestStats::default();
    zero_stats.total_requests = 0;
    zero_stats.successful_requests = 0;
    zero_stats.failed_requests = 0;

    assert_eq!(zero_stats.success_rate(), 0.0);
}

// 配置验证测试
#[tokio::test]
async fn test_config_validation() {
    let config = EdgeTestConfig {
        base_url: "http://localhost:8080".to_string(),
        test_user: "custom_user".to_string(),
        test_password: "custom_pass".to_string(),
        timeout: Duration::from_secs(60),
        max_retries: 5,
    };

    assert_eq!(config.base_url, "http://localhost:8080");
    assert_eq!(config.test_user, "custom_user");
    assert_eq!(config.test_password, "custom_pass");
    assert_eq!(config.timeout, Duration::from_secs(60));
    assert_eq!(config.max_retries, 5);
}

// 并发安全测试
#[tokio::test]
async fn test_concurrent_stats_updates() {
    use std::sync::Arc;
    use std::sync::atomic::{AtomicUsize, Ordering};
    use tokio::task::JoinSet;

    let successful_count = Arc::new(AtomicUsize::new(0));
    let failed_count = Arc::new(AtomicUsize::new(0));

    let mut join_set = JoinSet::new();

    // 启动多个并发任务来更新计数器
    for _ in 0..100 {
        let successful = successful_count.clone();
        let failed = failed_count.clone();

        join_set.spawn(async move {
            for _ in 0..10 {
                successful.fetch_add(1, Ordering::Relaxed);
                failed.fetch_add(1, Ordering::Relaxed);
            }
        });
    }

    // 等待所有任务完成
    while let Some(_) = join_set.join_next().await {}

    let final_successful = successful_count.load(Ordering::Relaxed);
    let final_failed = failed_count.load(Ordering::Relaxed);

    assert_eq!(final_successful, 1000); // 100 tasks * 10 increments
    assert_eq!(final_failed, 1000);
}

// 错误处理测试
#[tokio::test]
async fn test_error_handling() {
    let config = EdgeTestConfig {
        base_url: "http://invalid-url-that-does-not-exist:9999".to_string(),
        ..Default::default()
    };

    let test = NetworkInterruptionTest::new(config);

    // 这应该失败，因为URL无效
    match test.run_network_interruption_test().await {
        Ok(_) => panic!("测试应该失败，因为URL无效"),
        Err(e) => {
            println!("预期的错误: {}", e);
            // 验证错误处理正常工作
        }
    }
}
