# Axum企业级项目 - PostgreSQL 17 + DragonflyDB 容器配置
# 适用于Podman容器运行时环境
# 支持百万吞吐量百万并发的企业级移动手机聊天室应用后端
# 基于Context7 MCP最佳实践 - 2025年7月最新配置

version: '3.8'

services:
  # PostgreSQL 17 主数据库服务
  postgres:
    image: docker.io/library/postgres:17-alpine
    container_name: axum_postgres_17
    restart: unless-stopped
    
    # 环境变量配置
    environment:
      # 数据库基本配置
      POSTGRES_DB: axum_tutorial
      POSTGRES_USER: axum_user
      POSTGRES_PASSWORD: axum_secure_password_2025
      
      # 性能优化配置
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
      
      # 连接配置
      POSTGRES_MAX_CONNECTIONS: 200
      
    # 端口映射
    ports:
      - "5432:5432"
    
    # 数据持久化
    volumes:
      - postgres_17_data:/var/lib/postgresql/data
      - ./config/postgresql.conf:/etc/postgresql/postgresql.conf:ro
      - ./config/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    
    # 启动命令 - 使用自定义配置
    command: >
      postgres
      -c config_file=/etc/postgresql/postgresql.conf
      -c hba_file=/etc/postgresql/pg_hba.conf
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB
      -c max_worker_processes=8
      -c max_parallel_workers_per_gather=2
      -c max_parallel_workers=8
      -c max_parallel_maintenance_workers=2
    
    # 健康检查
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U axum_user -d axum_tutorial"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'
    
    # 网络配置
    networks:
      - axum_network

  # DragonflyDB 高性能缓存服务
  dragonflydb:
    image: docker.dragonflydb.io/dragonflydb/dragonfly:latest
    container_name: axum_dragonflydb
    restart: unless-stopped

    # 环境变量配置 (保留支持的基础配置)
    environment:
      # 基础配置 (通过命令行参数设置，这里保留用于文档)
      DFLY_port: 6379
      DFLY_bind: "0.0.0.0"
      DFLY_maxmemory: "2gb"
      DFLY_cache_mode: "true"
      DFLY_cluster_mode: "emulated"
      DFLY_maxclients: "100000"
      DFLY_tcp_nodelay: "true"
      DFLY_tcp_keepalive: "300"
      DFLY_proactor_threads: "0"
      DFLY_conn_io_threads: "0"
      DFLY_num_shards: "0"
      DFLY_enable_heartbeat_eviction: "true"
      DFLY_max_eviction_per_heartbeat: "200"
      DFLY_max_segment_to_consider: "8"
      DFLY_mem_defrag_threshold: "0.7"
      DFLY_mem_defrag_waste_threshold: "0.2"
      DFLY_pipeline_squash: "20"
      DFLY_migrate_connections: "true"
      DFLY_list_compress_depth: "2"
      DFLY_requirepass: "dragonfly_secure_password_2025"
      DFLY_dbnum: "16"
      DFLY_lua_auto_async: "true"
      DFLY_interpreter_per_thread: "20"
      DFLY_df_snapshot_format: "true"
      DFLY_dbfilename: "dump-dragonflydb"
      DFLY_compression_mode: "3"
      DFLY_compression_level: "2"
      DFLY_slowlog_log_slower_than: "10000"
      DFLY_slowlog_max_len: "100"
      DFLY_logtostderr: "true"
      DFLY_minloglevel: "0"

    # 端口映射 (主端口和管理端口)
    ports:
      - "6379:6379"
      - "6380:6380"

    # 启动命令 - 使用支持的命令行参数
    command: >
      dragonfly
      --logtostderr
      --requirepass=dragonfly_secure_password_2025
      --cache_mode=true
      --dbnum=16
      --bind=0.0.0.0
      --port=6379
      --maxmemory=2gb
      --maxclients=100000
      --tcp_nodelay=true
      --tcp_keepalive=300
      --mem_defrag_threshold=0.7
      --enable_heartbeat_eviction=true
      --max_eviction_per_heartbeat=200
      --cluster_mode=emulated
      --migrate_connections=true
      --pipeline_squash=20
      --list_compress_depth=2
      --df_snapshot_format=true
      --dbfilename=dump-dragonflydb
      --dir=/data
      --compression_mode=3
      --compression_level=2
      --slowlog_log_slower_than=10000
      --slowlog_max_len=100
      --hz=100
      --keys_output_limit=10000
      --interpreter_per_thread=20
      --lua_auto_async=true
      --multi_exec_squash=true
      --admin_port=6380
      --admin_bind=0.0.0.0
      --minloglevel=0

    # 数据持久化挂载 (移除配置文件挂载)
    volumes:
      - dragonflydb_data:/data

    # 健康检查 (使用密码认证)
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "dragonfly_secure_password_2025", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

    # 资源限制 (为百万并发优化)
    deploy:
      resources:
        limits:
          memory: 3G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'

    # 网络配置
    networks:
      - axum_network

  # Redis Exporter - DragonflyDB监控
  redis-exporter:
    image: docker.io/oliver006/redis_exporter:latest
    container_name: axum_redis_exporter
    restart: unless-stopped

    # 环境变量配置
    environment:
      # DragonflyDB连接配置 (使用密码认证)
      REDIS_ADDR: "redis://:dragonfly_secure_password_2025@dragonflydb:6379"

      # 导出器配置
      REDIS_EXPORTER_LOG_FORMAT: "txt"
      REDIS_EXPORTER_DEBUG: "true"

    # 端口映射
    ports:
      - "9121:9121"

    # 健康检查
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9121/metrics"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

    # 服务依赖
    depends_on:
      - dragonflydb

    # 资源限制
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

    # 网络配置
    networks:
      - axum_network

  # Prometheus 监控服务
  prometheus:
    image: docker.io/prom/prometheus:latest
    container_name: axum_prometheus
    restart: unless-stopped
    
    # 端口映射
    ports:
      - "9090:9090"
    
    # 启动命令
    command:
      - --config.file=/etc/prometheus/prometheus.yml
      - --storage.tsdb.path=/prometheus
      - --web.console.libraries=/etc/prometheus/console_libraries
      - --web.console.templates=/etc/prometheus/consoles
      - --storage.tsdb.retention.time=200h
      - --web.enable-lifecycle
      - --storage.tsdb.retention.size=10GB
    
    # 配置文件挂载
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    
    # 服务依赖
    depends_on:
      - postgres
      - dragonflydb
    
    # 网络配置
    networks:
      - axum_network

  # Grafana 可视化服务
  grafana:
    image: grafana/grafana:latest
    container_name: axum_grafana
    restart: unless-stopped
    
    # 端口映射
    ports:
      - "3001:3000"
    
    # 环境变量
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=grafana_admin_2025
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    
    # 配置文件挂载
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    
    # 服务依赖
    depends_on:
      - prometheus
    
    # 网络配置
    networks:
      - axum_network

# 数据卷定义
volumes:
  # PostgreSQL 17 数据卷
  postgres_17_data:

  # DragonflyDB 数据卷
  dragonflydb_data:

  # Prometheus 数据卷
  prometheus_data:

  # Grafana 数据卷
  grafana_data:

# 网络定义
networks:
  axum_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
