//! # 错误处理测试运行器
//!
//! 实现任务7：错误处理测试的统一运行器
//! 整合所有错误处理测试并生成覆盖率报告
//! 遵循Context7 MCP最佳实践和rust_axum_Rules.md规范

use anyhow::Result;
use std::process::Command;
use std::time::{Duration, Instant};
use tracing::{error, info, warn};

/// 错误处理测试运行器
pub struct ErrorHandlingTestRunner {
    test_timeout: Duration,
    coverage_enabled: bool,
    verbose: bool,
}

impl Default for ErrorHandlingTestRunner {
    fn default() -> Self {
        Self {
            test_timeout: Duration::from_secs(300), // 5分钟超时
            coverage_enabled: true,
            verbose: true,
        }
    }
}

impl ErrorHandlingTestRunner {
    /// 创建新的测试运行器
    pub fn new() -> Self {
        Self::default()
    }

    /// 设置测试超时时间
    pub fn with_timeout(mut self, timeout: Duration) -> Self {
        self.test_timeout = timeout;
        self
    }

    /// 启用或禁用覆盖率收集
    pub fn with_coverage(mut self, enabled: bool) -> Self {
        self.coverage_enabled = enabled;
        self
    }

    /// 启用或禁用详细输出
    pub fn with_verbose(mut self, verbose: bool) -> Self {
        self.verbose = verbose;
        self
    }

    /// 运行所有错误处理测试
    pub async fn run_all_tests(&self) -> Result<TestResults> {
        info!("🚀 开始运行错误处理综合测试套件");
        let start_time = Instant::now();

        let mut results = TestResults::new();

        // 1. 运行Rust单元测试
        info!("📋 运行Rust错误处理测试...");
        match self.run_rust_tests().await {
            Ok(rust_result) => {
                results.rust_tests = rust_result;
                info!("✅ Rust测试完成");
            }
            Err(e) => {
                error!("❌ Rust测试失败: {}", e);
                results.rust_tests.has_failures = true;
                results.rust_tests.error_message = Some(e.to_string());
            }
        }

        // 2. 运行Playwright E2E测试
        info!("🎭 运行Playwright错误处理测试...");
        match self.run_playwright_tests().await {
            Ok(playwright_result) => {
                results.playwright_tests = playwright_result;
                info!("✅ Playwright测试完成");
            }
            Err(e) => {
                error!("❌ Playwright测试失败: {}", e);
                results.playwright_tests.has_failures = true;
                results.playwright_tests.error_message = Some(e.to_string());
            }
        }

        // 3. 生成覆盖率报告
        if self.coverage_enabled {
            info!("📊 生成覆盖率报告...");
            match self.generate_coverage_report().await {
                Ok(coverage_result) => {
                    results.coverage_report = Some(coverage_result);
                    info!("✅ 覆盖率报告生成完成");
                }
                Err(e) => {
                    warn!("⚠️ 覆盖率报告生成失败: {}", e);
                }
            }
        }

        let duration = start_time.elapsed();
        results.total_duration = duration;

        info!("🎉 错误处理测试套件执行完成，耗时: {:?}", duration);
        self.print_summary(&results);

        Ok(results)
    }

    /// 运行Rust错误处理测试
    async fn run_rust_tests(&self) -> Result<TestResult> {
        let start_time = Instant::now();

        let mut cmd = Command::new("cargo");
        cmd.arg("test")
            .arg("error_handling_comprehensive_tests")
            .arg("--")
            .arg("--nocapture");

        if self.verbose {
            cmd.arg("--verbose");
        }

        let output = cmd.output()?;
        let duration = start_time.elapsed();

        let stdout = String::from_utf8_lossy(&output.stdout);
        let stderr = String::from_utf8_lossy(&output.stderr);

        let success = output.status.success();
        let (passed, failed) = self.parse_test_output(&stdout);

        Ok(TestResult {
            passed,
            failed_count: if success { 0 } else { failed.max(1) },
            duration,
            output: stdout.to_string(),
            error_output: if stderr.is_empty() {
                None
            } else {
                Some(stderr.to_string())
            },
            has_failures: !success,
            error_message: if success {
                None
            } else {
                Some("测试执行失败".to_string())
            },
        })
    }

    /// 运行Playwright错误处理测试
    async fn run_playwright_tests(&self) -> Result<TestResult> {
        let start_time = Instant::now();

        let mut cmd = Command::new("npx");
        cmd.arg("playwright")
            .arg("test")
            .arg("tests/ui/specs/error-handling-comprehensive.spec.ts");

        if self.verbose {
            cmd.arg("--reporter=verbose");
        }

        let output = cmd.output()?;
        let duration = start_time.elapsed();

        let stdout = String::from_utf8_lossy(&output.stdout);
        let stderr = String::from_utf8_lossy(&output.stderr);

        let success = output.status.success();
        let (passed, failed) = self.parse_playwright_output(&stdout);

        Ok(TestResult {
            passed,
            failed_count: if success { 0 } else { failed.max(1) },
            duration,
            output: stdout.to_string(),
            error_output: if stderr.is_empty() {
                None
            } else {
                Some(stderr.to_string())
            },
            has_failures: !success,
            error_message: if success {
                None
            } else {
                Some("Playwright测试执行失败".to_string())
            },
        })
    }

    /// 生成覆盖率报告
    async fn generate_coverage_report(&self) -> Result<CoverageResult> {
        let start_time = Instant::now();

        let mut cmd = Command::new("cargo");
        cmd.arg("tarpaulin")
            .arg("--out")
            .arg("Html")
            .arg("--output-dir")
            .arg("tests/reports/coverage")
            .arg("--include-tests")
            .arg("--exclude-files")
            .arg("tests/*");

        let output = cmd.output()?;
        let duration = start_time.elapsed();

        let stdout = String::from_utf8_lossy(&output.stdout);
        let success = output.status.success();

        let coverage_percentage = self.parse_coverage_output(&stdout);

        Ok(CoverageResult {
            line_coverage: coverage_percentage,
            branch_coverage: 0.0, // tarpaulin不直接提供分支覆盖率
            duration,
            report_path: "tests/reports/coverage/tarpaulin-report.html".to_string(),
            success,
        })
    }

    /// 解析测试输出获取通过/失败数量
    fn parse_test_output(&self, output: &str) -> (u32, u32) {
        let mut passed = 0;
        let mut failed = 0;

        for line in output.lines() {
            if line.contains("test result:") {
                if let Some(results) = line.split("test result:").nth(1) {
                    if let Some(passed_str) = results.split("passed").nth(0) {
                        if let Ok(p) = passed_str.trim().parse::<u32>() {
                            passed = p;
                        }
                    }
                    if let Some(failed_str) = results.split("failed").nth(0) {
                        if let Some(f_str) = failed_str.split(";").nth(1) {
                            if let Ok(f) = f_str.trim().parse::<u32>() {
                                failed = f;
                            }
                        }
                    }
                }
            }
        }

        (passed, failed)
    }

    /// 解析Playwright输出获取通过/失败数量
    fn parse_playwright_output(&self, output: &str) -> (u32, u32) {
        let mut passed = 0;
        let mut failed = 0;

        for line in output.lines() {
            if line.contains("passed") && line.contains("failed") {
                // 解析Playwright输出格式
                if let Some(passed_match) = line.split("passed").nth(0) {
                    if let Ok(p) = passed_match
                        .trim()
                        .split_whitespace()
                        .last()
                        .unwrap_or("0")
                        .parse::<u32>()
                    {
                        passed = p;
                    }
                }
                if let Some(failed_match) = line.split("failed").nth(0) {
                    if let Ok(f) = failed_match
                        .trim()
                        .split_whitespace()
                        .last()
                        .unwrap_or("0")
                        .parse::<u32>()
                    {
                        failed = f;
                    }
                }
            }
        }

        (passed, failed)
    }

    /// 解析覆盖率输出获取覆盖率百分比
    fn parse_coverage_output(&self, output: &str) -> f64 {
        for line in output.lines() {
            if line.contains("%") && (line.contains("coverage") || line.contains("lines")) {
                if let Some(percentage_str) = line.split('%').nth(0) {
                    if let Some(num_str) = percentage_str.split_whitespace().last() {
                        if let Ok(percentage) = num_str.parse::<f64>() {
                            return percentage;
                        }
                    }
                }
            }
        }
        0.0
    }

    /// 打印测试结果摘要
    fn print_summary(&self, results: &TestResults) {
        info!("📊 错误处理测试结果摘要:");
        info!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

        info!("🦀 Rust测试:");
        info!(
            "   通过: {}, 失败: {}, 耗时: {:?}",
            results.rust_tests.passed, results.rust_tests.failed_count, results.rust_tests.duration
        );

        info!("🎭 Playwright测试:");
        info!(
            "   通过: {}, 失败: {}, 耗时: {:?}",
            results.playwright_tests.passed,
            results.playwright_tests.failed_count,
            results.playwright_tests.duration
        );

        if let Some(coverage) = &results.coverage_report {
            info!("📊 覆盖率报告:");
            info!(
                "   行覆盖率: {:.2}%, 报告路径: {}",
                coverage.line_coverage, coverage.report_path
            );
        }

        info!("⏱️ 总耗时: {:?}", results.total_duration);
        info!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

        let total_passed = results.rust_tests.passed + results.playwright_tests.passed;
        let total_failed = results.rust_tests.failed_count + results.playwright_tests.failed_count;

        if total_failed == 0 {
            info!("🎉 所有测试通过！总计: {} 个测试", total_passed);
        } else {
            error!(
                "❌ 有测试失败！通过: {}, 失败: {}",
                total_passed, total_failed
            );
        }
    }
}

/// 测试结果
#[derive(Debug, Clone)]
pub struct TestResult {
    pub passed: u32,
    pub failed_count: u32,
    pub duration: Duration,
    pub output: String,
    pub error_output: Option<String>,
    pub has_failures: bool,
    pub error_message: Option<String>,
}

impl Default for TestResult {
    fn default() -> Self {
        Self {
            passed: 0,
            failed_count: 0,
            duration: Duration::from_secs(0),
            output: String::new(),
            error_output: None,
            has_failures: false,
            error_message: None,
        }
    }
}

/// 覆盖率结果
#[derive(Debug, Clone)]
pub struct CoverageResult {
    pub line_coverage: f64,
    pub branch_coverage: f64,
    pub duration: Duration,
    pub report_path: String,
    pub success: bool,
}

/// 完整测试结果
#[derive(Debug, Clone)]
pub struct TestResults {
    pub rust_tests: TestResult,
    pub playwright_tests: TestResult,
    pub coverage_report: Option<CoverageResult>,
    pub total_duration: Duration,
}

impl TestResults {
    pub fn new() -> Self {
        Self {
            rust_tests: TestResult::default(),
            playwright_tests: TestResult::default(),
            coverage_report: None,
            total_duration: Duration::from_secs(0),
        }
    }

    /// 检查是否所有测试都通过
    pub fn all_tests_passed(&self) -> bool {
        !self.rust_tests.has_failures && !self.playwright_tests.has_failures
    }

    /// 获取总的通过测试数
    pub fn total_passed(&self) -> u32 {
        self.rust_tests.passed + self.playwright_tests.passed
    }

    /// 获取总的失败测试数
    pub fn total_failed(&self) -> u32 {
        self.rust_tests.failed_count + self.playwright_tests.failed_count
    }
}

/// 主函数 - 运行错误处理测试
#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志
    tracing_subscriber::fmt().with_env_filter("info").init();

    let runner = ErrorHandlingTestRunner::new()
        .with_timeout(Duration::from_secs(600)) // 10分钟超时
        .with_coverage(true)
        .with_verbose(true);

    let results = runner.run_all_tests().await?;

    // 根据测试结果设置退出码
    if results.all_tests_passed() {
        std::process::exit(0);
    } else {
        std::process::exit(1);
    }
}
