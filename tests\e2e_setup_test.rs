// E2E测试环境配置验证测试

use anyhow::Result;
use std::time::Duration;
use tokio::time::sleep;

// 引入E2E测试辅助模块
mod e2e {
    pub mod helpers;
}

use e2e::helpers::playwright::PlaywrightClient;
use e2e::helpers::{E2EConfig, cleanup_test_data, ensure_dir_exists, load_fixture};

/// 测试E2E环境配置加载
#[tokio::test]
async fn test_e2e_config_loading() -> Result<()> {
    println!("测试E2E环境配置加载...");

    // 加载配置
    let config = E2EConfig::from_env()?;

    // 验证基本配置
    assert!(!config.server_host.is_empty(), "服务器主机不能为空");
    assert!(config.server_port > 0, "服务器端口必须大于0");
    assert!(!config.base_url.is_empty(), "基础URL不能为空");
    assert!(!config.test_username.is_empty(), "测试用户名不能为空");
    assert!(!config.test_password.is_empty(), "测试密码不能为空");

    println!("✅ E2E配置加载测试通过");
    println!("   服务器: {}:{}", config.server_host, config.server_port);
    println!("   基础URL: {}", config.base_url);
    println!("   测试用户: {}", config.test_username);

    Ok(())
}

/// 测试目录结构创建
#[tokio::test]
async fn test_directory_structure() -> Result<()> {
    println!("测试目录结构创建...");

    let config = E2EConfig::from_env()?;

    // 确保所有必要目录存在
    ensure_dir_exists(&config.report_dir)?;
    ensure_dir_exists(&config.screenshot_dir)?;
    ensure_dir_exists(&config.video_dir)?;

    // 验证目录是否创建成功
    assert!(config.report_dir.exists(), "报告目录应该存在");
    assert!(config.screenshot_dir.exists(), "截图目录应该存在");
    assert!(config.video_dir.exists(), "视频目录应该存在");

    println!("✅ 目录结构测试通过");
    println!("   报告目录: {:?}", config.report_dir);
    println!("   截图目录: {:?}", config.screenshot_dir);
    println!("   视频目录: {:?}", config.video_dir);

    Ok(())
}

/// 测试夹具数据加载
#[tokio::test]
async fn test_fixture_loading() -> Result<()> {
    println!("测试夹具数据加载...");

    // 加载用户夹具
    let users_fixture = load_fixture("users")?;
    assert!(
        users_fixture.get("testUsers").is_some(),
        "用户夹具应包含testUsers"
    );
    assert!(
        users_fixture.get("invalidUsers").is_some(),
        "用户夹具应包含invalidUsers"
    );

    // 加载任务夹具
    let tasks_fixture = load_fixture("tasks")?;
    assert!(
        tasks_fixture.get("testTasks").is_some(),
        "任务夹具应包含testTasks"
    );
    assert!(
        tasks_fixture.get("taskTemplates").is_some(),
        "任务夹具应包含taskTemplates"
    );

    // 验证测试用户数据
    let test_users = users_fixture["testUsers"].as_array().unwrap();
    assert!(!test_users.is_empty(), "应该有测试用户数据");

    let first_user = &test_users[0];
    assert!(first_user.get("username").is_some(), "用户应该有用户名");
    assert!(first_user.get("email").is_some(), "用户应该有邮箱");
    assert!(first_user.get("password").is_some(), "用户应该有密码");

    println!("✅ 夹具数据加载测试通过");
    println!("   加载了 {} 个测试用户", test_users.len());

    let test_tasks = tasks_fixture["testTasks"].as_array().unwrap();
    println!("   加载了 {} 个测试任务", test_tasks.len());

    Ok(())
}

/// 测试Playwright MCP配置验证
#[tokio::test]
async fn test_playwright_config_validation() -> Result<()> {
    println!("测试Playwright MCP配置验证...");

    let config = E2EConfig::from_env()?;
    let playwright_client = PlaywrightClient::new(config);

    // 验证配置
    playwright_client.validate_config()?;

    println!("✅ Playwright配置验证测试通过");

    Ok(())
}

/// 测试Playwright MCP服务器启动（可选，需要较长时间）
#[tokio::test]
#[ignore] // 默认忽略，需要手动运行
async fn test_playwright_server_startup() -> Result<()> {
    println!("测试Playwright MCP服务器启动...");

    let config = E2EConfig::from_env()?;
    let mut playwright_client = PlaywrightClient::new(config);

    // 安装浏览器（如果需要）
    playwright_client.install_browser().await?;

    // 启动服务器
    playwright_client.start_server().await?;

    // 等待一段时间确保服务器稳定
    sleep(Duration::from_secs(2)).await;

    // 验证服务器URL
    let server_url = playwright_client.get_server_url();
    assert!(!server_url.is_empty(), "服务器URL不能为空");

    println!("✅ Playwright服务器启动测试通过");
    println!("   服务器URL: {}", server_url);

    // 服务器会在PlaywrightClient drop时自动停止

    Ok(())
}

/// 测试服务器连接性
#[tokio::test]
async fn test_server_connectivity() -> Result<()> {
    println!("测试服务器连接性...");

    let config = E2EConfig::from_env()?;

    // 尝试连接到测试服务器
    let client = reqwest::Client::new();
    let response = client
        .get(&config.base_url)
        .timeout(Duration::from_secs(5))
        .send()
        .await;

    match response {
        Ok(resp) => {
            println!("✅ 服务器连接测试通过");
            println!("   状态码: {}", resp.status());
            println!("   服务器响应正常");
        }
        Err(e) => {
            println!("⚠️  服务器连接测试失败: {}", e);
            println!("   请确保Axum服务器正在运行在 {}", config.base_url);
            println!("   运行命令: cargo run -p server");

            // 这不是致命错误，因为服务器可能还没启动
            return Ok(());
        }
    }

    Ok(())
}

/// 测试清理功能
#[tokio::test]
async fn test_cleanup_functionality() -> Result<()> {
    println!("测试清理功能...");

    // 创建一些临时文件用于测试清理
    let temp_file = std::path::Path::new("./tests/e2e/reports/temp/test_cleanup.txt");
    if let Some(parent) = temp_file.parent() {
        std::fs::create_dir_all(parent)?;
    }
    std::fs::write(temp_file, "临时测试文件")?;

    // 验证文件存在
    assert!(temp_file.exists(), "临时文件应该存在");

    // 执行清理
    cleanup_test_data()?;

    // 验证清理效果（临时目录应该被删除）
    let temp_dir = std::path::Path::new("./tests/e2e/reports/temp");
    assert!(!temp_dir.exists(), "临时目录应该被清理");

    println!("✅ 清理功能测试通过");

    Ok(())
}

/// 集成测试：完整的E2E环境验证
#[tokio::test]
async fn test_complete_e2e_environment() -> Result<()> {
    println!("执行完整的E2E环境验证...");

    // 1. 加载配置
    let config = E2EConfig::from_env()?;
    println!("✅ 配置加载成功");

    // 2. 创建目录结构
    ensure_dir_exists(&config.report_dir)?;
    ensure_dir_exists(&config.screenshot_dir)?;
    ensure_dir_exists(&config.video_dir)?;
    println!("✅ 目录结构创建成功");

    // 3. 加载测试数据
    let _users = load_fixture("users")?;
    let _tasks = load_fixture("tasks")?;
    println!("✅ 测试数据加载成功");

    // 4. 验证Playwright配置
    let playwright_client = PlaywrightClient::new(config.clone());
    playwright_client.validate_config()?;
    println!("✅ Playwright配置验证成功");

    // 5. 生成测试报告
    playwright_client
        .generate_test_report("E2E环境验证完成")
        .await?;
    println!("✅ 测试报告生成成功");

    println!("🎉 完整的E2E环境验证通过！");
    println!("   环境已准备就绪，可以开始编写E2E测试用例");

    Ok(())
}
