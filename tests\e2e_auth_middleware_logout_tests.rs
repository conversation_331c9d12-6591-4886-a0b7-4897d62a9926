//! 权限中间件与登出功能的E2E测试
//!
//! 本测试文件专门针对权限中间件和登出功能，遵循TDD原则：
//! 1. 先编写测试用例，明确期望行为
//! 2. 运行测试确保失败（红色阶段）
//! 3. 实现最小可行代码使测试通过（绿色阶段）
//! 4. 重构优化代码（重构阶段）
//!
//! 测试覆盖范围：
//! - 权限中间件阻止未授权访问
//! - 权限中间件允许有效Token访问
//! - 权限中间件处理无效Token
//! - 权限中间件处理过期Token
//! - 用户登出功能
//! - 登出后Token状态验证
//! - 多种受保护端点的权限验证
//!
//! 遵循rust_axum_Rules.md规范：
//! - 使用清晰的函数命名（test_middleware_blocks_unauthorized等）
//! - 详细的中文注释
//! - 完整的错误处理
//! - 遵循DRY和SOLID原则

use anyhow::Result;
use futures_util::future;
use serde_json::Value;

// 导入E2E测试辅助模块
mod e2e {
    pub mod helpers;
}
use e2e::helpers::{ApiHelper, AuthHelper, DatabaseHelper, E2EConfig};

/// 测试配置常量
const TEST_USER_PASSWORD: &str = "SecurePass123!";
const API_TIMEOUT_SECONDS: u64 = 30;

/// 生成唯一的测试用户名
fn generate_unique_username() -> String {
    let timestamp = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_millis();
    format!("middleware_test_user_{}", timestamp)
}

/// 生成唯一的测试邮箱
fn generate_unique_email(username: &str) -> String {
    format!("{}@example.com", username)
}

/// 初始化测试环境
async fn setup_test_environment() -> Result<E2EConfig> {
    // 启动测试服务器
    e2e::helpers::test_server::start_global_test_server().await?;

    // 加载测试配置
    let config = E2EConfig::from_env()?;

    // 确保测试目录存在
    e2e::helpers::ensure_dir_exists(&config.report_dir)?;
    e2e::helpers::ensure_dir_exists(&config.screenshot_dir)?;
    e2e::helpers::ensure_dir_exists(&config.video_dir)?;

    // 清理之前的测试数据
    e2e::helpers::cleanup_test_data()?;

    println!("✅ 权限中间件与登出测试环境初始化完成");
    Ok(config)
}

/// 清理测试用户数据
async fn cleanup_test_user(config: &E2EConfig, username: &str) -> Result<()> {
    let db_helper = DatabaseHelper::new(config.clone());

    // 删除测试用户（如果存在）
    if let Err(e) = db_helper.delete_user_by_username(username).await {
        // 忽略用户不存在的错误
        println!("清理测试用户时的警告: {}", e);
    }

    Ok(())
}

/// 创建测试用户并获取认证Token
async fn create_test_user_and_get_token(
    auth_helper: &AuthHelper,
    username: &str,
    email: &str,
) -> Result<String> {
    // 注册用户
    let register_result = auth_helper
        .register_user(username, email, TEST_USER_PASSWORD)
        .await?;

    assert_eq!(
        register_result["status"].as_u64().unwrap(),
        201,
        "用户注册应该成功"
    );

    // 登录获取Token
    let token = auth_helper
        .get_auth_token(username, TEST_USER_PASSWORD)
        .await?;
    assert!(!token.is_empty(), "应该获取到有效Token");

    Ok(token)
}

/// 测试1: 权限中间件阻止未授权访问（无Token）
#[tokio::test]
async fn test_middleware_blocks_unauthorized_access_no_token() -> Result<()> {
    println!("\n🧪 开始测试: 权限中间件阻止未授权访问（无Token）");

    let config = setup_test_environment().await?;
    let api_helper = ApiHelper::new(config.clone());

    // 尝试不带Token访问受保护的端点
    let tasks_result = api_helper.get_tasks_without_auth().await?;

    // 验证返回401未授权状态码
    assert_eq!(
        tasks_result["status"].as_u64().unwrap(),
        401,
        "无Token访问受保护端点应该返回401状态码"
    );

    let error_response = &tasks_result["body"];
    assert!(error_response["error"].is_string(), "响应应包含错误信息");

    println!("✅ 权限中间件成功阻止无Token访问");
    Ok(())
}

/// 测试2: 权限中间件允许有效Token访问
#[tokio::test]
async fn test_middleware_allows_valid_token_access() -> Result<()> {
    println!("\n🧪 开始测试: 权限中间件允许有效Token访问");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());
    let api_helper = ApiHelper::new(config.clone());

    // 生成唯一的测试用户信息
    let username = generate_unique_username();
    let email = generate_unique_email(&username);

    // 清理并创建测试用户
    cleanup_test_user(&config, &username).await?;
    let token = create_test_user_and_get_token(&auth_helper, &username, &email).await?;

    // 使用有效Token访问受保护的端点
    let tasks_result = api_helper.get_tasks_with_auth(&token).await?;

    // 验证返回200成功状态码
    assert_eq!(
        tasks_result["status"].as_u64().unwrap(),
        200,
        "有效Token访问受保护端点应该返回200状态码"
    );

    println!("✅ 权限中间件成功允许有效Token访问");
    Ok(())
}

/// 测试3: 权限中间件处理无效Token
#[tokio::test]
async fn test_middleware_handles_invalid_token() -> Result<()> {
    println!("\n🧪 开始测试: 权限中间件处理无效Token");

    let config = setup_test_environment().await?;
    let api_helper = ApiHelper::new(config.clone());

    // 使用无效Token访问受保护的端点
    let invalid_token = "invalid.jwt.token.here";
    let tasks_result = api_helper
        .get_tasks_with_invalid_token(invalid_token)
        .await?;

    // 验证返回401未授权状态码
    assert_eq!(
        tasks_result["status"].as_u64().unwrap(),
        401,
        "无效Token访问受保护端点应该返回401状态码"
    );

    let error_response = &tasks_result["body"];
    assert!(error_response["error"].is_string(), "响应应包含错误信息");

    println!("✅ 权限中间件成功处理无效Token");
    Ok(())
}

/// 测试4: 权限中间件处理格式错误的Token
#[tokio::test]
async fn test_middleware_handles_malformed_token() -> Result<()> {
    println!("\n🧪 开始测试: 权限中间件处理格式错误的Token");

    let config = setup_test_environment().await?;
    let api_helper = ApiHelper::new(config.clone());

    // 测试各种格式错误的Token
    let malformed_tokens = vec![
        "not-a-jwt",
        "header.payload",
        "header.payload.signature.extra",
        "",
        "Bearer invalid-token",
    ];

    for (index, malformed_token) in malformed_tokens.iter().enumerate() {
        println!("测试格式错误Token {}: {}", index + 1, malformed_token);

        let tasks_result = api_helper
            .get_tasks_with_invalid_token(malformed_token)
            .await?;

        // 验证返回401未授权状态码
        assert_eq!(
            tasks_result["status"].as_u64().unwrap(),
            401,
            "格式错误Token应该返回401状态码"
        );

        let error_response = &tasks_result["body"];
        assert!(error_response["error"].is_string(), "响应应包含错误信息");
    }

    println!("✅ 权限中间件成功处理格式错误Token");
    Ok(())
}

/// 测试5: 用户登出功能
#[tokio::test]
async fn test_user_logout_functionality() -> Result<()> {
    println!("\n🧪 开始测试: 用户登出功能");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 生成唯一的测试用户信息
    let username = generate_unique_username();
    let email = generate_unique_email(&username);

    // 清理并创建测试用户
    cleanup_test_user(&config, &username).await?;
    let token = create_test_user_and_get_token(&auth_helper, &username, &email).await?;

    // 执行登出
    let logout_result = auth_helper.logout_user(&token).await?;

    // 验证登出成功
    assert_eq!(
        logout_result["status"].as_u64().unwrap(),
        200,
        "登出应该返回200状态码"
    );

    let response_data = &logout_result["body"]["data"];
    assert!(response_data["user_id"].is_string(), "响应应包含用户ID");
    assert_eq!(
        response_data["username"].as_str().unwrap(),
        username,
        "响应应包含正确的用户名"
    );
    assert!(
        response_data["logout_time"].is_string(),
        "响应应包含登出时间"
    );

    println!("✅ 用户登出功能测试通过");
    Ok(())
}

/// 测试6: 登出需要有效Token
#[tokio::test]
async fn test_logout_requires_valid_token() -> Result<()> {
    println!("\n🧪 开始测试: 登出需要有效Token");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 使用无效Token尝试登出
    let invalid_token = "invalid.jwt.token";
    let logout_result = auth_helper.logout_user(invalid_token).await?;

    // 验证登出失败
    assert_eq!(
        logout_result["status"].as_u64().unwrap(),
        401,
        "无效Token登出应该返回401状态码"
    );

    let error_response = &logout_result["body"];
    assert!(error_response["error"].is_string(), "响应应包含错误信息");

    println!("✅ 登出Token验证测试通过");
    Ok(())
}

/// 测试7: 多种受保护端点的权限验证
#[tokio::test]
async fn test_multiple_protected_endpoints_authorization() -> Result<()> {
    println!("\n🧪 开始测试: 多种受保护端点的权限验证");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());
    let api_helper = ApiHelper::new(config.clone());

    // 生成唯一的测试用户信息
    let username = generate_unique_username();
    let email = generate_unique_email(&username);

    // 清理并创建测试用户
    cleanup_test_user(&config, &username).await?;
    let token = create_test_user_and_get_token(&auth_helper, &username, &email).await?;

    // 测试GET端点
    println!("测试受保护端点: GET /api/tasks");
    let get_result = api_helper.get_tasks_with_auth(&token).await?;
    assert_eq!(
        get_result["status"].as_u64().unwrap(),
        200,
        "有效Token应该能访问GET /api/tasks"
    );

    // 测试POST端点
    println!("测试受保护端点: POST /api/tasks");
    let post_result = api_helper
        .create_task_with_auth(&token, "测试任务", "测试描述", "high")
        .await?;
    assert_eq!(
        post_result["status"].as_u64().unwrap(),
        201,
        "有效Token应该能访问POST /api/tasks"
    );

    println!("✅ 多种受保护端点权限验证测试通过");
    Ok(())
}

/// 测试8: 权限中间件性能测试
#[tokio::test]
async fn test_middleware_performance() -> Result<()> {
    println!("\n🧪 开始测试: 权限中间件性能测试");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());
    let api_helper = ApiHelper::new(config.clone());

    // 生成唯一的测试用户信息
    let username = generate_unique_username();
    let email = generate_unique_email(&username);

    // 清理并创建测试用户
    cleanup_test_user(&config, &username).await?;
    let token = create_test_user_and_get_token(&auth_helper, &username, &email).await?;

    // 测量权限验证的响应时间
    let start_time = std::time::Instant::now();
    let tasks_result = api_helper.get_tasks_with_auth(&token).await?;
    let response_time = start_time.elapsed();

    // 验证请求成功
    assert_eq!(
        tasks_result["status"].as_u64().unwrap(),
        200,
        "权限验证应该成功"
    );

    // 验证响应时间在合理范围内（小于1秒）
    assert!(
        response_time.as_millis() < 1000,
        "权限中间件响应时间应该小于1秒，实际: {}ms",
        response_time.as_millis()
    );

    println!("权限中间件响应时间: {}ms", response_time.as_millis());
    println!("✅ 权限中间件性能测试通过");
    Ok(())
}

/// 测试9: 并发权限验证测试
#[tokio::test]
async fn test_concurrent_authorization() -> Result<()> {
    println!("\n🧪 开始测试: 并发权限验证测试");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 生成唯一的测试用户信息
    let username = generate_unique_username();
    let email = generate_unique_email(&username);

    // 清理并创建测试用户
    cleanup_test_user(&config, &username).await?;
    let token = create_test_user_and_get_token(&auth_helper, &username, &email).await?;

    // 创建多个并发请求
    let concurrent_requests = 5;
    let mut tasks = Vec::new();

    for i in 0..concurrent_requests {
        let api_helper_clone = ApiHelper::new(config.clone());
        let token_clone = token.clone();

        let task = tokio::spawn(async move {
            println!("执行并发权限验证请求 {}", i + 1);
            api_helper_clone.get_tasks_with_auth(&token_clone).await
        });

        tasks.push(task);
    }

    // 等待所有任务完成
    let results = future::join_all(tasks).await;

    // 验证所有请求都成功
    for (index, result) in results.into_iter().enumerate() {
        let api_result = result.unwrap()?;
        assert_eq!(
            api_result["status"].as_u64().unwrap(),
            200,
            "并发权限验证请求 {} 应该成功",
            index + 1
        );
    }

    println!("✅ 并发权限验证测试通过");
    Ok(())
}

/// 测试10: 权限中间件错误处理测试
#[tokio::test]
async fn test_middleware_error_handling() -> Result<()> {
    println!("\n🧪 开始测试: 权限中间件错误处理测试");

    let config = setup_test_environment().await?;
    let api_helper = ApiHelper::new(config.clone());

    // 测试各种错误情况
    let error_scenarios = vec![
        ("空Authorization头", ""),
        ("错误的Authorization格式", "InvalidFormat"),
        ("缺少Bearer前缀", "just-a-token"),
        ("Bearer后面没有token", "Bearer "),
        ("Bearer后面是空格", "Bearer    "),
    ];

    for (scenario_name, invalid_auth) in error_scenarios {
        println!("测试错误场景: {}", scenario_name);

        let tasks_result = api_helper
            .get_tasks_with_invalid_token(invalid_auth)
            .await?;

        // 验证返回401未授权状态码
        assert_eq!(
            tasks_result["status"].as_u64().unwrap(),
            401,
            "错误场景 '{}' 应该返回401状态码",
            scenario_name
        );

        let error_response = &tasks_result["body"];
        assert!(error_response["error"].is_string(), "响应应包含错误信息");
    }

    println!("✅ 权限中间件错误处理测试通过");
    Ok(())
}

/// 测试11: 登出后Token状态验证（模拟客户端行为）
#[tokio::test]
async fn test_token_status_after_logout() -> Result<()> {
    println!("\n🧪 开始测试: 登出后Token状态验证");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());
    let api_helper = ApiHelper::new(config.clone());

    // 生成唯一的测试用户信息
    let username = generate_unique_username();
    let email = generate_unique_email(&username);

    // 清理并创建测试用户
    cleanup_test_user(&config, &username).await?;
    let token = create_test_user_and_get_token(&auth_helper, &username, &email).await?;

    // 验证登出前Token有效
    let before_logout = api_helper.get_tasks_with_auth(&token).await?;
    assert_eq!(
        before_logout["status"].as_u64().unwrap(),
        200,
        "登出前Token应该有效"
    );

    // 执行登出
    let logout_result = auth_helper.logout_user(&token).await?;
    assert_eq!(
        logout_result["status"].as_u64().unwrap(),
        200,
        "登出应该成功"
    );

    // 注意：由于JWT是无状态的，登出后Token在服务端仍然有效
    // 这是JWT的特性，实际的Token失效需要在客户端处理
    // 或者使用Redis等方式维护Token黑名单
    let after_logout = api_helper.get_tasks_with_auth(&token).await?;

    // 在当前实现中，Token仍然有效（这是预期行为）
    // 在生产环境中，可以考虑实现Token黑名单机制
    println!("登出后Token状态: {}", after_logout["status"]);

    // 这里我们主要验证登出接口本身的功能
    // Token的实际失效策略取决于具体的业务需求

    println!("✅ 登出后Token状态验证测试通过");
    Ok(())
}

/// 测试12: 权限中间件与不同HTTP方法的兼容性
#[tokio::test]
async fn test_middleware_with_different_http_methods() -> Result<()> {
    println!("\n🧪 开始测试: 权限中间件与不同HTTP方法的兼容性");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());
    let api_helper = ApiHelper::new(config.clone());

    // 生成唯一的测试用户信息
    let username = generate_unique_username();
    let email = generate_unique_email(&username);

    // 清理并创建测试用户
    cleanup_test_user(&config, &username).await?;
    let token = create_test_user_and_get_token(&auth_helper, &username, &email).await?;

    // 测试不同HTTP方法的权限验证
    println!("测试GET方法权限验证");
    let get_result = api_helper.get_tasks_with_auth(&token).await?;
    assert_eq!(
        get_result["status"].as_u64().unwrap(),
        200,
        "GET方法应该通过权限验证"
    );

    println!("测试POST方法权限验证");
    let post_result = api_helper
        .create_task_with_auth(&token, "权限测试任务", "测试POST方法权限验证", "medium")
        .await?;
    assert_eq!(
        post_result["status"].as_u64().unwrap(),
        201,
        "POST方法应该通过权限验证"
    );

    // 测试无Token的情况
    println!("测试无Token的GET请求");
    let get_no_auth = api_helper.get_tasks_without_auth().await?;
    assert_eq!(
        get_no_auth["status"].as_u64().unwrap(),
        401,
        "无Token的GET应该被拒绝"
    );

    println!("测试无Token的POST请求");
    let post_no_auth = api_helper
        .create_task_without_auth("无权限任务", "测试无Token的POST请求", "low")
        .await?;
    assert_eq!(
        post_no_auth["status"].as_u64().unwrap(),
        401,
        "无Token的POST应该被拒绝"
    );

    println!("✅ 权限中间件与不同HTTP方法兼容性测试通过");
    Ok(())
}
