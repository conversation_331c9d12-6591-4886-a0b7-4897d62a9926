# 系统性验收测试执行脚本 - 任务ID 27
# 用于执行Axum企业级聊天室后端项目的完整系统验收测试

param(
    [string]$ServerUrl = "http://127.0.0.1:3000",
    [switch]$StartServer = $false,
    [switch]$SkipPlaywright = $false,
    [switch]$SkipPerformance = $false,
    [switch]$SkipSecurity = $false,
    [switch]$Verbose = $false
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colors = @{
        "Red" = [ConsoleColor]::Red
        "Green" = [ConsoleColor]::Green
        "Yellow" = [ConsoleColor]::Yellow
        "Blue" = [ConsoleColor]::Blue
        "Cyan" = [ConsoleColor]::Cyan
        "Magenta" = [ConsoleColor]::Magenta
        "White" = [ConsoleColor]::White
    }
    
    Write-Host $Message -ForegroundColor $colors[$Color]
}

# 主函数
function Main {
    Write-ColorOutput "🚀 启动系统性验收测试 - 任务ID 27" "Cyan"
    Write-ColorOutput "📋 目标: 对Axum企业级聊天室后端项目进行全面验收测试" "White"
    Write-ColorOutput "🔗 测试服务器: $ServerUrl" "Yellow"
    Write-ColorOutput "⏰ 测试开始时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" "White"
    Write-Host ""

    # 检查Rust环境
    Test-RustEnvironment

    # 检查项目结构
    Test-ProjectStructure

    # 可选：启动服务器
    if ($StartServer) {
        Start-AxumServer
    }

    # 检查服务器状态
    Test-ServerStatus

    # 执行系统验收测试
    Run-SystemAcceptanceTests

    # 生成最终报告
    Generate-FinalReport

    Write-ColorOutput "✅ 系统性验收测试执行完成" "Green"
}

# 检查Rust环境
function Test-RustEnvironment {
    Write-ColorOutput "🔧 检查Rust开发环境..." "Blue"
    
    try {
        $rustVersion = cargo --version
        Write-ColorOutput "   ✓ Rust环境: $rustVersion" "Green"
        
        $cargoCheck = cargo check --workspace --quiet
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "   ✓ 项目编译检查通过" "Green"
        } else {
            Write-ColorOutput "   ❌ 项目编译检查失败" "Red"
            throw "项目编译失败"
        }
    }
    catch {
        Write-ColorOutput "   ❌ Rust环境检查失败: $_" "Red"
        throw
    }
    
    Write-Host ""
}

# 检查项目结构
function Test-ProjectStructure {
    Write-ColorOutput "📁 检查项目结构..." "Blue"
    
    $requiredPaths = @(
        "Cargo.toml",
        "server",
        "crates/app_common",
        "crates/app_domain",
        "crates/app_application", 
        "crates/app_infrastructure",
        "tests/system_acceptance_test.rs",
        "tests/comprehensive_system_acceptance_test.rs"
    )
    
    foreach ($path in $requiredPaths) {
        if (Test-Path $path) {
            Write-ColorOutput "   ✓ $path" "Green"
        } else {
            Write-ColorOutput "   ❌ 缺少: $path" "Red"
            throw "项目结构不完整"
        }
    }
    
    Write-Host ""
}

# 启动Axum服务器
function Start-AxumServer {
    Write-ColorOutput "🚀 启动Axum服务器..." "Blue"
    
    try {
        # 检查端口是否被占用
        $port = ($ServerUrl -split ':')[-1]
        $connection = Test-NetConnection -ComputerName "127.0.0.1" -Port $port -InformationLevel Quiet -WarningAction SilentlyContinue
        
        if ($connection) {
            Write-ColorOutput "   ⚠️ 端口 $port 已被占用，跳过服务器启动" "Yellow"
            return
        }
        
        # 启动服务器（后台运行）
        Write-ColorOutput "   🔄 正在启动服务器..." "Yellow"
        $serverProcess = Start-Process -FilePath "cargo" -ArgumentList "run", "-p", "axum-server" -NoNewWindow -PassThru
        
        # 等待服务器启动
        $timeout = 30
        $elapsed = 0
        
        do {
            Start-Sleep -Seconds 2
            $elapsed += 2
            
            try {
                $response = Invoke-WebRequest -Uri "$ServerUrl/api/health" -TimeoutSec 5 -UseBasicParsing
                if ($response.StatusCode -eq 200) {
                    Write-ColorOutput "   ✓ 服务器启动成功" "Green"
                    return
                }
            }
            catch {
                # 继续等待
            }
            
            Write-ColorOutput "   ⏳ 等待服务器启动... ($elapsed/$timeout 秒)" "Yellow"
            
        } while ($elapsed -lt $timeout)
        
        Write-ColorOutput "   ❌ 服务器启动超时" "Red"
        throw "服务器启动失败"
    }
    catch {
        Write-ColorOutput "   ❌ 启动服务器失败: $_" "Red"
        throw
    }
    
    Write-Host ""
}

# 检查服务器状态
function Test-ServerStatus {
    Write-ColorOutput "🔍 检查服务器状态..." "Blue"
    
    try {
        $response = Invoke-WebRequest -Uri "$ServerUrl/api/health" -TimeoutSec 10 -UseBasicParsing
        
        if ($response.StatusCode -eq 200) {
            Write-ColorOutput "   ✓ 服务器健康检查通过" "Green"
            Write-ColorOutput "   ✓ 服务器响应正常: $($response.StatusCode)" "Green"
        } else {
            Write-ColorOutput "   ⚠️ 服务器响应异常: $($response.StatusCode)" "Yellow"
        }
    }
    catch {
        Write-ColorOutput "   ❌ 无法连接到服务器: $_" "Red"
        Write-ColorOutput "   ℹ️ 将执行离线测试模式" "Yellow"
    }
    
    Write-Host ""
}

# 执行系统验收测试
function Run-SystemAcceptanceTests {
    Write-ColorOutput "🧪 执行系统验收测试..." "Blue"
    
    # 设置环境变量
    $env:TEST_SERVER_URL = $ServerUrl
    $env:RUST_LOG = "info"
    
    try {
        # 执行综合系统验收测试
        Write-ColorOutput "   🔄 运行综合验收测试..." "Yellow"
        
        $testResult = cargo run --bin comprehensive_system_acceptance_test
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "   ✅ 综合验收测试完成" "Green"
        } else {
            Write-ColorOutput "   ⚠️ 验收测试完成，但发现问题" "Yellow"
        }
        
        # 可选：执行单独的测试模块
        if (!$SkipPlaywright) {
            Write-ColorOutput "   🎭 运行Playwright端到端测试..." "Yellow"
            # 这里可以添加Playwright测试执行逻辑
        }
        
        if (!$SkipPerformance) {
            Write-ColorOutput "   ⚡ 运行性能测试..." "Yellow"
            # 这里可以添加性能测试执行逻辑
        }
        
        if (!$SkipSecurity) {
            Write-ColorOutput "   🔒 运行安全测试..." "Yellow"
            # 这里可以添加安全测试执行逻辑
        }
        
    }
    catch {
        Write-ColorOutput "   ❌ 验收测试执行失败: $_" "Red"
        throw
    }
    
    Write-Host ""
}

# 生成最终报告
function Generate-FinalReport {
    Write-ColorOutput "📊 生成测试报告..." "Blue"
    
    try {
        # 检查报告文件
        $reportFiles = @(
            "reports/system_acceptance_test_report.json",
            "reports/system_acceptance_test_report.md",
            "reports/comprehensive_acceptance_test_report.json"
        )
        
        foreach ($reportFile in $reportFiles) {
            if (Test-Path $reportFile) {
                $fileSize = (Get-Item $reportFile).Length
                Write-ColorOutput "   ✓ 生成报告: $reportFile ($fileSize 字节)" "Green"
            } else {
                Write-ColorOutput "   ⚠️ 报告文件未找到: $reportFile" "Yellow"
            }
        }
        
        # 显示报告摘要
        if (Test-Path "reports/comprehensive_acceptance_test_report.json") {
            $report = Get-Content "reports/comprehensive_acceptance_test_report.json" | ConvertFrom-Json
            
            Write-ColorOutput "   📈 测试结果摘要:" "Cyan"
            Write-ColorOutput "      综合评分: $($report.overall_score)%" "White"
            Write-ColorOutput "      总耗时: $($report.total_duration)" "White"
            
            if ($report.overall_score -ge 80) {
                Write-ColorOutput "      状态: ✅ 优秀" "Green"
            } elseif ($report.overall_score -ge 60) {
                Write-ColorOutput "      状态: ⚠️ 良好" "Yellow"
            } else {
                Write-ColorOutput "      状态: ❌ 需要改进" "Red"
            }
        }
        
    }
    catch {
        Write-ColorOutput "   ❌ 生成报告失败: $_" "Red"
    }
    
    Write-Host ""
}

# 显示帮助信息
function Show-Help {
    Write-Host @"
系统性验收测试执行脚本 - 任务ID 27

用法:
    .\run_system_acceptance_test.ps1 [选项]

选项:
    -ServerUrl <URL>     指定测试服务器URL (默认: http://127.0.0.1:3000)
    -StartServer         自动启动Axum服务器
    -SkipPlaywright      跳过Playwright端到端测试
    -SkipPerformance     跳过性能测试
    -SkipSecurity        跳过安全测试
    -Verbose             显示详细输出
    -Help                显示此帮助信息

示例:
    .\run_system_acceptance_test.ps1
    .\run_system_acceptance_test.ps1 -StartServer -Verbose
    .\run_system_acceptance_test.ps1 -ServerUrl "http://localhost:8080" -SkipPlaywright

"@
}

# 主程序入口
if ($args -contains "-Help" -or $args -contains "--help" -or $args -contains "-h") {
    Show-Help
    exit 0
}

try {
    Main
    Write-ColorOutput "🎉 系统性验收测试执行成功完成！" "Green"
    exit 0
}
catch {
    Write-ColorOutput "💥 系统性验收测试执行失败: $_" "Red"
    Write-ColorOutput "📋 请检查错误信息并重新运行测试" "Yellow"
    exit 1
}
