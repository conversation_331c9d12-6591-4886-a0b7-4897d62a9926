//! # 路由处理器模块
//!
//! 包含所有HTTP请求处理器的实现，采用模块化DDD架构

pub mod auth;
pub mod cache;
pub mod chat;
pub mod database_health;
pub mod health;
pub mod monitoring;
pub mod query_optimization;
pub mod task;
pub mod user;
pub mod websocket;

// 单元测试模块
#[cfg(test)]
pub mod tests;

// 重新导出常用类型
pub use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::{IntoResponse, Json},
};
pub use chrono::{DateTime, Utc};
pub use serde::{Deserialize, Serialize};
pub use uuid::Uuid;

// 重新导出应用服务（只导出服务，不导出DTO类型）
// 注意：这些服务通过依赖注入容器使用，不需要直接导入
// pub use app_application::{...};
pub use app_common::error::{AppError, Result};
pub use app_common::utils::error_response::ErrorHandler;

// 重新导出API契约层类型
pub use app_interfaces::*;

/// 通用错误响应结构
#[derive(Debug, Serialize)]
pub struct ErrorResponse {
    pub error: String,
    pub message: String,
}

/// 将AppError转换为HTTP响应的辅助函数
///
/// 【重构说明】：使用统一的错误处理器，消除重复的错误处理逻辑
pub fn app_error_to_response(error: AppError) -> axum::response::Response {
    // 使用统一的错误处理器
    ErrorHandler::handle_app_error(&error).build()
}

/// 创建成功响应的辅助函数（使用统一的ApiResponse格式）
pub fn success_response<T: Serialize>(data: T, message: &str) -> Json<ApiResponse<T>> {
    Json(ApiResponse::success_with_message(data, message.to_string()))
}
