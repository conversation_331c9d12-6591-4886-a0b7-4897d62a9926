// 认证相关的E2E测试辅助函数

use super::E2EConfig;
use anyhow::Result;
use serde_json::{Value, json};

/// 认证辅助结构
pub struct AuthHelper {
    config: E2EConfig,
    client: reqwest::Client,
}

impl AuthHelper {
    /// 创建新的认证辅助实例
    pub fn new(config: E2EConfig) -> Self {
        Self {
            config,
            client: reqwest::Client::new(),
        }
    }

    /// 用户注册
    pub async fn register_user(
        &self,
        username: &str,
        _email: &str, // 保持接口兼容性，但不使用email
        password: &str,
    ) -> Result<Value> {
        let url = format!("{}/api/auth/register", self.config.base_url);
        let payload = json!({
            "username": username,
            "password": password,
            "confirm_password": password  // 使用confirm_password而不是email
        });

        let response = self.client.post(&url).json(&payload).send().await?;

        let status = response.status();
        let body: Value = response.json().await?;

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 用户登录
    pub async fn login_user(&self, username: &str, password: &str) -> Result<Value> {
        let url = format!("{}/api/auth/login", self.config.base_url);
        let payload = json!({
            "username": username,
            "password": password
        });

        let response = self.client.post(&url).json(&payload).send().await?;

        let status = response.status();
        let response_text = response.text().await?;

        println!("🔍 登录响应状态: {}", status);
        println!("🔍 登录响应内容: {}", response_text);

        // 尝试解析JSON，如果失败则返回错误信息
        let body: Value = if response_text.trim().is_empty() {
            json!({"error": "Empty response from server"})
        } else {
            match serde_json::from_str(&response_text) {
                Ok(json) => json,
                Err(e) => {
                    println!("❌ JSON解析失败: {}", e);
                    json!({"error": format!("JSON parse error: {}", e), "raw_response": response_text})
                }
            }
        };

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 获取JWT Token
    pub async fn get_auth_token(&self, username: &str, password: &str) -> Result<String> {
        let login_result = self.login_user(username, password).await?;

        if login_result["status"].as_u64().unwrap_or(0) == 200 {
            // 根据实际的响应格式获取access_token
            if let Some(token) = login_result["body"]["data"]["access_token"].as_str() {
                return Ok(token.to_string());
            }
        }

        Err(anyhow::anyhow!("无法获取认证令牌"))
    }

    /// 验证JWT Token
    pub async fn verify_token(&self, token: &str) -> Result<Value> {
        let url = format!("{}/api/auth/verify", self.config.base_url);

        let response = self.client.get(&url).bearer_auth(token).send().await?;

        let status = response.status();
        let response_text = response.text().await?;

        println!("🔍 令牌验证响应状态: {}", status);
        println!("🔍 令牌验证响应内容: {}", response_text);

        // 尝试解析JSON，如果失败则返回错误信息
        let body: Value = if response_text.trim().is_empty() {
            json!({"error": "Empty response from server"})
        } else {
            match serde_json::from_str(&response_text) {
                Ok(json) => json,
                Err(e) => {
                    println!("❌ JSON解析失败: {}", e);
                    json!({"error": format!("JSON parse error: {}", e), "raw_response": response_text})
                }
            }
        };

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 用户登出
    pub async fn logout_user(&self, token: &str) -> Result<Value> {
        let url = format!("{}/api/auth/logout", self.config.base_url);

        let response = self.client.post(&url).bearer_auth(token).send().await?;

        let status = response.status();

        // 尝试解析JSON，如果失败则使用错误信息
        let body = match response.json::<Value>().await {
            Ok(json_body) => json_body,
            Err(_) => {
                json!({
                    "error": "response_parse_error",
                    "message": "无法解析响应为JSON"
                })
            }
        };

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 刷新JWT Token
    pub async fn refresh_token(&self, token: &str) -> Result<Value> {
        let url = format!("{}/api/auth/refresh", self.config.base_url);

        let response = self.client.post(&url).bearer_auth(token).send().await?;

        let status = response.status();
        let body: Value = response.json().await?;

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 验证Token是否过期
    pub async fn check_token_expiry(&self, token: &str) -> Result<Value> {
        let url = format!("{}/api/auth/check-expiry", self.config.base_url);

        let response = self.client.get(&url).bearer_auth(token).send().await?;

        let status = response.status();
        let body: Value = response.json().await?;

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 获取当前用户信息（需要认证）
    pub async fn get_current_user(&self, token: &str) -> Result<Value> {
        let url = format!("{}/api/auth/me", self.config.base_url);

        let response = self.client.get(&url).bearer_auth(token).send().await?;

        let status = response.status();
        let body: Value = response.json().await?;

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 测试无效格式的Token
    pub async fn test_malformed_token(&self, malformed_token: &str) -> Result<Value> {
        let url = format!("{}/api/auth/verify", self.config.base_url);

        let response = self
            .client
            .get(&url)
            .bearer_auth(malformed_token)
            .send()
            .await?;

        let status = response.status();
        let body: Value = response.json().await?;

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 测试过期的Token
    pub async fn test_expired_token(&self, expired_token: &str) -> Result<Value> {
        let url = format!("{}/api/auth/verify", self.config.base_url);

        let response = self
            .client
            .get(&url)
            .bearer_auth(expired_token)
            .send()
            .await?;

        let status = response.status();
        let body: Value = response.json().await?;

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 创建带认证的HTTP客户端
    pub fn create_authenticated_client(&self, token: &str) -> reqwest::Client {
        let mut headers = reqwest::header::HeaderMap::new();
        headers.insert(
            reqwest::header::AUTHORIZATION,
            reqwest::header::HeaderValue::from_str(&format!("Bearer {}", token)).unwrap(),
        );

        reqwest::Client::builder()
            .default_headers(headers)
            .build()
            .unwrap()
    }

    /// 验证登录响应格式
    pub fn validate_login_response(&self, response: &Value) -> Result<()> {
        // 验证必需字段存在
        if !response["data"]["access_token"].is_string() {
            return Err(anyhow::anyhow!("登录响应缺少access_token字段"));
        }

        if !response["data"]["token_type"].is_string() {
            return Err(anyhow::anyhow!("登录响应缺少token_type字段"));
        }

        if !response["data"]["expires_in"].is_number() {
            return Err(anyhow::anyhow!("登录响应缺少expires_in字段"));
        }

        if !response["data"]["user"].is_object() {
            return Err(anyhow::anyhow!("登录响应缺少user字段"));
        }

        // 验证用户信息字段
        let user = &response["data"]["user"];
        if !user["id"].is_string() {
            return Err(anyhow::anyhow!("用户信息缺少id字段"));
        }

        if !user["username"].is_string() {
            return Err(anyhow::anyhow!("用户信息缺少username字段"));
        }

        if !user["email"].is_string() {
            return Err(anyhow::anyhow!("用户信息缺少email字段"));
        }

        Ok(())
    }

    /// 验证Token验证响应格式
    pub fn validate_token_response(&self, response: &Value) -> Result<()> {
        // 验证必需字段存在
        if !response["data"]["sub"].is_string() {
            return Err(anyhow::anyhow!("Token验证响应缺少sub字段"));
        }

        if !response["data"]["username"].is_string() {
            return Err(anyhow::anyhow!("Token验证响应缺少username字段"));
        }

        if !response["data"]["exp"].is_number() {
            return Err(anyhow::anyhow!("Token验证响应缺少exp字段"));
        }

        if !response["data"]["iat"].is_number() {
            return Err(anyhow::anyhow!("Token验证响应缺少iat字段"));
        }

        Ok(())
    }
}
