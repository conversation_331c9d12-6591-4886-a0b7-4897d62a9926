# 端到端测试文档

## 概述

本目录包含了Axum企业级后端项目的完整端到端测试套件，用于验证所有API端点的功能正确性、性能表现和错误处理能力。

## 测试架构

### 技术栈
- **测试框架**: Rust内置测试框架 + Tokio异步运行时
- **HTTP客户端**: reqwest 0.12.22
- **JSON处理**: serde_json
- **错误处理**: anyhow
- **UUID生成**: uuid (用于避免测试数据冲突)

### 测试文件结构
```
tests/
├── README.md                           # 本文档
├── comprehensive_e2e_api_test.rs        # 主要的端到端测试套件
└── e2e_complete_test.rs                # 原有的端到端测试文件
```

## 主要测试套件

### comprehensive_e2e_api_test.rs

这是我们的核心端到端测试文件，包含以下功能：

#### 1. 测试客户端封装 (`ApiTestClient`)
- 支持所有HTTP方法（GET、POST、PUT、DELETE）
- 自动处理JWT认证令牌
- 请求超时控制
- 详细的日志输出
- 服务器启动等待功能

#### 2. 测试覆盖范围

**健康检查和监控端点**
- `/api/health` - 基础健康检查
- `/api/health/deep` - 深度健康检查
- `/api/performance/health` - 性能健康检查
- `/api/health/database` - 数据库健康检查
- `/metrics` - Prometheus指标端点

**用户认证功能**
- `/api/auth/register` - 用户注册
- `/api/auth/login` - 用户登录
- `/api/auth/logout` - 用户登出
- 无效登录处理测试

**任务管理CRUD操作**
- `/api/tasks` - 获取任务列表
- `/api/tasks` - 创建新任务
- `/api/tasks/{id}` - 获取单个任务
- `/api/tasks/{id}` - 更新任务
- `/api/tasks/{id}` - 删除任务

**缓存监控端点**
- `/api/cache/stats` - 缓存统计
- `/api/cache/health` - 缓存健康检查
- `/api/cache/pool` - 缓存连接池状态

**WebSocket相关端点**
- `/api/websocket/stats` - WebSocket统计
- `/api/websocket/connections` - WebSocket连接信息
- `/api/websocket/metrics` - WebSocket指标

**错误处理和边缘情况**
- 404错误处理
- 无效JSON请求处理
- 未授权访问处理
- 无效任务ID处理

#### 3. 测试功能特性

**并发测试支持**
- 支持多个并发客户端同时测试
- 测试数据隔离（使用UUID生成唯一用户名）
- 并发安全的测试结果统计

**详细的测试报告**
- 测试通过/失败统计
- 详细的错误信息记录
- 测试执行时间统计
- 彩色输出和emoji标识

**灵活的配置**
- 可配置的服务器地址和端口
- 可配置的请求超时时间
- 可配置的测试用户信息
- 详细日志开关

## 使用方法

### 1. 运行单个测试

```bash
# 运行健康检查测试
cargo test --test comprehensive_e2e_api_test test_health_endpoints_integration

# 运行认证测试
cargo test --test comprehensive_e2e_api_test test_authentication_integration

# 运行任务管理测试
cargo test --test comprehensive_e2e_api_test test_task_management_integration
```

### 2. 运行完整测试套件

```bash
# 运行所有端到端测试
cargo test --test comprehensive_e2e_api_test

# 运行测试并显示详细输出
cargo test --test comprehensive_e2e_api_test -- --nocapture
```

### 3. 使用自动化测试脚本

```bash
# 运行完整的自动化测试流程（启动服务器 + 运行测试 + 生成报告）
cargo run --bin run_e2e_tests

# 运行详细模式
cargo run --bin run_e2e_tests -- --verbose
```

### 4. 运行并发测试

```bash
# 直接运行测试文件的main函数进行并发测试
cargo run --test comprehensive_e2e_api_test concurrent
```

## 测试前提条件

### 1. 服务器启动
确保Axum服务器在 `http://127.0.0.1:3000` 上运行：
```bash
cargo run --bin axum-tutorial
```

### 2. 数据库连接
确保PostgreSQL数据库正在运行并且可以连接。

### 3. 缓存服务
确保DragonflyDB缓存服务正在运行（如果相关端点需要）。

## 测试数据管理

### 用户数据隔离
- 每次测试运行都会生成唯一的测试用户名（使用UUID前8位）
- 格式：`testuser_{uuid_prefix}`
- 避免了并发测试和重复运行的数据冲突

### 任务数据清理
- 测试创建的任务会在测试结束时自动删除
- 使用事务回滚机制确保数据库状态一致

## 性能基准

### 测试执行时间
- 单个健康检查测试：~1-2秒
- 完整认证流程测试：~3-5秒
- 任务管理CRUD测试：~5-8秒
- 完整测试套件：~20-30秒

### 并发性能
- 支持5-10个并发客户端同时测试
- 每个并发客户端独立的数据空间
- 总并发测试时间：~30-60秒

## 故障排除

### 常见问题

1. **服务器连接失败**
   - 检查服务器是否在正确端口运行
   - 检查防火墙设置
   - 确认数据库连接正常

2. **认证测试失败**
   - 检查JWT密钥配置
   - 确认用户注册端点正常工作
   - 检查密码哈希算法配置

3. **任务管理测试失败**
   - 确认数据库表结构正确
   - 检查SeaORM实体定义
   - 验证数据库权限设置

4. **超时错误**
   - 增加测试配置中的超时时间
   - 检查服务器性能和负载
   - 确认网络连接稳定

### 调试技巧

1. **启用详细日志**
   ```bash
   RUST_LOG=debug cargo test --test comprehensive_e2e_api_test -- --nocapture
   ```

2. **单独运行失败的测试**
   ```bash
   cargo test --test comprehensive_e2e_api_test test_specific_function -- --nocapture
   ```

3. **检查服务器日志**
   在另一个终端窗口中运行服务器并观察日志输出。

## 扩展和定制

### 添加新的测试端点
1. 在相应的测试函数组中添加新的测试函数
2. 更新测试结果统计
3. 添加相应的错误处理

### 修改测试配置
修改 `TestConfig` 结构体中的默认值：
```rust
impl Default for TestConfig {
    fn default() -> Self {
        Self {
            base_url: "http://127.0.0.1:3000".to_string(),
            timeout: Duration::from_secs(30),
            // ... 其他配置
        }
    }
}
```

### 添加新的测试类型
1. 创建新的测试函数组
2. 在主测试执行函数中调用
3. 更新测试报告生成逻辑

## 最佳实践

1. **测试隔离**: 每个测试应该独立运行，不依赖其他测试的状态
2. **数据清理**: 测试后清理创建的数据，避免影响后续测试
3. **错误处理**: 提供详细的错误信息，便于问题定位
4. **性能考虑**: 避免过长的测试执行时间，合理设置超时
5. **文档更新**: 添加新功能时及时更新测试文档

## 贡献指南

1. 遵循现有的代码风格和注释规范
2. 为新功能添加相应的测试用例
3. 更新相关文档
4. 确保所有测试通过后再提交代码

---

**注意**: 这个测试套件是为学习和实践Axum框架的高级特性而设计的，为构建支持百万吞吐量百万并发的企业级移动手机聊天室应用后端项目奠定技术基础。
