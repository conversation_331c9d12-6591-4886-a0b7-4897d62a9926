//! # HTML测试报告模板生成器
//!
//! 提供可重用的HTML模板，用于展示测试结果
//!
//! ## 功能特性
//! - 响应式设计
//! - 动态数据填充
//! - 图表集成支持
//! - 多主题支持

use super::*;
use anyhow::{ Result, Context };
use std::fs;
use std::path::Path;

/// HTML模板生成器
pub struct HtmlTemplateGenerator {
    /// 模板配置
    config: ReportConfig,
}

impl HtmlTemplateGenerator {
    /// 创建新的HTML模板生成器
    pub fn new(config: ReportConfig) -> Self {
        Self { config }
    }

    /// 生成完整的HTML报告
    pub fn generate_html_report(&self, report: &TestReport) -> Result<String> {
        let html_content = self.build_html_template(report)?;

        // 确保输出目录存在
        let output_dir = Path::new(&self.config.output_dir);
        if !output_dir.exists() {
            fs
                ::create_dir_all(output_dir)
                .with_context(|| format!("创建输出目录失败: {}", self.config.output_dir))?;
        }

        // 写入HTML文件
        let html_path = output_dir.join("test_report.html");
        fs
            ::write(&html_path, &html_content)
            .with_context(|| format!("写入HTML报告失败: {:?}", html_path))?;

        println!("✅ HTML测试报告已生成: {:?}", html_path);
        Ok(html_content)
    }

    /// 构建HTML模板
    fn build_html_template(&self, report: &TestReport) -> Result<String> {
        let html = format!(
            r#"<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{} - 测试报告</title>
    <style>
        {}
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        {}
        {}
        {}
        {}
        {}
    </div>
    <script>
        {}
    </script>
</body>
</html>"#,
            self.config.project_name,
            self.get_css_styles(),
            self.generate_header(report),
            self.generate_summary_section(report),
            self.generate_charts_section(report),
            self.generate_test_suites_section(report),
            self.generate_footer(report),
            self.get_javascript_code(report)
        );

        Ok(html)
    }

    /// 获取CSS样式
    fn get_css_styles(&self) -> &'static str {
        r#"
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f7fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .summary-card:hover {
            transform: translateY(-5px);
        }

        .summary-card .number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .summary-card .label {
            color: #666;
            font-size: 1.1em;
        }

        .passed { color: #27ae60; }
        .failed { color: #e74c3c; }
        .skipped { color: #f39c12; }
        .error { color: #8e44ad; }

        .charts-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
        }

        .chart-container {
            position: relative;
            height: 300px;
        }

        .test-suites {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .suite-header {
            background: #34495e;
            color: white;
            padding: 20px;
            font-size: 1.3em;
            font-weight: bold;
        }

        .suite-content {
            padding: 20px;
        }

        .test-case {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s ease;
        }

        .test-case:hover {
            background-color: #f8f9fa;
        }

        .test-case:last-child {
            border-bottom: none;
        }

        .test-name {
            flex: 1;
            font-weight: 500;
        }

        .test-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }

        .status-passed {
            background: #d4edda;
            color: #155724;
        }

        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }

        .status-skipped {
            background: #fff3cd;
            color: #856404;
        }

        .status-error {
            background: #e2e3e5;
            color: #383d41;
        }

        .test-duration {
            margin-left: 15px;
            color: #666;
            font-size: 0.9em;
        }

        .error-details {
            background: #f8f9fa;
            border-left: 4px solid #e74c3c;
            padding: 15px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            white-space: pre-wrap;
        }

        .footer {
            text-align: center;
            padding: 30px;
            color: #666;
            border-top: 1px solid #eee;
            margin-top: 30px;
        }

        .section-title {
            font-size: 1.8em;
            margin-bottom: 20px;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .summary {
                grid-template-columns: 1fr;
            }
            
            .charts-grid {
                grid-template-columns: 1fr;
            }
        }
        "#
    }

    /// 生成页面头部
    fn generate_header(&self, report: &TestReport) -> String {
        format!(
            r#"<div class="header">
                <h1>{}</h1>
                <div class="subtitle">
                    版本: {} | 生成时间: {}
                </div>
            </div>"#,
            self.config.project_name,
            self.config.project_version,
            report.timestamp.format("%Y-%m-%d %H:%M:%S UTC")
        )
    }

    /// 生成摘要部分
    fn generate_summary_section(&self, report: &TestReport) -> String {
        let summary = &report.summary;
        format!(
            r#"<div class="summary">
                <div class="summary-card">
                    <div class="number">{}</div>
                    <div class="label">总测试数</div>
                </div>
                <div class="summary-card">
                    <div class="number passed">{}</div>
                    <div class="label">通过</div>
                </div>
                <div class="summary-card">
                    <div class="number failed">{}</div>
                    <div class="label">失败</div>
                </div>
                <div class="summary-card">
                    <div class="number skipped">{}</div>
                    <div class="label">跳过</div>
                </div>
                <div class="summary-card">
                    <div class="number">{:.1}%</div>
                    <div class="label">成功率</div>
                </div>
                <div class="summary-card">
                    <div class="number">{:.2}s</div>
                    <div class="label">总耗时</div>
                </div>
            </div>"#,
            summary.total_tests,
            summary.passed_tests,
            summary.failed_tests,
            summary.skipped_tests,
            summary.success_rate,
            (summary.total_duration_ms as f64) / 1000.0
        )
    }

    /// 生成图表部分
    fn generate_charts_section(&self, _report: &TestReport) -> String {
        if !self.config.include_charts {
            return String::new();
        }

        r#"<div class="charts-section">
            <h2 class="section-title">📊 测试结果可视化</h2>
            <div class="charts-grid">
                <div class="chart-container">
                    <canvas id="statusChart"></canvas>
                </div>
                <div class="chart-container">
                    <canvas id="durationChart"></canvas>
                </div>
            </div>
        </div>"#.to_string()
    }

    /// 生成测试套件部分
    fn generate_test_suites_section(&self, report: &TestReport) -> String {
        let mut suites_html = String::new();

        suites_html.push_str(r#"<div class="test-suites">"#);
        suites_html.push_str(r#"<div class="suite-header">🧪 测试套件详情</div>"#);

        for suite in &report.test_suites {
            suites_html.push_str(
                &format!(
                    r#"<div class="suite-content">
                    <h3>{}</h3>
                    <div class="suite-stats">
                        通过: {} | 失败: {} | 跳过: {} | 成功率: {:.1}%
                    </div>
                    <div class="test-cases">"#,
                    suite.name,
                    suite.passed_count,
                    suite.failed_count,
                    suite.skipped_count,
                    suite.success_rate()
                )
            );

            for test_case in &suite.test_cases {
                let status_class = match test_case.status {
                    TestStatus::Passed => "status-passed",
                    TestStatus::Failed => "status-failed",
                    TestStatus::Skipped => "status-skipped",
                    TestStatus::Error => "status-error",
                };

                suites_html.push_str(
                    &format!(
                        r#"<div class="test-case">
                        <div class="test-name">{}</div>
                        <div class="test-status {}">
                            {}
                        </div>
                        <div class="test-duration">{}ms</div>
                    </div>"#,
                        test_case.name,
                        status_class,
                        test_case.status,
                        test_case.duration_ms
                    )
                );

                // 如果有错误信息，显示错误详情
                if let Some(error) = &test_case.error_message {
                    suites_html.push_str(
                        &format!(
                            r#"<div class="error-details">{}</div>"#,
                            html_escape::encode_text(error)
                        )
                    );
                }
            }

            suites_html.push_str("</div></div>");
        }

        suites_html.push_str("</div>");
        suites_html
    }

    /// 生成页脚
    fn generate_footer(&self, report: &TestReport) -> String {
        format!(
            r#"<div class="footer">
                <p>报告生成于 {} | 运行环境: {} {} | Rust版本: {}</p>
                <p>由 Axum 测试报告生成器自动生成</p>
            </div>"#,
            report.timestamp.format("%Y-%m-%d %H:%M:%S UTC"),
            report.environment.os,
            report.environment.arch,
            report.environment.rust_version
        )
    }

    /// 获取JavaScript代码
    fn get_javascript_code(&self, report: &TestReport) -> String {
        if !self.config.include_charts {
            return String::new();
        }

        let summary = &report.summary;

        format!(
            r#"
            // 测试状态饼图
            const statusCtx = document.getElementById('statusChart').getContext('2d');
            new Chart(statusCtx, {{
                type: 'doughnut',
                data: {{
                    labels: ['通过', '失败', '跳过', '错误'],
                    datasets: [{{
                        data: [{}, {}, {}, {}],
                        backgroundColor: [
                            '#27ae60',
                            '#e74c3c',
                            '#f39c12',
                            '#8e44ad'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }}]
                }},
                options: {{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {{
                        title: {{
                            display: true,
                            text: '测试状态分布'
                        }},
                        legend: {{
                            position: 'bottom'
                        }}
                    }}
                }}
            }});

            // 测试套件执行时间柱状图
            const durationCtx = document.getElementById('durationChart').getContext('2d');
            new Chart(durationCtx, {{
                type: 'bar',
                data: {{
                    labels: [{}],
                    datasets: [{{
                        label: '执行时间 (ms)',
                        data: [{}],
                        backgroundColor: 'rgba(52, 152, 219, 0.8)',
                        borderColor: 'rgba(52, 152, 219, 1)',
                        borderWidth: 1
                    }}]
                }},
                options: {{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {{
                        title: {{
                            display: true,
                            text: '测试套件执行时间'
                        }}
                    }},
                    scales: {{
                        y: {{
                            beginAtZero: true,
                            title: {{
                                display: true,
                                text: '时间 (毫秒)'
                            }}
                        }}
                    }}
                }}
            }});
            "#,
            summary.passed_tests,
            summary.failed_tests,
            summary.skipped_tests,
            summary.error_tests,
            report.test_suites
                .iter()
                .map(|s| format!("'{}'", s.name))
                .collect::<Vec<_>>()
                .join(", "),
            report.test_suites
                .iter()
                .map(|s| s.total_duration_ms.to_string())
                .collect::<Vec<_>>()
                .join(", ")
        )
    }
}
