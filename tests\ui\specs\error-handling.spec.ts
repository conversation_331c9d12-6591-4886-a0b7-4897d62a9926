import { test, expect } from '@playwright/test';
import { MainPage } from '../helpers/main-page';
import { testUsers, errorMessages, testConfig } from '../fixtures/test-data';

/**
 * 错误处理和提示测试套件
 * 遵循Context7 MCP最佳实践，测试错误提示机制
 */
test.describe('错误处理测试', () => {
  let mainPage: MainPage;

  test.beforeEach(async ({ page }) => {
    mainPage = new MainPage(page);
    // 使用静态文件进行测试
    await page.goto('file://' + process.cwd() + '/static/index.html');
    await mainPage.waitForPageLoad();
  });

  test.describe('表单验证错误', () => {
    test('应该显示必填字段错误提示', async ({ page }) => {
      // 查找必填输入框
      const requiredInputs = page.locator('input[required]');
      const requiredCount = await requiredInputs.count();
      
      if (requiredCount > 0) {
        const firstRequired = requiredInputs.first();
        
        // 尝试提交空表单
        await firstRequired.focus();
        await firstRequired.blur();
        
        // 验证HTML5验证消息
        const validationMessage = await firstRequired.evaluate(el => 
          (el as HTMLInputElement).validationMessage
        );
        
        if (validationMessage) {
          expect(validationMessage.length).toBeGreaterThan(0);
        }
      }
    });

    test('应该显示邮箱格式错误提示', async ({ page }) => {
      const emailInputs = page.locator('input[type="email"]');
      const emailCount = await emailInputs.count();
      
      if (emailCount > 0) {
        const emailInput = emailInputs.first();
        
        // 输入无效邮箱格式
        await emailInput.fill('invalid-email');
        await emailInput.blur();
        
        // 验证HTML5验证消息
        const validationMessage = await emailInput.evaluate(el => 
          (el as HTMLInputElement).validationMessage
        );
        
        if (validationMessage) {
          expect(validationMessage.length).toBeGreaterThan(0);
        }
        
        // 验证输入框无效状态
        const isValid = await emailInput.evaluate(el => 
          (el as HTMLInputElement).validity.valid
        );
        expect(isValid).toBe(false);
      }
    });

    test('应该显示密码长度错误提示', async ({ page }) => {
      const passwordInputs = page.locator('input[type="password"]');
      const passwordCount = await passwordInputs.count();
      
      if (passwordCount > 0) {
        const passwordInput = passwordInputs.first();
        
        // 检查是否有最小长度要求
        const minLength = await passwordInput.getAttribute('minlength');
        
        if (minLength) {
          const minLengthNum = parseInt(minLength);
          
          // 输入过短密码
          await passwordInput.fill('123');
          await passwordInput.blur();
          
          // 验证HTML5验证消息
          const validationMessage = await passwordInput.evaluate(el => 
            (el as HTMLInputElement).validationMessage
          );
          
          if (validationMessage) {
            expect(validationMessage.length).toBeGreaterThan(0);
          }
        }
      }
    });

    test('应该显示自定义错误消息', async ({ page }) => {
      // 查找错误消息容器
      const errorContainers = page.locator('.error, .error-message, .auth-message.error, [role="alert"]');
      const errorCount = await errorContainers.count();
      
      // 如果页面有错误消息容器，验证其样式
      if (errorCount > 0) {
        const firstError = errorContainers.first();
        
        // 验证错误容器的样式
        const color = await firstError.evaluate(el => 
          window.getComputedStyle(el).color
        );
        
        const backgroundColor = await firstError.evaluate(el => 
          window.getComputedStyle(el).backgroundColor
        );
        
        // 错误消息应该有明显的视觉样式
        expect(color).toBeTruthy();
        expect(backgroundColor).toBeTruthy();
      }
    });
  });

  test.describe('网络错误处理', () => {
    test('应该处理网络连接失败', async ({ page }) => {
      // 模拟网络错误
      await page.route('**/*', route => {
        route.abort('failed');
      });
      
      // 尝试触发网络请求（如果有的话）
      const buttons = page.locator('button');
      const buttonCount = await buttons.count();
      
      if (buttonCount > 0) {
        // 点击第一个按钮可能会触发网络请求
        const firstButton = buttons.first();
        
        // 监听控制台错误
        const consoleErrors: string[] = [];
        page.on('console', msg => {
          if (msg.type() === 'error') {
            consoleErrors.push(msg.text());
          }
        });
        
        try {
          await firstButton.click({ timeout: 5000 });
        } catch (error) {
          // 预期可能会有超时或网络错误
        }
        
        // 验证是否有适当的错误处理
        // 这里不强制要求特定的错误处理，因为静态页面可能没有网络请求
      }
    });

    test('应该处理服务器错误响应', async ({ page }) => {
      // 模拟服务器错误
      await page.route('**/api/**', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Internal Server Error' })
        });
      });
      
      // 查找可能触发API请求的元素
      const apiButtons = page.locator('button[data-api], .api-button, button:has-text("登录"), button:has-text("注册")');
      const apiButtonCount = await apiButtons.count();
      
      if (apiButtonCount > 0) {
        const apiButton = apiButtons.first();
        
        try {
          await apiButton.click({ timeout: 5000 });
        } catch (error) {
          // 预期可能会有错误
        }
        
        // 等待可能的错误消息显示
        await page.waitForTimeout(1000);
        
        // 查找错误消息
        const errorMessages = page.locator('.error, .error-message, [role="alert"]');
        // 不强制要求错误消息，因为静态页面可能没有错误处理逻辑
      }
    });
  });

  test.describe('用户输入错误', () => {
    test('应该处理特殊字符输入', async ({ page }) => {
      const textInputs = page.locator('input[type="text"], input:not([type]), textarea');
      const inputCount = await textInputs.count();
      
      if (inputCount > 0) {
        const textInput = textInputs.first();
        
        // 输入特殊字符
        const specialChars = '<script>alert("xss")</script>';
        await textInput.fill(specialChars);
        
        // 验证输入值
        const inputValue = await textInput.inputValue();
        expect(inputValue).toBe(specialChars);
        
        // 验证页面没有执行脚本（XSS防护）
        const alerts: string[] = [];
        page.on('dialog', dialog => {
          alerts.push(dialog.message());
          dialog.dismiss();
        });
        
        await page.waitForTimeout(1000);
        expect(alerts.length).toBe(0);
      }
    });

    test('应该处理超长文本输入', async ({ page }) => {
      const textInputs = page.locator('input[type="text"], textarea');
      const inputCount = await textInputs.count();
      
      if (inputCount > 0) {
        const textInput = textInputs.first();
        
        // 输入超长文本
        const longText = 'A'.repeat(1000);
        await textInput.fill(longText);
        
        // 验证输入处理
        const inputValue = await textInput.inputValue();
        
        // 检查是否有最大长度限制
        const maxLength = await textInput.getAttribute('maxlength');
        
        if (maxLength) {
          const maxLengthNum = parseInt(maxLength);
          expect(inputValue.length).toBeLessThanOrEqual(maxLengthNum);
        } else {
          expect(inputValue.length).toBeGreaterThan(0);
        }
      }
    });

    test('应该处理Unicode字符输入', async ({ page }) => {
      const textInputs = page.locator('input[type="text"], textarea');
      const inputCount = await textInputs.count();
      
      if (inputCount > 0) {
        const textInput = textInputs.first();
        
        // 输入Unicode字符
        const unicodeText = '测试 🚀 Hello こんにちは 안녕하세요 العربية';
        await textInput.fill(unicodeText);
        
        // 验证Unicode字符正确处理
        const inputValue = await textInput.inputValue();
        expect(inputValue).toBe(unicodeText);
      }
    });
  });

  test.describe('界面错误状态', () => {
    test('应该正确显示禁用状态', async ({ page }) => {
      const buttons = page.locator('button');
      const buttonCount = await buttons.count();

      if (buttonCount > 0) {
        // 查找禁用按钮
        const disabledButtons = page.locator('button:disabled, button[disabled]');
        const disabledCount = await disabledButtons.count();

        if (disabledCount > 0) {
          const disabledButton = disabledButtons.first();

          // 验证禁用状态
          await expect(disabledButton).toBeDisabled();

          // 验证禁用按钮的不透明度或其他视觉指示
          const opacity = await disabledButton.evaluate(el =>
            window.getComputedStyle(el).opacity
          );

          // 禁用按钮通常有较低的不透明度
          const opacityValue = parseFloat(opacity);
          expect(opacityValue).toBeLessThanOrEqual(1);
        } else {
          // 如果没有禁用按钮，测试通过（这是正常情况）
          console.log('No disabled buttons found - this is acceptable');
        }
      }
    });

    test('应该正确显示加载状态', async ({ page }) => {
      // 查找可能的加载指示器
      const loadingElements = page.locator('.loading, .spinner, [aria-busy="true"]');
      const loadingCount = await loadingElements.count();
      
      // 如果有加载元素，验证其样式
      if (loadingCount > 0) {
        const loadingElement = loadingElements.first();
        
        // 验证加载元素可见性
        const isVisible = await loadingElement.isVisible();
        
        if (isVisible) {
          // 验证加载动画或样式
          const animation = await loadingElement.evaluate(el => 
            window.getComputedStyle(el).animation
          );
          
          // 加载元素应该有动画或特殊样式
          expect(animation).toBeTruthy();
        }
      }
    });

    test('应该正确处理空状态', async ({ page }) => {
      // 查找可能的空状态容器
      const emptyContainers = page.locator('.empty, .no-data, .empty-state');
      const emptyCount = await emptyContainers.count();
      
      if (emptyCount > 0) {
        const emptyContainer = emptyContainers.first();
        
        // 验证空状态消息
        const emptyText = await emptyContainer.textContent();
        expect(emptyText).toBeTruthy();
        expect(emptyText!.length).toBeGreaterThan(0);
      }
    });
  });

  test.describe('错误恢复机制', () => {
    test('应该支持错误重试', async ({ page }) => {
      // 查找重试按钮
      const retryButtons = page.locator('button:has-text("重试"), button:has-text("再试一次"), .retry-button');
      const retryCount = await retryButtons.count();
      
      if (retryCount > 0) {
        const retryButton = retryButtons.first();
        
        // 验证重试按钮可点击
        await expect(retryButton).toBeVisible();
        await expect(retryButton).toBeEnabled();
      }
    });

    test('应该支持错误清除', async ({ page }) => {
      // 查找错误清除按钮
      const clearButtons = page.locator('button:has-text("清除"), button:has-text("关闭"), .close-button, .clear-error');
      const clearCount = await clearButtons.count();
      
      if (clearCount > 0) {
        const clearButton = clearButtons.first();
        
        // 验证清除按钮可点击
        await expect(clearButton).toBeVisible();
        await expect(clearButton).toBeEnabled();
      }
    });
  });
});
