# Debug authentication issue
$baseUrl = "http://127.0.0.1:3000"

Write-Host "=== 调试认证问题 ===" -ForegroundColor Cyan

# 1. 登录并获取详细信息
Write-Host "`n1. 登录测试..." -ForegroundColor Yellow
$loginBody = @{
    username = "testuser456"
    password = "password123"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/auth/login" -Method Post -ContentType "application/json" -Body $loginBody
    Write-Host "登录响应:" -ForegroundColor Green
    $loginResponse | ConvertTo-Json -Depth 3 | Write-Host
    
    $token = $loginResponse.data.token
    Write-Host "`n获取的Token: $($token.Substring(0,50))..." -ForegroundColor Cyan
    
    # 2. 测试受保护的用户端点
    Write-Host "`n2. 测试用户端点..." -ForegroundColor Yellow
    $headers = @{ "Authorization" = "Bearer $token" }
    
    try {
        $userResponse = Invoke-RestMethod -Uri "$baseUrl/api/users/online-users" -Method Get -Headers $headers
        Write-Host "用户端点成功:" -ForegroundColor Green
        $userResponse | ConvertTo-Json -Depth 2 | Write-Host
    } catch {
        Write-Host "用户端点失败: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        }
    }
    
    # 3. 测试消息搜索端点
    Write-Host "`n3. 测试消息搜索端点..." -ForegroundColor Yellow
    try {
        $searchResponse = Invoke-RestMethod -Uri "$baseUrl/api/messages/search?keyword=test" -Method Get -Headers $headers
        Write-Host "搜索端点成功:" -ForegroundColor Green
        $searchResponse | ConvertTo-Json -Depth 2 | Write-Host
    } catch {
        Write-Host "搜索端点失败: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
            try {
                $errorStream = $_.Exception.Response.GetResponseStream()
                $reader = New-Object System.IO.StreamReader($errorStream)
                $errorBody = $reader.ReadToEnd()
                Write-Host "错误响应体: $errorBody" -ForegroundColor Red
            } catch {
                Write-Host "无法读取错误响应体" -ForegroundColor Red
            }
        }
    }
    
    # 4. 测试任务端点
    Write-Host "`n4. 测试任务端点..." -ForegroundColor Yellow
    try {
        $taskResponse = Invoke-RestMethod -Uri "$baseUrl/api/tasks" -Method Get -Headers $headers
        Write-Host "任务端点成功:" -ForegroundColor Green
        $taskResponse | ConvertTo-Json -Depth 2 | Write-Host
    } catch {
        Write-Host "任务端点失败: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        }
    }
    
} catch {
    Write-Host "登录失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host "`n=== 调试完成 ===" -ForegroundColor Cyan
