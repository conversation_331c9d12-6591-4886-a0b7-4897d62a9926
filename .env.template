# Axum Tutorial 环境配置模板
# 复制此文件为 .env 并根据您的环境进行配置

# HTTP 服务器配置
HTTP_ADDR=127.0.0.1:3000

# 数据库配置 - PostgreSQL 17
DATABASE_URL=postgres://axum_user:axum_secure_password_2025@localhost:5432/axum_tutorial

# PostgreSQL连接池配置
MAX_CONNECTIONS=1000
MIN_CONNECTIONS=10
CONNECTION_TIMEOUT=30
IDLE_TIMEOUT=600
ACQUIRE_TIMEOUT=30

# =============================================================================
# 缓存配置 - DragonflyDB (选择适合您环境的配置)
# =============================================================================

# 选项1: WSL2 + Podman (当前使用)
# 如果您使用WSL2和Podman，请使用WSL2的实际IP地址
# 获取IP: wsl hostname -I
CACHE_URL=redis://:dragonfly_secure_password_2025@************:6379

# 选项2: Docker Desktop (推荐)
# 如果您使用Docker Desktop，可以直接使用localhost
# CACHE_URL=redis://:dragonfly_secure_password_2025@localhost:6379

# 选项3: 本地Redis/DragonflyDB
# 如果您在本地直接运行Redis或DragonflyDB
# CACHE_URL=redis://:your_password@localhost:6379

# 选项4: 远程缓存服务
# 如果您使用云端或远程缓存服务
# CACHE_URL=redis://:your_password@your_remote_host:6379

# 缓存配置参数
CACHE_DEFAULT_TTL=3600
CACHE_KEY_PREFIX=axum_tutorial:

# JWT 密钥配置（开发环境）
JWT_SECRET=your-secret-key-change-in-production

# 运行环境
ENVIRONMENT=development

# 日志级别
RUST_LOG=info

# =============================================================================
# 环境检测配置 (可选)
# =============================================================================

# 强制指定环境类型，跳过自动检测
# ENV_TYPE=wsl2_podman | docker_desktop | native | remote
# ENV_TYPE=wsl2_podman

# WSL2自动IP检测 (启用/禁用)
# AUTO_DETECT_WSL2_IP=true

# 连接测试超时 (秒)
# CONNECTION_TEST_TIMEOUT=5
