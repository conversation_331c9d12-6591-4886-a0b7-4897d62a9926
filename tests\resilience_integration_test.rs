//! # 弹性功能集成测试
//!
//! 测试熔断器、限流器和降级策略的集成功能

use app_infrastructure::{
    CircuitBreakerConfig, CircuitBreakerError, RateLimiterConfig, RateLimiterType,
    ResilienceConfig, ResilienceManager,
};
use std::time::Duration;
use tokio::time::sleep;
use tracing::info;

/// 测试弹性管理器的创建和基本功能
#[tokio::test]
async fn test_resilience_manager_creation() {
    // 初始化日志
    let _ = tracing_subscriber::fmt::try_init();

    info!("🧪 开始测试弹性管理器创建");

    // 创建弹性配置
    let config = ResilienceConfig {
        circuit_breaker: CircuitBreakerConfig {
            failure_threshold: 3,
            timeout_seconds: 5,
            max_timeout_seconds: 30,
            success_threshold: 2,
        },
        rate_limiter: RateLimiterConfig {
            requests_per_second: 10,
            burst_capacity: 20,
            name: "test_limiter".to_string(),
            enabled: true,
        },
        enabled: true,
        monitoring_interval_seconds: 10,
    };

    // 创建弹性管理器
    let manager = ResilienceManager::new(config).await;
    assert!(manager.is_ok(), "弹性管理器创建应该成功");

    let manager = manager.unwrap();

    // 测试获取熔断器
    let circuit_breaker = manager.get_circuit_breaker("cache").await;
    assert!(circuit_breaker.is_some(), "应该能获取到缓存熔断器");

    info!("✅ 弹性管理器创建测试通过");
}

/// 测试限流功能
#[tokio::test]
async fn test_rate_limiting() {
    let _ = tracing_subscriber::fmt::try_init();

    info!("🧪 开始测试限流功能");

    let config = ResilienceConfig::default();
    let manager = ResilienceManager::new(config).await.unwrap();

    // 测试全局限流
    let mut success_count = 0;
    let mut failure_count = 0;

    // 快速发送多个请求
    for i in 0..50 {
        let result = manager
            .check_rate_limit(RateLimiterType::Global, "test", 1.0)
            .await;

        if result {
            success_count += 1;
        } else {
            failure_count += 1;
        }

        if i % 10 == 0 {
            info!(
                "已发送 {} 个请求，成功: {}, 失败: {}",
                i + 1,
                success_count,
                failure_count
            );
        }
    }

    info!(
        "限流测试结果 - 成功: {}, 失败: {}",
        success_count, failure_count
    );

    // 应该有一些请求被限流
    assert!(failure_count > 0, "应该有请求被限流");
    assert!(success_count > 0, "应该有请求成功");

    info!("✅ 限流功能测试通过");
}

/// 测试用户级限流
#[tokio::test]
async fn test_user_rate_limiting() {
    let _ = tracing_subscriber::fmt::try_init();

    info!("🧪 开始测试用户级限流");

    let config = ResilienceConfig::default();
    let manager = ResilienceManager::new(config).await.unwrap();

    let user_id = uuid::Uuid::new_v4().to_string();

    // 测试用户级限流
    let mut success_count = 0;
    let mut failure_count = 0;

    for _ in 0..30 {
        let result = manager
            .check_rate_limit(RateLimiterType::UserBased, &user_id, 1.0)
            .await;

        if result {
            success_count += 1;
        } else {
            failure_count += 1;
        }
    }

    info!(
        "用户级限流测试结果 - 成功: {}, 失败: {}",
        success_count, failure_count
    );

    // 应该有一些请求被限流
    assert!(failure_count > 0, "用户级限流应该生效");
    assert!(success_count > 0, "应该有请求成功");

    info!("✅ 用户级限流测试通过");
}

/// 测试熔断器功能
#[tokio::test]
async fn test_circuit_breaker() {
    let _ = tracing_subscriber::fmt::try_init();

    info!("🧪 开始测试熔断器功能");

    let config = ResilienceConfig {
        circuit_breaker: CircuitBreakerConfig {
            failure_threshold: 2, // 2次失败后开启熔断器
            timeout_seconds: 1,   // 1秒后尝试恢复
            max_timeout_seconds: 5,
            success_threshold: 1,
        },
        ..Default::default()
    };

    let manager = ResilienceManager::new(config).await.unwrap();
    let circuit_breaker = manager.get_circuit_breaker("cache").await.unwrap();

    // 模拟连续失败操作
    info!("模拟连续失败操作");
    for i in 0..3 {
        let result = circuit_breaker
            .execute(async { Err::<(), String>("模拟失败".to_string()) })
            .await;

        match result {
            Err(CircuitBreakerError::OperationFailed(_)) => {
                info!("第 {} 次操作失败", i + 1);
            }
            Err(CircuitBreakerError::CircuitOpen) => {
                info!("第 {} 次操作被熔断器拒绝", i + 1);
                break;
            }
            _ => {
                panic!("意外的结果");
            }
        }
    }

    // 等待熔断器恢复
    info!("等待熔断器恢复...");
    sleep(Duration::from_secs(2)).await;

    // 测试成功操作
    let result = circuit_breaker
        .execute(async { Ok::<i32, String>(42) })
        .await;

    match result {
        Ok(value) => {
            info!("熔断器恢复后操作成功，返回值: {}", value);
            assert_eq!(value, 42);
        }
        Err(e) => {
            panic!("熔断器恢复后操作应该成功，但得到错误: {:?}", e);
        }
    }

    info!("✅ 熔断器功能测试通过");
}

/// 测试统计信息
#[tokio::test]
async fn test_resilience_stats() {
    let _ = tracing_subscriber::fmt::try_init();

    info!("🧪 开始测试弹性统计信息");

    let config = ResilienceConfig::default();
    let manager = ResilienceManager::new(config).await.unwrap();

    // 记录一些成功和失败
    manager.record_success().await;
    manager.record_success().await;
    manager.record_failure().await;

    // 触发一些限流
    for _ in 0..50 {
        manager
            .check_rate_limit(RateLimiterType::Global, "test", 1.0)
            .await;
    }

    let stats = manager.get_stats().await;

    info!("弹性统计信息: {:?}", stats);

    assert_eq!(stats.successful_requests, 2, "成功请求数应该为2");
    assert_eq!(stats.failed_requests, 1, "失败请求数应该为1");
    assert!(stats.rate_limit_rejections > 0, "应该有限流拒绝");

    // 重置统计信息
    manager.reset_stats().await;
    let reset_stats = manager.get_stats().await;

    assert_eq!(
        reset_stats.successful_requests, 0,
        "重置后成功请求数应该为0"
    );
    assert_eq!(reset_stats.failed_requests, 0, "重置后失败请求数应该为0");

    info!("✅ 弹性统计信息测试通过");
}

/// 测试降级策略
#[tokio::test]
async fn test_fallback_strategy() {
    let _ = tracing_subscriber::fmt::try_init();

    info!("🧪 开始测试降级策略");

    let config = ResilienceConfig::default();
    let manager = ResilienceManager::new(config).await.unwrap();

    // 创建一个搜索请求
    let request = app_domain::entities::chat::SearchGlobalChatRoomMessagesRequest {
        query: "test query".to_string(),
        limit: Some(10),
        start_time: None,
        end_time: None,
        sender_id: None,
    };

    // 测试限流降级
    let fallback_result = manager
        .execute_fallback("rate_limit", &request, "测试限流降级")
        .await;

    assert!(fallback_result.is_ok(), "降级策略应该成功执行");

    let result = fallback_result.unwrap();
    assert!(result.is_fallback, "结果应该标记为降级结果");
    assert!(!result.fallback_reason.is_empty(), "应该有降级原因");

    info!("降级结果: {:?}", result);
    info!("✅ 降级策略测试通过");
}

#[tokio::test]
async fn test_comprehensive_resilience() {
    let _ = tracing_subscriber::fmt::try_init();

    info!("🧪 开始综合弹性测试");

    let config = ResilienceConfig::default();
    let manager = ResilienceManager::new(config).await.unwrap();

    // 启动监控（在后台运行）
    manager.start_monitoring().await;

    // 模拟各种场景
    info!("模拟混合负载场景");

    for i in 0..20 {
        // 模拟不同用户的请求
        let user_id = format!("user_{}", i % 5);

        // 检查限流
        let rate_limit_ok = manager
            .check_rate_limit(RateLimiterType::UserBased, &user_id, 1.0)
            .await;

        if rate_limit_ok {
            // 模拟操作成功/失败
            if i % 7 == 0 {
                manager.record_failure().await;
            } else {
                manager.record_success().await;
            }
        }

        // 短暂延迟
        sleep(Duration::from_millis(50)).await;
    }

    let final_stats = manager.get_stats().await;
    info!("综合测试最终统计: {:?}", final_stats);

    assert!(final_stats.successful_requests > 0, "应该有成功请求");

    info!("✅ 综合弹性测试通过");
}
