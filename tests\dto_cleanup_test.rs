//! DTO清理测试
//!
//! 验证DTO定义的唯一性和正确性

use app_interfaces::{Claims, LoginRequest, RegisterRequest};

#[test]
fn test_dto_uniqueness() {
    // 验证DTO类型可以正常使用
    let register_req = RegisterRequest {
        username: "test_user".to_string(),
        password: "password123".to_string(),
        confirm_password: "password123".to_string(),
        email: Some("<EMAIL>".to_string()),
    };

    assert_eq!(register_req.username, "test_user");

    let login_req = LoginRequest {
        username: "test_user".to_string(),
        password: "password123".to_string(),
    };

    assert_eq!(login_req.username, "test_user");

    let claims = Claims {
        sub: "user_id".to_string(),
        username: "test_user".to_string(),
        exp: 1234567890,
        iat: 1234567890,
    };

    assert_eq!(claims.username, "test_user");
}

#[test]
fn test_dto_serialization() {
    let register_req = RegisterRequest {
        username: "test_user".to_string(),
        password: "password123".to_string(),
        confirm_password: "password123".to_string(),
        email: Some("<EMAIL>".to_string()),
    };

    // 测试序列化
    let json = serde_json::to_string(&register_req).unwrap();
    assert!(json.contains("test_user"));

    // 测试反序列化
    let deserialized: RegisterRequest = serde_json::from_str(&json).unwrap();
    assert_eq!(deserialized.username, "test_user");
}
