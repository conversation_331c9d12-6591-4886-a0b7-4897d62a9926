# Axum Tutorial 独立迁移工具演示脚本

Write-Host "🚀 Axum Tutorial 独立迁移工具演示" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# 1. 检查数据库连接
Write-Host "`n1. 检查数据库连接..." -ForegroundColor Yellow
cargo run -p migration check

# 2. 查看迁移状态
Write-Host "`n2. 查看迁移状态..." -ForegroundColor Yellow
cargo run -p migration status

# 3. 显示帮助信息
Write-Host "`n3. 显示帮助信息..." -ForegroundColor Yellow
cargo run -p migration -- --help

Write-Host "`n✅ 迁移工具演示完成！" -ForegroundColor Green
Write-Host "`n📋 可用命令总结:" -ForegroundColor Cyan
Write-Host "  cargo run -p migration check     # 检查数据库连接" -ForegroundColor White
Write-Host "  cargo run -p migration status    # 查看迁移状态" -ForegroundColor White
Write-Host "  cargo run -p migration up        # 执行迁移" -ForegroundColor White
Write-Host "  cargo run -p migration down      # 回滚迁移" -ForegroundColor White
Write-Host "  cargo run -p migration reset --confirm  # 重置数据库" -ForegroundColor White

Write-Host "`nBest Practice Deployment Flow:" -ForegroundColor Cyan
Write-Host "  1. cargo run -p migration status  # Check migration status" -ForegroundColor White
Write-Host "  2. cargo run -p migration up      # Execute migrations" -ForegroundColor White
Write-Host "  3. cargo run -p migration status  # Verify migration results" -ForegroundColor White
Write-Host "  4. cd server && cargo run         # Start application server" -ForegroundColor White
