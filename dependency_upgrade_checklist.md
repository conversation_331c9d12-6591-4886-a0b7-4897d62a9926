# 依赖升级清单 - SQLite到PostgreSQL+DragonflyDB

**生成时间**: 2025-07-19
**项目**: Axum Tutorial 企业级架构升级
**目标**: SQLite → PostgreSQL 17 + DragonflyDB

## 🔍 当前依赖分析

### SQLite相关依赖 (需要更新)

```toml
# 当前配置 - 需要更改features
sea-orm = { version = "1.1.12", features = ["sqlx-sqlite", "runtime-tokio-native-tls", "mock"] }
sea-orm-migration = { version = "1.1.12", features = ["runtime-tokio-native-tls", "sqlx-sqlite"] }
```

## 📦 需要添加的新依赖

### 1. PostgreSQL支持

```toml
# 主要依赖更新 (Cargo.toml)
[workspace.dependencies]
# 更新SeaORM特性：sqlx-sqlite → sqlx-postgres
sea-orm = { version = "1.1.12", features = ["sqlx-postgres", "runtime-tokio-native-tls", "mock"] }
sea-orm-migration = { version = "1.1.12", features = ["runtime-tokio-native-tls", "sqlx-postgres"] }
```

### 2. DragonflyDB缓存支持

```toml
# Fred - 现代异步Redis/DragonflyDB客户端
fred = { version = "6.0", features = ["enable-tls"] }

# 可选：连接池管理
deadpool-redis = "0.14"  # 如果需要额外的连接池管理

# 可选：序列化支持
serde_json = "1.0"  # 已存在，用于缓存数据序列化
```

### 3. 容器化支持依赖

```toml
# 环境变量管理 (已存在，确保版本)
dotenvy = "0.15"

# 配置管理增强
config = "0.14"  # 可选：更强大的配置管理
```

## 🔧 具体更新步骤

### 步骤1: 更新workspace依赖

**文件**: `Cargo.toml`

```toml
[workspace.dependencies]
# 更新SeaORM特性
sea-orm = { version = "1.1.12", features = ["sqlx-postgres", "runtime-tokio-native-tls", "mock"] }
sea-orm-migration = { version = "1.1.12", features = ["runtime-tokio-native-tls", "sqlx-postgres"] }

# 添加Fred缓存客户端
fred = { version = "6.0", features = ["enable-tls"] }
```

### 步骤2: 更新主项目依赖

**文件**: `Cargo.toml` (主依赖部分)

```toml
[dependencies]
# 更新SeaORM特性
sea-orm = { version = "1.1.12", features = ["sqlx-postgres", "runtime-tokio-native-tls", "mock"] }

# 添加Fred缓存客户端
fred = { version = "6.0", features = ["enable-tls"] }
```

### 步骤3: 更新migration依赖

**文件**: `migration/Cargo.toml`

```toml
[dependencies]
# 使用workspace版本 (自动继承postgres特性)
sea-orm-migration = { workspace = true }
sea-orm = { workspace = true }
```

### 步骤4: 更新infrastructure依赖

**文件**: `crates/app_infrastructure/Cargo.toml`

```toml
[dependencies]
# 使用workspace版本
sea-orm = { workspace = true }
sea-orm-migration = { workspace = true }

# 添加缓存客户端
fred = { workspace = true }
```

## 🌐 环境配置更新

### 新增环境变量

**文件**: `.env.example`

```bash
# PostgreSQL配置
DATABASE_URL=postgres://postgres:password@localhost:5432/axum_tutorial
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=axum_tutorial

# DragonflyDB配置
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600
CACHE_PREFIX=axum_tutorial:
CACHE_MAX_CONNECTIONS=50

# 容器配置
CONTAINER_POSTGRES_PORT=5432
CONTAINER_REDIS_PORT=6379
```

## 🐳 容器配置文件

### podman-compose.yml (新建)

```yaml
version: '3.8'
services:
  postgres:
    image: postgres:17
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: axum_tutorial
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  dragonfly:
    image: docker.dragonflydb.io/dragonflydb/dragonfly:latest
    ports:
      - "6379:6379"
    volumes:
      - dragonfly_data:/data

volumes:
  postgres_data:
  dragonfly_data:
```

## ✅ 验证清单

### 编译验证

```bash
# 1. 清理构建缓存
cargo clean

# 2. 检查依赖解析
cargo check --workspace

# 3. 构建所有目标
cargo build --workspace

# 4. 运行测试
cargo test --workspace
```

### 功能验证

```bash
# 1. 启动容器
podman-compose up -d

# 2. 运行迁移
cargo run -p migration

# 3. 启动应用
cargo run -p server

# 4. 测试API端点
curl http://localhost:3000/api/health
```

## 🔄 依赖版本兼容性

### 确认版本

- **SeaORM**: 1.1.12 (保持不变)
- **Fred**: 6.0 (最新稳定版)
- **Tokio**: 1.45.1 (保持不变)
- **Axum**: 0.8.4 (保持不变)

### 特性标志变更

| 组件 | 当前特性 | 新特性 |
|------|----------|--------|
| sea-orm | sqlx-sqlite | sqlx-postgres |
| sea-orm-migration | sqlx-sqlite | sqlx-postgres |
| fred | - | enable-tls |

## 🚨 注意事项

1. **版本锁定**: 确保所有SeaORM相关依赖使用相同版本
2. **特性一致性**: 所有crate必须使用相同的数据库特性
3. **编译顺序**: 先更新workspace依赖，再更新各crate
4. **测试覆盖**: 每个步骤后都要验证编译和基本功能
5. **回滚准备**: 保留原始配置备份以便快速回滚

## 📋 执行顺序

1. ✅ 分析当前依赖 (已完成)
2. ✅ 备份SQLite配置 (已完成)
3. ⏳ 更新workspace依赖
4. ⏳ 更新各crate依赖
5. ⏳ 验证编译状态
6. ⏳ 创建容器配置
7. ⏳ 更新环境配置
8. ⏳ 测试基本功能
