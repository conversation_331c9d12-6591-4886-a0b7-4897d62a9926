//! # 路由模块
//!
//! 定义所有HTTP路由和处理器，采用模块化DDD+整洁架构设计
//!
//! ## 企业级架构设计原则
//! - **模块化DDD**: 按领域边界组织路由（用户域、任务域、聊天域等）
//! - **整洁架构**: 清晰的依赖方向，外层依赖内层
//! - **职责单一**: 每个路由组只负责一个特定的业务领域
//! - **接口隔离**: 公开API与内部管理API分离
//! - **依赖倒置**: 通过AppState注入依赖，而非直接依赖具体实现
//!
//! ## 路由组织结构
//! - `/api/v1/auth/*` - 认证与授权域
//! - `/api/v1/users/*` - 用户管理域
//! - `/api/v1/tasks/*` - 任务管理域
//! - `/api/v1/chat/*` - 聊天通信域
//! - `/api/v1/ws/*` - WebSocket实时通信
//! - `/api/system/*` - 系统监控与管理（内部API）
//! - `/metrics` - Prometheus标准指标端点

use app_common::{
    logging::{request_logging_middleware, trace_layer},
    middleware::{
        create_default_permission_checker, create_permission_middleware_state,
        inject_authenticated_user, inject_permission_state, require_admin,
        require_delete_permission, require_manager, require_write_permission,
    },
};
use axum::{
    Router, middleware,
    routing::{delete, get, post, put},
};
use std::sync::Arc;
use tower::ServiceBuilder;
use tower_http::{cors::CorsLayer, services::ServeDir};

// 处理器模块
pub mod handlers;

// 重新导出处理器
pub use handlers::*;

// 导入应用服务trait
use app_application::{
    ChatApplicationService, TaskApplicationService, UserApplicationService,
    WebSocketApplicationService,
};
use sea_orm::DatabaseConnection;

/// 应用状态结构体
///
/// 包含所有应用服务和共享资源，通过依赖注入模式提供给各个处理器
#[derive(Clone)]
pub struct AppState {
    /// 用户应用服务
    pub user_service: Arc<dyn UserApplicationService>,
    /// 任务应用服务
    pub task_service: Arc<dyn TaskApplicationService>,
    /// 聊天应用服务（基础版本）
    pub chat_service: Arc<dyn ChatApplicationService>,
    /// 弹性聊天应用服务（企业级版本，集成TASK_52搜索系统）
    pub resilient_chat_service: Option<Arc<app_application::ResilientChatApplicationService>>,
    /// WebSocket应用服务
    pub websocket_service: Arc<dyn WebSocketApplicationService>,
    /// 数据库连接
    #[allow(dead_code)]
    pub db: Arc<DatabaseConnection>,
    /// JWT密钥
    pub jwt_secret: String,
}

/// 创建认证域路由（公开API）
///
/// 认证与授权领域的公开接口，遵循DDD领域边界
/// 包含用户注册、登录等无需认证的操作
fn create_auth_public_routes(app_state: AppState) -> Router {
    Router::new()
        // 用户注册
        .route("/register", post(handlers::auth::register))
        // 用户登录
        .route("/login", post(handlers::auth::login))
        // 用户名可用性检查（公开接口，属于用户域但放在认证路由中便于管理）
        .route("/check-username", get(handlers::user::get_username_availability))
        .with_state(app_state)
}

/// 创建认证域路由（受保护API）
///
/// 认证与授权领域的受保护接口，需要JWT认证
/// 包含登出、会话管理等需要认证的操作
fn create_auth_protected_routes(app_state: AppState) -> Router {
    Router::new()
        // 用户登出
        .route("/logout", post(handlers::auth::logout))
        .with_state(app_state)
}

/// 创建任务域路由
///
/// 任务管理领域的完整CRUD操作，遵循RESTful API设计原则
/// 应用细粒度权限控制：读取需要基础权限，创建/更新需要写权限，删除需要删除权限
fn create_task_domain_routes(app_state: AppState) -> Router {
    // 创建权限中间件状态
    let permission_state = create_permission_middleware_state(create_default_permission_checker());

    // 只读操作路由（需要基础认证）
    let read_routes = Router::new()
        .route("/", get(handlers::task::get_all_tasks))
        .route("/{id}", get(handlers::task::get_task_by_id))
        .with_state(app_state.clone());

    // 写操作路由（需要写权限）
    let write_routes = Router::new()
        .route("/", post(handlers::task::post_task))
        .route("/{id}", put(handlers::task::put_task))
        .layer(middleware::from_fn(require_write_permission()))
        .layer(middleware::from_fn(inject_authenticated_user()))
        .layer(middleware::from_fn_with_state(
            permission_state.clone(),
            inject_permission_state,
        ))
        .with_state(app_state.clone());

    // 删除操作路由（需要删除权限）
    let delete_routes = Router::new()
        .route("/{id}", delete(handlers::task::delete_task))
        .layer(middleware::from_fn(require_delete_permission()))
        .layer(middleware::from_fn(inject_authenticated_user()))
        .layer(middleware::from_fn_with_state(
            permission_state,
            inject_permission_state,
        ))
        .with_state(app_state);

    // 合并所有任务路由
    Router::new()
        .merge(read_routes)
        .merge(write_routes)
        .merge(delete_routes)
}

/// 创建用户域路由
///
/// 用户管理领域的信息查询和管理功能，遵循用户域边界
/// 应用细粒度权限控制：查看需要基础权限，修改个人资料需要写权限
fn create_user_domain_routes(app_state: AppState) -> Router {
    // 创建权限中间件状态
    let permission_state = create_permission_middleware_state(create_default_permission_checker());

    // 只读操作路由（需要基础认证）
    let read_routes = Router::new()
        .route("/me", get(handlers::user::get_current_user))
        .route("/me/profile", get(handlers::user::get_profile))
        .route("/{id}", get(handlers::user::get_user_by_id))
        .route("/online", get(handlers::user::get_online_users))
        .with_state(app_state.clone());

    // 写操作路由（需要写权限）
    let write_routes = Router::new()
        .route("/me/profile", put(handlers::user::put_profile))
        .layer(middleware::from_fn(require_write_permission()))
        .layer(middleware::from_fn(inject_authenticated_user()))
        .layer(middleware::from_fn_with_state(
            permission_state,
            inject_permission_state,
        ))
        .with_state(app_state);

    // 合并所有用户路由
    Router::new().merge(read_routes).merge(write_routes)
}

/// 创建聊天域路由
///
/// 聊天通信领域的消息管理和搜索功能，遵循聊天域边界
/// 所有操作都需要JWT认证，确保聊天安全性
fn create_chat_domain_routes(app_state: AppState) -> Router {
    Router::new()
        // 消息搜索功能
        .route("/messages/search", get(handlers::chat::get_chat_search))
        // 聊天室历史记录
        .route("/rooms/{id}/messages", get(handlers::chat::get_chat_history))
        .with_state(app_state)
}

/// 创建WebSocket实时通信路由
///
/// WebSocket实时通信的连接管理和监控功能
/// 连接端点需要认证，监控端点为系统内部使用
fn create_websocket_routes(app_state: AppState) -> Router {
    Router::new()
        // WebSocket连接端点（需要认证）
        .route("/", get(handlers::websocket::websocket_handler))
        // WebSocket实时监控端点（系统内部）
        .route("/monitoring", get(handlers::websocket::websocket_monitoring_handler))
        // WebSocket统计信息（系统内部）
        .route("/stats", get(handlers::websocket::get_websocket_stats))
        .route("/connections", get(handlers::websocket::get_websocket_connections))
        .route("/metrics", get(handlers::websocket::get_websocket_metrics))
        // WebSocket稳定性监控（系统内部）
        .route("/stability", get(handlers::websocket::get_websocket_stability))
        .with_state(app_state)
}

/// 创建监控面板路由
///
/// 提供WebSocket实时监控面板的HTML页面访问，无需认证
/// 这些是面向用户的监控界面，不属于API范畴
fn create_monitoring_dashboard_routes() -> Router {
    Router::new()
        // WebSocket监控面板页面
        .route("/websocket-stats.html", get(handlers::monitoring::websocket_stats_page))
        // 监控面板首页重定向
        .route("/monitoring", get(handlers::monitoring::monitoring_dashboard_redirect))
}

/// 创建系统管理路由 - 查询优化子模块
///
/// 数据库查询性能优化相关功能，属于系统管理域
/// 需要管理员权限（当前使用JWT认证）
fn create_system_query_routes(app_state: AppState) -> Router {
    Router::new()
        // 单个查询优化
        .route("/optimize", post(handlers::query_optimization::optimize_query))
        // 批量查询优化
        .route("/batch-optimize", post(handlers::query_optimization::batch_optimize_queries))
        // 数据库统计信息
        .route("/stats", get(handlers::query_optimization::get_database_stats))
        // 索引推荐
        .route(
            "/index-recommendations",
            get(handlers::query_optimization::get_index_recommendations)
        )
        .with_state(app_state)
}

/// 创建系统管理路由 - 缓存管理子模块
///
/// 缓存系统的监控和管理功能，属于系统管理域
/// 统计信息公开访问，管理操作需要管理员权限
fn create_system_cache_routes(app_state: AppState) -> Router {
    // 创建权限中间件状态
    let permission_state = create_permission_middleware_state(create_default_permission_checker());

    // 公开访问的缓存统计路由
    let public_cache_routes = Router::new()
        .route("/stats", get(handlers::cache::get_cache_stats))
        .route("/health", get(handlers::cache::get_cache_health))
        .route("/pool", get(handlers::cache::get_cache_pool_status))
        .with_state(app_state.clone());

    // 需要管理员权限的缓存管理路由
    let admin_cache_routes = Router::new()
        .route("/stats/reset", post(handlers::cache::reset_cache_stats))
        .layer(middleware::from_fn(require_admin()))
        .layer(middleware::from_fn(inject_authenticated_user()))
        .layer(middleware::from_fn_with_state(
            permission_state,
            inject_permission_state,
        ))
        .with_state(app_state);

    // 合并公开和管理员路由
    Router::new()
        .merge(public_cache_routes)
        .merge(admin_cache_routes)
}

/// 创建系统管理路由 - 性能监控子模块
///
/// 系统性能统计和指标监控，属于系统管理域
/// 专注于性能数据收集，公开访问无需认证
fn create_system_performance_routes(app_state: AppState) -> Router {
    Router::new()
        // 性能统计信息
        .route("/stats", get(handlers::health::get_performance_stats))
        .route("/async-stats", get(handlers::health::get_async_performance_stats))
        .route("/metrics", get(handlers::health::get_detailed_metrics))
        .route("/prometheus", get(handlers::health::get_prometheus_metrics))
        .with_state(app_state)
}

/// 创建系统管理路由 - 健康检查子模块
///
/// 系统健康检查服务，包含不同级别的健康检查
/// 遵循Kubernetes健康检查标准，公开访问无需认证
fn create_system_health_routes(app_state: AppState) -> Router {
    Router::new()
        // 基础健康检查 - 统一的健康检查接口
        .route("/", get(handlers::health::health_check))
        // 深度健康检查
        .route("/deep", get(handlers::health::deep_health_check))
        // Kubernetes风格的就绪和存活检查
        .route("/ready", get(handlers::health::readiness_check))
        .route("/live", get(handlers::health::liveness_check))
        .with_state(app_state)
}

/// 创建系统管理路由 - 数据库管理子模块
///
/// 数据库连接池和性能管理功能，属于系统管理域
/// 状态查询公开访问，管理操作需要认证
fn create_system_database_routes(app_state: AppState) -> Router {
    Router::new()
        // 数据库健康检查（公开访问）
        .route("/health", get(handlers::database_health::get_database_health))
        .route("/config", get(handlers::database_health::get_database_config))
        .route("/pool", get(handlers::database_health::get_database_pool_status))
        // 数据库管理操作（需要认证）
        .route("/stress-test", post(handlers::database_health::run_database_stress_test))
        .with_state(app_state)
}

/// 创建系统管理路由 - 监控告警子模块
///
/// 系统资源监控和告警检查功能，属于系统管理域
/// 公开访问无需认证，便于外部监控系统集成
fn create_system_monitoring_routes(app_state: AppState) -> Router {
    Router::new()
        // 系统资源监控和告警
        .route("/alerts", get(handlers::health::get_system_alerts))
        // 错误恢复状态监控
        .route("/error-recovery", get(handlers::health::error_recovery_status_handler))
        .with_state(app_state)
}

/// 创建标准Prometheus指标路由
///
/// 符合Prometheus监控系统标准，公开访问无需认证
/// 这是行业标准的指标端点，放在根路径下
fn create_prometheus_metrics_routes(app_state: AppState) -> Router {
    Router::new()
        // 标准Prometheus指标端点
        .route("/metrics", get(handlers::health::get_prometheus_metrics))
        .with_state(app_state)
}

/// 创建业务API路由（v1版本）
///
/// 组合所有需要JWT认证的业务API，遵循版本化API设计
/// 使用统一认证系统，包括JWT认证和权限控制中间件
fn create_business_api_v1_routes(app_state: AppState) -> Router {
    // 提取JWT密钥以避免借用检查器问题
    let jwt_secret = app_state.jwt_secret.clone();

    // 创建权限中间件状态
    let permission_state = create_permission_middleware_state(create_default_permission_checker());

    // 组合所有业务领域路由
    Router::new()
        // 认证域（受保护部分）
        .nest("/auth", create_auth_protected_routes(app_state.clone()))
        // 用户域
        .nest("/users", create_user_domain_routes(app_state.clone()))
        // 任务域
        .nest("/tasks", create_task_domain_routes(app_state.clone()))
        // 聊天域
        .nest("/chat", create_chat_domain_routes(app_state.clone()))
        // WebSocket实时通信（连接端点需要认证）
        .nest("/ws", create_websocket_routes(app_state.clone()))
        // 应用统一认证中间件栈（注意顺序：从外到内执行）
        .layer(middleware::from_fn(inject_authenticated_user()))
        .layer(middleware::from_fn_with_state(permission_state, inject_permission_state))
        .layer(
            middleware::from_fn(
                move |mut req: axum::extract::Request, next: axum::middleware::Next| {
                    let jwt_secret = jwt_secret.clone();
                    async move {
                        // 将JWT密钥注入到请求扩展中，供AuthenticatedUser提取器使用
                        req.extensions_mut().insert(jwt_secret);
                        next.run(req).await
                    }
                }
            )
        )
}

/// 创建系统管理API路由
///
/// 组合所有系统管理相关的API，部分需要认证，部分公开访问
/// 按管理功能模块组织，便于运维和监控
fn create_system_api_routes(app_state: AppState) -> Router {
    // 提取JWT密钥以避免借用检查器问题
    let jwt_secret = app_state.jwt_secret.clone();

    // 公开访问的系统API（无需认证）
    let public_system_routes = Router::new()
        // 健康检查
        .nest("/health", create_system_health_routes(app_state.clone()))
        // 性能监控
        .nest("/performance", create_system_performance_routes(app_state.clone()))
        // 缓存状态（只读）
        .nest("/cache", create_system_cache_routes(app_state.clone()))
        // 数据库状态（只读）
        .nest("/database", create_system_database_routes(app_state.clone()))
        // 监控告警
        .nest("/monitoring", create_system_monitoring_routes(app_state.clone()));

    // 创建权限中间件状态
    let permission_state = create_permission_middleware_state(create_default_permission_checker());

    // 需要管理员权限的系统管理API
    let protected_system_routes = Router::new()
        // 查询优化（管理员功能）
        .nest("/query", create_system_query_routes(app_state.clone()))
        // 应用管理员权限中间件栈
        .layer(middleware::from_fn(require_admin()))
        .layer(middleware::from_fn(inject_authenticated_user()))
        .layer(middleware::from_fn_with_state(permission_state, inject_permission_state))
        .layer(
            middleware::from_fn(
                move |mut req: axum::extract::Request, next: axum::middleware::Next| {
                    let jwt_secret = jwt_secret.clone();
                    async move {
                        req.extensions_mut().insert(jwt_secret);
                        next.run(req).await
                    }
                }
            )
        );

    // 合并公开和受保护的系统路由
    Router::new()
        .merge(public_system_routes)
        .merge(protected_system_routes)
}

/// 创建应用程序主路由
///
/// 采用企业级模块化DDD+整洁架构设计，组合所有路由模块
/// 遵循版本化API、领域边界分离、职责单一等设计原则
pub fn create_routes(app_state: AppState) -> Router {
    // Favicon处理器
    let favicon_handler = || async {
        // 返回简单的SVG favicon
        (
            [("content-type", "image/svg+xml")],
            r#"<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><text y="14" font-size="16">📋</text></svg>"#,
        )
    };

    // 静态文件服务 - 从项目根目录提供静态文件
    let static_service = ServeDir::new("static");

    // 构建企业级中间件栈
    let middleware_stack = ServiceBuilder::new()
        .layer(trace_layer())
        .layer(middleware::from_fn(request_logging_middleware))
        .layer(CorsLayer::permissive());

    // 组合所有路由模块，遵循企业级架构原则
    Router::new()
        // === 基础设施路由 ===
        // Favicon路由：专门处理 /favicon.ico 请求，避免404错误
        .route("/favicon.ico", get(favicon_handler))
        // 静态文件路由：明确处理 /static/* 路径
        .nest_service("/static", ServeDir::new("static"))
        // 监控面板路由（面向用户的监控界面）
        .merge(create_monitoring_dashboard_routes())

        // === 业务API路由（版本化） ===
        // v1版本业务API - 按领域边界组织
        .nest("/api/v1", create_business_api_v1_routes(app_state.clone()))
        // 认证域公开API（注册、登录等）
        .nest("/api/v1/auth", create_auth_public_routes(app_state.clone()))

        // === 系统管理API路由 ===
        // 系统管理和监控API - 按管理功能组织
        .nest("/api/system", create_system_api_routes(app_state.clone()))

        // === 标准监控端点 ===
        // Prometheus标准指标端点（行业标准，放在根路径）
        .merge(create_prometheus_metrics_routes(app_state))

        // === 静态文件服务 ===
        // 静态文件服务作为fallback，处理根路径和其他未匹配路径
        .fallback_service(static_service)

        // === 中间件栈 ===
        // 应用企业级中间件栈
        .layer(middleware_stack)
}
