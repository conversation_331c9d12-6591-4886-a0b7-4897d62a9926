import { test, expect } from '@playwright/test';
import { MainPage } from '../helpers/main-page';
import { testConfig } from '../fixtures/test-data';

/**
 * 数据验证测试套件
 * 验证页面数据正确加载和显示
 */
test.describe('数据验证测试', () => {
  let mainPage: MainPage;

  test.beforeEach(async ({ page }) => {
    mainPage = new MainPage(page);
    await page.goto('file://' + process.cwd() + '/static/index.html');
    await mainPage.waitForPageLoad();
  });

  test.describe('页面内容验证', () => {
    test('应该正确显示页面标题和元数据', async ({ page }) => {
      // 验证页面标题
      await expect(page).toHaveTitle(/Axum 任务管理系统/);
      
      // 验证页面语言设置
      const htmlLang = await page.locator('html').getAttribute('lang');
      expect(htmlLang).toBe('zh-CN');
      
      // 验证字符编码
      const charset = await page.locator('meta[charset]').getAttribute('charset');
      expect(charset).toBe('UTF-8');
      
      // 验证视口设置
      const viewport = await page.locator('meta[name="viewport"]').getAttribute('content');
      expect(viewport).toContain('width=device-width');
    });

    test('应该正确显示主要内容区域', async ({ page }) => {
      // 验证主容器存在且可见
      const mainContainer = page.locator('.main-container');
      await expect(mainContainer).toBeVisible();
      
      // 验证主容器使用Grid布局
      const display = await mainContainer.evaluate(el => 
        window.getComputedStyle(el).display
      );
      expect(display).toBe('grid');
      
      // 验证三个面板都存在
      const panels = page.locator('.panel');
      await expect(panels).toHaveCount(3);
      
      // 验证每个面板都有正确的结构
      for (let i = 0; i < 3; i++) {
        const panel = panels.nth(i);
        await expect(panel).toBeVisible();
        
        // 验证面板有标题
        const heading = panel.locator('h2, h3, h4').first();
        await expect(heading).toBeVisible();
        
        // 验证面板有内容
        const content = await panel.textContent();
        expect(content).toBeTruthy();
        expect(content!.length).toBeGreaterThan(0);
      }
    });

    test('应该正确显示认证面板内容', async ({ page }) => {
      const authPanel = page.locator('.panel').first();
      
      // 验证认证面板标题
      const title = authPanel.locator('h2').first();
      const titleText = await title.textContent();
      expect(titleText).toContain('用户认证');
      
      // 验证认证状态显示
      const authStatus = authPanel.locator('.auth-status, .status');
      if (await authStatus.count() > 0) {
        await expect(authStatus.first()).toBeVisible();
      }
      
      // 验证表单元素存在
      const forms = authPanel.locator('form');
      const formCount = await forms.count();
      expect(formCount).toBeGreaterThan(0);
      
      // 验证输入框标签
      const labels = authPanel.locator('label');
      const labelCount = await labels.count();
      if (labelCount > 0) {
        for (let i = 0; i < labelCount; i++) {
          const label = labels.nth(i);
          const labelText = await label.textContent();
          expect(labelText).toBeTruthy();
          expect(labelText!.length).toBeGreaterThan(0);
        }
      }
    });

    test('应该正确显示任务管理面板内容', async ({ page }) => {
      const taskPanel = page.locator('.panel').nth(1);
      
      // 验证任务面板标题
      const title = taskPanel.locator('h2').first();
      const titleText = await title.textContent();
      expect(titleText).toContain('任务管理');
      
      // 验证任务列表容器
      const taskList = taskPanel.locator('#taskList, .task-list');
      if (await taskList.count() > 0) {
        await expect(taskList.first()).toBeVisible();
        
        // 验证空状态提示
        const emptyMessage = taskList.first().locator('::before');
        // 注意：::before伪元素的内容检查在Playwright中比较复杂，这里简化处理
      }
      
      // 验证任务创建表单
      const taskForm = taskPanel.locator('form');
      if (await taskForm.count() > 0) {
        await expect(taskForm.first()).toBeVisible();
        
        // 验证表单输入框
        const inputs = taskForm.first().locator('input, textarea');
        const inputCount = await inputs.count();
        expect(inputCount).toBeGreaterThan(0);
      }
    });

    test('应该正确显示WebSocket聊天面板内容', async ({ page }) => {
      const chatPanel = page.locator('.panel').nth(2);
      
      // 验证聊天面板标题
      const title = chatPanel.locator('h2').first();
      const titleText = await title.textContent();
      expect(titleText).toContain('WebSocket');
      
      // 验证聊天区域结构
      const chatContainer = chatPanel.locator('.chat-hall-container, .chat-area');
      if (await chatContainer.count() > 0) {
        await expect(chatContainer.first()).toBeVisible();
      }
      
      // 验证连接状态显示
      const connectionStatus = chatPanel.locator('.connection-status');
      if (await connectionStatus.count() > 0) {
        await expect(connectionStatus.first()).toBeVisible();
        
        const statusText = await connectionStatus.first().textContent();
        expect(statusText).toBeTruthy();
      }
      
      // 验证控制按钮
      const buttons = chatPanel.locator('button');
      const buttonCount = await buttons.count();
      expect(buttonCount).toBeGreaterThan(0);
    });
  });

  test.describe('样式和布局验证', () => {
    test('应该应用正确的CSS样式', async ({ page }) => {
      const body = page.locator('body');
      
      // 验证字体设置
      const fontFamily = await body.evaluate(el => 
        window.getComputedStyle(el).fontFamily
      );
      expect(fontFamily).toContain('Segoe UI');
      
      // 验证背景色
      const backgroundColor = await body.evaluate(el => 
        window.getComputedStyle(el).backgroundColor
      );
      expect(backgroundColor).toBeTruthy();
      
      // 验证文字渲染优化
      const fontSmoothing = await body.evaluate(el => 
        window.getComputedStyle(el).webkitFontSmoothing
      );
      expect(fontSmoothing).toBe('antialiased');
    });

    test('应该正确应用面板样式', async ({ page }) => {
      const panels = page.locator('.panel');
      
      for (let i = 0; i < await panels.count(); i++) {
        const panel = panels.nth(i);
        
        // 验证圆角
        const borderRadius = await panel.evaluate(el => 
          window.getComputedStyle(el).borderRadius
        );
        expect(borderRadius).toBeTruthy();
        
        // 验证阴影
        const boxShadow = await panel.evaluate(el => 
          window.getComputedStyle(el).boxShadow
        );
        expect(boxShadow).not.toBe('none');
        
        // 验证内边距
        const padding = await panel.evaluate(el => 
          window.getComputedStyle(el).padding
        );
        expect(padding).toBeTruthy();
      }
    });

    test('应该支持响应式布局', async ({ page }) => {
      // 测试桌面布局
      await page.setViewportSize(testConfig.viewports.desktop);
      const mainContainer = page.locator('.main-container');
      
      const desktopColumns = await mainContainer.evaluate(el => 
        window.getComputedStyle(el).gridTemplateColumns
      );
      expect(desktopColumns).toBeTruthy();
      
      // 测试移动布局
      await page.setViewportSize(testConfig.viewports.mobile);
      await page.waitForTimeout(500); // 等待CSS过渡
      
      const mobileColumns = await mainContainer.evaluate(el => 
        window.getComputedStyle(el).gridTemplateColumns
      );
      
      // 移动端应该是单列布局
      expect(mobileColumns).not.toBe(desktopColumns);
    });
  });

  test.describe('交互元素验证', () => {
    test('应该正确配置表单元素', async ({ page }) => {
      const inputs = page.locator('input');
      const inputCount = await inputs.count();
      
      if (inputCount > 0) {
        for (let i = 0; i < Math.min(inputCount, 5); i++) { // 限制检查前5个输入框
          const input = inputs.nth(i);
          
          // 验证输入框可编辑
          const isEditable = await input.isEditable();
          if (isEditable) {
            // 验证输入框样式
            const borderRadius = await input.evaluate(el => 
              window.getComputedStyle(el).borderRadius
            );
            expect(borderRadius).toBeTruthy();
            
            // 验证字体设置
            const fontSize = await input.evaluate(el => 
              window.getComputedStyle(el).fontSize
            );
            expect(fontSize).toBeTruthy();
          }
        }
      }
    });

    test('应该正确配置按钮元素', async ({ page }) => {
      const buttons = page.locator('button:visible');
      const buttonCount = await buttons.count();
      
      if (buttonCount > 0) {
        for (let i = 0; i < Math.min(buttonCount, 3); i++) { // 限制检查前3个按钮
          const button = buttons.nth(i);
          
          // 验证按钮样式
          const backgroundColor = await button.evaluate(el => 
            window.getComputedStyle(el).backgroundColor
          );
          expect(backgroundColor).toBeTruthy();
          
          // 验证按钮文字
          const buttonText = await button.textContent();
          expect(buttonText).toBeTruthy();
          expect(buttonText!.length).toBeGreaterThan(0);
          
          // 验证按钮可点击性
          const cursor = await button.evaluate(el => 
            window.getComputedStyle(el).cursor
          );
          expect(cursor).toBe('pointer');
        }
      }
    });

    test('应该支持键盘导航', async ({ page }) => {
      // 测试Tab键导航
      await page.keyboard.press('Tab');
      
      // 验证有元素获得焦点
      const focusedElement = page.locator(':focus');
      const focusedCount = await focusedElement.count();
      expect(focusedCount).toBeGreaterThan(0);
      
      if (focusedCount > 0) {
        // 验证焦点样式
        const outline = await focusedElement.first().evaluate(el => 
          window.getComputedStyle(el).outline
        );
        // 焦点元素应该有轮廓或其他视觉指示
        expect(outline).toBeTruthy();
      }
    });
  });

  test.describe('性能和加载验证', () => {
    test('应该快速加载页面内容', async ({ page }) => {
      const startTime = Date.now();
      
      await page.goto('file://' + process.cwd() + '/static/index.html');
      await page.waitForLoadState('domcontentloaded');
      
      const loadTime = Date.now() - startTime;
      
      // 页面应该在2秒内加载完成
      expect(loadTime).toBeLessThan(2000);
    });

    test('应该正确处理图片和资源', async ({ page }) => {
      // 监听网络请求
      const failedRequests: string[] = [];
      
      page.on('requestfailed', request => {
        failedRequests.push(request.url());
      });
      
      await page.goto('file://' + process.cwd() + '/static/index.html');
      await page.waitForLoadState('networkidle');
      
      // 过滤掉预期的失败请求（如API调用）
      const criticalFailures = failedRequests.filter(url => 
        !url.includes('/api/') && 
        !url.includes('ws://') &&
        !url.includes('localhost:3000')
      );
      
      expect(criticalFailures.length).toBe(0);
    });

    test('应该没有JavaScript错误', async ({ page }) => {
      const consoleErrors: string[] = [];
      
      page.on('console', msg => {
        if (msg.type() === 'error') {
          consoleErrors.push(msg.text());
        }
      });
      
      await page.goto('file://' + process.cwd() + '/static/index.html');
      await page.waitForLoadState('networkidle');
      
      // 过滤掉预期的错误（如网络连接错误）
      const criticalErrors = consoleErrors.filter(error => 
        !error.includes('net::') && 
        !error.includes('Failed to load') &&
        !error.includes('WebSocket') &&
        !error.includes('fetch')
      );
      
      expect(criticalErrors.length).toBe(0);
    });
  });
});
