//! # 测试配置和工具
//!
//! 提供测试环境配置、测试数据生成、测试工具函数等。
//! 支持不同测试场景的配置管理。

use std::time::Duration;
use std::collections::HashMap;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use anyhow::Result;

/// 测试环境配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestEnvironmentConfig {
    /// 数据库配置
    pub database: DatabaseTestConfig,
    /// 缓存配置
    pub cache: CacheTestConfig,
    /// 性能测试配置
    pub performance: PerformanceTestConfig,
    /// 测试数据配置
    pub test_data: TestDataConfig,
}

/// 数据库测试配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseTestConfig {
    /// 测试数据库URL
    pub url: String,
    /// 最大连接数
    pub max_connections: u32,
    /// 连接超时时间（秒）
    pub connection_timeout_secs: u64,
    /// 是否启用SQL日志
    pub enable_sql_logging: bool,
    /// 测试表前缀
    pub table_prefix: String,
}

/// 缓存测试配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheTestConfig {
    /// 缓存服务器URL
    pub url: String,
    /// 连接池大小
    pub pool_size: u32,
    /// 默认TTL（秒）
    pub default_ttl_secs: u64,
    /// 测试键前缀
    pub key_prefix: String,
    /// 是否启用缓存统计
    pub enable_stats: bool,
}

/// 性能测试配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceTestConfig {
    /// 最大并发用户数
    pub max_concurrent_users: usize,
    /// 测试持续时间（秒）
    pub test_duration_secs: u64,
    /// 预热时间（秒）
    pub warmup_duration_secs: u64,
    /// 性能阈值
    pub thresholds: PerformanceThresholds,
}

/// 性能阈值配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceThresholds {
    /// 最大P99延迟（毫秒）
    pub max_p99_latency_ms: u64,
    /// 最大P95延迟（毫秒）
    pub max_p95_latency_ms: u64,
    /// 最小吞吐量（QPS）
    pub min_throughput_qps: f64,
    /// 最大错误率
    pub max_error_rate: f64,
    /// 最小缓存命中率
    pub min_cache_hit_ratio: f64,
}

/// 测试数据配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestDataConfig {
    /// 测试消息数量
    pub message_count: usize,
    /// 测试用户数量
    pub user_count: usize,
    /// 测试聊天室数量
    pub chat_room_count: usize,
    /// 消息内容模板
    pub message_templates: Vec<String>,
    /// 搜索查询模板
    pub search_query_templates: Vec<String>,
}

/// 测试数据生成器
pub struct TestDataGenerator {
    config: TestDataConfig,
}

impl TestDataGenerator {
    /// 创建新的测试数据生成器
    pub fn new(config: TestDataConfig) -> Self {
        Self { config }
    }

    /// 生成测试消息
    pub fn generate_test_messages(&self) -> Vec<TestMessage> {
        let mut messages = Vec::new();
        
        for i in 0..self.config.message_count {
            let template_index = i % self.config.message_templates.len();
            let content = self.config.message_templates[template_index]
                .replace("{index}", &i.to_string())
                .replace("{timestamp}", &Utc::now().format("%Y-%m-%d %H:%M:%S").to_string());
            
            let message = TestMessage {
                id: Uuid::new_v4(),
                content,
                sender_id: self.generate_user_id(i),
                chat_room_id: self.generate_chat_room_id(i),
                message_type: self.generate_message_type(i),
                created_at: Utc::now() - chrono::Duration::seconds((i as i64) * 60),
                metadata: self.generate_metadata(i),
                priority: (i % 10) as i32,
                is_pinned: i % 100 == 0,
            };
            
            messages.push(message);
        }
        
        messages
    }

    /// 生成测试搜索查询
    pub fn generate_test_queries(&self) -> Vec<TestSearchQuery> {
        let mut queries = Vec::new();
        
        for (i, template) in self.config.search_query_templates.iter().enumerate() {
            let query = TestSearchQuery {
                id: Uuid::new_v4(),
                query: template.clone(),
                expected_result_count: None,
                performance_threshold_ms: 100 + (i as u64 * 50),
                tags: vec![format!("template_{}", i)],
            };
            
            queries.push(query);
        }
        
        queries
    }

    /// 生成用户ID
    fn generate_user_id(&self, index: usize) -> Uuid {
        // 使用固定种子生成可重复的UUID
        let user_index = index % self.config.user_count;
        Uuid::new_v5(&Uuid::NAMESPACE_DNS, format!("test_user_{}", user_index).as_bytes())
    }

    /// 生成聊天室ID
    fn generate_chat_room_id(&self, index: usize) -> Uuid {
        let room_index = index % self.config.chat_room_count;
        Uuid::new_v5(&Uuid::NAMESPACE_DNS, format!("test_room_{}", room_index).as_bytes())
    }

    /// 生成消息类型
    fn generate_message_type(&self, index: usize) -> TestMessageType {
        match index % 6 {
            0 => TestMessageType::Text,
            1 => TestMessageType::Image,
            2 => TestMessageType::File,
            3 => TestMessageType::System,
            4 => TestMessageType::Voice,
            5 => TestMessageType::Video,
            _ => TestMessageType::Text,
        }
    }

    /// 生成消息元数据
    fn generate_metadata(&self, index: usize) -> Option<String> {
        let categories = vec!["general", "tech", "business", "social", "support"];
        let category = categories[index % categories.len()];
        
        let metadata = serde_json::json!({
            "category": category,
            "index": index,
            "generated_at": Utc::now().to_rfc3339(),
            "test_batch": index / 1000
        });
        
        Some(metadata.to_string())
    }
}

/// 测试消息结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestMessage {
    pub id: Uuid,
    pub content: String,
    pub sender_id: Uuid,
    pub chat_room_id: Uuid,
    pub message_type: TestMessageType,
    pub created_at: DateTime<Utc>,
    pub metadata: Option<String>,
    pub priority: i32,
    pub is_pinned: bool,
}

/// 测试消息类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TestMessageType {
    Text,
    Image,
    File,
    System,
    Voice,
    Video,
}

/// 测试搜索查询
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestSearchQuery {
    pub id: Uuid,
    pub query: String,
    pub expected_result_count: Option<usize>,
    pub performance_threshold_ms: u64,
    pub tags: Vec<String>,
}

/// 测试工具函数
pub struct TestUtils;

impl TestUtils {
    /// 等待条件满足
    pub async fn wait_for_condition<F, Fut>(
        condition: F,
        timeout: Duration,
        check_interval: Duration,
    ) -> Result<()>
    where
        F: Fn() -> Fut,
        Fut: std::future::Future<Output = bool>,
    {
        let start_time = std::time::Instant::now();
        
        while start_time.elapsed() < timeout {
            if condition().await {
                return Ok(());
            }
            tokio::time::sleep(check_interval).await;
        }
        
        Err(anyhow::anyhow!("等待条件超时"))
    }

    /// 生成随机字符串
    pub fn generate_random_string(length: usize) -> String {
        use rand::Rng;
        const CHARSET: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZ\
                                abcdefghijklmnopqrstuvwxyz\
                                0123456789";
        let mut rng = rand::thread_rng();
        
        (0..length)
            .map(|_| {
                let idx = rng.gen_range(0..CHARSET.len());
                CHARSET[idx] as char
            })
            .collect()
    }

    /// 计算百分位数
    pub fn calculate_percentile(values: &[f64], percentile: f64) -> f64 {
        if values.is_empty() {
            return 0.0;
        }
        
        let mut sorted_values = values.to_vec();
        sorted_values.sort_by(|a, b| a.partial_cmp(b).unwrap());
        
        let index = (sorted_values.len() as f64 * percentile / 100.0) as usize;
        let index = index.min(sorted_values.len() - 1);
        
        sorted_values[index]
    }

    /// 验证性能指标
    pub fn validate_performance_metrics(
        metrics: &PerformanceMetrics,
        thresholds: &PerformanceThresholds,
    ) -> Result<()> {
        if metrics.latency_stats.p99_ms > thresholds.max_p99_latency_ms as f64 {
            return Err(anyhow::anyhow!(
                "P99延迟超过阈值: {:.2}ms > {}ms",
                metrics.latency_stats.p99_ms,
                thresholds.max_p99_latency_ms
            ));
        }
        
        if metrics.latency_stats.p95_ms > thresholds.max_p95_latency_ms as f64 {
            return Err(anyhow::anyhow!(
                "P95延迟超过阈值: {:.2}ms > {}ms",
                metrics.latency_stats.p95_ms,
                thresholds.max_p95_latency_ms
            ));
        }
        
        if metrics.throughput_qps < thresholds.min_throughput_qps {
            return Err(anyhow::anyhow!(
                "吞吐量低于阈值: {:.2} QPS < {:.2} QPS",
                metrics.throughput_qps,
                thresholds.min_throughput_qps
            ));
        }
        
        if metrics.error_rate > thresholds.max_error_rate {
            return Err(anyhow::anyhow!(
                "错误率超过阈值: {:.2}% > {:.2}%",
                metrics.error_rate * 100.0,
                thresholds.max_error_rate * 100.0
            ));
        }
        
        if metrics.cache_hit_ratio < thresholds.min_cache_hit_ratio {
            return Err(anyhow::anyhow!(
                "缓存命中率低于阈值: {:.2}% < {:.2}%",
                metrics.cache_hit_ratio * 100.0,
                thresholds.min_cache_hit_ratio * 100.0
            ));
        }
        
        Ok(())
    }

    /// 清理测试数据
    pub async fn cleanup_test_data(config: &TestEnvironmentConfig) -> Result<()> {
        tracing::info!("开始清理测试数据");
        
        // 清理数据库测试数据
        // TODO: 实现数据库清理逻辑
        
        // 清理缓存测试数据
        // TODO: 实现缓存清理逻辑
        
        tracing::info!("测试数据清理完成");
        Ok(())
    }
}

/// 性能指标结构（重新导入以避免循环依赖）
#[derive(Debug, Clone)]
pub struct PerformanceMetrics {
    pub latency_stats: LatencyStats,
    pub throughput_qps: f64,
    pub cache_hit_ratio: f64,
    pub error_rate: f64,
}

/// 延迟统计结构
#[derive(Debug, Clone)]
pub struct LatencyStats {
    pub mean_ms: f64,
    pub p50_ms: f64,
    pub p95_ms: f64,
    pub p99_ms: f64,
    pub min_ms: f64,
    pub max_ms: f64,
}

/// 默认测试环境配置
impl Default for TestEnvironmentConfig {
    fn default() -> Self {
        Self {
            database: DatabaseTestConfig {
                url: "postgresql://user:password@localhost:5432/test_db".to_string(),
                max_connections: 10,
                connection_timeout_secs: 30,
                enable_sql_logging: false,
                table_prefix: "test_".to_string(),
            },
            cache: CacheTestConfig {
                url: "redis://localhost:6379".to_string(),
                pool_size: 10,
                default_ttl_secs: 3600,
                key_prefix: "test:".to_string(),
                enable_stats: true,
            },
            performance: PerformanceTestConfig {
                max_concurrent_users: 1000,
                test_duration_secs: 60,
                warmup_duration_secs: 10,
                thresholds: PerformanceThresholds {
                    max_p99_latency_ms: 200,
                    max_p95_latency_ms: 100,
                    min_throughput_qps: 1000.0,
                    max_error_rate: 0.01,
                    min_cache_hit_ratio: 0.8,
                },
            },
            test_data: TestDataConfig {
                message_count: 10000,
                user_count: 100,
                chat_room_count: 10,
                message_templates: vec![
                    "这是测试消息 {index} - {timestamp}".to_string(),
                    "关于Rust编程的讨论 {index}".to_string(),
                    "PostgreSQL数据库优化技巧 {index}".to_string(),
                    "DragonflyDB缓存配置 {index}".to_string(),
                    "企业级架构设计 {index}".to_string(),
                ],
                search_query_templates: vec![
                    "测试".to_string(),
                    "Rust编程".to_string(),
                    "数据库优化".to_string(),
                    "缓存配置".to_string(),
                    "架构设计".to_string(),
                ],
            },
        }
    }
}

/// 测试配置加载器
pub struct TestConfigLoader;

impl TestConfigLoader {
    /// 从文件加载测试配置
    pub fn load_from_file(path: &str) -> Result<TestEnvironmentConfig> {
        let content = std::fs::read_to_string(path)?;
        let config: TestEnvironmentConfig = serde_json::from_str(&content)?;
        Ok(config)
    }

    /// 从环境变量加载测试配置
    pub fn load_from_env() -> TestEnvironmentConfig {
        let mut config = TestEnvironmentConfig::default();
        
        // 从环境变量覆盖配置
        if let Ok(db_url) = std::env::var("TEST_DATABASE_URL") {
            config.database.url = db_url;
        }
        
        if let Ok(cache_url) = std::env::var("TEST_CACHE_URL") {
            config.cache.url = cache_url;
        }
        
        if let Ok(max_users) = std::env::var("TEST_MAX_CONCURRENT_USERS") {
            if let Ok(users) = max_users.parse::<usize>() {
                config.performance.max_concurrent_users = users;
            }
        }
        
        config
    }

    /// 保存测试配置到文件
    pub fn save_to_file(config: &TestEnvironmentConfig, path: &str) -> Result<()> {
        let content = serde_json::to_string_pretty(config)?;
        std::fs::write(path, content)?;
        Ok(())
    }
}
