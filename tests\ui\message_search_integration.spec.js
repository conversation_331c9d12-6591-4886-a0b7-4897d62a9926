/**
 * 消息搜索接口集成测试
 * 测试任务32：实现消息搜索接口的完整功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-23
 */

import { test, expect } from '@playwright/test';

// 测试配置
const BASE_URL = 'http://127.0.0.1:3000';
const TEST_USER = {
    username: 'testuser456',
    password: 'password123'
};

test.describe('任务32：消息搜索接口集成测试', () => {
    let authToken;

    test.beforeAll(async ({ request }) => {
        // 用户登录获取认证令牌
        const loginResponse = await request.post(`${BASE_URL}/api/auth/login`, {
            data: TEST_USER
        });
        
        expect(loginResponse.ok()).toBeTruthy();
        const loginData = await loginResponse.json();
        authToken = loginData.data.access_token;
        
        console.log('✅ 测试用户登录成功，获取认证令牌');
    });

    test.describe('API接口直接测试', () => {
        test('应该成功执行基本消息搜索', async ({ request }) => {
            const searchResponse = await request.get(`${BASE_URL}/api/messages/search`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                },
                params: {
                    keyword: '测试',
                    page: 1,
                    limit: 20
                }
            });

            expect(searchResponse.ok()).toBeTruthy();
            const searchData = await searchResponse.json();
            
            // 验证响应结构
            expect(searchData).toHaveProperty('success', true);
            expect(searchData.data).toHaveProperty('messages');
            expect(searchData.data).toHaveProperty('total_count');
            expect(searchData.data).toHaveProperty('page', 1);
            expect(searchData.data).toHaveProperty('limit', 20);
            expect(searchData.data).toHaveProperty('keyword', '测试');
            
            console.log(`✅ 基本搜索成功，返回 ${searchData.data.total_count} 条结果`);
        });

        test('应该支持高级搜索参数', async ({ request }) => {
            const searchParams = {
                keyword: '消息',
                message_type: 'text',
                page: 1,
                limit: 10,
                start_date: '2025-07-20T00:00:00Z',
                end_date: '2025-07-23T23:59:59Z'
            };

            const searchResponse = await request.get(`${BASE_URL}/api/messages/search`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                },
                params: searchParams
            });

            expect(searchResponse.ok()).toBeTruthy();
            const searchData = await searchResponse.json();
            
            expect(searchData.success).toBe(true);
            expect(searchData.data.page).toBe(1);
            expect(searchData.data.limit).toBe(10);
            expect(searchData.data.keyword).toBe('消息');
            
            console.log(`✅ 高级搜索成功，返回 ${searchData.data.total_count} 条结果`);
        });

        test('应该正确处理空搜索关键词', async ({ request }) => {
            const searchResponse = await request.get(`${BASE_URL}/api/messages/search`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                },
                params: {
                    keyword: '',
                    page: 1,
                    limit: 20
                }
            });

            expect(searchResponse.ok()).toBeTruthy();
            const searchData = await searchResponse.json();
            
            // 空关键词应该返回空结果
            expect(searchData.success).toBe(true);
            expect(searchData.data.messages).toEqual([]);
            expect(searchData.data.total_count).toBe(0);
            
            console.log('✅ 空关键词处理正确，返回空结果');
        });

        test('应该支持分页功能', async ({ request }) => {
            // 测试第一页
            const page1Response = await request.get(`${BASE_URL}/api/messages/search`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                },
                params: {
                    keyword: 'test',
                    page: 1,
                    limit: 5
                }
            });

            expect(page1Response.ok()).toBeTruthy();
            const page1Data = await page1Response.json();
            
            expect(page1Data.data.page).toBe(1);
            expect(page1Data.data.limit).toBe(5);
            
            // 测试第二页
            const page2Response = await request.get(`${BASE_URL}/api/messages/search`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                },
                params: {
                    keyword: 'test',
                    page: 2,
                    limit: 5
                }
            });

            expect(page2Response.ok()).toBeTruthy();
            const page2Data = await page2Response.json();
            
            expect(page2Data.data.page).toBe(2);
            expect(page2Data.data.limit).toBe(5);
            
            console.log('✅ 分页功能正常工作');
        });

        test('应该正确处理未授权访问', async ({ request }) => {
            const searchResponse = await request.get(`${BASE_URL}/api/messages/search`, {
                params: {
                    keyword: '测试',
                    page: 1,
                    limit: 20
                }
            });

            expect(searchResponse.status()).toBe(401);
            console.log('✅ 未授权访问正确被拒绝');
        });
    });

    test.describe('前端集成测试', () => {
        test('应该在前端页面中集成搜索功能', async ({ page }) => {
            // 访问主页面
            await page.goto(BASE_URL);
            
            // 等待页面加载
            await page.waitForLoadState('networkidle');
            
            // 检查是否有登录表单或已登录状态
            const hasLoginForm = await page.locator('#loginForm').isVisible().catch(() => false);
            
            if (hasLoginForm) {
                // 执行登录
                await page.fill('#username', TEST_USER.username);
                await page.fill('#password', TEST_USER.password);
                await page.click('#loginBtn');
                
                // 等待登录完成
                await page.waitForSelector('#dashboard', { timeout: 10000 });
                console.log('✅ 前端登录成功');
            }
            
            // 检查页面是否加载了API模块
            const apiModuleLoaded = await page.evaluate(() => {
                return typeof window.chatAPI !== 'undefined' || 
                       typeof window.searchMessages !== 'undefined';
            });
            
            if (!apiModuleLoaded) {
                console.log('⚠️ API模块未在前端加载，跳过前端集成测试');
                return;
            }
            
            // 测试前端搜索功能
            const searchResult = await page.evaluate(async () => {
                try {
                    // 假设chatAPI已经在全局作用域中
                    if (typeof chatAPI !== 'undefined' && chatAPI.searchMessages) {
                        return await chatAPI.searchMessages({ keyword: '测试' });
                    }
                    return { error: 'chatAPI not available' };
                } catch (error) {
                    return { error: error.message };
                }
            });
            
            if (searchResult.error) {
                console.log(`⚠️ 前端搜索测试跳过: ${searchResult.error}`);
            } else {
                expect(searchResult).toHaveProperty('success');
                console.log('✅ 前端搜索功能集成成功');
            }
        });
    });

    test.describe('性能和缓存测试', () => {
        test('应该在合理时间内响应搜索请求', async ({ request }) => {
            const startTime = Date.now();
            
            const searchResponse = await request.get(`${BASE_URL}/api/messages/search`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                },
                params: {
                    keyword: '性能测试',
                    page: 1,
                    limit: 20
                }
            });
            
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            
            expect(searchResponse.ok()).toBeTruthy();
            expect(responseTime).toBeLessThan(5000); // 5秒内响应
            
            console.log(`✅ 搜索响应时间: ${responseTime}ms`);
        });

        test('应该支持大结果集分块加载', async ({ request }) => {
            // 测试大限制值
            const searchResponse = await request.get(`${BASE_URL}/api/messages/search`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                },
                params: {
                    keyword: 'test',
                    page: 1,
                    limit: 100 // 较大的限制值
                }
            });

            expect(searchResponse.ok()).toBeTruthy();
            const searchData = await searchResponse.json();
            
            expect(searchData.success).toBe(true);
            expect(searchData.data.limit).toBe(100);
            
            console.log(`✅ 大结果集处理正常，请求限制: ${searchData.data.limit}`);
        });
    });
});
