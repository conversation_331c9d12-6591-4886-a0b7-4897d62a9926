/**
 * 健康检查前端API测试
 * 测试任务36：集成健康检查API的前端功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-25
 */

import { healthAPI } from '../static/js/modules/api.js';

// 测试配置
const TEST_CONFIG = {
    timeout: 30000,
    retryAttempts: 3,
    retryDelay: 1000
};

/**
 * 模拟健康检查响应数据
 */
const MOCK_HEALTH_RESPONSES = {
    basic: {
        status: 'healthy',
        timestamp: '2025-07-25T10:00:00Z',
        version: '1.0.0',
        uptime: 3600,
        services: {
            database: 'healthy',
            cache: 'healthy',
            external_apis: 'healthy'
        }
    },
    deep: {
        status: 'healthy',
        timestamp: '2025-07-25T10:00:00Z',
        version: '1.0.0',
        uptime: 3600,
        system: {
            memory_usage: 0.65,
            cpu_usage: 0.45,
            disk_usage: 0.30
        },
        services: {
            database: {
                status: 'healthy',
                response_time_ms: 5,
                last_check: '2025-07-25T10:00:00Z'
            },
            cache: {
                status: 'healthy',
                response_time_ms: 2,
                last_check: '2025-07-25T10:00:00Z'
            },
            external_apis: {
                status: 'healthy',
                response_time_ms: 150,
                last_check: '2025-07-25T10:00:00Z'
            }
        },
        metrics: {
            requests_per_minute: 120,
            error_rate: 0.01,
            active_connections: 45
        }
    },
    database: {
        status: 'healthy',
        timestamp: '2025-07-25T10:00:00Z',
        pool_status: {
            active_connections: 5,
            idle_connections: 10,
            max_connections: 20
        }
    },
    unhealthy: {
        status: 'unhealthy',
        timestamp: '2025-07-25T10:00:00Z',
        version: '1.0.0',
        uptime: 3600,
        services: {
            database: 'unhealthy',
            cache: 'healthy',
            external_apis: 'degraded'
        },
        system: {
            memory_usage: 0.95,
            cpu_usage: 0.85,
            disk_usage: 0.90
        }
    }
};

/**
 * 测试工具函数
 */
const TestUtils = {
    /**
     * 等待指定时间
     * @param {number} ms - 等待时间（毫秒）
     */
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    },

    /**
     * 验证健康检查响应结构
     * @param {Object} response - 响应对象
     * @param {string} type - 响应类型
     */
    validateHealthResponse(response, type) {
        expect(response).toBeDefined();
        expect(typeof response).toBe('object');
        expect(response.status).toBeDefined();
        expect(response.timestamp).toBeDefined();

        switch (type) {
            case 'basic':
                expect(response.version).toBeDefined();
                expect(response.uptime).toBeDefined();
                expect(response.services).toBeDefined();
                break;
            case 'deep':
                expect(response.system).toBeDefined();
                expect(response.metrics).toBeDefined();
                break;
            case 'database':
                expect(response.pool_status).toBeDefined();
                break;
        }
    },

    /**
     * 验证批量检查结果
     * @param {Object} batchResult - 批量检查结果
     */
    validateBatchResult(batchResult) {
        expect(batchResult).toBeDefined();
        expect(batchResult.total_checks).toBe(7);
        expect(batchResult.successful_checks).toBeDefined();
        expect(batchResult.failed_checks).toBeDefined();
        expect(batchResult.checks).toBeDefined();
        expect(batchResult.overall_status).toBeDefined();
        expect(batchResult.execution_time_ms).toBeDefined();

        // 验证检查项
        const expectedChecks = ['basic', 'deep', 'database', 'database_config', 'performance', 'readiness', 'liveness'];
        for (const checkName of expectedChecks) {
            expect(batchResult.checks[checkName]).toBeDefined();
            expect(batchResult.checks[checkName].success).toBeDefined();
            expect(batchResult.checks[checkName].status).toBeDefined();
        }
    }
};

/**
 * 健康检查API基础功能测试
 */
describe('健康检查API基础功能测试', () => {
    beforeEach(() => {
        // 清除缓存
        healthAPI.clearHealthCache();
    });

    test('基础健康检查', async () => {
        // 模拟API响应
        global.fetch = jest.fn().mockResolvedValue({
            ok: true,
            json: async () => MOCK_HEALTH_RESPONSES.basic
        });

        const result = await healthAPI.fetchBasicHealth();
        TestUtils.validateHealthResponse(result, 'basic');
        expect(result.status).toBe('healthy');
    });

    test('深度健康检查', async () => {
        global.fetch = jest.fn().mockResolvedValue({
            ok: true,
            json: async () => MOCK_HEALTH_RESPONSES.deep
        });

        const result = await healthAPI.fetchDeepHealth();
        TestUtils.validateHealthResponse(result, 'deep');
        expect(result.system).toBeDefined();
        expect(result.metrics).toBeDefined();
    });

    test('数据库健康检查', async () => {
        global.fetch = jest.fn().mockResolvedValue({
            ok: true,
            json: async () => MOCK_HEALTH_RESPONSES.database
        });

        const result = await healthAPI.fetchDatabaseHealth();
        TestUtils.validateHealthResponse(result, 'database');
        expect(result.pool_status).toBeDefined();
    });

    test('缓存功能测试', async () => {
        global.fetch = jest.fn().mockResolvedValue({
            ok: true,
            json: async () => MOCK_HEALTH_RESPONSES.basic
        });

        // 第一次调用
        await healthAPI.fetchBasicHealth({ useCache: true });
        expect(global.fetch).toHaveBeenCalledTimes(1);

        // 第二次调用应该使用缓存
        await healthAPI.fetchBasicHealth({ useCache: true });
        expect(global.fetch).toHaveBeenCalledTimes(1);

        // 禁用缓存的调用
        await healthAPI.fetchBasicHealth({ useCache: false });
        expect(global.fetch).toHaveBeenCalledTimes(2);
    });
});

/**
 * 批量健康检查测试
 */
describe('批量健康检查测试', () => {
    beforeEach(() => {
        healthAPI.clearHealthCache();
    });

    test('并行批量检查', async () => {
        // 模拟所有API响应
        global.fetch = jest.fn()
            .mockResolvedValueOnce({ ok: true, json: async () => MOCK_HEALTH_RESPONSES.basic })
            .mockResolvedValueOnce({ ok: true, json: async () => MOCK_HEALTH_RESPONSES.deep })
            .mockResolvedValueOnce({ ok: true, json: async () => MOCK_HEALTH_RESPONSES.database })
            .mockResolvedValueOnce({ ok: true, json: async () => MOCK_HEALTH_RESPONSES.database })
            .mockResolvedValueOnce({ ok: true, json: async () => MOCK_HEALTH_RESPONSES.basic })
            .mockResolvedValueOnce({ ok: true, json: async () => ({ status: 'ready' }) })
            .mockResolvedValueOnce({ ok: true, json: async () => ({ status: 'alive' }) });

        const result = await healthAPI.fetchBatchHealthCheck({ parallel: true });
        TestUtils.validateBatchResult(result);
        expect(result.overall_status).toBe('healthy');
    });

    test('串行批量检查', async () => {
        global.fetch = jest.fn()
            .mockResolvedValue({ ok: true, json: async () => MOCK_HEALTH_RESPONSES.basic });

        const result = await healthAPI.fetchBatchHealthCheck({ parallel: false });
        TestUtils.validateBatchResult(result);
    });

    test('部分失败的批量检查', async () => {
        global.fetch = jest.fn()
            .mockResolvedValueOnce({ ok: true, json: async () => MOCK_HEALTH_RESPONSES.basic })
            .mockRejectedValueOnce(new Error('网络错误'))
            .mockResolvedValueOnce({ ok: true, json: async () => MOCK_HEALTH_RESPONSES.database })
            .mockResolvedValueOnce({ ok: true, json: async () => MOCK_HEALTH_RESPONSES.database })
            .mockResolvedValueOnce({ ok: true, json: async () => MOCK_HEALTH_RESPONSES.basic })
            .mockResolvedValueOnce({ ok: true, json: async () => ({ status: 'ready' }) })
            .mockResolvedValueOnce({ ok: true, json: async () => ({ status: 'alive' }) });

        const result = await healthAPI.fetchBatchHealthCheck();
        TestUtils.validateBatchResult(result);
        expect(result.failed_checks).toBeGreaterThan(0);
        expect(result.overall_status).toBe('degraded');
    });
});

/**
 * 状态分类和图标测试
 */
describe('状态分类和图标测试', () => {
    test('健康状态分类', () => {
        const classification = healthAPI.classifyHealthStatus(MOCK_HEALTH_RESPONSES.basic);
        expect(classification.severity).toBe('healthy');
        expect(classification.issues).toContain('系统运行正常');
    });

    test('不健康状态分类', () => {
        const classification = healthAPI.classifyHealthStatus(MOCK_HEALTH_RESPONSES.unhealthy);
        expect(classification.severity).toBe('unhealthy');
        expect(classification.issues.length).toBeGreaterThan(1);
    });

    test('状态图标获取', () => {
        const healthyIcon = healthAPI.getStatusIcon('healthy');
        expect(healthyIcon.icon).toBe('✅');
        expect(healthyIcon.color).toBe('#28a745');

        const unhealthyIcon = healthAPI.getStatusIcon('unhealthy');
        expect(unhealthyIcon.icon).toBe('❌');
        expect(unhealthyIcon.color).toBe('#dc3545');
    });

    test('数据格式化显示', () => {
        const formatted = healthAPI.formatHealthDataForDisplay(MOCK_HEALTH_RESPONSES.deep);
        expect(formatted.status).toBeDefined();
        expect(formatted.icon).toBeDefined();
        expect(formatted.details).toBeInstanceOf(Array);
        expect(formatted.details.length).toBeGreaterThan(0);
    });
});

/**
 * 缓存管理测试
 */
describe('缓存管理测试', () => {
    test('缓存状态获取', () => {
        const status = healthAPI.getCacheStatus();
        expect(status.total_entries).toBeDefined();
        expect(status.cache_timeout).toBeDefined();
        expect(status.entries).toBeInstanceOf(Array);
    });

    test('缓存超时设置', () => {
        healthAPI.setCacheTimeout(60000);
        const status = healthAPI.getCacheStatus();
        expect(status.cache_timeout).toBe(60000);
    });

    test('模式匹配缓存清理', () => {
        // 添加一些缓存项
        healthAPI._healthCache.set('basic_health', { data: {}, timestamp: Date.now() });
        healthAPI._healthCache.set('deep_health', { data: {}, timestamp: Date.now() });
        healthAPI._healthCache.set('database_health', { data: {}, timestamp: Date.now() });

        // 清理包含'database'的缓存
        healthAPI.clearHealthCache('database');
        
        const status = healthAPI.getCacheStatus();
        expect(status.total_entries).toBe(2);
    });
});
