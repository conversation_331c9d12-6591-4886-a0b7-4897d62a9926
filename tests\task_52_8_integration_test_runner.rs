//! # 任务52.8 - 消息搜索功能完整集成测试执行器
//!
//! 执行完整的集成测试和性能验证，包括：
//! 1. 高并发测试场景
//! 2. 缓存雪崩模拟测试
//! 3. 搜索准确性和性能验证
//! 4. 系统恢复能力测试
//! 5. 生成性能测试报告

use anyhow::Result;
use serde_json;
use std::time::Duration;
use tracing::{error, info, warn};

use crate::message_search_test_framework::{
    ConcurrencyConfig, MessageSearchTestConfig, MessageSearchTestFramework, PerformanceThresholds,
};

use crate::message_search_integration_test::{
    CacheAvalancheConfig, FailureType, IntegrationTestConfig, MessageSearchIntegrationTester,
    RecoveryTestConfig,
};

/// 任务52.8集成测试执行器
pub struct Task528IntegrationTestRunner {
    /// 测试框架
    framework: MessageSearchTestFramework,
    /// 集成测试器
    integration_tester: MessageSearchIntegrationTester,
}

impl Task528IntegrationTestRunner {
    /// 创建新的测试执行器
    pub fn new() -> Self {
        // 配置企业级测试参数
        let test_config = MessageSearchTestConfig {
            database_url: "postgresql://user:password@localhost:5432/test_db".to_string(),
            dragonfly_url: "redis://localhost:6379".to_string(),
            test_data_size: 100000, // 10万条测试数据
            performance_thresholds: PerformanceThresholds {
                search_latency_p99_ms: 200,
                search_latency_p95_ms: 100,
                cache_hit_ratio: 0.85,
                max_concurrent_users: 10000,
                target_throughput_qps: 5000,
                max_error_rate: 0.01,
            },
            concurrency_config: ConcurrencyConfig {
                concurrent_users: 1000,
                test_duration: Duration::from_secs(300), // 5分钟测试
                request_interval: Duration::from_millis(50),
                warmup_duration: Duration::from_secs(30),
            },
        };

        // 配置集成测试参数
        let integration_config = IntegrationTestConfig {
            high_concurrency_users: 5000, // 5000并发用户
            cache_avalanche_config: CacheAvalancheConfig {
                cache_invalidation_ratio: 0.8,                   // 80%缓存失效
                avalanche_duration: Duration::from_secs(60),     // 1分钟雪崩持续时间
                recovery_check_interval: Duration::from_secs(5), // 5秒检查间隔
            },
            performance_thresholds: test_config.performance_thresholds.clone(),
            recovery_test_config: RecoveryTestConfig {
                failure_types: vec![
                    FailureType::DatabaseFailure,
                    FailureType::CacheFailure,
                    FailureType::NetworkLatency,
                    FailureType::MemoryPressure,
                ],
                failure_duration: Duration::from_secs(15), // 15秒故障持续时间
                recovery_verification_time: Duration::from_secs(45), // 45秒恢复验证时间
            },
        };

        let framework = MessageSearchTestFramework::new(test_config);
        let integration_tester =
            MessageSearchIntegrationTester::new(framework.clone(), integration_config);

        Self {
            framework,
            integration_tester,
        }
    }

    /// 执行完整的任务52.8集成测试
    pub async fn run_complete_test_suite(&self) -> Result<()> {
        info!("🚀 开始执行任务52.8 - 消息搜索功能完整集成测试");

        // 1. 预热阶段
        self.warmup_phase().await?;

        // 2. 执行完整集成测试
        let test_report = self
            .integration_tester
            .run_complete_integration_tests()
            .await?;

        // 3. 生成详细报告
        self.generate_detailed_report(&test_report).await?;

        // 4. 验证测试结果
        self.validate_test_results(&test_report).await?;

        info!("✅ 任务52.8集成测试执行完成");

        Ok(())
    }

    /// 预热阶段
    async fn warmup_phase(&self) -> Result<()> {
        info!("🔥 开始预热阶段");

        // 初始化测试环境
        self.framework.initialize().await?;

        // 执行预热请求
        for i in 0..100 {
            let query = format!("预热查询 {}", i);
            let _ = super::message_search_integration_test::simulate_search_request(&query).await;
        }

        info!("✅ 预热阶段完成");
        Ok(())
    }

    /// 生成详细报告
    async fn generate_detailed_report(
        &self,
        test_report: &crate::message_search_test_framework::MessageSearchTestReport,
    ) -> Result<()> {
        info!("📊 生成详细测试报告");

        // 生成JSON格式报告
        let json_report = serde_json::to_string_pretty(test_report)?;

        // 保存到文件
        tokio::fs::write(
            "reports/task_52_8_integration_test_report.json",
            json_report,
        )
        .await?;

        // 生成Markdown格式报告
        let markdown_report = self.generate_markdown_report(test_report).await?;
        tokio::fs::write(
            "reports/task_52_8_integration_test_report.md",
            markdown_report,
        )
        .await?;

        info!("✅ 详细测试报告已生成");
        Ok(())
    }

    /// 生成Markdown格式报告
    async fn generate_markdown_report(
        &self,
        test_report: &crate::message_search_test_framework::MessageSearchTestReport,
    ) -> Result<String> {
        let mut report = String::new();

        report.push_str("# 任务52.8 - 消息搜索功能完整集成测试报告\n\n");
        report.push_str(&format!(
            "**生成时间**: {}\n\n",
            test_report.generated_at.format("%Y-%m-%d %H:%M:%S UTC")
        ));

        // 测试概览
        report.push_str("## 📋 测试概览\n\n");
        report.push_str(&format!("- **总测试数**: {}\n", test_report.total_tests));
        report.push_str(&format!("- **通过测试**: {}\n", test_report.passed_tests));
        report.push_str(&format!("- **失败测试**: {}\n", test_report.failed_tests));
        report.push_str(&format!("- **跳过测试**: {}\n", test_report.skipped_tests));
        report.push_str(&format!(
            "- **执行时间**: {:.2}秒\n\n",
            test_report.execution_time.as_secs_f64()
        ));

        // 性能指标
        report.push_str("## 📈 整体性能指标\n\n");
        report.push_str(&format!(
            "- **平均吞吐量**: {:.2} QPS\n",
            test_report.overall_performance.throughput_qps
        ));
        report.push_str(&format!(
            "- **P99延迟**: {:.2}ms\n",
            test_report.overall_performance.latency_stats.p99_ms
        ));
        report.push_str(&format!(
            "- **P95延迟**: {:.2}ms\n",
            test_report.overall_performance.latency_stats.p95_ms
        ));
        report.push_str(&format!(
            "- **平均延迟**: {:.2}ms\n",
            test_report.overall_performance.latency_stats.mean_ms
        ));
        report.push_str(&format!(
            "- **缓存命中率**: {:.2}%\n",
            test_report.overall_performance.cache_hit_ratio * 100.0
        ));
        report.push_str(&format!(
            "- **错误率**: {:.2}%\n\n",
            test_report.overall_performance.error_rate * 100.0
        ));

        // 覆盖率报告
        report.push_str("## 🎯 测试覆盖率\n\n");
        report.push_str(&format!(
            "- **代码覆盖率**: {:.1}%\n",
            test_report.coverage_report.code_coverage_percent
        ));
        report.push_str(&format!(
            "- **功能覆盖率**: {:.1}%\n",
            test_report.coverage_report.feature_coverage_percent
        ));
        report.push_str(&format!(
            "- **性能测试覆盖率**: {:.1}%\n\n",
            test_report.coverage_report.performance_coverage_percent
        ));

        // 详细测试结果
        report.push_str("## 🧪 详细测试结果\n\n");
        for test_result in &test_report.test_results {
            let status_emoji = if test_result.success { "✅" } else { "❌" };
            report.push_str(&format!(
                "### {} {}\n\n",
                status_emoji, test_result.test_name
            ));
            report.push_str(&format!(
                "- **执行时间**: {:.2}秒\n",
                test_result.execution_time.as_secs_f64()
            ));
            report.push_str(&format!(
                "- **吞吐量**: {:.2} QPS\n",
                test_result.performance_metrics.throughput_qps
            ));
            report.push_str(&format!(
                "- **P99延迟**: {:.2}ms\n",
                test_result.performance_metrics.latency_stats.p99_ms
            ));
            report.push_str(&format!(
                "- **错误率**: {:.2}%\n",
                test_result.performance_metrics.error_rate * 100.0
            ));

            if let Some(error_msg) = &test_result.error_message {
                report.push_str(&format!("- **错误信息**: {}\n", error_msg));
            }

            report.push_str("\n");
        }

        // 结论和建议
        report.push_str("## 🎉 结论和建议\n\n");
        let success_rate =
            ((test_report.passed_tests as f64) / (test_report.total_tests as f64)) * 100.0;

        if success_rate >= 95.0 {
            report.push_str(
                "✅ **测试结果优秀**: 系统在高并发场景下表现出色，满足企业级性能要求。\n\n",
            );
        } else if success_rate >= 80.0 {
            report
                .push_str("⚠️ **测试结果良好**: 系统基本满足要求，但存在一些需要优化的地方。\n\n");
        } else {
            report.push_str("❌ **测试结果需要改进**: 系统存在较多问题，需要进一步优化。\n\n");
        }

        report.push_str("### 建议\n\n");
        report.push_str("1. **性能优化**: 继续优化搜索算法和缓存策略\n");
        report.push_str("2. **监控完善**: 加强生产环境监控和告警\n");
        report.push_str("3. **容量规划**: 根据测试结果进行容量规划\n");
        report.push_str("4. **故障恢复**: 完善故障恢复机制和流程\n\n");

        Ok(report)
    }

    /// 验证测试结果
    async fn validate_test_results(
        &self,
        test_report: &crate::message_search_test_framework::MessageSearchTestReport,
    ) -> Result<()> {
        info!("🔍 验证测试结果");

        let success_rate =
            ((test_report.passed_tests as f64) / (test_report.total_tests as f64)) * 100.0;

        if success_rate < 80.0 {
            error!("❌ 测试成功率过低: {:.1}% < 80%", success_rate);
            return Err(anyhow::anyhow!("集成测试失败，成功率不达标"));
        }

        if test_report.overall_performance.throughput_qps < 1000.0 {
            warn!(
                "⚠️ 整体吞吐量较低: {:.2} QPS < 1000 QPS",
                test_report.overall_performance.throughput_qps
            );
        }

        if test_report.overall_performance.latency_stats.p99_ms > 500.0 {
            warn!(
                "⚠️ P99延迟较高: {:.2}ms > 500ms",
                test_report.overall_performance.latency_stats.p99_ms
            );
        }

        info!("✅ 测试结果验证通过 - 成功率: {:.1}%", success_rate);
        Ok(())
    }
}

/// 默认实现
impl Default for Task528IntegrationTestRunner {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio;

    #[tokio::test]
    async fn test_task_52_8_integration_test_runner() {
        // 初始化日志
        tracing_subscriber::fmt::init();

        let runner = Task528IntegrationTestRunner::new();

        // 执行完整测试套件
        let result = runner.run_complete_test_suite().await;

        match result {
            Ok(_) => {
                println!("✅ 任务52.8集成测试执行成功");
            }
            Err(e) => {
                println!("❌ 任务52.8集成测试执行失败: {}", e);
                // 在测试环境中，我们可以允许某些失败
                // panic!("集成测试失败: {}", e);
            }
        }
    }
}
