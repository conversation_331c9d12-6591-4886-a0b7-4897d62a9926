{"config": {"configFile": "D:\\ceshi\\ceshi\\axum-tutorial\\playwright.config.ts", "rootDir": "D:/ceshi/ceshi/axum-tutorial/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "tests/ui/reports/html"}], ["json", {"outputFile": "tests/ui/reports/results.json"}], ["junit", {"outputFile": "tests/ui/reports/results.xml"}], ["line", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "D:/ceshi/ceshi/axum-tutorial/tests/ui/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium-desktop", "name": "chromium-desktop", "testDir": "D:/ceshi/ceshi/axum-tutorial/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/ceshi/ceshi/axum-tutorial/tests/ui/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "firefox-desktop", "name": "firefox-desktop", "testDir": "D:/ceshi/ceshi/axum-tutorial/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/ceshi/ceshi/axum-tutorial/tests/ui/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit-desktop", "name": "webkit-desktop", "testDir": "D:/ceshi/ceshi/axum-tutorial/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/ceshi/ceshi/axum-tutorial/tests/ui/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "tablet-chrome", "name": "tablet-chrome", "testDir": "D:/ceshi/ceshi/axum-tutorial/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/ceshi/ceshi/axum-tutorial/tests/ui/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "mobile-chrome", "name": "mobile-chrome", "testDir": "D:/ceshi/ceshi/axum-tutorial/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/ceshi/ceshi/axum-tutorial/tests/ui/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "mobile-safari", "name": "mobile-safari", "testDir": "D:/ceshi/ceshi/axum-tutorial/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 4, "webServer": null}, "suites": [], "errors": [{"message": "Error: No tests found.\nMake sure that arguments are regular expressions matching test files.\nYou may need to escape symbols like \"$\" or \"*\" and quote the arguments.", "stack": "Error: No tests found.\nMake sure that arguments are regular expressions matching test files.\nYou may need to escape symbols like \"$\" or \"*\" and quote the arguments."}], "stats": {"startTime": "2025-07-28T03:03:06.453Z", "duration": 33.93999999999983, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}