/**
 * 聊天API消息搜索功能测试
 * 遵循TDD开发模式，先写测试再实现功能
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-23
 */

// 设置测试环境
const originalFetch = global.fetch;
const originalConsole = global.console;

// 模拟fetch API
global.fetch = jest.fn();

// 模拟console方法以避免测试输出污染
global.console = {
    log: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
};

// 模拟localStorage
const localStorageMock = {
    getItem: jest.fn(() => 'mock-jwt-token'),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn()
};
global.localStorage = localStorageMock;

// 模拟auth模块
jest.mock('../static/js/modules/auth.js', () => ({
    getAuthToken: jest.fn(() => 'mock-jwt-token'),
    isAuthenticated: jest.fn(() => true)
}));

// 导入要测试的模块
const { chatAPI, APIError } = require('../static/js/modules/api.js');

describe('chatAPI.searchMessages', () => {
    beforeEach(() => {
        // 重置所有mock
        jest.clearAllMocks();
        fetch.mockClear();
        
        // 清理缓存
        chatAPI.clearSearchCache();
    });

    describe('参数验证', () => {
        test('应该在搜索关键词为空时抛出错误', async () => {
            await expect(chatAPI.searchMessages({}))
                .rejects.toThrow('搜索关键词不能为空');
        });

        test('应该在搜索关键词为null时抛出错误', async () => {
            await expect(chatAPI.searchMessages({ keyword: null }))
                .rejects.toThrow('搜索关键词不能为空');
        });

        test('应该在搜索关键词为空白字符时抛出错误', async () => {
            await expect(chatAPI.searchMessages({ keyword: '   ' }))
                .rejects.toThrow('搜索关键词不能为空白字符');
        });

        test('应该在搜索关键词过长时抛出错误', async () => {
            const longKeyword = 'a'.repeat(101);
            await expect(chatAPI.searchMessages({ keyword: longKeyword }))
                .rejects.toThrow('搜索关键词长度不能超过100个字符');
        });
    });

    describe('基本搜索功能', () => {
        test('应该成功执行基本搜索', async () => {
            const mockResponse = {
                success: true,
                data: {
                    messages: [
                        {
                            id: 'msg-1',
                            content: '测试消息',
                            sender_username: '用户1',
                            room_name: '全局聊天室',
                            created_at: '2025-07-23T10:00:00Z',
                            message_type: 'text'
                        }
                    ],
                    total_count: 1,
                    page: 1,
                    limit: 20,
                    keyword: '测试'
                }
            };

            fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => mockResponse
            });

            const result = await chatAPI.searchMessages({ keyword: '测试' });

            expect(fetch).toHaveBeenCalledWith(
                '/api/messages/search?keyword=%E6%B5%8B%E8%AF%95&page=1&limit=20',
                expect.objectContaining({
                    method: 'GET',
                    headers: expect.objectContaining({
                        'Authorization': 'Bearer mock-jwt-token'
                    })
                })
            );

            expect(result).toEqual(mockResponse);
        });

        test('应该正确处理所有搜索参数', async () => {
            const mockResponse = {
                success: true,
                data: {
                    messages: [],
                    total_count: 0,
                    page: 2,
                    limit: 10,
                    keyword: '高级搜索'
                }
            };

            fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => mockResponse
            });

            const searchParams = {
                keyword: '高级搜索',
                message_type: 'text',
                sender: 'user-123',
                room_id: 'room-456',
                start_date: '2025-07-20T00:00:00Z',
                end_date: '2025-07-23T23:59:59Z',
                page: 2,
                limit: 10
            };

            await chatAPI.searchMessages(searchParams);

            const expectedUrl = '/api/messages/search?' +
                'keyword=%E9%AB%98%E7%BA%A7%E6%90%9C%E7%B4%A2&' +
                'message_type=text&' +
                'sender=user-123&' +
                'room_id=room-456&' +
                'start_date=2025-07-20T00%3A00%3A00Z&' +
                'end_date=2025-07-23T23%3A59%3A59Z&' +
                'page=2&' +
                'limit=10';

            expect(fetch).toHaveBeenCalledWith(
                expectedUrl,
                expect.objectContaining({
                    method: 'GET'
                })
            );
        });
    });

    describe('缓存功能', () => {
        test('应该缓存搜索结果', async () => {
            const mockResponse = {
                success: true,
                data: {
                    messages: [{ id: 'msg-1', content: '缓存测试' }],
                    total_count: 1,
                    page: 1,
                    limit: 20,
                    keyword: '缓存'
                }
            };

            fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => mockResponse
            });

            // 第一次调用
            const result1 = await chatAPI.searchMessages({ keyword: '缓存' });
            expect(fetch).toHaveBeenCalledTimes(1);

            // 第二次调用应该使用缓存
            const result2 = await chatAPI.searchMessages({ keyword: '缓存' });
            expect(fetch).toHaveBeenCalledTimes(1); // 仍然是1次，说明使用了缓存
            expect(result1).toEqual(result2);
        });

        test('应该在禁用缓存时不使用缓存', async () => {
            const mockResponse = {
                success: true,
                data: {
                    messages: [],
                    total_count: 0,
                    page: 1,
                    limit: 20,
                    keyword: '无缓存'
                }
            };

            fetch.mockResolvedValue({
                ok: true,
                status: 200,
                json: async () => mockResponse
            });

            // 两次调用都禁用缓存
            await chatAPI.searchMessages({ keyword: '无缓存' }, { useCache: false });
            await chatAPI.searchMessages({ keyword: '无缓存' }, { useCache: false });

            expect(fetch).toHaveBeenCalledTimes(2);
        });

        test('应该正确清理搜索缓存', async () => {
            const mockResponse = {
                success: true,
                data: { messages: [], total_count: 0, page: 1, limit: 20, keyword: '清理' }
            };

            fetch.mockResolvedValue({
                ok: true,
                status: 200,
                json: async () => mockResponse
            });

            // 先缓存一个结果
            await chatAPI.searchMessages({ keyword: '清理' });
            expect(fetch).toHaveBeenCalledTimes(1);

            // 清理缓存
            chatAPI.clearSearchCache();

            // 再次调用应该重新请求
            await chatAPI.searchMessages({ keyword: '清理' });
            expect(fetch).toHaveBeenCalledTimes(2);
        });
    });

    describe('错误处理', () => {
        test('应该正确处理网络错误', async () => {
            fetch.mockRejectedValueOnce(new Error('网络连接失败'));

            await expect(chatAPI.searchMessages({ keyword: '网络错误' }))
                .rejects.toThrow('网络连接失败');
        });

        test('应该正确处理HTTP错误状态', async () => {
            fetch.mockResolvedValueOnce({
                ok: false,
                status: 500,
                statusText: 'Internal Server Error',
                json: async () => ({
                    success: false,
                    error: '服务器内部错误'
                })
            });

            await expect(chatAPI.searchMessages({ keyword: '服务器错误' }))
                .rejects.toThrow();
        });

        test('应该正确处理401未授权错误', async () => {
            fetch.mockResolvedValueOnce({
                ok: false,
                status: 401,
                statusText: 'Unauthorized',
                json: async () => ({
                    success: false,
                    error: '未授权访问'
                })
            });

            await expect(chatAPI.searchMessages({ keyword: '未授权' }))
                .rejects.toThrow();
        });
    });

    describe('分页支持', () => {
        test('应该支持分页参数', async () => {
            const mockResponse = {
                success: true,
                data: {
                    messages: [],
                    total_count: 100,
                    page: 3,
                    limit: 15,
                    keyword: '分页测试'
                }
            };

            fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => mockResponse
            });

            await chatAPI.searchMessages({
                keyword: '分页测试',
                page: 3,
                limit: 15
            });

            expect(fetch).toHaveBeenCalledWith(
                expect.stringContaining('page=3&limit=15'),
                expect.any(Object)
            );
        });

        test('应该使用默认分页参数', async () => {
            const mockResponse = {
                success: true,
                data: {
                    messages: [],
                    total_count: 0,
                    page: 1,
                    limit: 20,
                    keyword: '默认分页'
                }
            };

            fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => mockResponse
            });

            await chatAPI.searchMessages({ keyword: '默认分页' });

            expect(fetch).toHaveBeenCalledWith(
                expect.stringContaining('page=1&limit=20'),
                expect.any(Object)
            );
        });
    });
});
