import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './base-page';

/**
 * 主页面对象模型 - Axum任务管理系统
 * 遵循Context7 MCP最佳实践，使用用户友好的定位器
 */
export class MainPage extends BasePage {
  // 页面标题和导航元素
  readonly pageTitle: Locator;
  readonly mainContainer: Locator;

  // 认证面板元素
  readonly authPanel: Locator;
  readonly authStatus: Locator;
  readonly loginForm: Locator;
  readonly registerForm: Locator;
  readonly usernameInput: Locator;
  readonly passwordInput: Locator;
  readonly emailInput: Locator;
  readonly loginButton: Locator;
  readonly registerButton: Locator;
  readonly logoutButton: Locator;
  readonly authMessage: Locator;

  // 任务管理面板元素
  readonly taskPanel: Locator;
  readonly taskForm: Locator;
  readonly taskTitleInput: Locator;
  readonly taskDescriptionInput: Locator;
  readonly createTaskButton: Locator;
  readonly taskList: Locator;
  readonly taskItems: Locator;
  readonly filterButtons: Locator;

  // 聊天面板元素
  readonly chatPanel: Locator;
  readonly connectionStatus: Locator;
  readonly connectButton: Locator;
  readonly disconnectButton: Locator;
  readonly pingButton: Locator;
  readonly getUsersButton: Locator;
  readonly messagesContainer: Locator;
  readonly messageInput: Locator;
  readonly sendButton: Locator;
  readonly onlineUsers: Locator;
  readonly clearLogButton: Locator;

  constructor(page: Page) {
    super(page);
    
    // 页面标题和导航
    this.pageTitle = page.getByRole('heading', { name: /Axum 任务管理系统/i });
    this.mainContainer = page.locator('.main-container');

    // 认证面板定位器 - 使用用户友好的定位器
    this.authPanel = page.locator('.panel').first();
    this.authStatus = page.locator('.auth-status');
    this.loginForm = page.locator('#loginForm');
    this.registerForm = page.locator('#registerForm');
    this.usernameInput = page.getByLabel('用户名', { exact: false });
    this.passwordInput = page.getByLabel('密码', { exact: false });
    this.emailInput = page.getByLabel('邮箱', { exact: false });
    this.loginButton = page.getByRole('button', { name: '登录' });
    this.registerButton = page.getByRole('button', { name: '注册' });
    this.logoutButton = page.getByRole('button', { name: '登出' });
    this.authMessage = page.locator('.auth-message');

    // 任务管理面板定位器
    this.taskPanel = page.locator('.panel').nth(1);
    this.taskForm = page.locator('#taskForm');
    this.taskTitleInput = page.getByPlaceholder('任务标题');
    this.taskDescriptionInput = page.getByPlaceholder('任务描述');
    this.createTaskButton = page.getByRole('button', { name: '创建任务' });
    this.taskList = page.locator('#taskList');
    this.taskItems = page.locator('.task-item');
    this.filterButtons = page.locator('.filter-btn');

    // 聊天面板定位器
    this.chatPanel = page.locator('.panel').nth(2);
    this.connectionStatus = page.locator('.connection-status');
    this.connectButton = page.getByRole('button', { name: '连接' });
    this.disconnectButton = page.getByRole('button', { name: '断开连接' });
    this.pingButton = page.getByRole('button', { name: '心跳测试' });
    this.getUsersButton = page.getByRole('button', { name: '获取在线用户' });
    this.messagesContainer = page.locator('.messages-container');
    this.messageInput = page.getByPlaceholder('输入消息...');
    this.sendButton = page.getByRole('button', { name: '发送' });
    this.onlineUsers = page.locator('.online-users');
    this.clearLogButton = page.getByRole('button', { name: '清除日志' });
  }

  /**
   * 验证页面已加载
   */
  async verifyPageLoaded() {
    await expect(this.pageTitle).toBeVisible();
    await expect(this.mainContainer).toBeVisible();
    await expect(this.authPanel).toBeVisible();
    await expect(this.taskPanel).toBeVisible();
    await expect(this.chatPanel).toBeVisible();
  }

  /**
   * 用户登录
   * @param username 用户名
   * @param password 密码
   */
  async login(username: string, password: string) {
    await this.safeFill(this.usernameInput, username);
    await this.safeFill(this.passwordInput, password);
    await this.safeClick(this.loginButton);
    
    // 等待登录响应
    await this.waitForResponse(/\/api\/auth\/login/);
  }

  /**
   * 用户注册
   * @param username 用户名
   * @param email 邮箱
   * @param password 密码
   */
  async register(username: string, email: string, password: string) {
    await this.safeFill(this.usernameInput, username);
    await this.safeFill(this.emailInput, email);
    await this.safeFill(this.passwordInput, password);
    await this.safeClick(this.registerButton);
    
    // 等待注册响应
    await this.waitForResponse(/\/api\/auth\/register/);
  }

  /**
   * 用户登出
   */
  async logout() {
    await this.safeClick(this.logoutButton);
    await this.waitForResponse(/\/api\/auth\/logout/);
  }

  /**
   * 创建任务
   * @param title 任务标题
   * @param description 任务描述
   */
  async createTask(title: string, description: string) {
    await this.safeFill(this.taskTitleInput, title);
    await this.safeFill(this.taskDescriptionInput, description);
    await this.safeClick(this.createTaskButton);
    
    // 等待任务创建响应
    await this.waitForResponse(/\/api\/tasks/);
  }

  /**
   * 获取任务列表中的任务数量
   */
  async getTaskCount(): Promise<number> {
    return await this.taskItems.count();
  }

  /**
   * 获取指定索引的任务文本
   * @param index 任务索引
   */
  async getTaskText(index: number): Promise<string> {
    return await this.taskItems.nth(index).textContent() || '';
  }

  /**
   * 删除指定索引的任务
   * @param index 任务索引
   */
  async deleteTask(index: number) {
    const taskItem = this.taskItems.nth(index);
    const deleteButton = taskItem.locator('.delete-btn');
    await this.safeClick(deleteButton);
    
    // 等待删除响应
    await this.waitForResponse(/\/api\/tasks\/\d+/);
  }

  /**
   * 连接WebSocket
   */
  async connectWebSocket() {
    await this.safeClick(this.connectButton);
    await expect(this.connectionStatus).toHaveText(/已连接/);
  }

  /**
   * 断开WebSocket连接
   */
  async disconnectWebSocket() {
    await this.safeClick(this.disconnectButton);
    await expect(this.connectionStatus).toHaveText(/未连接/);
  }

  /**
   * 发送聊天消息
   * @param message 消息内容
   */
  async sendMessage(message: string) {
    await this.safeFill(this.messageInput, message);
    await this.safeClick(this.sendButton);
    
    // 验证消息输入框已清空
    await expect(this.messageInput).toHaveValue('');
  }

  /**
   * 获取聊天消息数量
   */
  async getMessageCount(): Promise<number> {
    return await this.messagesContainer.locator('.message').count();
  }

  /**
   * 验证认证状态
   * @param isAuthenticated 是否已认证
   */
  async verifyAuthStatus(isAuthenticated: boolean) {
    if (isAuthenticated) {
      await expect(this.authStatus).toContainText('已登录');
      await expect(this.logoutButton).toBeVisible();
    } else {
      await expect(this.authStatus).toContainText('未登录');
      await expect(this.loginButton).toBeVisible();
      await expect(this.registerButton).toBeVisible();
    }
  }

  /**
   * 验证错误消息
   * @param expectedMessage 期望的错误消息
   */
  async verifyErrorMessage(expectedMessage: string) {
    await expect(this.authMessage).toBeVisible();
    await expect(this.authMessage).toContainText(expectedMessage);
  }

  /**
   * 验证成功消息
   * @param expectedMessage 期望的成功消息
   */
  async verifySuccessMessage(expectedMessage: string) {
    await expect(this.authMessage).toBeVisible();
    await expect(this.authMessage).toContainText(expectedMessage);
  }
}
