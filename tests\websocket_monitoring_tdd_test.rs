//! # WebSocket实时监控面板TDD测试
//!
//! 测试WebSocket实时监控功能的完整性和正确性
//!
//! 测试覆盖：
//! - WebSocket监控连接建立
//! - 实时数据推送
//! - 监控数据格式验证
//! - 连接断开处理
//! - 错误处理

use axum::{
    Router,
    body::Body,
    http::{Request, StatusCode},
};
use axum_server::{
    dependency_injection::{ServiceContainer, container::AppContainer},
    routes::create_app_router,
};
use futures_util::{SinkExt, StreamExt};
use serde_json::Value;
use std::{sync::Arc, time::Duration};
use tokio::{net::TcpListener, time::timeout};
use tokio_tungstenite::{connect_async, tungstenite::Message};
use tower::ServiceExt;
use tracing::{info, warn};
use uuid::Uuid;

/// 测试配置
struct TestConfig {
    pub server_port: u16,
    pub timeout_duration: Duration,
}

impl Default for TestConfig {
    fn default() -> Self {
        Self {
            server_port: 0, // 使用随机端口
            timeout_duration: Duration::from_secs(10),
        }
    }
}

/// WebSocket监控测试套件
struct WebSocketMonitoringTestSuite {
    config: TestConfig,
    app_state: Option<axum_server::routes::AppState>,
    server_addr: Option<String>,
}

impl WebSocketMonitoringTestSuite {
    /// 创建新的测试套件
    pub fn new() -> Self {
        Self {
            config: TestConfig::default(),
            app_state: None,
            server_addr: None,
        }
    }

    /// 初始化测试环境
    pub async fn setup(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        // 创建应用容器
        let container = AppContainer::new().await?;

        // 创建应用状态
        let app_state = axum_server::routes::AppState {
            user_service: container.get_user_service(),
            task_service: container.get_task_service(),
            chat_service: container.get_chat_service(),
            websocket_service: container.get_websocket_service(),
            db: container.get_database(),
            jwt_secret: "test_secret_key_for_monitoring_test".to_string(),
        };
        self.app_state = Some(app_state.clone());

        // 创建测试服务器
        let listener = TcpListener::bind("127.0.0.1:0").await?;
        let addr = listener.local_addr()?;
        self.server_addr = Some(format!("127.0.0.1:{}", addr.port()));

        // 创建应用路由
        let app = create_app_router(app_state);

        // 启动服务器
        tokio::spawn(async move {
            axum::serve(listener, app).await.unwrap();
        });

        // 等待服务器启动
        tokio::time::sleep(Duration::from_millis(100)).await;

        info!(
            "测试服务器已启动，地址: {}",
            self.server_addr.as_ref().unwrap()
        );
        Ok(())
    }

    /// 测试WebSocket监控连接建立
    pub async fn test_websocket_monitoring_connection(
        &self,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let server_addr = self.server_addr.as_ref().unwrap();
        let ws_url = format!("ws://{}/api/websocket/monitoring", server_addr);

        info!("测试WebSocket监控连接: {}", ws_url);

        // 建立WebSocket连接
        let (ws_stream, response) =
            timeout(self.config.timeout_duration, connect_async(&ws_url)).await??;

        // 验证连接响应
        assert_eq!(response.status(), 101); // WebSocket升级成功
        info!("✅ WebSocket监控连接建立成功");

        // 关闭连接
        drop(ws_stream);
        Ok(())
    }

    /// 测试实时监控数据推送
    pub async fn test_realtime_monitoring_data_push(
        &self,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let server_addr = self.server_addr.as_ref().unwrap();
        let ws_url = format!("ws://{}/api/websocket/monitoring", server_addr);

        info!("测试实时监控数据推送: {}", ws_url);

        // 建立WebSocket连接
        let (ws_stream, _) =
            timeout(self.config.timeout_duration, connect_async(&ws_url)).await??;

        let (mut _write, mut read) = ws_stream.split();

        // 等待接收监控数据（最多等待10秒）
        let message = timeout(Duration::from_secs(10), read.next()).await?;

        match message {
            Some(Ok(Message::Text(data))) => {
                info!("收到监控数据: {}", data);

                // 解析JSON数据
                let json_data: Value = serde_json::from_str(&data)?;

                // 验证数据结构
                self.validate_monitoring_data_structure(&json_data)?;

                info!("✅ 实时监控数据推送测试成功");
                Ok(())
            }
            Some(Ok(msg)) => {
                warn!("收到非文本消息: {:?}", msg);
                Err("期望文本消息，但收到其他类型".into())
            }
            Some(Err(e)) => Err(format!("WebSocket错误: {}", e).into()),
            None => Err("连接意外关闭".into()),
        }
    }

    /// 验证监控数据结构
    fn validate_monitoring_data_structure(
        &self,
        data: &Value,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // 验证顶级字段
        let timestamp = data
            .get("timestamp")
            .ok_or("缺少timestamp字段")?
            .as_str()
            .ok_or("timestamp字段不是字符串")?;

        let msg_type = data
            .get("type")
            .ok_or("缺少type字段")?
            .as_str()
            .ok_or("type字段不是字符串")?;

        let data_obj = data
            .get("data")
            .ok_or("缺少data字段")?
            .as_object()
            .ok_or("data字段不是对象")?;

        // 验证消息类型
        assert_eq!(
            msg_type, "monitoring_update",
            "消息类型应该是monitoring_update"
        );

        // 验证时间戳格式
        chrono::DateTime::parse_from_rfc3339(timestamp).map_err(|_| "时间戳格式无效")?;

        // 验证连接统计
        let connection_stats = data_obj
            .get("connection_stats")
            .ok_or("缺少connection_stats字段")?
            .as_object()
            .ok_or("connection_stats不是对象")?;

        connection_stats
            .get("active_connections")
            .ok_or("缺少active_connections字段")?
            .as_u64()
            .ok_or("active_connections不是数字")?;

        connection_stats
            .get("total_connections")
            .ok_or("缺少total_connections字段")?
            .as_u64()
            .ok_or("total_connections不是数字")?;

        // 验证消息统计
        let message_stats = data_obj
            .get("message_stats")
            .ok_or("缺少message_stats字段")?
            .as_object()
            .ok_or("message_stats不是对象")?;

        message_stats
            .get("total_messages")
            .ok_or("缺少total_messages字段")?
            .as_u64()
            .ok_or("total_messages不是数字")?;

        // 验证性能指标
        let performance = data_obj
            .get("performance")
            .ok_or("缺少performance字段")?
            .as_object()
            .ok_or("performance不是对象")?;

        performance
            .get("average_latency_ms")
            .ok_or("缺少average_latency_ms字段")?
            .as_f64()
            .ok_or("average_latency_ms不是数字")?;

        // 验证稳定性指标
        let stability = data_obj
            .get("stability")
            .ok_or("缺少stability字段")?
            .as_object()
            .ok_or("stability不是对象")?;

        stability
            .get("stability_score")
            .ok_or("缺少stability_score字段")?
            .as_f64()
            .ok_or("stability_score不是数字")?;

        info!("✅ 监控数据结构验证通过");
        Ok(())
    }

    /// 测试ping-pong机制
    pub async fn test_ping_pong_mechanism(&self) -> Result<(), Box<dyn std::error::Error>> {
        let server_addr = self.server_addr.as_ref().unwrap();
        let ws_url = format!("ws://{}/api/websocket/monitoring", server_addr);

        info!("测试ping-pong机制: {}", ws_url);

        // 建立WebSocket连接
        let (ws_stream, _) =
            timeout(self.config.timeout_duration, connect_async(&ws_url)).await??;

        let (mut write, mut read) = ws_stream.split();

        // 发送ping消息
        write.send(Message::Text("ping".to_string())).await?;
        info!("已发送ping消息");

        // 等待pong响应
        let response = timeout(Duration::from_secs(5), read.next()).await?;

        match response {
            Some(Ok(Message::Text(data))) => {
                if data == "pong" {
                    info!("✅ 收到正确的pong响应");
                    Ok(())
                } else {
                    Err(format!("期望pong响应，但收到: {}", data).into())
                }
            }
            _ => Err("未收到预期的pong响应".into()),
        }
    }

    /// 清理测试环境
    pub async fn cleanup(&mut self) {
        info!("清理测试环境");
        self.app_state = None;
        self.server_addr = None;
    }
}

/// 主测试函数
#[tokio::test]
async fn test_websocket_monitoring_dashboard_basic_functionality() {
    // 初始化日志
    tracing_subscriber::fmt::init();

    let mut test_suite = WebSocketMonitoringTestSuite::new();

    // 设置测试环境
    if let Err(e) = test_suite.setup().await {
        panic!("测试环境设置失败: {}", e);
    }

    // 运行测试
    let test_results = vec![
        (
            "WebSocket监控连接建立",
            test_suite.test_websocket_monitoring_connection().await,
        ),
        (
            "实时监控数据推送",
            test_suite.test_realtime_monitoring_data_push().await,
        ),
        ("Ping-Pong机制", test_suite.test_ping_pong_mechanism().await),
    ];

    // 清理环境
    test_suite.cleanup().await;

    // 检查测试结果
    let mut failed_tests = Vec::new();
    for (test_name, result) in test_results {
        match result {
            Ok(_) => info!("✅ {} - 通过", test_name),
            Err(e) => {
                warn!("❌ {} - 失败: {}", test_name, e);
                failed_tests.push((test_name, e));
            }
        }
    }

    // 如果有失败的测试，panic
    if !failed_tests.is_empty() {
        let error_msg = failed_tests
            .iter()
            .map(|(name, err)| format!("{}: {}", name, err))
            .collect::<Vec<_>>()
            .join("\n");
        panic!("以下测试失败:\n{}", error_msg);
    }

    info!("🎉 所有WebSocket实时监控测试通过！");
}
