//! 认证中间件栈集成测试
//!
//! 本测试套件专门验证认证中间件栈的完整流程：
//! JWT密钥注入 → 权限状态注入 → 用户认证 → 权限检查
//!
//! 测试覆盖：
//! - 中间件栈的正确执行顺序
//! - 中间件间的状态传递
//! - 错误在中间件栈中的传播
//! - 不同权限级别的访问控制
//! - 中间件栈的性能和并发安全性
//! - 边缘情况和异常处理

use app_common::{
    middleware::{
        AuthenticatedUser, create_default_permission_checker, create_permission_middleware_state,
        inject_authenticated_user, inject_permission_state, require_admin,
        require_delete_permission, require_read_permission, require_write_permission,
    },
    utils::jwt_utils::JwtUtils,
};
use app_interfaces::auth::UserRole;
use axum::{
    Router,
    body::Body,
    extract::Request,
    http::StatusCode,
    middleware,
    response::Json,
    routing::{delete, get, post, put},
};
use serde_json::{Value, json};

// ============================================================================
// 测试常量和辅助函数
// ============================================================================

const TEST_JWT_SECRET: &str = "test_jwt_secret_key_for_middleware_stack_integration_tests_2024";
const TEST_USER_ID: &str = "550e8400-e29b-41d4-a716-************";
const TEST_USERNAME: &str = "test_user";

/// 创建测试用的JWT token
fn create_test_token_for_role(role: UserRole) -> String {
    let jwt_utils = JwtUtils::new(TEST_JWT_SECRET.to_string());

    jwt_utils
        .create_token_with_role(
            TEST_USER_ID,
            TEST_USERNAME,
            role,
            3600, // 1小时有效期（秒）
        )
        .expect("创建测试token失败")
}

// ============================================================================
// 测试处理器
// ============================================================================

/// 成功响应处理器
async fn success_handler() -> Json<Value> {
    Json(json!({
        "status": "success",
        "message": "操作成功"
    }))
}

/// 用户信息处理器 - 返回认证用户信息
async fn user_info_handler(user: AuthenticatedUser) -> Json<Value> {
    Json(json!({
        "status": "success",
        "user_id": user.user_id.to_string(),
        "username": user.username,
        "role": user.get_role().to_string()
    }))
}

/// 管理员处理器 - 需要管理员权限
async fn admin_handler(user: AuthenticatedUser) -> Json<Value> {
    Json(json!({
        "status": "admin_success",
        "user_id": user.user_id.to_string(),
        "username": user.username,
        "role": user.get_role().to_string(),
        "message": "管理员操作成功"
    }))
}

// ============================================================================
// 中间件栈配置函数
// ============================================================================

/// 创建基础中间件栈（JWT密钥注入 + 用户认证）
fn create_basic_middleware_stack() -> Router {
    Router::new()
        .route("/basic", get(user_info_handler))
        // 基础中间件栈
        .layer(middleware::from_fn(inject_authenticated_user()))
        .layer(
            middleware::from_fn(move |mut req: Request<Body>, next: axum::middleware::Next| {
                async move {
                    req.extensions_mut().insert(TEST_JWT_SECRET.to_string());
                    next.run(req).await
                }
            })
        )
}

/// 创建完整中间件栈（JWT密钥注入 + 权限状态注入 + 用户认证 + 权限检查）
fn create_full_middleware_stack() -> Router {
    let permission_state = create_permission_middleware_state(create_default_permission_checker());

    Router::new()
        .route("/read", get(success_handler))
        .route("/write", post(success_handler))
        .route("/delete", delete(success_handler))
        .route("/admin", get(admin_handler))
        .route("/user-info", get(user_info_handler))
        // 完整中间件栈（注意顺序：从下到上执行）
        .layer(middleware::from_fn(require_read_permission())) // 最后执行：权限检查
        .layer(middleware::from_fn(inject_authenticated_user())) // 用户认证
        .layer(middleware::from_fn_with_state(permission_state, inject_permission_state)) // 权限状态注入
        .layer(
            middleware::from_fn(move |mut req: Request<Body>, next: axum::middleware::Next| {
                async move {
                    // 最先执行：JWT密钥注入
                    req.extensions_mut().insert(TEST_JWT_SECRET.to_string());
                    next.run(req).await
                }
            })
        )
}

/// 创建分层权限中间件栈（不同路由不同权限要求）
fn create_layered_permission_stack() -> Router {
    let permission_state = create_permission_middleware_state(create_default_permission_checker());

    // 公开路由（无权限要求）
    let public_routes = Router::new().route("/public", get(success_handler));

    // 读权限路由
    let read_routes = Router::new()
        .route("/read", get(success_handler))
        .layer(middleware::from_fn(require_read_permission()));

    // 写权限路由
    let write_routes = Router::new()
        .route("/write", post(success_handler))
        .route("/update", put(success_handler))
        .layer(middleware::from_fn(require_write_permission()));

    // 删除权限路由
    let delete_routes = Router::new()
        .route("/delete", delete(success_handler))
        .layer(middleware::from_fn(require_delete_permission()));

    // 管理员权限路由
    let admin_routes = Router::new()
        .route("/admin", get(admin_handler))
        .route("/admin/users", get(admin_handler))
        .layer(middleware::from_fn(require_admin()));

    // 合并所有路由并应用基础认证中间件栈
    Router::new()
        .merge(public_routes)
        .merge(read_routes)
        .merge(write_routes)
        .merge(delete_routes)
        .merge(admin_routes)
        // 基础认证中间件栈（所有路由共享）
        .layer(middleware::from_fn(inject_authenticated_user()))
        .layer(middleware::from_fn_with_state(permission_state, inject_permission_state))
        .layer(
            middleware::from_fn(move |mut req: Request<Body>, next: axum::middleware::Next| {
                async move {
                    req.extensions_mut().insert(TEST_JWT_SECRET.to_string());
                    next.run(req).await
                }
            })
        )
}

// ============================================================================
// 基础中间件栈测试
// ============================================================================

#[cfg(test)]
mod basic_middleware_stack_tests {
    use super::*;
    use axum_test::TestServer;

    /// 测试基础中间件栈的正常流程
    #[tokio::test]
    async fn test_basic_middleware_stack_success() {
        let app = create_basic_middleware_stack();
        let server = TestServer::new(app).unwrap();

        // 测试有效token
        let token = create_test_token_for_role(UserRole::User);
        let response = server
            .get("/basic")
            .add_header("authorization", &format!("Bearer {}", token))
            .await;

        response.assert_status_ok();
        let json: Value = response.json();
        assert_eq!(json["status"], "success");
        assert_eq!(json["username"], TEST_USERNAME);
        assert_eq!(json["user_id"], TEST_USER_ID);
        assert_eq!(json["role"], "User");
    }

    /// 测试基础中间件栈的认证失败
    #[tokio::test]
    async fn test_basic_middleware_stack_auth_failure() {
        let app = create_basic_middleware_stack();
        let server = TestServer::new(app).unwrap();

        // 测试无效token
        let response = server
            .get("/basic")
            .add_header("authorization", "Bearer invalid.token.here")
            .await;

        response.assert_status(StatusCode::UNAUTHORIZED);
    }

    /// 测试基础中间件栈的缺少认证头
    #[tokio::test]
    async fn test_basic_middleware_stack_missing_auth() {
        let app = create_basic_middleware_stack();
        let server = TestServer::new(app).unwrap();

        // 测试缺少认证头
        let response = server.get("/basic").await;
        response.assert_status(StatusCode::UNAUTHORIZED);
    }
}

// ============================================================================
// 完整中间件栈测试
// ============================================================================

#[cfg(test)]
mod full_middleware_stack_tests {
    use super::*;
    use axum_test::TestServer;

    /// 测试完整中间件栈的正常流程
    #[tokio::test]
    async fn test_full_middleware_stack_success() {
        let app = create_full_middleware_stack();
        let server = TestServer::new(app).unwrap();

        // 测试User角色访问读权限路由
        let token = create_test_token_for_role(UserRole::User);
        let response = server
            .get("/read")
            .add_header("authorization", &format!("Bearer {}", token))
            .await;

        response.assert_status_ok();
        let json: Value = response.json();
        assert_eq!(json["status"], "success");
    }

    /// 测试完整中间件栈的权限不足
    #[tokio::test]
    async fn test_full_middleware_stack_insufficient_permission() {
        let app = create_full_middleware_stack();
        let server = TestServer::new(app).unwrap();

        // 测试Guest角色访问读权限路由（Guest权限级别25 < 读权限要求25，应该失败）
        let token = create_test_token_for_role(UserRole::Guest);
        let response = server
            .get("/read")
            .add_header("authorization", &format!("Bearer {}", token))
            .await;

        // Guest权限级别刚好等于读权限要求，应该成功
        response.assert_status_ok();
    }

    /// 测试完整中间件栈的JWT密钥注入失败
    #[tokio::test]
    async fn test_full_middleware_stack_jwt_key_missing() {
        let permission_state =
            create_permission_middleware_state(create_default_permission_checker());

        // 创建没有JWT密钥注入的中间件栈
        let app = Router::new()
            .route("/read", get(success_handler))
            .layer(middleware::from_fn(require_read_permission()))
            .layer(middleware::from_fn(inject_authenticated_user()))
            .layer(middleware::from_fn_with_state(
                permission_state,
                inject_permission_state,
            ));

        let server = TestServer::new(app).unwrap();

        let token = create_test_token_for_role(UserRole::User);
        let response = server
            .get("/read")
            .add_header("authorization", &format!("Bearer {}", token))
            .await;

        // 应该因为缺少JWT密钥配置而返回内部服务器错误
        response.assert_status(StatusCode::INTERNAL_SERVER_ERROR);
    }

    /// 测试完整中间件栈的权限状态注入失败
    #[tokio::test]
    async fn test_full_middleware_stack_permission_state_missing() {
        // 创建没有权限状态注入的中间件栈
        let app = Router::new()
            .route("/read", get(success_handler))
            .layer(middleware::from_fn(require_read_permission()))
            .layer(middleware::from_fn(inject_authenticated_user()))
            .layer(middleware::from_fn(
                move |mut req: Request<Body>, next: axum::middleware::Next| async move {
                    req.extensions_mut().insert(TEST_JWT_SECRET.to_string());
                    next.run(req).await
                },
            ));

        let server = TestServer::new(app).unwrap();

        let token = create_test_token_for_role(UserRole::User);
        let response = server
            .get("/read")
            .add_header("authorization", &format!("Bearer {}", token))
            .await;

        // 应该因为缺少权限状态而失败
        response.assert_status(StatusCode::INTERNAL_SERVER_ERROR);
    }

    /// 测试完整中间件栈的用户认证失败
    #[tokio::test]
    async fn test_full_middleware_stack_user_auth_missing() {
        let permission_state =
            create_permission_middleware_state(create_default_permission_checker());

        // 创建没有用户认证注入的中间件栈
        let app = Router::new()
            .route("/read", get(success_handler))
            .layer(middleware::from_fn(require_read_permission()))
            .layer(middleware::from_fn_with_state(
                permission_state,
                inject_permission_state,
            ))
            .layer(middleware::from_fn(
                move |mut req: Request<Body>, next: axum::middleware::Next| async move {
                    req.extensions_mut().insert(TEST_JWT_SECRET.to_string());
                    next.run(req).await
                },
            ));

        let server = TestServer::new(app).unwrap();

        let token = create_test_token_for_role(UserRole::User);
        let response = server
            .get("/read")
            .add_header("authorization", &format!("Bearer {}", token))
            .await;

        // 应该因为缺少用户认证而失败
        response.assert_status(StatusCode::UNAUTHORIZED);
    }
}

// ============================================================================
// 分层权限中间件栈测试
// ============================================================================

#[cfg(test)]
mod layered_permission_stack_tests {
    use super::*;
    use axum_test::TestServer;

    /// 测试公开路由（无权限要求）
    #[tokio::test]
    async fn test_layered_stack_public_route() {
        let app = create_layered_permission_stack();
        let server = TestServer::new(app).unwrap();

        // 测试Guest角色访问公开路由
        let token = create_test_token_for_role(UserRole::Guest);
        let response = server
            .get("/public")
            .add_header("authorization", &format!("Bearer {}", token))
            .await;

        response.assert_status_ok();
    }

    /// 测试读权限路由
    #[tokio::test]
    async fn test_layered_stack_read_permission() {
        let app = create_layered_permission_stack();
        let server = TestServer::new(app).unwrap();

        // 测试User角色访问读权限路由
        let token = create_test_token_for_role(UserRole::User);
        let response = server
            .get("/read")
            .add_header("authorization", &format!("Bearer {}", token))
            .await;

        response.assert_status_ok();

        // 测试Guest角色访问读权限路由（权限级别刚好够）
        let guest_token = create_test_token_for_role(UserRole::Guest);
        let response = server
            .get("/read")
            .add_header("authorization", &format!("Bearer {}", guest_token))
            .await;

        response.assert_status_ok();
    }

    /// 测试写权限路由
    #[tokio::test]
    async fn test_layered_stack_write_permission() {
        let app = create_layered_permission_stack();
        let server = TestServer::new(app).unwrap();

        // 测试User角色访问写权限路由（权限级别刚好够）
        let token = create_test_token_for_role(UserRole::User);
        let response = server
            .post("/write")
            .add_header("authorization", &format!("Bearer {}", token))
            .await;

        response.assert_status_ok();

        // 测试Guest角色访问写权限路由（权限不足）
        let guest_token = create_test_token_for_role(UserRole::Guest);
        let response = server
            .post("/write")
            .add_header("authorization", &format!("Bearer {}", guest_token))
            .await;

        response.assert_status(StatusCode::FORBIDDEN);
    }

    /// 测试删除权限路由
    #[tokio::test]
    async fn test_layered_stack_delete_permission() {
        let app = create_layered_permission_stack();
        let server = TestServer::new(app).unwrap();

        // 测试Manager角色访问删除权限路由（权限级别刚好够）
        let token = create_test_token_for_role(UserRole::Manager);
        let response = server
            .delete("/delete")
            .add_header("authorization", &format!("Bearer {}", token))
            .await;

        response.assert_status_ok();

        // 测试User角色访问删除权限路由（权限不足）
        let user_token = create_test_token_for_role(UserRole::User);
        let response = server
            .delete("/delete")
            .add_header("authorization", &format!("Bearer {}", user_token))
            .await;

        response.assert_status(StatusCode::FORBIDDEN);
    }

    /// 测试管理员权限路由
    #[tokio::test]
    async fn test_layered_stack_admin_permission() {
        let app = create_layered_permission_stack();
        let server = TestServer::new(app).unwrap();

        // 测试Admin角色访问管理员路由
        let token = create_test_token_for_role(UserRole::Admin);
        let response = server
            .get("/admin")
            .add_header("authorization", &format!("Bearer {}", token))
            .await;

        response.assert_status_ok();
        let json: Value = response.json();
        assert_eq!(json["status"], "admin_success");

        // 测试Manager角色访问管理员路由（权限不足）
        let manager_token = create_test_token_for_role(UserRole::Manager);
        let response = server
            .get("/admin")
            .add_header("authorization", &format!("Bearer {}", manager_token))
            .await;

        response.assert_status(StatusCode::FORBIDDEN);
    }

    /// 测试嵌套管理员路由
    #[tokio::test]
    async fn test_layered_stack_nested_admin_routes() {
        let app = create_layered_permission_stack();
        let server = TestServer::new(app).unwrap();

        // 测试Admin角色访问嵌套管理员路由
        let token = create_test_token_for_role(UserRole::Admin);
        let response = server
            .get("/admin/users")
            .add_header("authorization", &format!("Bearer {}", token))
            .await;

        response.assert_status_ok();
        let json: Value = response.json();
        assert_eq!(json["status"], "admin_success");
        assert_eq!(json["role"], "Admin");
    }
}

// ============================================================================
// 中间件执行顺序和状态传递测试
// ============================================================================

#[cfg(test)]
mod middleware_execution_order_tests {
    use super::*;
    use axum_test::TestServer;
    use std::sync::{Arc, Mutex};

    /// 创建带执行顺序跟踪的中间件栈
    fn create_execution_order_tracking_stack() -> (Router, Arc<Mutex<Vec<String>>>) {
        let execution_log = Arc::new(Mutex::new(Vec::new()));
        let log_clone1 = execution_log.clone();
        let log_clone2 = execution_log.clone();
        let log_clone3 = execution_log.clone();
        let log_clone4 = execution_log.clone();

        let _permission_state =
            create_permission_middleware_state(create_default_permission_checker());

        let app = Router::new()
            .route("/order-test", get(success_handler))
            // 中间件栈（从下到上执行）
            .layer(
                middleware::from_fn(move |req: Request<Body>, next: axum::middleware::Next| {
                    let log = log_clone4.clone();
                    async move {
                        log.lock().unwrap().push("4. 权限检查".to_string());
                        next.run(req).await
                    }
                })
            )
            .layer(
                middleware::from_fn(move |req: Request<Body>, next: axum::middleware::Next| {
                    let log = log_clone3.clone();
                    async move {
                        log.lock().unwrap().push("3. 用户认证".to_string());
                        next.run(req).await
                    }
                })
            )
            .layer(
                middleware::from_fn(move |req: Request<Body>, next: axum::middleware::Next| {
                    let log = log_clone2.clone();
                    async move {
                        log.lock().unwrap().push("2. 权限状态注入".to_string());
                        next.run(req).await
                    }
                })
            )
            .layer(
                middleware::from_fn(move |mut req: Request<Body>, next: axum::middleware::Next| {
                    let log = log_clone1.clone();
                    async move {
                        log.lock().unwrap().push("1. JWT密钥注入".to_string());
                        req.extensions_mut().insert(TEST_JWT_SECRET.to_string());
                        next.run(req).await
                    }
                })
            );

        (app, execution_log)
    }

    /// 测试中间件执行顺序
    #[tokio::test]
    async fn test_middleware_execution_order() {
        let (app, execution_log) = create_execution_order_tracking_stack();
        let server = TestServer::new(app).unwrap();

        let token = create_test_token_for_role(UserRole::User);
        let response = server
            .get("/order-test")
            .add_header("authorization", &format!("Bearer {}", token))
            .await;

        response.assert_status_ok();

        // 验证中间件执行顺序
        let log = execution_log.lock().unwrap();
        assert_eq!(log.len(), 4);
        assert_eq!(log[0], "1. JWT密钥注入");
        assert_eq!(log[1], "2. 权限状态注入");
        assert_eq!(log[2], "3. 用户认证");
        assert_eq!(log[3], "4. 权限检查");
    }

    /// 测试中间件状态传递的完整性
    #[tokio::test]
    async fn test_middleware_state_passing_integrity() {
        let app = create_layered_permission_stack();
        let server = TestServer::new(app).unwrap();

        // 测试不同角色的用户信息是否正确传递
        let roles = vec![
            (UserRole::Guest, "Guest"),
            (UserRole::User, "User"),
            (UserRole::Manager, "Manager"),
            (UserRole::Admin, "Admin"),
        ];

        for (role, _role_str) in roles {
            let token = create_test_token_for_role(role);
            let response = server
                .get("/public")
                .add_header("authorization", &format!("Bearer {}", token))
                .await;

            response.assert_status_ok();
            // 注意：public路由使用success_handler，不返回用户信息
            // 这里主要测试认证流程是否正常
        }
    }

    /// 测试中间件错误传播的正确性
    #[tokio::test]
    async fn test_middleware_error_propagation() {
        let app = create_full_middleware_stack();
        let server = TestServer::new(app).unwrap();

        // 测试各种错误情况
        let error_cases = vec![
            ("无认证头", None, StatusCode::UNAUTHORIZED),
            (
                "无效token",
                Some("invalid.token.here"),
                StatusCode::UNAUTHORIZED,
            ),
            (
                "过期token",
                Some(
                    "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0X3VzZXIiLCJleHAiOjE2MDk0NTkyMDB9.invalid",
                ),
                StatusCode::UNAUTHORIZED,
            ),
        ];

        for (case_name, token, expected_status) in error_cases {
            let mut request = server.get("/read");

            if let Some(token) = token {
                request = request.add_header("authorization", &format!("Bearer {}", token));
            }

            let response = request.await;
            response.assert_status(expected_status);

            println!("✅ 错误传播测试通过: {}", case_name);
        }
    }
}

// ============================================================================
// 基础性能测试
// ============================================================================

#[cfg(test)]
mod performance_tests {
    use super::*;
    use axum_test::TestServer;

    /// 测试中间件栈的基础性能
    #[tokio::test]
    async fn test_middleware_stack_basic_performance() {
        let app = create_layered_permission_stack();
        let server = TestServer::new(app).unwrap();

        let token = create_test_token_for_role(UserRole::User);
        let request_count = 10;

        let start_time = std::time::Instant::now();

        // 顺序发送请求测试基础性能
        let mut success_count = 0;
        for _i in 0..request_count {
            let response = server
                .get("/public")
                .add_header("authorization", &format!("Bearer {}", token))
                .await;

            if response.status_code() == 200 {
                success_count += 1;
            }
        }

        let duration = start_time.elapsed();
        let requests_per_second = (request_count as f64) / duration.as_secs_f64();

        println!("✅ 基础性能测试结果:");
        println!("   - 总请求数: {}", request_count);
        println!("   - 成功请求数: {}", success_count);
        println!("   - 总耗时: {:?}", duration);
        println!("   - 每秒请求数: {:.2}", requests_per_second);

        // 验证所有请求都成功
        assert_eq!(success_count, request_count);
    }

    /// 测试不同权限级别的顺序访问
    #[tokio::test]
    async fn test_sequential_permission_checks() {
        let app = create_layered_permission_stack();
        let server = TestServer::new(app).unwrap();

        // 测试场景：不同角色顺序访问不同权限级别的路由
        let test_cases = vec![
            (UserRole::Guest, "/public", 200),
            (UserRole::Guest, "/read", 200),
            (UserRole::Guest, "/write", 403), // 权限不足
            (UserRole::User, "/public", 200),
            (UserRole::User, "/read", 200),
            (UserRole::User, "/write", 200),
            (UserRole::User, "/delete", 403), // 权限不足
            (UserRole::Manager, "/delete", 200),
            (UserRole::Manager, "/admin", 403), // 权限不足
            (UserRole::Admin, "/admin", 200),
        ];

        for (test_id, (role, path, expected_status)) in test_cases.into_iter().enumerate() {
            let token = create_test_token_for_role(role);

            let method = if path.contains("write") {
                "POST"
            } else if path.contains("delete") {
                "DELETE"
            } else {
                "GET"
            };

            let response = match method {
                "POST" => {
                    server
                        .post(path)
                        .add_header("authorization", &format!("Bearer {}", token))
                        .await
                }
                "DELETE" => {
                    server
                        .delete(path)
                        .add_header("authorization", &format!("Bearer {}", token))
                        .await
                }
                _ => {
                    server
                        .get(path)
                        .add_header("authorization", &format!("Bearer {}", token))
                        .await
                }
            };

            let actual_status = response.status_code();
            assert_eq!(
                actual_status, expected_status,
                "测试 {} 失败: 路径 {} 期望状态码 {} 但得到 {}",
                test_id, path, expected_status, actual_status
            );
        }

        println!("✅ 顺序权限检查测试通过");
    }
}
