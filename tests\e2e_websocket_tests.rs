//! # WebSocket功能E2E测试
//!
//! 测试WebSocket连接建立、消息收发、断开重连、多用户并发和广播功能
//! 遵循TDD原则，确保与Axum 0.8.4兼容

use anyhow::Result;

use futures_util::{SinkExt, StreamExt};
use reqwest::Client;
use serde_json::{Value, json};
use std::time::Duration;
use tokio::time::{sleep, timeout};
use tokio_tungstenite::{connect_async, tungstenite::Message as TungsteniteMessage};

/// 测试配置
const SERVER_URL: &str = "127.0.0.1:3000";
const WS_URL: &str = "ws://127.0.0.1:3000/ws";
const TEST_TIMEOUT: Duration = Duration::from_secs(30);

/// 测试用户凭据
const TEST_USER_USERNAME: &str = "testuser456";
const TEST_USER_EMAIL: &str = "<EMAIL>";
const TEST_USER_PASSWORD: &str = "password123";

/// WebSocket测试辅助结构
struct WebSocketTestHelper {
    client: Client,
    jwt_token: Option<String>,
}

impl WebSocketTestHelper {
    /// 创建新的测试辅助实例
    fn new() -> Self {
        Self {
            client: Client::new(),
            jwt_token: None,
        }
    }

    /// 用户注册
    async fn register(&self) -> Result<()> {
        let register_url = format!("http://{}/api/auth/register", SERVER_URL);
        let register_data = json!({
            "username": TEST_USER_USERNAME,
            "email": TEST_USER_EMAIL,
            "password": TEST_USER_PASSWORD
        });

        let response = self
            .client
            .post(&register_url)
            .json(&register_data)
            .send()
            .await?;

        // 如果用户已存在，忽略错误
        if response.status().is_success() || response.status() == reqwest::StatusCode::CONFLICT {
            Ok(())
        } else {
            anyhow::bail!("注册失败: {}", response.status());
        }
    }

    /// 用户登录并获取JWT token
    async fn login(&mut self) -> Result<String> {
        let login_url = format!("http://{}/api/auth/login", SERVER_URL);
        let login_data = json!({
            "username": TEST_USER_USERNAME,
            "password": TEST_USER_PASSWORD
        });

        let mut response = self
            .client
            .post(&login_url)
            .json(&login_data)
            .send()
            .await?;

        if !response.status().is_success() {
            // 如果登录失败，尝试先注册用户
            println!("⚠️ 登录失败，尝试注册用户...");
            self.register().await?;

            // 重新尝试登录
            response = self
                .client
                .post(&login_url)
                .json(&login_data)
                .send()
                .await?;
            if !response.status().is_success() {
                anyhow::bail!("登录失败: {}", response.status());
            }
        }

        let response_json: Value = response.json().await?;
        println!(
            "🔍 登录响应: {}",
            serde_json::to_string_pretty(&response_json)?
        );

        // 尝试不同的响应格式
        let token = if let Some(token) = response_json["data"]["token"].as_str() {
            token.to_string()
        } else if let Some(token) = response_json["token"].as_str() {
            token.to_string()
        } else if let Some(data) = response_json["data"].as_object() {
            if let Some(token) = data["access_token"].as_str() {
                token.to_string()
            } else {
                anyhow::bail!(
                    "响应中缺少token字段，响应格式: {}",
                    serde_json::to_string_pretty(&response_json)?
                );
            }
        } else {
            anyhow::bail!(
                "响应中缺少token字段，响应格式: {}",
                serde_json::to_string_pretty(&response_json)?
            );
        };

        self.jwt_token = Some(token.clone());
        Ok(token)
    }

    /// 建立WebSocket连接
    async fn connect_websocket(
        &self,
    ) -> Result<
        tokio_tungstenite::WebSocketStream<
            tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>,
        >,
    > {
        let token = self
            .jwt_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("需要先登录获取JWT token"))?;

        let ws_url_with_token = format!("{}?token={}", WS_URL, token);

        let (ws_stream, _) = connect_async(&ws_url_with_token).await?;
        Ok(ws_stream)
    }

    /// 发送文本消息
    async fn send_text_message(
        &self,
        ws_stream: &mut tokio_tungstenite::WebSocketStream<
            tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>,
        >,
        message: &str,
    ) -> Result<()> {
        ws_stream
            .send(TungsteniteMessage::Text(message.to_string().into()))
            .await?;
        Ok(())
    }

    /// 接收消息（带超时）
    async fn receive_message_with_timeout(
        &self,
        ws_stream: &mut tokio_tungstenite::WebSocketStream<
            tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>,
        >,
        timeout_duration: Duration,
    ) -> Result<Option<TungsteniteMessage>> {
        match timeout(timeout_duration, ws_stream.next()).await {
            Ok(Some(Ok(message))) => Ok(Some(message)),
            Ok(Some(Err(e))) => Err(e.into()),
            Ok(None) => Ok(None),
            Err(_) => Ok(None), // 超时
        }
    }
}

/// 测试1: WebSocket连接建立测试
async fn test_websocket_connection_establishment() -> Result<()> {
    println!("🔧 开始测试: WebSocket连接建立");

    let mut helper = WebSocketTestHelper::new();

    // 步骤1: 用户登录获取JWT token
    println!("📝 步骤1: 用户登录获取JWT token");
    let token = helper.login().await?;
    assert!(!token.is_empty(), "JWT token不应为空");
    println!("✅ 登录成功，获取到JWT token");

    // 步骤2: 建立WebSocket连接
    println!("📝 步骤2: 建立WebSocket连接");
    let ws_stream = helper.connect_websocket().await?;
    println!("✅ WebSocket连接建立成功");

    // 步骤3: 验证连接状态
    println!("📝 步骤3: 验证连接状态");
    // 连接成功建立，无需额外验证
    drop(ws_stream); // 关闭连接

    println!("🎉 WebSocket连接建立测试通过");
    Ok(())
}

/// 测试2: WebSocket连接认证失败测试
async fn test_websocket_connection_auth_failure() -> Result<()> {
    println!("🔧 开始测试: WebSocket连接认证失败");

    // 步骤1: 尝试不带token的连接
    println!("📝 步骤1: 尝试不带token的连接");
    let connection_result = connect_async(WS_URL).await;

    // 应该连接失败或立即关闭
    match connection_result {
        Err(_) => {
            println!("✅ 无token连接被正确拒绝");
        }
        Ok((mut ws_stream, _)) => {
            // 如果连接成功，应该立即收到关闭消息
            if let Some(Ok(message)) = ws_stream.next().await {
                match message {
                    TungsteniteMessage::Close(_) => {
                        println!("✅ 连接被正确关闭");
                    }
                    _ => {
                        anyhow::bail!("期望收到关闭消息，但收到其他消息");
                    }
                }
            }
        }
    }

    // 步骤2: 尝试使用无效token的连接
    println!("📝 步骤2: 尝试使用无效token的连接");
    let invalid_token_url = format!("{}?token=invalid_token", WS_URL);
    let connection_result = connect_async(&invalid_token_url).await;

    match connection_result {
        Err(_) => {
            println!("✅ 无效token连接被正确拒绝");
        }
        Ok((mut ws_stream, _)) => {
            // 如果连接成功，应该立即收到关闭消息
            if let Some(Ok(message)) = ws_stream.next().await {
                match message {
                    TungsteniteMessage::Close(_) => {
                        println!("✅ 无效token连接被正确关闭");
                    }
                    _ => {
                        anyhow::bail!("期望收到关闭消息，但收到其他消息");
                    }
                }
            }
        }
    }

    println!("🎉 WebSocket连接认证失败测试通过");
    Ok(())
}

/// 测试3: WebSocket消息收发功能测试
async fn test_websocket_message_exchange() -> Result<()> {
    println!("🔧 开始测试: WebSocket消息收发功能");

    let mut helper = WebSocketTestHelper::new();

    // 步骤1: 登录并建立连接
    println!("📝 步骤1: 登录并建立WebSocket连接");
    helper.login().await?;
    let mut ws_stream = helper.connect_websocket().await?;
    println!("✅ WebSocket连接建立成功");

    // 步骤2: 发送文本消息
    println!("📝 步骤2: 发送文本消息");
    let test_message = "Hello, WebSocket!";
    helper
        .send_text_message(&mut ws_stream, test_message)
        .await?;
    println!("✅ 消息发送成功: {}", test_message);

    // 步骤3: 等待并验证消息处理
    println!("📝 步骤3: 等待消息处理");
    sleep(Duration::from_millis(100)).await; // 给服务器时间处理消息

    // 步骤4: 发送Ping消息测试
    println!("📝 步骤4: 发送Ping消息测试");
    ws_stream
        .send(TungsteniteMessage::Ping(vec![1, 2, 3, 4].into()))
        .await?;

    // 等待Pong响应
    if let Some(message) = helper
        .receive_message_with_timeout(&mut ws_stream, Duration::from_secs(5))
        .await?
    {
        match message {
            TungsteniteMessage::Pong(data) => {
                assert_eq!(data, vec![1, 2, 3, 4], "Pong数据应该与Ping数据匹配");
                println!("✅ Ping/Pong测试通过");
            }
            _ => {
                println!("⚠️ 收到非Pong消息，可能是其他类型的响应");
            }
        }
    }

    println!("🎉 WebSocket消息收发功能测试通过");
    Ok(())
}

/// 测试4: WebSocket断开重连机制测试
async fn test_websocket_reconnection() -> Result<()> {
    println!("🔧 开始测试: WebSocket断开重连机制");

    let mut helper = WebSocketTestHelper::new();

    // 步骤1: 建立初始连接
    println!("📝 步骤1: 建立初始WebSocket连接");
    helper.login().await?;
    let mut ws_stream = helper.connect_websocket().await?;
    println!("✅ 初始连接建立成功");

    // 步骤2: 发送消息验证连接正常
    println!("📝 步骤2: 验证初始连接正常工作");
    helper
        .send_text_message(&mut ws_stream, "测试消息1")
        .await?;
    println!("✅ 初始连接消息发送成功");

    // 步骤3: 主动关闭连接
    println!("📝 步骤3: 主动关闭WebSocket连接");
    ws_stream.close(None).await?;
    println!("✅ 连接已关闭");

    // 步骤4: 重新建立连接
    println!("📝 步骤4: 重新建立WebSocket连接");
    let mut new_ws_stream = helper.connect_websocket().await?;
    println!("✅ 重连成功");

    // 步骤5: 验证重连后的连接正常工作
    println!("📝 步骤5: 验证重连后连接正常工作");
    helper
        .send_text_message(&mut new_ws_stream, "重连后测试消息")
        .await?;
    println!("✅ 重连后消息发送成功");

    println!("🎉 WebSocket断开重连机制测试通过");
    Ok(())
}

/// 测试5: 多用户并发连接测试
async fn test_websocket_concurrent_connections() -> Result<()> {
    println!("🔧 开始测试: 多用户并发连接");

    // 步骤1: 创建多个用户连接
    println!("📝 步骤1: 创建多个并发WebSocket连接");
    let connection_count = 3;
    let mut connections = Vec::new();

    for i in 0..connection_count {
        let mut helper = WebSocketTestHelper::new();
        helper.login().await?;
        let ws_stream = helper.connect_websocket().await?;
        connections.push((helper, ws_stream));
        println!("✅ 连接 {} 建立成功", i + 1);
    }

    // 步骤2: 验证所有连接都正常工作
    println!("📝 步骤2: 验证所有连接正常工作");
    for (i, (helper, ws_stream)) in connections.iter_mut().enumerate() {
        let message = format!("来自连接{}的消息", i + 1);
        helper.send_text_message(ws_stream, &message).await?;
        println!("✅ 连接 {} 消息发送成功", i + 1);
    }

    // 步骤3: 等待消息处理
    println!("📝 步骤3: 等待消息处理");
    sleep(Duration::from_millis(500)).await;

    // 步骤4: 关闭所有连接
    println!("📝 步骤4: 关闭所有连接");
    for (i, (_, ws_stream)) in connections.into_iter().enumerate() {
        drop(ws_stream);
        println!("✅ 连接 {} 已关闭", i + 1);
    }

    println!("🎉 多用户并发连接测试通过");
    Ok(())
}

/// 测试6: WebSocket广播功能测试
async fn test_websocket_broadcast_functionality() -> Result<()> {
    println!("🔧 开始测试: WebSocket广播功能");

    // 步骤1: 创建发送者连接
    println!("📝 步骤1: 创建发送者WebSocket连接");
    let mut sender_helper = WebSocketTestHelper::new();
    sender_helper.login().await?;
    let mut sender_stream = sender_helper.connect_websocket().await?;
    println!("✅ 发送者连接建立成功");

    // 步骤2: 创建接收者连接
    println!("📝 步骤2: 创建接收者WebSocket连接");
    let mut receiver_helper = WebSocketTestHelper::new();
    receiver_helper.login().await?;
    let mut receiver_stream = receiver_helper.connect_websocket().await?;
    println!("✅ 接收者连接建立成功");

    // 步骤3: 等待连接稳定
    println!("📝 步骤3: 等待连接稳定");
    sleep(Duration::from_millis(500)).await;

    // 步骤4: 发送广播消息
    println!("📝 步骤4: 发送广播消息");
    let broadcast_message = "这是一条广播消息";
    sender_helper
        .send_text_message(&mut sender_stream, broadcast_message)
        .await?;
    println!("✅ 广播消息发送成功");

    // 步骤5: 验证接收者收到广播消息
    println!("📝 步骤5: 验证接收者收到广播消息");

    // 循环接收消息，直到收到文本消息或超时
    let start_time = std::time::Instant::now();
    let timeout_duration = Duration::from_secs(10);
    let mut received_broadcast = false;

    while start_time.elapsed() < timeout_duration && !received_broadcast {
        if let Some(message) = receiver_helper
            .receive_message_with_timeout(&mut receiver_stream, Duration::from_secs(2))
            .await?
        {
            match message {
                TungsteniteMessage::Text(content) => {
                    println!("✅ 接收到广播消息: {}", content);
                    // 验证消息内容包含发送的内容
                    if content.contains(broadcast_message) {
                        received_broadcast = true;
                        break;
                    } else {
                        println!("⚠️ 收到文本消息但内容不匹配: {}", content);
                    }
                }
                TungsteniteMessage::Binary(data) => {
                    println!(
                        "🔍 收到二进制消息，长度: {} 字节，继续等待文本消息",
                        data.len()
                    );
                }
                TungsteniteMessage::Ping(data) => {
                    println!(
                        "🔍 收到Ping消息，长度: {} 字节，继续等待文本消息",
                        data.len()
                    );
                }
                TungsteniteMessage::Pong(data) => {
                    println!(
                        "🔍 收到Pong消息，长度: {} 字节，继续等待文本消息",
                        data.len()
                    );
                }
                TungsteniteMessage::Close(frame) => {
                    println!("🔍 收到关闭消息: {:?}", frame);
                    anyhow::bail!("连接被关闭");
                }
                TungsteniteMessage::Frame(_) => {
                    println!("🔍 收到原始帧消息，继续等待文本消息");
                }
            }
        }
    }

    if !received_broadcast {
        anyhow::bail!("未在超时时间内收到预期的广播消息");
    }

    println!("🎉 WebSocket广播功能测试通过");
    Ok(())
}

/// 运行所有WebSocket功能测试
async fn run_all_websocket_tests() -> Result<()> {
    println!("🚀 开始运行完整WebSocket功能测试套件");

    // 等待服务器启动
    sleep(Duration::from_secs(2)).await;

    // 运行所有测试
    println!("\n=== 测试1: 连接建立 ===");
    test_websocket_connection_establishment().await?;

    println!("\n=== 测试2: 认证失败 ===");
    test_websocket_connection_auth_failure().await?;

    println!("\n=== 测试3: 消息收发 ===");
    test_websocket_message_exchange().await?;

    println!("\n=== 测试4: 断开重连 ===");
    test_websocket_reconnection().await?;

    println!("\n=== 测试5: 并发连接 ===");
    test_websocket_concurrent_connections().await?;

    println!("\n=== 测试6: 广播功能 ===");
    test_websocket_broadcast_functionality().await?;

    println!("\n🎉 所有WebSocket功能测试通过！");
    Ok(())
}

/// 主函数 - 用于二进制执行
#[tokio::main]
async fn main() -> Result<()> {
    run_all_websocket_tests().await
}
