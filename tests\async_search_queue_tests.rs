//! # 异步搜索队列系统测试
//!
//! 测试异步搜索队列系统的核心功能，包括：
//! - 队列基本操作（入队、出队）
//! - 优先级排序
//! - 任务去重
//! - 并发处理
//! - 错误恢复

use app_application::{
    AsyncSearchQueue, NotifierConfig, QueueConfig, SchedulerConfig, SearchResultNotifier,
    SearchTaskExecutor, SearchTaskScheduler, TaskResultNotifier,
};
use app_common::{AppError, Result};
use app_domain::entities::search_task::{
    SearchTask, SearchTaskPriority, SearchTaskResult, SearchTaskType,
};
use std::sync::Arc;
use tokio::time::{Duration, sleep};
use uuid::Uuid;

/// 模拟搜索任务执行器
struct MockSearchExecutor {
    delay_ms: u64,
    should_fail: bool,
}

impl MockSearchExecutor {
    fn new(delay_ms: u64, should_fail: bool) -> Self {
        Self {
            delay_ms,
            should_fail,
        }
    }
}

#[async_trait::async_trait]
impl SearchTaskExecutor for MockSearchExecutor {
    async fn execute(&self, task: &SearchTask) -> Result<SearchTaskResult> {
        // 模拟执行延迟
        sleep(Duration::from_millis(self.delay_ms)).await;

        if self.should_fail {
            return Err(AppError::InternalServerError("模拟执行失败".to_string()));
        }

        // 创建模拟搜索结果
        let result_data = serde_json::json!({
            "query": task.query,
            "results": [
                {"id": 1, "content": "测试消息1"},
                {"id": 2, "content": "测试消息2"}
            ]
        });

        Ok(SearchTaskResult::new(
            task.id,
            result_data,
            2,
            self.delay_ms,
            false,
        ))
    }
}

/// 模拟结果通知器
struct MockResultNotifier;

#[async_trait::async_trait]
impl TaskResultNotifier for MockResultNotifier {
    async fn notify_completion(
        &self,
        _task: &SearchTask,
        _result: Option<SearchTaskResult>,
        _error: Option<String>,
    ) -> Result<()> {
        // 模拟通知成功
        Ok(())
    }
}

#[tokio::test]
async fn test_queue_basic_operations() {
    // 测试队列基本操作
    let config = QueueConfig::default();
    let queue = AsyncSearchQueue::new(config);

    // 创建测试任务
    let task = SearchTask::new(
        Uuid::new_v4(),
        "测试查询".to_string(),
        SearchTaskType::FullTextSearch,
        SearchTaskPriority::Normal,
    );

    // 测试入队
    let task_id = queue.enqueue(task.clone()).await.unwrap();
    assert_eq!(task_id, task.id);

    // 检查队列统计
    let metrics = queue.get_metrics().await;
    assert_eq!(metrics.pending_tasks, 1);
    assert_eq!(metrics.processing_tasks, 0);

    // 测试出队
    let dequeued_task = queue.dequeue().await.unwrap();
    assert_eq!(dequeued_task.id, task.id);
    assert_eq!(dequeued_task.query, "测试查询");

    // 检查队列统计
    let metrics = queue.get_metrics().await;
    assert_eq!(metrics.pending_tasks, 0);
    assert_eq!(metrics.processing_tasks, 1);
}

#[tokio::test]
async fn test_priority_ordering() {
    // 测试优先级排序
    let config = QueueConfig::default();
    let queue = AsyncSearchQueue::new(config);

    // 创建不同优先级的任务
    let low_task = SearchTask::new(
        Uuid::new_v4(),
        "低优先级查询".to_string(),
        SearchTaskType::FullTextSearch,
        SearchTaskPriority::Low,
    );

    let high_task = SearchTask::new(
        Uuid::new_v4(),
        "高优先级查询".to_string(),
        SearchTaskType::FullTextSearch,
        SearchTaskPriority::High,
    );

    let critical_task = SearchTask::new(
        Uuid::new_v4(),
        "紧急查询".to_string(),
        SearchTaskType::FullTextSearch,
        SearchTaskPriority::Critical,
    );

    // 按低、高、紧急的顺序入队
    queue.enqueue(low_task.clone()).await.unwrap();
    queue.enqueue(high_task.clone()).await.unwrap();
    queue.enqueue(critical_task.clone()).await.unwrap();

    // 出队应该按紧急、高、低的顺序
    let first_task = queue.dequeue().await.unwrap();
    assert_eq!(first_task.id, critical_task.id);
    assert_eq!(first_task.priority, SearchTaskPriority::Critical);

    let second_task = queue.dequeue().await.unwrap();
    assert_eq!(second_task.id, high_task.id);
    assert_eq!(second_task.priority, SearchTaskPriority::High);

    let third_task = queue.dequeue().await.unwrap();
    assert_eq!(third_task.id, low_task.id);
    assert_eq!(third_task.priority, SearchTaskPriority::Low);
}

#[tokio::test]
async fn test_task_deduplication() {
    // 测试任务去重
    let mut config = QueueConfig::default();
    config.enable_deduplication = true;
    let queue = AsyncSearchQueue::new(config);

    let user_id = Uuid::new_v4();

    // 创建两个相同的任务
    let task1 = SearchTask::new(
        user_id,
        "重复查询".to_string(),
        SearchTaskType::FullTextSearch,
        SearchTaskPriority::Normal,
    );

    let mut task2 = task1.clone();
    task2.id = Uuid::new_v4(); // 不同的ID但内容相同

    // 入队第一个任务
    let task_id1 = queue.enqueue(task1).await.unwrap();

    // 入队第二个任务（应该被去重）
    let task_id2 = queue.enqueue(task2).await.unwrap();

    // 应该返回相同的任务ID
    assert_eq!(task_id1, task_id2);

    // 队列中应该只有一个任务
    let metrics = queue.get_metrics().await;
    assert_eq!(metrics.pending_tasks, 1);
    assert_eq!(metrics.deduplicated_tasks, 1);
}

#[tokio::test]
async fn test_queue_capacity_limit() {
    // 测试队列容量限制
    let mut config = QueueConfig::default();
    config.max_capacity = 2; // 设置最大容量为2
    let queue = AsyncSearchQueue::new(config);

    // 添加两个任务（应该成功）
    let task1 = SearchTask::new(
        Uuid::new_v4(),
        "查询1".to_string(),
        SearchTaskType::FullTextSearch,
        SearchTaskPriority::Normal,
    );

    let task2 = SearchTask::new(
        Uuid::new_v4(),
        "查询2".to_string(),
        SearchTaskType::FullTextSearch,
        SearchTaskPriority::Normal,
    );

    assert!(queue.enqueue(task1).await.is_ok());
    assert!(queue.enqueue(task2).await.is_ok());

    // 添加第三个任务（应该失败）
    let task3 = SearchTask::new(
        Uuid::new_v4(),
        "查询3".to_string(),
        SearchTaskType::FullTextSearch,
        SearchTaskPriority::Normal,
    );

    let result = queue.enqueue(task3).await;
    assert!(result.is_err());

    if let Err(AppError::ValidationError(msg)) = result {
        assert!(msg.contains("队列已满"));
    } else {
        panic!("期望ValidationError错误");
    }
}

#[tokio::test]
async fn test_task_cancellation() {
    // 测试任务取消
    let config = QueueConfig::default();
    let queue = AsyncSearchQueue::new(config);

    // 创建测试任务
    let task = SearchTask::new(
        Uuid::new_v4(),
        "待取消查询".to_string(),
        SearchTaskType::FullTextSearch,
        SearchTaskPriority::Normal,
    );

    let task_id = queue.enqueue(task).await.unwrap();

    // 取消任务
    let cancelled = queue.cancel_task(task_id).await;
    assert!(cancelled);

    // 检查队列统计
    let metrics = queue.get_metrics().await;
    assert_eq!(metrics.pending_tasks, 0);
    assert_eq!(metrics.cancelled_tasks, 1);
}

#[tokio::test]
async fn test_task_completion() {
    // 测试任务完成
    let config = QueueConfig::default();
    let queue = AsyncSearchQueue::new(config);

    // 创建测试任务
    let task = SearchTask::new(
        Uuid::new_v4(),
        "完成查询".to_string(),
        SearchTaskType::FullTextSearch,
        SearchTaskPriority::Normal,
    );

    let task_id = queue.enqueue(task).await.unwrap();
    let _dequeued_task = queue.dequeue().await.unwrap();

    // 标记任务完成
    queue.complete_task(task_id, true, None).await;

    // 检查队列统计
    let metrics = queue.get_metrics().await;
    assert_eq!(metrics.processing_tasks, 0);
    assert_eq!(metrics.completed_tasks, 1);
}

#[tokio::test]
async fn test_task_failure() {
    // 测试任务失败
    let config = QueueConfig::default();
    let queue = AsyncSearchQueue::new(config);

    // 创建测试任务
    let task = SearchTask::new(
        Uuid::new_v4(),
        "失败查询".to_string(),
        SearchTaskType::FullTextSearch,
        SearchTaskPriority::Normal,
    );

    let task_id = queue.enqueue(task).await.unwrap();
    let _dequeued_task = queue.dequeue().await.unwrap();

    // 标记任务失败
    queue
        .complete_task(task_id, false, Some("执行失败".to_string()))
        .await;

    // 检查队列统计
    let metrics = queue.get_metrics().await;
    assert_eq!(metrics.processing_tasks, 0);
    assert_eq!(metrics.failed_tasks, 1);
}

#[tokio::test]
async fn test_scheduler_basic_functionality() {
    // 测试调度器基本功能
    let scheduler_config = SchedulerConfig {
        worker_count: 2,
        ..Default::default()
    };
    let queue_config = QueueConfig::default();

    let executor = Arc::new(MockSearchExecutor::new(10, false));
    let notifier = Arc::new(MockResultNotifier);

    let scheduler = SearchTaskScheduler::new(scheduler_config, queue_config, executor, notifier);

    // 启动调度器
    scheduler.start().await.unwrap();

    // 提交测试任务
    let task = SearchTask::new(
        Uuid::new_v4(),
        "调度器测试查询".to_string(),
        SearchTaskType::FullTextSearch,
        SearchTaskPriority::Normal,
    );

    let task_id = scheduler.submit_task(task).await.unwrap();

    // 等待任务处理
    sleep(Duration::from_millis(100)).await;

    // 检查任务状态
    let task_status = scheduler.get_task_status(task_id).await;
    assert!(task_status.is_some());

    // 停止调度器
    scheduler.stop().await.unwrap();
}

#[tokio::test]
async fn test_result_notifier() {
    // 测试结果通知器
    let config = NotifierConfig::default();
    let notifier = SearchResultNotifier::new(config, None);

    // 启动通知器
    notifier.start().await.unwrap();

    // 创建测试任务和结果
    let task = SearchTask::new(
        Uuid::new_v4(),
        "通知测试查询".to_string(),
        SearchTaskType::FullTextSearch,
        SearchTaskPriority::Normal,
    );

    let result_data = serde_json::json!({
        "query": task.query,
        "results": []
    });

    let result = SearchTaskResult::new(task.id, result_data, 0, 50, false);

    // 测试成功通知
    let notify_result = notifier.notify_completion(&task, Some(result), None).await;
    assert!(notify_result.is_ok());

    // 测试失败通知
    let notify_result = notifier
        .notify_completion(&task, None, Some("测试错误".to_string()))
        .await;
    assert!(notify_result.is_ok());

    // 检查通知统计
    let stats = notifier.get_notification_stats().await;
    assert!(stats.get("total").unwrap_or(&0) > &0);
}

#[tokio::test]
async fn test_concurrent_operations() {
    // 测试并发操作
    let config = QueueConfig::default();
    let queue = Arc::new(AsyncSearchQueue::new(config));

    let mut handles = Vec::new();

    // 并发添加多个任务
    for i in 0..10 {
        let queue_clone = queue.clone();
        let handle = tokio::spawn(async move {
            let task = SearchTask::new(
                Uuid::new_v4(),
                format!("并发查询{}", i),
                SearchTaskType::FullTextSearch,
                SearchTaskPriority::Normal,
            );
            queue_clone.enqueue(task).await
        });
        handles.push(handle);
    }

    // 等待所有任务完成
    for handle in handles {
        let result = handle.await.unwrap();
        assert!(result.is_ok());
    }

    // 检查队列统计
    let metrics = queue.get_metrics().await;
    assert_eq!(metrics.pending_tasks, 10);
}
