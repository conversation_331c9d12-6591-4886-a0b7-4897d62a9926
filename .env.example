# Axum Tutorial 环境配置示例
# 复制此文件为 .env 并根据需要修改配置

# =============================================================================
# 服务器配置
# =============================================================================

# HTTP服务器监听地址和端口
# 格式: IP:PORT
# 开发环境推荐使用 127.0.0.1:3000
# 生产环境可以使用 0.0.0.0:3000 (允许外部访问)
HTTP_ADDR=127.0.0.1:3000

# =============================================================================
# 数据库配置
# =============================================================================

# 数据库连接URL
# 当前使用SQLite进行开发和测试
DATABASE_URL=sqlite://task_manager.db

# 未来PostgreSQL配置示例（用于生产环境）
# DATABASE_URL=postgres://username:password@localhost:5432/axum_tutorial

# 数据库连接池配置
# 最大连接数（根据CPU核心数调整，通常为核心数的2-4倍）
MAX_CONNECTIONS=10

# 连接超时时间（秒）
CONNECTION_TIMEOUT=30

# 空闲连接超时时间（秒）
IDLE_TIMEOUT=600

# =============================================================================
# 认证配置
# =============================================================================

# JWT密钥（生产环境必须使用强随机字符串）
# 建议使用至少32字符的随机字符串
# 可以使用以下命令生成: openssl rand -base64 32
JWT_SECRET=your-super-secret-and-long-jwt-key-change-this-in-production

# JWT令牌过期时间（小时）
JWT_EXPIRATION_HOURS=24

# 密码哈希轮数（Argon2配置，值越高越安全但越慢）
PASSWORD_HASH_ROUNDS=12

# =============================================================================
# 日志配置
# =============================================================================

# 日志级别配置
# 可选值: trace, debug, info, warn, error
# 开发环境推荐: debug 或 info
# 生产环境推荐: info 或 warn
RUST_LOG=info,server=debug,app_application=debug,app_infrastructure=debug

# 日志格式
# 可选值: json, pretty, compact
LOG_FORMAT=pretty

# 日志文件配置（可选）
# 如果设置，日志将同时输出到文件
# LOG_FILE=logs/axum-tutorial.log

# 日志文件轮转配置
# LOG_MAX_SIZE=10MB
# LOG_MAX_FILES=5

# =============================================================================
# 运行环境配置
# =============================================================================

# 运行环境标识
# 可选值: development, production, testing
RUST_ENV=development

# 是否启用开发模式特性
# 开发模式会启用额外的调试信息和工具
DEV_MODE=true

# =============================================================================
# 性能监控配置
# =============================================================================

# 是否启用性能监控
ENABLE_METRICS=true

# Prometheus指标端点路径
METRICS_PATH=/metrics

# 性能监控采样率（0.0-1.0）
METRICS_SAMPLE_RATE=1.0

# =============================================================================
# WebSocket配置
# =============================================================================

# WebSocket连接超时时间（秒）
WS_TIMEOUT=300

# WebSocket心跳间隔（秒）
WS_HEARTBEAT_INTERVAL=30

# 最大WebSocket连接数
MAX_WS_CONNECTIONS=1000

# =============================================================================
# 缓存配置（未来Redis/DragonflyDB配置）
# =============================================================================

# Redis/DragonflyDB连接URL（未来使用）
# REDIS_URL=redis://localhost:6379

# 缓存过期时间（秒）
# CACHE_TTL=3600

# 缓存键前缀
# CACHE_PREFIX=axum_tutorial:

# =============================================================================
# 安全配置
# =============================================================================

# CORS允许的源（逗号分隔）
# 开发环境可以使用 * 允许所有源
# 生产环境应该指定具体的域名
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# 是否启用HTTPS重定向
FORCE_HTTPS=false

# 安全头配置
ENABLE_SECURITY_HEADERS=true

# =============================================================================
# 限流配置
# =============================================================================

# API请求限流（每分钟请求数）
RATE_LIMIT_PER_MINUTE=60

# 是否启用限流
ENABLE_RATE_LIMITING=true

# =============================================================================
# 文件上传配置
# =============================================================================

# 最大文件上传大小（字节）
MAX_UPLOAD_SIZE=10485760  # 10MB

# 上传文件存储目录
UPLOAD_DIR=uploads

# =============================================================================
# 邮件配置（未来功能）
# =============================================================================

# SMTP服务器配置
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your-app-password
# SMTP_FROM=<EMAIL>

# =============================================================================
# 第三方服务配置
# =============================================================================

# 外部API配置示例
# EXTERNAL_API_URL=https://api.example.com
# EXTERNAL_API_KEY=your-api-key

# =============================================================================
# 开发工具配置
# =============================================================================

# 是否启用API文档
ENABLE_API_DOCS=true

# 是否启用开发者工具
ENABLE_DEV_TOOLS=true

# 热重载配置（开发环境）
ENABLE_HOT_RELOAD=true

# =============================================================================
# MCP工具配置（可选）
# =============================================================================

# API Keys (Required to enable respective provider)
QWEN_API_KEY="your_qwen_api_key_here"                 # Required: 阿里云千问API密钥 Format: sk-...
GITHUB_TOKEN="your_github_token_here"                 # Optional: For GitHub integration features. Format: ghp_... or github_pat_...