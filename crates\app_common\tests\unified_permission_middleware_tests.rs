//! # 统一权限中间件单元测试
//!
//! 本测试模块验证统一权限中间件系统的完整功能，包括：
//! - UnifiedPermissionChecker核心功能测试
//! - 权限检查函数测试（require_permission、require_role等）
//! - 角色验证和权限级别测试
//! - 权限缓存机制测试
//! - 中间件状态管理测试
//! - 错误处理和边缘情况测试
//!
//! ## 测试覆盖范围
//! - 单元测试：权限检查器、缓存机制、配置管理
//! - 集成测试：中间件函数、状态注入、错误处理
//! - 性能测试：缓存效果、批量权限检查
//! - 边缘测试：无效输入、异常情况处理

use app_common::middleware::{
    AuthenticatedUser, PermissionCheckerConfig, UnifiedPermissionChecker,
    create_default_permission_checker, create_enterprise_permission_checker,
    create_high_performance_permission_checker, create_permission_checker_with_config,
    create_permission_middleware_state, require_admin, require_delete_permission, require_manager,
    require_permission, require_read_permission, require_role, require_write_permission,
};
use app_interfaces::auth::{Permission, UserRole};
use axum::{Router, extract::Request, http::StatusCode, middleware, response::Json, routing::get};
use axum_test::TestServer;
use sea_orm::prelude::Uuid;
use serde_json::{Value, json};
use std::time::{Duration, Instant};

// ============================================================================
// 测试常量和辅助函数
// ============================================================================

const TEST_USER_ID: &str = "550e8400-e29b-41d4-a716-************";
const TEST_USERNAME: &str = "test_user";

/// 创建测试用的AuthenticatedUser
fn create_test_user(role: UserRole) -> AuthenticatedUser {
    AuthenticatedUser::new_with_role(
        Uuid::parse_str(TEST_USER_ID).unwrap(),
        TEST_USERNAME.to_string(),
        role,
    )
}

/// 测试用的处理器，返回成功响应
async fn success_handler() -> Json<Value> {
    Json(json!({
        "status": "success",
        "message": "访问成功"
    }))
}

// ============================================================================
// UnifiedPermissionChecker 单元测试
// ============================================================================

#[cfg(test)]
mod unified_permission_checker_tests {
    use super::*;

    /// 测试权限检查器创建和基本功能
    #[test]
    fn test_permission_checker_creation() {
        // 测试默认创建
        let checker = UnifiedPermissionChecker::new();
        assert!(checker.has_permission(&UserRole::Admin, &Permission::Read));
        assert!(checker.has_permission(&UserRole::Admin, &Permission::Admin));

        // 测试带配置创建
        let config = PermissionCheckerConfig {
            cache_enabled: true,
            cache_ttl: 600,
            verbose_logging: true,
        };
        let checker = UnifiedPermissionChecker::with_config(config);
        assert!(checker.has_permission(&UserRole::Manager, &Permission::Write));
    }

    /// 测试基本权限检查逻辑
    #[test]
    fn test_basic_permission_checks() {
        let checker = UnifiedPermissionChecker::new();

        // 测试管理员权限（应该拥有所有权限）
        assert!(checker.has_permission(&UserRole::Admin, &Permission::Read));
        assert!(checker.has_permission(&UserRole::Admin, &Permission::Write));
        assert!(checker.has_permission(&UserRole::Admin, &Permission::Delete));
        assert!(checker.has_permission(&UserRole::Admin, &Permission::Admin));

        // 测试经理权限
        assert!(checker.has_permission(&UserRole::Manager, &Permission::Read));
        assert!(checker.has_permission(&UserRole::Manager, &Permission::Write));
        assert!(checker.has_permission(&UserRole::Manager, &Permission::Delete));
        assert!(!checker.has_permission(&UserRole::Manager, &Permission::Admin));

        // 测试普通用户权限
        assert!(checker.has_permission(&UserRole::User, &Permission::Read));
        assert!(checker.has_permission(&UserRole::User, &Permission::Write));
        assert!(!checker.has_permission(&UserRole::User, &Permission::Delete));
        assert!(!checker.has_permission(&UserRole::User, &Permission::Admin));

        // 测试访客权限
        assert!(checker.has_permission(&UserRole::Guest, &Permission::Read));
        assert!(!checker.has_permission(&UserRole::Guest, &Permission::Write));
        assert!(!checker.has_permission(&UserRole::Guest, &Permission::Delete));
        assert!(!checker.has_permission(&UserRole::Guest, &Permission::Admin));
    }

    /// 测试权限级别边界情况
    #[test]
    fn test_permission_level_boundaries() {
        // 创建一个禁用缓存的检查器来避免缓存问题
        let checker = create_permission_checker_with_config(false, 0, false);

        // 测试自定义角色权限边界
        let custom_role_49 = UserRole::Custom {
            name: "CustomUser".to_string(),
            level: 49,
        };
        let custom_role_50 = UserRole::Custom {
            name: "CustomUser".to_string(),
            level: 50,
        };
        let custom_role_51 = UserRole::Custom {
            name: "CustomUser".to_string(),
            level: 51,
        };

        // 级别49应该没有写权限（需要50）
        assert!(!checker.has_permission(&custom_role_49, &Permission::Write));
        // 级别50应该有写权限
        assert!(checker.has_permission(&custom_role_50, &Permission::Write));
        // 级别51应该有写权限
        assert!(checker.has_permission(&custom_role_51, &Permission::Write));
    }

    /// 测试获取用户权限列表
    #[test]
    fn test_get_user_permissions() {
        let checker = UnifiedPermissionChecker::new();

        // 测试管理员权限列表
        let admin_permissions = checker.get_user_permissions(&UserRole::Admin);
        assert_eq!(admin_permissions.len(), 4);
        assert!(admin_permissions.contains(&Permission::Read));
        assert!(admin_permissions.contains(&Permission::Write));
        assert!(admin_permissions.contains(&Permission::Delete));
        assert!(admin_permissions.contains(&Permission::Admin));

        // 测试普通用户权限列表
        let user_permissions = checker.get_user_permissions(&UserRole::User);
        assert_eq!(user_permissions.len(), 2);
        assert!(user_permissions.contains(&Permission::Read));
        assert!(user_permissions.contains(&Permission::Write));
        assert!(!user_permissions.contains(&Permission::Delete));
        assert!(!user_permissions.contains(&Permission::Admin));

        // 测试访客权限列表
        let guest_permissions = checker.get_user_permissions(&UserRole::Guest);
        assert_eq!(guest_permissions.len(), 1);
        assert!(guest_permissions.contains(&Permission::Read));
    }
}

// ============================================================================
// 权限缓存机制测试
// ============================================================================

#[cfg(test)]
mod permission_cache_tests {
    use super::*;

    /// 测试权限缓存基本功能
    #[test]
    fn test_permission_cache_basic() {
        let checker = create_permission_checker_with_config(true, 300, false);

        // 第一次检查（应该计算并缓存）
        let start = Instant::now();
        let result1 = checker.has_permission(&UserRole::User, &Permission::Write);
        let duration1 = start.elapsed();

        // 第二次检查（应该从缓存获取，更快）
        let start = Instant::now();
        let result2 = checker.has_permission(&UserRole::User, &Permission::Write);
        let duration2 = start.elapsed();

        // 结果应该一致
        assert_eq!(result1, result2);
        assert!(result1); // User应该有Write权限

        // 第二次应该更快（从缓存获取）
        // 注意：这个测试可能不稳定，因为权限检查本身很快
        println!("第一次检查耗时: {duration1:?}, 第二次检查耗时: {duration2:?}");
    }

    /// 测试缓存清理功能
    #[test]
    fn test_cache_cleanup() {
        let checker = create_permission_checker_with_config(true, 1, false); // 1秒TTL

        // 执行一些权限检查以填充缓存
        checker.has_permission(&UserRole::User, &Permission::Read);
        checker.has_permission(&UserRole::User, &Permission::Write);
        checker.has_permission(&UserRole::Manager, &Permission::Delete);

        // 等待缓存过期
        std::thread::sleep(Duration::from_secs(2));

        // 执行缓存清理
        checker.cleanup_expired_cache();

        // 验证缓存已被清理（通过再次检查权限，应该重新计算）
        let result = checker.has_permission(&UserRole::User, &Permission::Read);
        assert!(result);
    }

    /// 测试禁用缓存的情况
    #[test]
    fn test_cache_disabled() {
        let checker = create_permission_checker_with_config(false, 300, false);

        // 多次检查相同权限
        let result1 = checker.has_permission(&UserRole::User, &Permission::Write);
        let result2 = checker.has_permission(&UserRole::User, &Permission::Write);

        // 结果应该一致
        assert_eq!(result1, result2);
        assert!(result1);

        // 缓存清理应该不做任何事情
        checker.cleanup_expired_cache();
    }
}

// ============================================================================
// 权限检查器配置测试
// ============================================================================

#[cfg(test)]
mod permission_checker_config_tests {
    use super::*;

    /// 测试默认配置
    #[test]
    fn test_default_config() {
        let config = PermissionCheckerConfig::default();
        assert!(config.cache_enabled);
        assert_eq!(config.cache_ttl, 300);
        assert!(!config.verbose_logging);
    }

    /// 测试配置工厂函数
    #[test]
    fn test_config_factory_functions() {
        // 测试默认权限检查器
        let default_checker = create_default_permission_checker();
        assert!(default_checker.has_permission(&UserRole::Admin, &Permission::Admin));

        // 测试企业级权限检查器
        let enterprise_checker = create_enterprise_permission_checker();
        assert!(enterprise_checker.has_permission(&UserRole::Manager, &Permission::Delete));

        // 测试高性能权限检查器
        let high_perf_checker = create_high_performance_permission_checker();
        assert!(high_perf_checker.has_permission(&UserRole::User, &Permission::Write));

        // 测试自定义配置权限检查器
        let custom_checker = create_permission_checker_with_config(true, 600, true);
        assert!(custom_checker.has_permission(&UserRole::Guest, &Permission::Read));
    }
}

// ============================================================================
// 权限中间件状态测试
// ============================================================================

#[cfg(test)]
mod permission_middleware_state_tests {
    use super::*;

    /// 测试权限中间件状态创建
    #[test]
    fn test_permission_middleware_state_creation() {
        let checker = create_default_permission_checker();
        let state = create_permission_middleware_state(checker);

        // 验证状态包含权限检查器
        assert!(
            state
                .checker
                .has_permission(&UserRole::Admin, &Permission::Admin)
        );
    }

    /// 测试状态克隆
    #[test]
    fn test_permission_middleware_state_clone() {
        let checker = create_default_permission_checker();
        let state1 = create_permission_middleware_state(checker);
        let state2 = state1.clone();

        // 验证克隆的状态功能正常
        assert!(
            state1
                .checker
                .has_permission(&UserRole::User, &Permission::Read)
        );
        assert!(
            state2
                .checker
                .has_permission(&UserRole::User, &Permission::Read)
        );
    }
}

// ============================================================================
// 中间件函数集成测试
// ============================================================================

#[cfg(test)]
mod middleware_function_tests {
    use super::*;

    /// 创建测试应用，包含权限中间件
    fn create_test_app_with_permission(required_permission: Permission) -> Router {
        let checker = create_default_permission_checker();
        let state = create_permission_middleware_state(checker);

        Router::new()
            .route("/protected", get(success_handler))
            .layer(middleware::from_fn(require_permission(required_permission)))
            .layer(middleware::from_fn(
                move |mut req: Request, next: axum::middleware::Next| {
                    let state = state.clone();
                    async move {
                        // 注入权限状态
                        req.extensions_mut().insert(state);
                        next.run(req).await
                    }
                },
            ))
            .layer(middleware::from_fn(
                move |mut req: Request, next: axum::middleware::Next| {
                    async move {
                        // 注入测试用户（模拟认证中间件）
                        let user = create_test_user(UserRole::User);
                        req.extensions_mut().insert(user);
                        next.run(req).await
                    }
                },
            ))
    }

    /// 创建测试应用，包含角色中间件
    fn create_test_app_with_role(required_role: UserRole) -> Router {
        Router::new()
            .route("/protected", get(success_handler))
            .layer(middleware::from_fn(require_role(required_role)))
            .layer(middleware::from_fn(
                move |mut req: Request, next: axum::middleware::Next| {
                    async move {
                        // 注入测试用户（模拟认证中间件）
                        let user = create_test_user(UserRole::Manager);
                        req.extensions_mut().insert(user);
                        next.run(req).await
                    }
                },
            ))
    }

    /// 测试require_permission中间件 - 权限足够
    #[tokio::test]
    async fn test_require_permission_sufficient() {
        let app = create_test_app_with_permission(Permission::Write);
        let server = TestServer::new(app).unwrap();

        let response = server.get("/protected").await;
        assert_eq!(response.status_code(), StatusCode::OK);

        let body: Value = response.json();
        assert_eq!(body["status"], "success");
    }

    /// 测试require_permission中间件 - 权限不足
    #[tokio::test]
    async fn test_require_permission_insufficient() {
        let app = create_test_app_with_permission(Permission::Admin);
        let server = TestServer::new(app).unwrap();

        let response = server.get("/protected").await;
        assert_eq!(response.status_code(), StatusCode::FORBIDDEN);
    }

    /// 测试require_role中间件 - 角色足够
    #[tokio::test]
    async fn test_require_role_sufficient() {
        let app = create_test_app_with_role(UserRole::User);
        let server = TestServer::new(app).unwrap();

        let response = server.get("/protected").await;
        assert_eq!(response.status_code(), StatusCode::OK);

        let body: Value = response.json();
        assert_eq!(body["status"], "success");
    }

    /// 测试require_role中间件 - 角色不足
    #[tokio::test]
    async fn test_require_role_insufficient() {
        let app = create_test_app_with_role(UserRole::Admin);
        let server = TestServer::new(app).unwrap();

        let response = server.get("/protected").await;
        assert_eq!(response.status_code(), StatusCode::FORBIDDEN);
    }
}

// ============================================================================
// 便捷中间件函数测试
// ============================================================================

#[cfg(test)]
mod convenience_middleware_tests {
    use super::*;

    /// 测试require_admin中间件
    #[tokio::test]
    async fn test_require_admin_middleware() {
        // 注入管理员用户
        let app_with_admin = Router::new()
            .route("/protected", get(success_handler))
            .layer(middleware::from_fn(require_admin()))
            .layer(middleware::from_fn(
                move |mut req: Request, next: axum::middleware::Next| {
                    let checker = create_default_permission_checker();
                    let state = create_permission_middleware_state(checker);
                    async move {
                        req.extensions_mut().insert(state);
                        next.run(req).await
                    }
                },
            ))
            .layer(middleware::from_fn(
                move |mut req: Request, next: axum::middleware::Next| async move {
                    let admin_user = create_test_user(UserRole::Admin);
                    req.extensions_mut().insert(admin_user);
                    next.run(req).await
                },
            ));

        let server_with_admin = TestServer::new(app_with_admin).unwrap();
        let response = server_with_admin.get("/protected").await;
        assert_eq!(response.status_code(), StatusCode::OK);
    }

    /// 测试require_manager中间件
    #[tokio::test]
    async fn test_require_manager_middleware() {
        let app = Router::new()
            .route("/protected", get(success_handler))
            .layer(middleware::from_fn(require_manager()))
            .layer(middleware::from_fn(
                move |mut req: Request, next: axum::middleware::Next| {
                    let checker = create_default_permission_checker();
                    let state = create_permission_middleware_state(checker);
                    async move {
                        req.extensions_mut().insert(state);
                        next.run(req).await
                    }
                },
            ))
            .layer(middleware::from_fn(
                move |mut req: Request, next: axum::middleware::Next| async move {
                    let manager_user = create_test_user(UserRole::Manager);
                    req.extensions_mut().insert(manager_user);
                    next.run(req).await
                },
            ));

        let server = TestServer::new(app).unwrap();
        let response = server.get("/protected").await;
        assert_eq!(response.status_code(), StatusCode::OK);
    }

    /// 测试require_read_permission中间件
    #[tokio::test]
    async fn test_require_read_permission_middleware() {
        let app = Router::new()
            .route("/protected", get(success_handler))
            .layer(middleware::from_fn(require_read_permission()))
            .layer(middleware::from_fn(
                move |mut req: Request, next: axum::middleware::Next| {
                    let checker = create_default_permission_checker();
                    let state = create_permission_middleware_state(checker);
                    async move {
                        req.extensions_mut().insert(state);
                        next.run(req).await
                    }
                },
            ))
            .layer(middleware::from_fn(
                move |mut req: Request, next: axum::middleware::Next| async move {
                    let guest_user = create_test_user(UserRole::Guest);
                    req.extensions_mut().insert(guest_user);
                    next.run(req).await
                },
            ));

        let server = TestServer::new(app).unwrap();
        let response = server.get("/protected").await;
        assert_eq!(response.status_code(), StatusCode::OK);
    }

    /// 测试require_write_permission中间件
    #[tokio::test]
    async fn test_require_write_permission_middleware() {
        let app = Router::new()
            .route("/protected", get(success_handler))
            .layer(middleware::from_fn(require_write_permission()))
            .layer(middleware::from_fn(
                move |mut req: Request, next: axum::middleware::Next| {
                    let checker = create_default_permission_checker();
                    let state = create_permission_middleware_state(checker);
                    async move {
                        req.extensions_mut().insert(state);
                        next.run(req).await
                    }
                },
            ))
            .layer(middleware::from_fn(
                move |mut req: Request, next: axum::middleware::Next| async move {
                    let user = create_test_user(UserRole::User);
                    req.extensions_mut().insert(user);
                    next.run(req).await
                },
            ));

        let server = TestServer::new(app).unwrap();
        let response = server.get("/protected").await;
        assert_eq!(response.status_code(), StatusCode::OK);
    }

    /// 测试require_delete_permission中间件
    #[tokio::test]
    async fn test_require_delete_permission_middleware() {
        let app = Router::new()
            .route("/protected", get(success_handler))
            .layer(middleware::from_fn(require_delete_permission()))
            .layer(middleware::from_fn(
                move |mut req: Request, next: axum::middleware::Next| {
                    let checker = create_default_permission_checker();
                    let state = create_permission_middleware_state(checker);
                    async move {
                        req.extensions_mut().insert(state);
                        next.run(req).await
                    }
                },
            ))
            .layer(middleware::from_fn(
                move |mut req: Request, next: axum::middleware::Next| async move {
                    let manager_user = create_test_user(UserRole::Manager);
                    req.extensions_mut().insert(manager_user);
                    next.run(req).await
                },
            ));

        let server = TestServer::new(app).unwrap();
        let response = server.get("/protected").await;
        assert_eq!(response.status_code(), StatusCode::OK);
    }
}

// ============================================================================
// 错误处理和边缘情况测试
// ============================================================================

#[cfg(test)]
mod error_handling_tests {
    use super::*;

    /// 测试缺少权限状态的情况
    #[tokio::test]
    async fn test_missing_permission_state() {
        let app = Router::new()
            .route("/protected", get(success_handler))
            .layer(middleware::from_fn(require_read_permission()))
            // 故意不注入权限状态
            .layer(
                middleware::from_fn(move |mut req: Request, next: axum::middleware::Next| {
                    async move {
                        let user = create_test_user(UserRole::User);
                        req.extensions_mut().insert(user);
                        next.run(req).await
                    }
                })
            );

        let server = TestServer::new(app).unwrap();
        let response = server.get("/protected").await;
        assert_eq!(response.status_code(), StatusCode::INTERNAL_SERVER_ERROR);
    }

    /// 测试缺少认证用户的情况
    #[tokio::test]
    async fn test_missing_authenticated_user() {
        let app = Router::new()
            .route("/protected", get(success_handler))
            .layer(middleware::from_fn(require_read_permission()))
            .layer(middleware::from_fn(
                move |mut req: Request, next: axum::middleware::Next| {
                    let checker = create_default_permission_checker();
                    let state = create_permission_middleware_state(checker);
                    async move {
                        req.extensions_mut().insert(state);
                        // 故意不注入认证用户
                        next.run(req).await
                    }
                },
            ));

        let server = TestServer::new(app).unwrap();
        let response = server.get("/protected").await;
        assert_eq!(response.status_code(), StatusCode::UNAUTHORIZED);
    }

    /// 测试自定义角色的权限检查
    #[tokio::test]
    async fn test_custom_role_permissions() {
        let checker = create_default_permission_checker();

        // 测试边界情况的自定义角色
        let custom_role_24 = UserRole::Custom {
            name: "LowLevel".to_string(),
            level: 24,
        };
        let custom_role_25 = UserRole::Custom {
            name: "ReadOnly".to_string(),
            level: 25,
        };
        let custom_role_49 = UserRole::Custom {
            name: "AlmostUser".to_string(),
            level: 49,
        };
        let custom_role_50 = UserRole::Custom {
            name: "BasicUser".to_string(),
            level: 50,
        };
        let custom_role_74 = UserRole::Custom {
            name: "AlmostManager".to_string(),
            level: 74,
        };
        let custom_role_75 = UserRole::Custom {
            name: "Manager".to_string(),
            level: 75,
        };
        let custom_role_99 = UserRole::Custom {
            name: "AlmostAdmin".to_string(),
            level: 99,
        };
        let custom_role_100 = UserRole::Custom {
            name: "SuperAdmin".to_string(),
            level: 100,
        };

        // 测试读权限边界（需要25级）
        assert!(!checker.has_permission(&custom_role_24, &Permission::Read));
        assert!(checker.has_permission(&custom_role_25, &Permission::Read));

        // 测试写权限边界（需要50级）
        assert!(!checker.has_permission(&custom_role_49, &Permission::Write));
        assert!(checker.has_permission(&custom_role_50, &Permission::Write));

        // 测试删除权限边界（需要75级）
        assert!(!checker.has_permission(&custom_role_74, &Permission::Delete));
        assert!(checker.has_permission(&custom_role_75, &Permission::Delete));

        // 测试管理员权限边界（需要100级）
        assert!(!checker.has_permission(&custom_role_99, &Permission::Admin));
        assert!(checker.has_permission(&custom_role_100, &Permission::Admin));
    }

    /// 测试权限检查器在高并发情况下的表现
    #[tokio::test]
    async fn test_concurrent_permission_checks() {
        let checker = std::sync::Arc::new(create_default_permission_checker());
        let mut handles = vec![];

        // 启动多个并发任务进行权限检查
        for i in 0..100 {
            let checker_clone = checker.clone();
            let handle = tokio::spawn(async move {
                let role = match i % 4 {
                    0 => UserRole::Admin,
                    1 => UserRole::Manager,
                    2 => UserRole::User,
                    _ => UserRole::Guest,
                };
                let permission = match i % 4 {
                    0 => Permission::Admin,
                    1 => Permission::Delete,
                    2 => Permission::Write,
                    _ => Permission::Read,
                };

                checker_clone.has_permission(&role, &permission)
            });
            handles.push(handle);
        }

        // 等待所有任务完成
        let results: Vec<bool> = futures::future::join_all(handles)
            .await
            .into_iter()
            .map(|r| r.unwrap())
            .collect();

        // 验证结果数量正确
        assert_eq!(results.len(), 100);

        // 验证一些预期的结果
        assert!(results[0]); // Admin应该有Admin权限
        assert!(results[1]); // Manager应该有Delete权限
        assert!(results[2]); // User应该有Write权限
        assert!(results[3]); // Guest应该有Read权限
    }

    /// 测试权限缓存在并发情况下的一致性
    #[tokio::test]
    async fn test_cache_consistency_under_concurrency() {
        let checker = std::sync::Arc::new(create_permission_checker_with_config(true, 300, false));
        let mut handles = vec![];

        // 启动多个并发任务检查相同的权限
        for _ in 0..50 {
            let checker_clone = checker.clone();
            let handle = tokio::spawn(async move {
                checker_clone.has_permission(&UserRole::User, &Permission::Write)
            });
            handles.push(handle);
        }

        // 等待所有任务完成
        let results: Vec<bool> = futures::future::join_all(handles)
            .await
            .into_iter()
            .map(|r| r.unwrap())
            .collect();

        // 验证所有结果都一致
        assert_eq!(results.len(), 50);
        assert!(results.iter().all(|&r| r)); // User应该有Write权限
    }
}

// ============================================================================
// PermissionError 错误类型测试
// ============================================================================

mod permission_error_tests {
    // 测试模块导入
    use app_common::middleware::permission_middleware::PermissionError;
    use axum::http::StatusCode;

    /// 测试PermissionError到StatusCode的转换
    #[test]
    fn test_permission_error_to_status_code() {
        // 测试权限不足错误
        let error = PermissionError::InsufficientPermission {
            required: "Admin".to_string(),
        };
        assert_eq!(StatusCode::from(error), StatusCode::FORBIDDEN);

        // 测试角色不足错误
        let error = PermissionError::InsufficientRole {
            required: "Manager".to_string(),
        };
        assert_eq!(StatusCode::from(error), StatusCode::FORBIDDEN);

        // 测试资源访问被拒绝错误
        let error = PermissionError::ResourceAccessDenied;
        assert_eq!(StatusCode::from(error), StatusCode::FORBIDDEN);

        // 测试权限检查失败错误
        let error = PermissionError::CheckFailed("测试错误".to_string());
        assert_eq!(StatusCode::from(error), StatusCode::INTERNAL_SERVER_ERROR);
    }

    /// 测试PermissionError的错误消息
    #[test]
    fn test_permission_error_messages() {
        // 测试权限不足错误消息
        let error = PermissionError::InsufficientPermission {
            required: "Admin".to_string(),
        };
        assert_eq!(error.to_string(), "权限不足：需要 Admin 权限");

        // 测试角色不足错误消息
        let error = PermissionError::InsufficientRole {
            required: "Manager".to_string(),
        };
        assert_eq!(error.to_string(), "角色验证失败：需要 Manager 角色");

        // 测试资源访问被拒绝错误消息
        let error = PermissionError::ResourceAccessDenied;
        assert_eq!(error.to_string(), "资源访问被拒绝");

        // 测试权限检查失败错误消息
        let error = PermissionError::CheckFailed("数据库连接失败".to_string());
        assert_eq!(error.to_string(), "权限检查失败：数据库连接失败");
    }
}

// ============================================================================
// 边缘情况和异常处理测试
// ============================================================================

mod edge_case_tests {
    use super::*;

    /// 测试空权限列表的批量检查
    #[test]
    fn test_empty_permissions_batch_check() {
        let checker = create_default_permission_checker();
        let permissions = vec![];
        let results = checker.check_multiple_permissions(&UserRole::Admin, &permissions);
        assert!(results.is_empty());
    }

    /// 测试大量权限的批量检查
    #[test]
    fn test_large_permissions_batch_check() {
        let checker = create_default_permission_checker();
        let mut permissions = vec![];

        // 创建100个自定义权限
        for i in 0..100 {
            // Permission枚举没有Custom变体，使用标准权限
            permissions.push(match i % 4 {
                0 => Permission::Read,
                1 => Permission::Write,
                2 => Permission::Delete,
                _ => Permission::Admin,
            });
        }

        let results = checker.check_multiple_permissions(&UserRole::Admin, &permissions);
        assert_eq!(results.len(), 100);
        // 管理员应该拥有所有权限
        assert!(results.iter().all(|&result| result));
    }

    /// 测试自定义角色的边界权限级别
    #[test]
    fn test_custom_role_boundary_levels() {
        let checker = create_default_permission_checker();

        // 测试权限级别为0的自定义角色
        let role_level_0 = UserRole::Custom {
            name: "NoPermission".to_string(),
            level: 0,
        };
        assert!(!checker.has_permission(&role_level_0, &Permission::Read));

        // 测试权限级别为25的自定义角色（刚好达到读权限）
        let role_level_25 = UserRole::Custom {
            name: "ReadOnly".to_string(),
            level: 25,
        };
        assert!(checker.has_permission(&role_level_25, &Permission::Read));
        assert!(!checker.has_permission(&role_level_25, &Permission::Write));

        // 测试权限级别为255的自定义角色（超高权限）
        let role_level_255 = UserRole::Custom {
            name: "SuperAdmin".to_string(),
            level: 255,
        };
        assert!(checker.has_permission(&role_level_255, &Permission::Admin));
    }

    /// 测试缓存清理功能的边缘情况
    #[test]
    fn test_cache_cleanup_edge_cases() {
        let checker = create_permission_checker_with_config(true, 1, false); // 1秒TTL

        // 添加一些缓存条目
        assert!(checker.has_permission(&UserRole::Admin, &Permission::Read));
        assert!(checker.has_permission(&UserRole::User, &Permission::Write));

        // 立即清理（应该没有过期条目）
        checker.cleanup_expired_cache();

        // 验证缓存仍然有效
        assert!(checker.has_permission(&UserRole::Admin, &Permission::Read));

        // 等待缓存过期
        std::thread::sleep(Duration::from_secs(2));

        // 清理过期缓存
        checker.cleanup_expired_cache();

        // 验证缓存已被清理（通过重新计算验证）
        assert!(checker.has_permission(&UserRole::Admin, &Permission::Read));
    }

    /// 测试缓存禁用时的清理操作
    #[test]
    fn test_cache_operations_when_disabled() {
        let checker = create_permission_checker_with_config(false, 300, false);

        // 执行权限检查
        assert!(checker.has_permission(&UserRole::Admin, &Permission::Read));

        // 清理缓存（应该是无操作）
        checker.cleanup_expired_cache();
        checker.clear_cache();

        // 验证权限检查仍然正常工作
        assert!(checker.has_permission(&UserRole::Admin, &Permission::Read));
    }

    /// 测试资源访问控制的边缘情况
    #[test]
    fn test_resource_access_edge_cases() {
        let checker = create_default_permission_checker();

        // 测试空字符串用户ID
        assert!(checker.can_access_resource(&UserRole::Admin, "", "user123"));
        assert!(!checker.can_access_resource(&UserRole::User, "", "user123"));
        assert!(checker.can_access_resource(&UserRole::User, "", ""));

        // 测试相同的空字符串用户ID
        assert!(checker.can_access_resource(&UserRole::User, "", ""));

        // 测试非常长的用户ID
        let long_user_id = "a".repeat(1000);
        assert!(checker.can_access_resource(&UserRole::Admin, &long_user_id, "user123"));
        assert!(checker.can_access_resource(&UserRole::User, &long_user_id, &long_user_id));
    }
}
