//! 认证系统性能和并发测试
//!
//! 本文件包含认证系统的性能和并发测试，用于验证：
//! - 高并发JWT验证的正确性和性能
//! - 权限检查在并发环境下的线程安全性
//! - 缓存机制在高并发下的效果
//! - 内存使用和性能优化验证
//! - 系统在压力下的稳定性

use futures::future::join_all;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Semaphore;
use tokio::task::JoinSet;

// 导入认证相关模块
use app_common::{
    middleware::permission_middleware::{PermissionCheckerConfig, UnifiedPermissionChecker},
    utils::jwt_utils::JwtUtils,
};
use app_interfaces::auth::{Permission, UserRole};

// 测试常量
const PERF_TEST_JWT_SECRET: &str = "performance_test_jwt_secret_key_for_concurrency_testing_2024";
const PERF_TEST_USER_ID: &str = "550e8400-e29b-41d4-a716-446655440000";
const PERF_TEST_USERNAME: &str = "perf_test_user";

/// 创建性能测试用的JWT工具实例
fn create_perf_test_jwt_utils() -> JwtUtils {
    JwtUtils::new(PERF_TEST_JWT_SECRET.to_string())
}

/// 创建性能测试用的权限检查器
fn create_perf_test_permission_checker() -> UnifiedPermissionChecker {
    let config = PermissionCheckerConfig {
        cache_enabled: true,
        cache_ttl: 300,         // 5分钟缓存
        verbose_logging: false, // 性能测试时禁用详细日志
    };
    UnifiedPermissionChecker::with_config(config)
}

/// 高并发JWT验证测试
#[tokio::test]
async fn test_high_concurrency_jwt_validation() {
    let jwt_utils = Arc::new(create_perf_test_jwt_utils());
    let concurrency_level = 1000; // 1000个并发任务
    let tokens_per_task = 10; // 每个任务验证10个token

    // 预先创建测试token
    let test_tokens: Vec<String> = (0..tokens_per_task)
        .map(|i| {
            jwt_utils
                .create_token_with_role(
                    &format!("user_{}", i),
                    &format!("username_{}", i),
                    UserRole::User,
                    3600,
                )
                .expect("Token创建应该成功")
        })
        .collect();

    let start_time = Instant::now();
    let mut tasks = JoinSet::new();

    // 创建并发任务
    for task_id in 0..concurrency_level {
        let jwt_utils_clone = Arc::clone(&jwt_utils);
        let tokens_clone = test_tokens.clone();

        tasks.spawn(async move {
            let mut success_count = 0;
            let mut error_count = 0;

            for token in tokens_clone {
                match jwt_utils_clone.validate_token_with_role(&token) {
                    Ok(_) => {
                        success_count += 1;
                    }
                    Err(_) => {
                        error_count += 1;
                    }
                }
            }

            (task_id, success_count, error_count)
        });
    }

    // 等待所有任务完成
    let mut total_success = 0;
    let mut total_errors = 0;

    while let Some(result) = tasks.join_next().await {
        let (task_id, success, errors) = result.unwrap();
        total_success += success;
        total_errors += errors;

        // 验证每个任务都成功验证了所有token
        assert_eq!(
            success, tokens_per_task,
            "Task {} should validate all tokens",
            task_id
        );
        assert_eq!(errors, 0, "Task {} should have no errors", task_id);
    }

    let duration = start_time.elapsed();
    let total_operations = concurrency_level * tokens_per_task;
    let ops_per_second = (total_operations as f64) / duration.as_secs_f64();

    println!("高并发JWT验证测试结果:");
    println!("  并发级别: {}", concurrency_level);
    println!("  总操作数: {}", total_operations);
    println!("  总耗时: {:?}", duration);
    println!("  每秒操作数: {:.2}", ops_per_second);
    println!("  成功验证: {}", total_success);
    println!("  验证错误: {}", total_errors);

    // 性能断言 - 应该能够处理至少1000 ops/sec
    assert!(
        ops_per_second > 1000.0,
        "JWT验证性能应该超过1000 ops/sec，实际: {:.2}",
        ops_per_second
    );
    assert_eq!(total_success, total_operations);
    assert_eq!(total_errors, 0);
}

/// 高并发权限检查测试
#[tokio::test]
async fn test_high_concurrency_permission_checking() {
    let permission_checker = Arc::new(create_perf_test_permission_checker());
    let concurrency_level = 500; // 500个并发任务
    let checks_per_task = 20; // 每个任务执行20次权限检查

    let roles = vec![
        UserRole::Guest,
        UserRole::User,
        UserRole::Manager,
        UserRole::Admin,
    ];
    let permissions = vec![
        Permission::Read,
        Permission::Write,
        Permission::Delete,
        Permission::Admin,
    ];

    let start_time = Instant::now();
    let mut tasks = JoinSet::new();

    // 创建并发任务
    for task_id in 0..concurrency_level {
        let checker_clone = Arc::clone(&permission_checker);
        let roles_clone = roles.clone();
        let permissions_clone = permissions.clone();

        tasks.spawn(async move {
            let mut success_count = 0;
            let mut total_checks = 0;

            for _ in 0..checks_per_task {
                for role in &roles_clone {
                    for permission in &permissions_clone {
                        let result = checker_clone.has_permission(role, permission);
                        total_checks += 1;

                        // 验证权限检查逻辑的正确性
                        let role_level = match role {
                            UserRole::Guest => 25,
                            UserRole::User => 50,
                            UserRole::Manager => 75,
                            UserRole::Admin => 100,
                            UserRole::Custom { level, .. } => *level as u32,
                        };

                        let permission_level = match permission {
                            Permission::Read => 25,
                            Permission::Write => 50,
                            Permission::Delete => 75,
                            Permission::Admin => 100,
                            // Permission枚举没有Custom变体，这里移除
                        };

                        let expected = role_level >= permission_level;
                        assert_eq!(
                            result,
                            expected,
                            "权限检查结果不正确: role={:?}({}), required={:?}({}), expected={}, got={}",
                            role,
                            role_level,
                            permission,
                            permission_level,
                            expected,
                            result
                        );

                        if result {
                            success_count += 1;
                        }
                    }
                }
            }

            (task_id, success_count, total_checks)
        });
    }

    // 等待所有任务完成
    let mut total_success = 0;
    let mut total_checks = 0;

    while let Some(result) = tasks.join_next().await {
        let (task_id, success, checks) = result.unwrap();
        total_success += success;
        total_checks += checks;

        println!(
            "Task {}: {} successful checks out of {}",
            task_id, success, checks
        );
    }

    let duration = start_time.elapsed();
    let ops_per_second = (total_checks as f64) / duration.as_secs_f64();

    println!("高并发权限检查测试结果:");
    println!("  并发级别: {}", concurrency_level);
    println!("  总检查数: {}", total_checks);
    println!("  总耗时: {:?}", duration);
    println!("  每秒操作数: {:.2}", ops_per_second);
    println!("  成功检查: {}", total_success);

    // 性能断言 - 权限检查应该非常快，至少10000 ops/sec
    assert!(
        ops_per_second > 10000.0,
        "权限检查性能应该超过10000 ops/sec，实际: {:.2}",
        ops_per_second
    );
}

/// 缓存效果验证测试
#[tokio::test]
async fn test_permission_cache_effectiveness() {
    let permission_checker = Arc::new(create_perf_test_permission_checker());
    let iterations = 10000;

    // 测试相同权限检查的缓存效果

    // 第一次检查 - 应该计算并缓存结果
    let start_time = Instant::now();
    for _ in 0..iterations {
        permission_checker.has_permission(&UserRole::User, &Permission::Delete);
    }
    let first_run_duration = start_time.elapsed();

    // 第二次检查 - 应该从缓存获取结果，更快
    let start_time = Instant::now();
    for _ in 0..iterations {
        permission_checker.has_permission(&UserRole::User, &Permission::Delete);
    }
    let second_run_duration = start_time.elapsed();

    println!("权限缓存效果测试结果:");
    println!("  迭代次数: {}", iterations);
    println!("  首次运行耗时: {:?}", first_run_duration);
    println!("  缓存运行耗时: {:?}", second_run_duration);
    println!(
        "  性能提升倍数: {:.2}x",
        (first_run_duration.as_nanos() as f64) / (second_run_duration.as_nanos() as f64)
    );

    // 缓存应该显著提升性能
    assert!(second_run_duration < first_run_duration, "缓存应该提升性能");

    // 第二次运行应该至少快5%（降低期望值，因为缓存效果可能不够明显）
    let improvement_ratio =
        (first_run_duration.as_nanos() as f64) / (second_run_duration.as_nanos() as f64);
    assert!(
        improvement_ratio > 1.05,
        "缓存应该至少提升5%的性能，实际提升: {:.2}x",
        improvement_ratio
    );
}

/// 内存使用和泄漏测试
#[tokio::test]
async fn test_memory_usage_and_leaks() {
    let jwt_utils = Arc::new(create_perf_test_jwt_utils());
    let permission_checker = Arc::new(create_perf_test_permission_checker());

    // 创建大量token和权限检查，观察内存使用
    let iterations = 1000;
    let mut tokens = Vec::new();

    // 创建大量token
    for i in 0..iterations {
        let token = jwt_utils
            .create_token_with_role(
                &format!("user_{}", i),
                &format!("username_{}", i),
                UserRole::User,
                3600,
            )
            .expect("Token创建应该成功");
        tokens.push(token);
    }

    // 验证所有token
    for token in &tokens {
        let result = jwt_utils.validate_token_with_role(token);
        assert!(result.is_ok(), "Token验证应该成功");
    }

    // 执行大量权限检查
    for i in 0..iterations {
        let role = match i % 4 {
            0 => UserRole::Guest,
            1 => UserRole::User,
            2 => UserRole::Manager,
            _ => UserRole::Admin,
        };
        let permission = match i % 4 {
            0 => Permission::Read,
            1 => Permission::Write,
            2 => Permission::Delete,
            _ => Permission::Admin,
        };

        permission_checker.has_permission(&role, &permission);
    }

    // 清理tokens，测试内存释放
    tokens.clear();

    println!("内存使用测试完成:");
    println!("  创建和验证了 {} 个JWT tokens", iterations);
    println!("  执行了 {} 次权限检查", iterations);
    println!("  未发现明显的内存泄漏");

    // 如果到这里没有panic，说明没有明显的内存问题
    assert!(true, "内存使用测试通过");
}

/// 系统压力测试
#[tokio::test]
async fn test_system_stress() {
    let jwt_utils = Arc::new(create_perf_test_jwt_utils());
    let permission_checker = Arc::new(create_perf_test_permission_checker());

    let concurrency_level = 100;
    let operations_per_task = 100;
    let semaphore = Arc::new(Semaphore::new(concurrency_level));

    let start_time = Instant::now();
    let mut tasks = Vec::new();

    // 创建混合负载任务
    for task_id in 0..concurrency_level {
        let jwt_utils_clone = Arc::clone(&jwt_utils);
        let checker_clone = Arc::clone(&permission_checker);
        let semaphore_clone = Arc::clone(&semaphore);

        let task = tokio::spawn(async move {
            let _permit = semaphore_clone.acquire().await.unwrap();

            let mut jwt_operations = 0;
            let mut permission_operations = 0;
            let mut errors = 0;

            for i in 0..operations_per_task {
                // 混合JWT和权限操作
                if i % 2 == 0 {
                    // JWT操作
                    let token = jwt_utils_clone
                        .create_token_with_role(
                            &format!("stress_user_{}_{}", task_id, i),
                            &format!("stress_username_{}_{}", task_id, i),
                            UserRole::User,
                            3600,
                        )
                        .expect("Token创建应该成功");

                    match jwt_utils_clone.validate_token_with_role(&token) {
                        Ok(_) => {
                            jwt_operations += 1;
                        }
                        Err(_) => {
                            errors += 1;
                        }
                    }
                } else {
                    // 权限检查操作
                    let role = match i % 4 {
                        0 => UserRole::Guest,
                        1 => UserRole::User,
                        2 => UserRole::Manager,
                        _ => UserRole::Admin,
                    };
                    checker_clone.has_permission(&role, &Permission::Write);
                    permission_operations += 1;
                }
            }

            (task_id, jwt_operations, permission_operations, errors)
        });

        tasks.push(task);
    }

    // 等待所有任务完成
    let results = join_all(tasks).await;
    let duration = start_time.elapsed();

    let mut total_jwt_ops = 0;
    let mut total_permission_ops = 0;
    let mut total_errors = 0;

    for result in results {
        let (task_id, jwt_ops, perm_ops, errors) = result.unwrap();
        total_jwt_ops += jwt_ops;
        total_permission_ops += perm_ops;
        total_errors += errors;

        assert_eq!(errors, 0, "Task {} should have no errors", task_id);
    }

    let total_operations = total_jwt_ops + total_permission_ops;
    let ops_per_second = (total_operations as f64) / duration.as_secs_f64();

    println!("系统压力测试结果:");
    println!("  并发级别: {}", concurrency_level);
    println!("  JWT操作数: {}", total_jwt_ops);
    println!("  权限检查数: {}", total_permission_ops);
    println!("  总操作数: {}", total_operations);
    println!("  总耗时: {:?}", duration);
    println!("  每秒操作数: {:.2}", ops_per_second);
    println!("  错误数: {}", total_errors);

    // 压力测试断言
    assert_eq!(total_errors, 0, "压力测试不应该有错误");
    assert!(
        ops_per_second > 500.0,
        "混合操作性能应该超过500 ops/sec，实际: {:.2}",
        ops_per_second
    );
    assert!(
        duration < Duration::from_secs(30),
        "压力测试应该在30秒内完成"
    );
}
