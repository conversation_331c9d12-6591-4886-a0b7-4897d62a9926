//! # PostgreSQL全文搜索功能测试
//!
//! 测试PostgreSQL GIN索引、tsvector优化和全文搜索功能。
//! 严格遵循TDD原则，确保搜索功能的正确性和性能。

use std::time::{Duration, Instant};
use tokio_test;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use sea_orm::{Database, DatabaseConnection, EntityTrait, QueryFilter, ColumnTrait};
use anyhow::Result;

use crate::message_search_test_framework::{
    MessageSearchTestFramework, MessageSearchTestConfig, TestMessage, SearchTestScenario
};

/// PostgreSQL全文搜索测试套件
pub struct PostgresFulltextSearchTests {
    framework: MessageSearchTestFramework,
    db_connection: Option<DatabaseConnection>,
}

impl PostgresFulltextSearchTests {
    /// 创建新的测试套件实例
    pub fn new() -> Self {
        let config = MessageSearchTestConfig::default();
        Self {
            framework: MessageSearchTestFramework::new(config),
            db_connection: None,
        }
    }

    /// 设置测试环境
    pub async fn setup(&mut self) -> Result<()> {
        tracing::info!("设置PostgreSQL全文搜索测试环境");
        
        // 初始化测试框架
        self.framework.initialize().await?;
        
        // 建立数据库连接
        self.db_connection = Some(
            Database::connect("postgresql://user:password@localhost:5432/test_db").await?
        );
        
        // 创建测试表和索引
        self.create_test_schema().await?;
        
        // 插入测试数据
        self.insert_test_data().await?;
        
        tracing::info!("PostgreSQL全文搜索测试环境设置完成");
        Ok(())
    }

    /// 创建测试数据库模式
    async fn create_test_schema(&self) -> Result<()> {
        tracing::info!("创建测试数据库模式");
        
        let db = self.db_connection.as_ref().unwrap();
        
        // 创建消息表（如果不存在）
        let create_table_sql = r#"
            CREATE TABLE IF NOT EXISTS test_messages (
                id UUID PRIMARY KEY,
                content TEXT NOT NULL,
                sender_id UUID NOT NULL,
                chat_room_id UUID NOT NULL,
                message_type VARCHAR(20) NOT NULL,
                created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                metadata JSONB,
                priority INTEGER DEFAULT 5,
                is_pinned BOOLEAN DEFAULT FALSE,
                search_vector TSVECTOR
            );
        "#;
        
        // 创建GIN索引用于全文搜索
        let create_gin_index_sql = r#"
            CREATE INDEX IF NOT EXISTS idx_messages_search_vector 
            ON test_messages USING GIN(search_vector);
        "#;
        
        // 创建触发器自动更新search_vector
        let create_trigger_sql = r#"
            CREATE OR REPLACE FUNCTION update_search_vector() 
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.search_vector := 
                    setweight(to_tsvector('chinese', COALESCE(NEW.content, '')), 'A') ||
                    setweight(to_tsvector('chinese', COALESCE(NEW.metadata::text, '')), 'B');
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;

            DROP TRIGGER IF EXISTS trigger_update_search_vector ON test_messages;
            CREATE TRIGGER trigger_update_search_vector
                BEFORE INSERT OR UPDATE ON test_messages
                FOR EACH ROW EXECUTE FUNCTION update_search_vector();
        "#;
        
        // 执行SQL语句
        sea_orm::Statement::from_string(
            sea_orm::DatabaseBackend::Postgres,
            create_table_sql.to_string()
        ).execute(db).await?;
        
        sea_orm::Statement::from_string(
            sea_orm::DatabaseBackend::Postgres,
            create_gin_index_sql.to_string()
        ).execute(db).await?;
        
        sea_orm::Statement::from_string(
            sea_orm::DatabaseBackend::Postgres,
            create_trigger_sql.to_string()
        ).execute(db).await?;
        
        tracing::info!("测试数据库模式创建完成");
        Ok(())
    }

    /// 插入测试数据
    async fn insert_test_data(&self) -> Result<()> {
        tracing::info!("插入测试数据");
        
        let db = self.db_connection.as_ref().unwrap();
        
        // 插入多样化的测试消息
        let test_messages = vec![
            ("这是一条关于Rust编程的消息", "programming"),
            ("今天天气很好，适合出去散步", "weather"),
            ("PostgreSQL全文搜索功能测试", "database"),
            ("Axum框架的性能优化技巧", "web"),
            ("DragonflyDB缓存配置指南", "cache"),
            ("企业级聊天应用架构设计", "architecture"),
            ("TDD测试驱动开发实践", "testing"),
            ("微服务架构最佳实践", "microservices"),
            ("数据库索引优化策略", "optimization"),
            ("分布式系统设计模式", "distributed"),
        ];
        
        for (i, (content, category)) in test_messages.iter().enumerate() {
            let insert_sql = format!(
                r#"
                INSERT INTO test_messages 
                (id, content, sender_id, chat_room_id, message_type, metadata, priority)
                VALUES ('{}', '{}', '{}', '{}', 'text', '{{"category": "{}"}}', {})
                "#,
                Uuid::new_v4(),
                content,
                Uuid::new_v4(),
                Uuid::new_v4(),
                category,
                i % 10
            );
            
            sea_orm::Statement::from_string(
                sea_orm::DatabaseBackend::Postgres,
                insert_sql
            ).execute(db).await?;
        }
        
        tracing::info!("测试数据插入完成");
        Ok(())
    }
}

/// GIN索引性能测试
#[tokio::test]
async fn test_gin_index_search_performance() {
    tracing::info!("开始GIN索引性能测试");
    
    let mut test_suite = PostgresFulltextSearchTests::new();
    test_suite.setup().await.expect("测试环境设置失败");
    
    let db = test_suite.db_connection.as_ref().unwrap();
    
    // 测试搜索性能
    let start_time = Instant::now();
    
    let search_sql = r#"
        SELECT id, content, ts_rank(search_vector, query) as rank
        FROM test_messages, plainto_tsquery('chinese', '测试') query
        WHERE search_vector @@ query
        ORDER BY rank DESC
        LIMIT 10;
    "#;
    
    let result = sea_orm::Statement::from_string(
        sea_orm::DatabaseBackend::Postgres,
        search_sql.to_string()
    ).all(db).await.expect("搜索查询失败");
    
    let elapsed = start_time.elapsed();
    
    // 验证性能要求：搜索时间应小于100ms
    assert!(elapsed < Duration::from_millis(100), 
        "GIN索引搜索性能不达标，耗时: {:?}", elapsed);
    
    // 验证搜索结果
    assert!(!result.is_empty(), "搜索结果不应为空");
    
    tracing::info!("GIN索引性能测试完成，耗时: {:?}", elapsed);
}

/// tsvector优化测试
#[tokio::test]
async fn test_tsvector_optimization() {
    tracing::info!("开始tsvector优化测试");
    
    let mut test_suite = PostgresFulltextSearchTests::new();
    test_suite.setup().await.expect("测试环境设置失败");
    
    let db = test_suite.db_connection.as_ref().unwrap();
    
    // 测试中文分词功能
    let chinese_search_sql = r#"
        SELECT content, ts_rank(search_vector, query) as rank
        FROM test_messages, plainto_tsquery('chinese', 'PostgreSQL 数据库') query
        WHERE search_vector @@ query
        ORDER BY rank DESC;
    "#;
    
    let result = sea_orm::Statement::from_string(
        sea_orm::DatabaseBackend::Postgres,
        chinese_search_sql.to_string()
    ).all(db).await.expect("中文搜索查询失败");
    
    // 验证中文搜索结果
    assert!(!result.is_empty(), "中文搜索结果不应为空");
    
    // 测试权重排序功能
    let weighted_search_sql = r#"
        SELECT content, 
               ts_rank(search_vector, query) as rank,
               ts_rank_cd(search_vector, query) as rank_cd
        FROM test_messages, plainto_tsquery('chinese', '测试') query
        WHERE search_vector @@ query
        ORDER BY rank DESC;
    "#;
    
    let weighted_result = sea_orm::Statement::from_string(
        sea_orm::DatabaseBackend::Postgres,
        weighted_search_sql.to_string()
    ).all(db).await.expect("权重搜索查询失败");
    
    // 验证权重排序结果
    assert!(!weighted_result.is_empty(), "权重搜索结果不应为空");
    
    tracing::info!("tsvector优化测试完成");
}

/// 中文全文搜索功能测试
#[tokio::test]
async fn test_chinese_fulltext_search() {
    tracing::info!("开始中文全文搜索功能测试");
    
    let mut test_suite = PostgresFulltextSearchTests::new();
    test_suite.setup().await.expect("测试环境设置失败");
    
    let db = test_suite.db_connection.as_ref().unwrap();
    
    // 测试精确匹配
    let exact_match_sql = r#"
        SELECT content
        FROM test_messages
        WHERE search_vector @@ plainto_tsquery('chinese', 'Rust编程')
    "#;
    
    let exact_result = sea_orm::Statement::from_string(
        sea_orm::DatabaseBackend::Postgres,
        exact_match_sql.to_string()
    ).all(db).await.expect("精确匹配查询失败");
    
    assert!(!exact_result.is_empty(), "精确匹配应该有结果");
    
    // 测试模糊匹配
    let fuzzy_match_sql = r#"
        SELECT content, ts_rank(search_vector, query) as rank
        FROM test_messages, to_tsquery('chinese', '编程 | 开发') query
        WHERE search_vector @@ query
        ORDER BY rank DESC
    "#;
    
    let fuzzy_result = sea_orm::Statement::from_string(
        sea_orm::DatabaseBackend::Postgres,
        fuzzy_match_sql.to_string()
    ).all(db).await.expect("模糊匹配查询失败");
    
    assert!(!fuzzy_result.is_empty(), "模糊匹配应该有结果");
    
    // 测试短语搜索
    let phrase_search_sql = r#"
        SELECT content
        FROM test_messages
        WHERE search_vector @@ phraseto_tsquery('chinese', '全文搜索功能')
    "#;
    
    let phrase_result = sea_orm::Statement::from_string(
        sea_orm::DatabaseBackend::Postgres,
        phrase_search_sql.to_string()
    ).all(db).await.expect("短语搜索查询失败");
    
    // 短语搜索可能没有结果，这是正常的
    tracing::info!("短语搜索结果数量: {}", phrase_result.len());
    
    tracing::info!("中文全文搜索功能测试完成");
}

/// 搜索结果排序算法测试
#[tokio::test]
async fn test_search_ranking_algorithm() {
    tracing::info!("开始搜索结果排序算法测试");
    
    let mut test_suite = PostgresFulltextSearchTests::new();
    test_suite.setup().await.expect("测试环境设置失败");
    
    let db = test_suite.db_connection.as_ref().unwrap();
    
    // 测试相关性排序
    let relevance_ranking_sql = r#"
        SELECT content, 
               ts_rank(search_vector, query) as relevance_rank,
               ts_rank_cd(search_vector, query) as cover_density_rank
        FROM test_messages, plainto_tsquery('chinese', '测试') query
        WHERE search_vector @@ query
        ORDER BY relevance_rank DESC, cover_density_rank DESC
    "#;
    
    let ranking_result = sea_orm::Statement::from_string(
        sea_orm::DatabaseBackend::Postgres,
        relevance_ranking_sql.to_string()
    ).all(db).await.expect("相关性排序查询失败");
    
    assert!(!ranking_result.is_empty(), "排序结果不应为空");
    
    // 测试时间权重排序
    let time_weighted_sql = r#"
        SELECT content, 
               ts_rank(search_vector, query) as relevance_rank,
               created_at,
               (ts_rank(search_vector, query) * 0.7 + 
                EXTRACT(EPOCH FROM (NOW() - created_at)) / 86400 * 0.3) as combined_rank
        FROM test_messages, plainto_tsquery('chinese', '架构') query
        WHERE search_vector @@ query
        ORDER BY combined_rank DESC
    "#;
    
    let time_weighted_result = sea_orm::Statement::from_string(
        sea_orm::DatabaseBackend::Postgres,
        time_weighted_sql.to_string()
    ).all(db).await.expect("时间权重排序查询失败");
    
    // 验证排序结果的合理性
    if time_weighted_result.len() > 1 {
        // 检查排序是否正确（这里简化验证）
        tracing::info!("时间权重排序结果数量: {}", time_weighted_result.len());
    }
    
    tracing::info!("搜索结果排序算法测试完成");
}

/// 大数据量搜索性能测试
#[tokio::test]
async fn test_large_dataset_search_performance() {
    tracing::info!("开始大数据量搜索性能测试");
    
    let mut test_suite = PostgresFulltextSearchTests::new();
    test_suite.setup().await.expect("测试环境设置失败");
    
    let db = test_suite.db_connection.as_ref().unwrap();
    
    // 插入大量测试数据
    for i in 0..1000 {
        let insert_sql = format!(
            r#"
            INSERT INTO test_messages 
            (id, content, sender_id, chat_room_id, message_type, metadata)
            VALUES ('{}', '大数据量测试消息 {} 包含各种关键词', '{}', '{}', 'text', '{{"batch": {}}}')
            "#,
            Uuid::new_v4(),
            i,
            Uuid::new_v4(),
            Uuid::new_v4(),
            i / 100
        );
        
        sea_orm::Statement::from_string(
            sea_orm::DatabaseBackend::Postgres,
            insert_sql
        ).execute(db).await.expect("插入大数据量失败");
    }
    
    // 测试搜索性能
    let start_time = Instant::now();
    
    let large_search_sql = r#"
        SELECT COUNT(*) as total_count
        FROM test_messages
        WHERE search_vector @@ plainto_tsquery('chinese', '测试 消息')
    "#;
    
    let result = sea_orm::Statement::from_string(
        sea_orm::DatabaseBackend::Postgres,
        large_search_sql.to_string()
    ).one(db).await.expect("大数据量搜索失败");
    
    let elapsed = start_time.elapsed();
    
    // 验证大数据量搜索性能：应在500ms内完成
    assert!(elapsed < Duration::from_millis(500), 
        "大数据量搜索性能不达标，耗时: {:?}", elapsed);
    
    assert!(result.is_some(), "搜索结果不应为空");
    
    tracing::info!("大数据量搜索性能测试完成，耗时: {:?}", elapsed);
}

/// 并发搜索测试
#[tokio::test]
async fn test_concurrent_search_performance() {
    tracing::info!("开始并发搜索性能测试");
    
    let mut test_suite = PostgresFulltextSearchTests::new();
    test_suite.setup().await.expect("测试环境设置失败");
    
    let db = test_suite.db_connection.as_ref().unwrap();
    
    // 创建多个并发搜索任务
    let mut tasks = Vec::new();
    let concurrent_count = 10;
    
    for i in 0..concurrent_count {
        let db_clone = db.clone();
        let search_query = format!("测试{}", i % 3); // 使用不同的搜索词
        
        let task = tokio::spawn(async move {
            let start_time = Instant::now();
            
            let search_sql = format!(
                r#"
                SELECT content, ts_rank(search_vector, query) as rank
                FROM test_messages, plainto_tsquery('chinese', '{}') query
                WHERE search_vector @@ query
                ORDER BY rank DESC
                LIMIT 5
                "#,
                search_query
            );
            
            let result = sea_orm::Statement::from_string(
                sea_orm::DatabaseBackend::Postgres,
                search_sql
            ).all(&db_clone).await;
            
            let elapsed = start_time.elapsed();
            (result.is_ok(), elapsed)
        });
        
        tasks.push(task);
    }
    
    // 等待所有任务完成
    let results = futures::future::join_all(tasks).await;
    
    // 验证并发搜索结果
    let mut total_elapsed = Duration::from_millis(0);
    let mut success_count = 0;
    
    for result in results {
        let (success, elapsed) = result.expect("任务执行失败");
        if success {
            success_count += 1;
        }
        total_elapsed += elapsed;
    }
    
    let average_elapsed = total_elapsed / concurrent_count as u32;
    
    // 验证并发搜索性能
    assert_eq!(success_count, concurrent_count, "所有并发搜索都应该成功");
    assert!(average_elapsed < Duration::from_millis(200), 
        "并发搜索平均响应时间不达标，平均耗时: {:?}", average_elapsed);
    
    tracing::info!("并发搜索性能测试完成，平均耗时: {:?}", average_elapsed);
}
