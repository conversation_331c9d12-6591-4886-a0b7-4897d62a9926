use axum::{
    Router,
    body::Body,
    extract::Extension,
    http::{HeaderMap, HeaderValue, Request, StatusCode},
    middleware::{self, Next},
    response::Response,
    routing::get,
};
use criterion::{BenchmarkId, Criterion, Throughput, criterion_group, criterion_main};
use std::hint::black_box;
use tokio::runtime::Runtime;
use tower::{ServiceBuilder, ServiceExt};
use tower_http::trace::TraceLayer;

use app_common::{
    middleware::{
        AuthenticatedUser, create_permission_middleware_state, inject_authenticated_user,
        inject_permission_state, require_write_permission,
    },
    utils::JwtUtils,
};
use app_interfaces::auth::{Permission, UserRole};

/// 创建测试用的JWT令牌
async fn create_test_token() -> String {
    let jwt_utils = JwtUtils::new("test_secret_key_for_benchmarking_purposes_only".to_string());
    let claims = app_common::utils::Claims {
        sub: "test_user".to_string(),
        exp: (chrono::Utc::now() + chrono::Duration::hours(1)).timestamp() as usize,
        iat: chrono::Utc::now().timestamp() as usize,
    };
    jwt_utils.create_token(&claims).await.unwrap()
}

/// 创建测试用的扩展JWT令牌
async fn create_test_extended_token() -> String {
    let jwt_utils = JwtUtils::new("test_secret_key_for_benchmarking_purposes_only".to_string());
    let claims = app_common::utils::jwt_utils::ExtendedClaims {
        sub: "test_user".to_string(),
        exp: (chrono::Utc::now() + chrono::Duration::hours(1)).timestamp() as usize,
        iat: chrono::Utc::now().timestamp() as usize,
        role: UserRole::User,
        permissions: vec![Permission::Read, Permission::Write],
    };
    jwt_utils.create_extended_token(&claims).await.unwrap()
}

/// 简单的测试处理器
async fn test_handler() -> &'static str {
    "Hello, World!"
}

/// 需要认证的测试处理器
async fn auth_handler(Extension(user): Extension<AuthenticatedUser>) -> String {
    format!("Hello, {}!", user.user_id)
}

/// 性能基准测试：认证中间件
/// 测试inject_authenticated_user中间件的性能
fn bench_auth_middleware(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    // 创建测试令牌
    let token = rt.block_on(create_test_token());
    let extended_token = rt.block_on(create_test_extended_token());

    let mut group = c.benchmark_group("认证中间件性能");
    group.throughput(Throughput::Elements(1));

    // 测试基础认证中间件
    group.bench_function("基础认证中间件", |b| {
        b.to_async(&rt).iter(|| async {
            let app = Router::new()
                .route("/", get(auth_handler))
                .layer(middleware::from_fn(inject_authenticated_user));

            let mut request = Request::builder()
                .uri("/")
                .header("Authorization", format!("Bearer {}", black_box(&token)))
                .body(Body::empty())
                .unwrap();

            let response = app.oneshot(request).await.unwrap();
            black_box(response);
        });
    });

    // 测试扩展认证中间件
    group.bench_function("扩展认证中间件", |b| {
        b.to_async(&rt).iter(|| async {
            let app = Router::new()
                .route("/", get(auth_handler))
                .layer(middleware::from_fn(inject_authenticated_user));

            let mut request = Request::builder()
                .uri("/")
                .header(
                    "Authorization",
                    format!("Bearer {}", black_box(&extended_token)),
                )
                .body(Body::empty())
                .unwrap();

            let response = app.oneshot(request).await.unwrap();
            black_box(response);
        });
    });

    group.finish();
}

/// 性能基准测试：权限中间件
/// 测试权限检查中间件的性能
fn bench_permission_middleware(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let extended_token = rt.block_on(create_test_extended_token());

    let mut group = c.benchmark_group("权限中间件性能");
    group.throughput(Throughput::Elements(1));

    // 测试权限中间件
    group.bench_function("写权限中间件", |b| {
        b.to_async(&rt).iter(|| async {
            let permission_state = create_permission_middleware_state();

            let app = Router::new()
                .route("/", get(auth_handler))
                .layer(middleware::from_fn_with_state(
                    permission_state.clone(),
                    require_write_permission,
                ))
                .layer(middleware::from_fn_with_state(
                    permission_state,
                    inject_permission_state,
                ))
                .layer(middleware::from_fn(inject_authenticated_user));

            let mut request = Request::builder()
                .uri("/")
                .header(
                    "Authorization",
                    format!("Bearer {}", black_box(&extended_token)),
                )
                .body(Body::empty())
                .unwrap();

            let response = app.oneshot(request).await.unwrap();
            black_box(response);
        });
    });

    group.finish();
}

/// 性能基准测试：中间件栈
/// 测试完整中间件栈的性能
fn bench_middleware_stack(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let extended_token = rt.block_on(create_test_extended_token());

    let mut group = c.benchmark_group("中间件栈性能");
    group.throughput(Throughput::Elements(1));

    // 测试完整中间件栈
    group.bench_function("完整中间件栈", |b| {
        b.to_async(&rt).iter(|| async {
            let permission_state = create_permission_middleware_state();

            let app = Router::new().route("/", get(auth_handler)).layer(
                ServiceBuilder::new()
                    .layer(TraceLayer::new_for_http())
                    .layer(middleware::from_fn_with_state(
                        permission_state.clone(),
                        require_write_permission,
                    ))
                    .layer(middleware::from_fn_with_state(
                        permission_state,
                        inject_permission_state,
                    ))
                    .layer(middleware::from_fn(inject_authenticated_user)),
            );

            let mut request = Request::builder()
                .uri("/")
                .header(
                    "Authorization",
                    format!("Bearer {}", black_box(&extended_token)),
                )
                .body(Body::empty())
                .unwrap();

            let response = app.oneshot(request).await.unwrap();
            black_box(response);
        });
    });

    group.finish();
}

/// 性能基准测试：并发中间件处理
/// 测试中间件在高并发下的性能
fn bench_concurrent_middleware(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let extended_token = rt.block_on(create_test_extended_token());

    let mut group = c.benchmark_group("并发中间件性能");

    // 测试不同并发级别
    for concurrency in [10, 50, 100, 500].iter() {
        group.throughput(Throughput::Elements(*concurrency as u64));
        group.bench_with_input(
            BenchmarkId::new("并发中间件处理", concurrency),
            concurrency,
            |b, &concurrency| {
                b.to_async(&rt).iter(|| async {
                    let permission_state = create_permission_middleware_state();

                    let app = Router::new()
                        .route("/", get(auth_handler))
                        .layer(middleware::from_fn_with_state(
                            permission_state.clone(),
                            require_write_permission,
                        ))
                        .layer(middleware::from_fn_with_state(
                            permission_state,
                            inject_permission_state,
                        ))
                        .layer(middleware::from_fn(inject_authenticated_user));

                    let mut handles = Vec::new();

                    for _ in 0..concurrency {
                        let app = app.clone();
                        let token = extended_token.clone();

                        let handle = tokio::spawn(async move {
                            let mut request = Request::builder()
                                .uri("/")
                                .header("Authorization", format!("Bearer {}", token))
                                .body(Body::empty())
                                .unwrap();

                            app.oneshot(request).await
                        });
                        handles.push(handle);
                    }

                    // 等待所有任务完成
                    for handle in handles {
                        let _ = handle.await.unwrap();
                    }
                });
            },
        );
    }

    group.finish();
}

criterion_group!(
    middleware_benches,
    bench_auth_middleware,
    bench_permission_middleware,
    bench_middleware_stack,
    bench_concurrent_middleware
);
criterion_main!(middleware_benches);
