//! 兼容性验证测试
//!
//! 验证SeaORM 1.1.12和Axum 0.8.4的兼容性

use sea_orm::{ConnectOptions, Database, DatabaseConnection};
use std::time::Duration;

/// 测试SeaORM 1.1.12兼容性
#[tokio::test]
async fn test_seaorm_compatibility() {
    println!("🔗 测试SeaORM 1.1.12兼容性...");

    // 使用PostgreSQL测试数据库
    let database_url = std::env::var("DATABASE_URL").unwrap_or_else(|_| {
        "postgres://axum_user:axum_secure_password_2025@localhost:5432/axum_tutorial".to_string()
    });

    let mut opt = ConnectOptions::new(database_url);
    opt.max_connections(100)
        .min_connections(5)
        .connect_timeout(Duration::from_secs(8))
        .acquire_timeout(Duration::from_secs(8))
        .idle_timeout(Duration::from_secs(8))
        .max_lifetime(Duration::from_secs(8));

    let db: DatabaseConnection = Database::connect(opt)
        .await
        .expect("Failed to connect to database");

    // 验证连接是否正常
    assert!(db.ping().await.is_ok(), "数据库连接失败");

    println!("✅ SeaORM 1.1.12兼容性验证通过");
}

/// 测试Axum 0.8.4兼容性
#[tokio::test]
async fn test_axum_compatibility() {
    println!("🌐 测试Axum 0.8.4兼容性...");

    use axum::{Router, response::Json, routing::get};
    use serde_json::{Value, json};

    // 创建简单的路由处理器
    async fn health_check() -> Json<Value> {
        Json(json!({
            "status": "ok",
            "version": "0.8.4"
        }))
    }

    // 创建路由
    let app: Router = Router::new().route("/health", get(health_check));

    // 验证路由创建成功
    assert!(!format!("{app:?}").is_empty(), "Axum路由创建失败");

    println!("✅ Axum 0.8.4兼容性验证通过");
}

/// 测试Rust edition 2024兼容性
#[test]
fn test_rust_edition_2024_compatibility() {
    println!("🦀 测试Rust edition 2024兼容性...");

    // 测试一些Rust 2024 edition的特性
    let _test_closure = || {
        println!("Rust 2024 edition closure test");
    };

    // 测试async/await语法
    let _async_test = async {
        tokio::time::sleep(Duration::from_millis(1)).await;
    };

    println!("✅ Rust edition 2024兼容性验证通过");
}

/// 测试依赖版本一致性
#[test]
fn test_dependency_version_consistency() {
    println!("📦 测试依赖版本一致性...");

    // 这个测试通过编译时检查来验证依赖版本一致性
    // 如果版本不一致，编译时会出现冲突

    // 验证关键依赖的版本
    let axum_version = env!("CARGO_PKG_VERSION_MAJOR");
    let _tokio_version = tokio::runtime::Runtime::new();

    assert!(!axum_version.is_empty(), "Axum版本信息获取失败");

    println!("✅ 依赖版本一致性验证通过");
}
