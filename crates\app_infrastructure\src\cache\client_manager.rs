//! # 缓存客户端管理器
//!
//! 基于fred客户端的DragonflyDB/Redis连接池管理器
//! 提供企业级缓存连接管理和监控功能

use super::config::CacheConfig;
use anyhow::{Result as AnyhowResult, anyhow};
use fred::prelude::*;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};

/// 缓存客户端连接池管理器
///
/// 【目的】: 管理DragonflyDB/Redis连接池，提供高性能缓存访问
/// 【设计】: 基于fred客户端，支持连接池、重连、监控等企业级功能
pub struct CacheClientManager {
    /// fred Redis客户端实例
    client: Arc<Client>,
    /// 缓存配置
    config: CacheConfig,
    /// 连接池统计信息
    stats: Arc<RwLock<CachePoolStats>>,
    /// 管理器启动时间
    start_time: Instant,
}

/// 缓存连接池统计信息
///
/// 【目的】: 记录连接池的运行状态和性能指标
#[derive(Debug, Clone, Default)]
pub struct CachePoolStats {
    /// 总连接数
    pub total_connections: u32,
    /// 活跃连接数
    pub active_connections: u32,
    /// 空闲连接数
    pub idle_connections: u32,
    /// 连接创建总数
    pub connections_created: u64,
    /// 连接销毁总数
    pub connections_destroyed: u64,
    /// 连接获取总数
    pub connections_acquired: u64,
    /// 连接释放总数
    pub connections_released: u64,
    /// 连接超时次数
    pub connection_timeouts: u64,
    /// 连接错误次数
    pub connection_errors: u64,
    /// 重连次数
    pub reconnect_count: u64,
    /// 最后一次健康检查时间
    pub last_health_check: Option<Instant>,
    /// 健康检查状态
    pub is_healthy: bool,
}

impl CacheClientManager {
    /// 创建新的缓存客户端管理器
    ///
    /// 【参数】:
    /// - config: 缓存配置
    ///
    /// 【返回】: 缓存客户端管理器实例
    pub async fn new(config: CacheConfig) -> AnyhowResult<Self> {
        info!("🔧 正在创建缓存客户端管理器...");
        debug!("缓存配置: {:?}", config);

        // 创建fred客户端配置
        let fred_config = Self::create_fred_config(&config)?;

        // 创建Redis客户端 (fred 10.1.0 API)
        let client = Builder::from_config(fred_config).build()?;

        // 连接到Redis服务器
        Self::connect_client(&client, &config).await?;

        let manager = Self {
            client: Arc::new(client),
            config,
            stats: Arc::new(RwLock::new(CachePoolStats::default())),
            start_time: Instant::now(),
        };

        info!("✅ 缓存客户端管理器创建成功");
        Ok(manager)
    }

    /// 创建fred客户端配置
    ///
    /// 【参数】:
    /// - config: 缓存配置
    ///
    /// 【返回】: fred客户端配置
    fn create_fred_config(config: &CacheConfig) -> AnyhowResult<Config> {
        debug!("正在创建fred客户端配置...");

        // 解析Redis URL并创建基础配置
        let mut fred_config = if config.cluster_mode && !config.cluster_nodes.is_empty() {
            // 集群模式配置
            info!("配置Redis集群模式，节点数: {}", config.cluster_nodes.len());
            // 对于集群模式，使用第一个节点作为入口点
            let first_node = config
                .cluster_nodes
                .first()
                .ok_or_else(|| anyhow!("集群节点列表为空"))?;
            Config::from_url(first_node)
                .map_err(|e| anyhow!("解析集群节点URL失败 '{}': {}", first_node, e))?
        } else {
            // 单机模式配置
            info!("配置Redis单机模式: {}", config.cache_url);
            Config::from_url(&config.cache_url)
                .map_err(|e| anyhow!("解析Redis URL失败 '{}': {}", config.cache_url, e))?
        };

        // 应用企业级性能优化配置
        Self::apply_performance_config(&mut fred_config, config)?;

        debug!("✅ fred客户端配置创建完成");
        Ok(fred_config)
    }

    /// 应用性能优化配置
    ///
    /// 【参数】:
    /// - fred_config: fred客户端配置（可变引用）
    /// - _config: 缓存配置（暂时未使用）
    fn apply_performance_config(
        fred_config: &mut Config,
        _config: &CacheConfig,
    ) -> AnyhowResult<()> {
        debug!("正在应用性能优化配置...");

        // 基于fred 10.1.0的实际API设置配置
        // 设置非阻塞模式以提高性能
        fred_config.blocking = Blocking::Block;

        // 设置快速失败模式
        fred_config.fail_fast = true;

        // 注意：fred 10.1.0中TLS配置已移至ServerConfig中

        info!("✅ 性能优化配置应用完成（使用fred基础配置）");
        Ok(())
    }

    /// 连接到缓存服务器（DragonflyDB/Redis兼容）
    ///
    /// 【参数】:
    /// - client: Redis兼容客户端
    /// - config: 缓存配置
    async fn connect_client(client: &Client, config: &CacheConfig) -> AnyhowResult<()> {
        info!("🔗 正在连接到缓存服务器（DragonflyDB）...");

        // 设置连接超时
        let connect_timeout = config.pool_config.connect_timeout;

        // 尝试连接
        let connect_result = tokio::time::timeout(connect_timeout, client.init()).await;

        match connect_result {
            Ok(Ok(_)) => {
                info!("✅ 成功连接到缓存服务器（DragonflyDB）");
                Ok(())
            }
            Ok(Err(e)) => {
                error!("❌ 连接缓存服务器失败: {}", e);
                Err(anyhow!("连接缓存服务器失败: {}", e))
            }
            Err(_) => {
                error!("❌ 连接缓存服务器超时 ({}秒)", connect_timeout.as_secs());
                Err(anyhow!("连接缓存服务器超时"))
            }
        }
    }

    /// 获取缓存客户端实例（Redis兼容）
    ///
    /// 【返回】: Redis兼容客户端的Arc引用
    pub fn get_client(&self) -> Arc<Client> {
        self.client.clone()
    }

    /// 获取缓存配置
    ///
    /// 【返回】: 缓存配置的引用
    pub fn get_config(&self) -> &CacheConfig {
        &self.config
    }

    /// 执行健康检查
    ///
    /// 【返回】: 健康检查结果
    pub async fn health_check(&self) -> bool {
        debug!("执行缓存健康检查...");

        match self.client.ping::<String>(None).await {
            Ok(_) => {
                debug!("✅ 缓存健康检查通过");

                // 更新统计信息
                {
                    let mut stats = self.stats.write().await;
                    stats.last_health_check = Some(Instant::now());
                    stats.is_healthy = true;
                }

                true
            }
            Err(e) => {
                warn!("❌ 缓存健康检查失败: {}", e);

                // 更新统计信息
                {
                    let mut stats = self.stats.write().await;
                    stats.last_health_check = Some(Instant::now());
                    stats.is_healthy = false;
                    stats.connection_errors += 1;
                }

                false
            }
        }
    }

    /// 启动连接池监控
    ///
    /// 【功能】: 定期执行健康检查和统计信息更新
    pub fn start_monitoring(&self) {
        let client = self.client.clone();
        let stats = self.stats.clone();
        let config = self.config.clone();

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(30)); // 每30秒检查一次

            loop {
                interval.tick().await;

                // 执行健康检查
                let is_healthy = match client.ping::<String>(None).await {
                    Ok(_) => {
                        debug!("定期健康检查: 正常");
                        true
                    }
                    Err(e) => {
                        warn!("定期健康检查失败: {}", e);
                        false
                    }
                };

                // 更新统计信息
                {
                    let mut stats_guard = stats.write().await;
                    stats_guard.last_health_check = Some(Instant::now());
                    stats_guard.is_healthy = is_healthy;

                    if !is_healthy {
                        stats_guard.connection_errors += 1;
                    }
                }

                // 如果不健康，记录警告
                if !is_healthy {
                    warn!("缓存连接不健康，可能需要重连");
                }
            }
        });

        info!("🔍 缓存连接池监控已启动");
    }

    /// 获取连接池统计信息
    ///
    /// 【返回】: 连接池统计信息
    pub async fn get_stats(&self) -> CachePoolStats {
        let stats = self.stats.read().await;
        stats.clone()
    }

    /// 获取运行时间
    ///
    /// 【返回】: 管理器运行时间
    pub fn get_uptime(&self) -> Duration {
        self.start_time.elapsed()
    }

    /// 关闭缓存客户端管理器
    ///
    /// 【功能】: 优雅关闭所有连接和资源
    pub async fn shutdown(&self) -> AnyhowResult<()> {
        info!("🔄 正在关闭缓存客户端管理器...");

        // 关闭Redis客户端连接
        if let Err(e) = self.client.quit().await {
            warn!("关闭Redis客户端时出现错误: {}", e);
        }

        info!("✅ 缓存客户端管理器已关闭");
        Ok(())
    }
}

impl Drop for CacheClientManager {
    /// 析构函数 - 确保资源被正确释放
    fn drop(&mut self) {
        debug!("缓存客户端管理器正在被销毁");
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::cache::config::CacheConfig;

    #[tokio::test]
    async fn test_cache_client_manager_creation() {
        // 使用测试配置
        let config = CacheConfig::for_tests();

        // 注意: 这个测试需要Redis服务器运行
        // 在CI环境中可能需要跳过或使用mock
        if std::env::var("SKIP_REDIS_TESTS").is_ok() {
            return;
        }

        let result = CacheClientManager::new(config).await;

        // 如果Redis服务器不可用，测试应该失败但不应该panic
        match result {
            Ok(manager) => {
                assert!(manager.health_check().await);
                let _ = manager.shutdown().await;
            }
            Err(e) => {
                println!("Redis服务器不可用，跳过测试: {e}");
            }
        }
    }

    #[test]
    fn test_cache_pool_stats_default() {
        let stats = CachePoolStats::default();
        assert_eq!(stats.total_connections, 0);
        assert_eq!(stats.active_connections, 0);
        assert!(!stats.is_healthy);
    }
}
