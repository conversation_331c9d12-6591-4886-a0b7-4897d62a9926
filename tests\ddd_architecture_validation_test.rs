//! DDD架构合规性验证测试
//!
//! 本测试模块验证项目是否完全符合模块化领域驱动设计(Modular DDD)和整洁架构的原则
//!
//! ## 验证范围
//! 1. 依赖倒置原则的正确实施
//! 2. 各层之间的依赖关系和边界
//! 3. 领域模型的纯净性和业务逻辑封装
//! 4. 仓储模式的正确实现
//! 5. 代码质量和编码规范遵循情况

use std::collections::HashMap;
use std::fs;
use std::path::Path;
// use serde_json::Value;
// use chrono;

/// DDD架构验证器
pub struct DddArchitectureValidator {
    /// 项目根目录
    project_root: String,
    /// 验证结果
    validation_results: HashMap<String, ValidationResult>,
}

/// 验证结果
#[derive(Debug, Clone)]
pub struct ValidationResult {
    /// 验证项目名称
    pub name: String,
    /// 是否通过验证
    pub passed: bool,
    /// 详细信息
    pub details: String,
    /// 错误信息（如果有）
    pub errors: Vec<String>,
}

impl DddArchitectureValidator {
    /// 创建新的架构验证器
    pub fn new(project_root: &str) -> Self {
        Self {
            project_root: project_root.to_string(),
            validation_results: HashMap::new(),
        }
    }

    /// 执行完整的DDD架构验证
    pub async fn validate_architecture(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        println!("🏛️ 开始DDD架构合规性验证...");

        // 1. 验证依赖倒置原则
        self.validate_dependency_inversion().await?;

        // 2. 验证各层依赖关系和边界
        self.validate_layer_boundaries().await?;

        // 3. 验证领域模型纯净性
        self.validate_domain_purity().await?;

        // 4. 验证仓储模式实现
        self.validate_repository_pattern().await?;

        // 5. 验证代码质量和规范
        self.validate_code_quality().await?;

        // 6. 生成验证报告
        self.generate_validation_report().await?;

        println!("✅ DDD架构合规性验证完成");
        Ok(())
    }

    /// 验证依赖倒置原则的实施
    async fn validate_dependency_inversion(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        println!("🔄 验证依赖倒置原则...");

        let mut result = ValidationResult {
            name: "依赖倒置原则验证".to_string(),
            passed: true,
            details: String::new(),
            errors: Vec::new(),
        };

        // 验证领域层不依赖基础设施层
        if let Err(e) = self.check_domain_independence().await {
            result.passed = false;
            result.errors.push(format!("领域层独立性验证失败: {}", e));
        }

        // 验证应用层依赖抽象而非具体实现
        if let Err(e) = self.check_application_abstractions().await {
            result.passed = false;
            result.errors.push(format!("应用层抽象验证失败: {}", e));
        }

        // 验证接口定义在正确的层级
        if let Err(e) = self.check_interface_placement().await {
            result.passed = false;
            result.errors.push(format!("接口放置验证失败: {}", e));
        }

        if result.passed {
            result.details = "依赖倒置原则实施正确，各层依赖抽象而非具体实现".to_string();
        }

        self.validation_results
            .insert("dependency_inversion".to_string(), result);
        Ok(())
    }

    /// 验证各层依赖关系和边界
    async fn validate_layer_boundaries(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        println!("🏗️ 验证层级边界...");

        let mut result = ValidationResult {
            name: "层级边界验证".to_string(),
            passed: true,
            details: String::new(),
            errors: Vec::new(),
        };

        // 验证领域层边界
        if let Err(e) = self.check_domain_boundaries().await {
            result.passed = false;
            result.errors.push(format!("领域层边界验证失败: {}", e));
        }

        // 验证应用层边界
        if let Err(e) = self.check_application_boundaries().await {
            result.passed = false;
            result.errors.push(format!("应用层边界验证失败: {}", e));
        }

        // 验证基础设施层边界
        if let Err(e) = self.check_infrastructure_boundaries().await {
            result.passed = false;
            result.errors.push(format!("基础设施层边界验证失败: {}", e));
        }

        // 验证接口层边界
        if let Err(e) = self.check_interface_boundaries().await {
            result.passed = false;
            result.errors.push(format!("接口层边界验证失败: {}", e));
        }

        if result.passed {
            result.details = "所有层级边界清晰，依赖方向正确".to_string();
        }

        self.validation_results
            .insert("layer_boundaries".to_string(), result);
        Ok(())
    }

    /// 验证领域模型纯净性
    async fn validate_domain_purity(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        println!("🧹 验证领域模型纯净性...");

        let mut result = ValidationResult {
            name: "领域模型纯净性验证".to_string(),
            passed: true,
            details: String::new(),
            errors: Vec::new(),
        };

        // 验证领域实体的纯净性
        if let Err(e) = self.check_entity_purity().await {
            result.passed = false;
            result.errors.push(format!("实体纯净性验证失败: {}", e));
        }

        // 验证领域服务的纯净性
        if let Err(e) = self.check_domain_service_purity().await {
            result.passed = false;
            result.errors.push(format!("领域服务纯净性验证失败: {}", e));
        }

        // 验证值对象的纯净性
        if let Err(e) = self.check_value_object_purity().await {
            result.passed = false;
            result.errors.push(format!("值对象纯净性验证失败: {}", e));
        }

        if result.passed {
            result.details = "领域模型保持纯净，无外部依赖污染".to_string();
        }

        self.validation_results
            .insert("domain_purity".to_string(), result);
        Ok(())
    }

    /// 验证仓储模式实现
    async fn validate_repository_pattern(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        println!("🗄️ 验证仓储模式实现...");

        let mut result = ValidationResult {
            name: "仓储模式验证".to_string(),
            passed: true,
            details: String::new(),
            errors: Vec::new(),
        };

        // 验证仓储接口定义
        if let Err(e) = self.check_repository_interfaces().await {
            result.passed = false;
            result.errors.push(format!("仓储接口验证失败: {}", e));
        }

        // 验证仓储实现
        if let Err(e) = self.check_repository_implementations().await {
            result.passed = false;
            result.errors.push(format!("仓储实现验证失败: {}", e));
        }

        // 验证聚合根映射
        if let Err(e) = self.check_aggregate_mapping().await {
            result.passed = false;
            result.errors.push(format!("聚合根映射验证失败: {}", e));
        }

        if result.passed {
            result.details = "仓储模式实现正确，接口与实现分离良好".to_string();
        }

        self.validation_results
            .insert("repository_pattern".to_string(), result);
        Ok(())
    }

    /// 验证代码质量和编码规范
    async fn validate_code_quality(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        println!("📋 验证代码质量和编码规范...");

        let mut result = ValidationResult {
            name: "代码质量验证".to_string(),
            passed: true,
            details: String::new(),
            errors: Vec::new(),
        };

        // 验证编码规范遵循
        if let Err(e) = self.check_coding_standards().await {
            result.passed = false;
            result.errors.push(format!("编码规范验证失败: {}", e));
        }

        // 验证文档完整性
        if let Err(e) = self.check_documentation_completeness().await {
            result.passed = false;
            result.errors.push(format!("文档完整性验证失败: {}", e));
        }

        // 验证测试覆盖率
        if let Err(e) = self.check_test_coverage().await {
            result.passed = false;
            result.errors.push(format!("测试覆盖率验证失败: {}", e));
        }

        if result.passed {
            result.details = "代码质量优秀，符合企业级标准".to_string();
        }

        self.validation_results
            .insert("code_quality".to_string(), result);
        Ok(())
    }

    /// 生成验证报告
    async fn generate_validation_report(&self) -> Result<(), Box<dyn std::error::Error>> {
        println!("📊 生成DDD架构验证报告...");

        let report_path = format!(
            "{}/docs/ddd-architecture-validation-report.md",
            self.project_root
        );

        let mut report = String::new();
        report.push_str("# DDD架构合规性验证报告\n\n");
        report.push_str(&format!("**验证时间**: {}\n", "2025-01-20 12:00:00 UTC"));
        report.push_str("**验证范围**: 模块化领域驱动设计(Modular DDD) + 整洁架构\n\n");

        // 生成验证摘要
        let total_checks = self.validation_results.len();
        let passed_checks = self
            .validation_results
            .values()
            .filter(|r| r.passed)
            .count();
        let success_rate = ((passed_checks as f64) / (total_checks as f64)) * 100.0;

        report.push_str("## 📊 验证摘要\n\n");
        report.push_str(&format!("- **总验证项**: {}\n", total_checks));
        report.push_str(&format!("- **通过项**: {}\n", passed_checks));
        report.push_str(&format!("- **成功率**: {:.1}%\n", success_rate));
        report.push_str(&format!(
            "- **整体状态**: {}\n\n",
            if success_rate >= 100.0 {
                "✅ 完全合规"
            } else if success_rate >= 80.0 {
                "⚠️ 基本合规"
            } else {
                "❌ 需要改进"
            }
        ));

        // 生成详细验证结果
        report.push_str("## 📋 详细验证结果\n\n");
        for (key, result) in &self.validation_results {
            let status = if result.passed { "✅" } else { "❌" };
            report.push_str(&format!("### {} {}\n\n", status, result.name));
            report.push_str(&format!(
                "**状态**: {}\n",
                if result.passed { "通过" } else { "失败" }
            ));
            report.push_str(&format!("**详情**: {}\n", result.details));

            if !result.errors.is_empty() {
                report.push_str("\n**错误信息**:\n");
                for error in &result.errors {
                    report.push_str(&format!("- {}\n", error));
                }
            }
            report.push_str("\n");
        }

        // 写入报告文件
        if let Some(parent) = Path::new(&report_path).parent() {
            fs::create_dir_all(parent)?;
        }
        fs::write(&report_path, report)?;

        println!("✅ 验证报告已生成: {}", report_path);
        Ok(())
    }

    // ============================================================================
    // 具体验证方法实现
    // ============================================================================

    /// 检查领域层独立性
    async fn check_domain_independence(&self) -> Result<(), String> {
        let domain_cargo_path = format!("{}/crates/app_domain/Cargo.toml", self.project_root);

        if !Path::new(&domain_cargo_path).exists() {
            return Err("app_domain crate不存在".to_string());
        }

        let content = fs::read_to_string(&domain_cargo_path)
            .map_err(|e| format!("读取app_domain Cargo.toml失败: {}", e))?;

        // 检查是否依赖基础设施层
        if content.contains("app_infrastructure") {
            return Err("领域层不应该依赖基础设施层".to_string());
        }

        // 检查是否依赖接口层
        if content.contains("app_interfaces") {
            return Err("领域层不应该依赖接口层".to_string());
        }

        // 检查是否依赖服务器层
        if content.contains("server") || content.contains("axum-server") {
            return Err("领域层不应该依赖服务器层".to_string());
        }

        Ok(())
    }

    /// 检查应用层抽象依赖
    async fn check_application_abstractions(&self) -> Result<(), String> {
        let app_cargo_path = format!("{}/crates/app_application/Cargo.toml", self.project_root);

        if !Path::new(&app_cargo_path).exists() {
            return Err("app_application crate不存在".to_string());
        }

        let content = fs::read_to_string(&app_cargo_path)
            .map_err(|e| format!("读取app_application Cargo.toml失败: {}", e))?;

        // 应用层应该依赖领域层
        if !content.contains("app_domain") {
            return Err("应用层应该依赖领域层".to_string());
        }

        // 应用层应该依赖公共层
        if !content.contains("app_common") {
            return Err("应用层应该依赖公共层".to_string());
        }

        // 应用层可以依赖接口层（用于DTO定义）
        // 这是允许的，因为接口层定义了API契约

        Ok(())
    }

    /// 检查接口放置正确性
    async fn check_interface_placement(&self) -> Result<(), String> {
        let interfaces_path = format!("{}/crates/app_interfaces", self.project_root);

        if !Path::new(&interfaces_path).exists() {
            return Err("app_interfaces crate不存在".to_string());
        }

        // 检查接口层是否包含正确的内容
        let src_path = format!("{}/src", interfaces_path);
        if !Path::new(&src_path).exists() {
            return Err("app_interfaces src目录不存在".to_string());
        }

        // 接口层应该包含DTO定义
        let dto_path = format!("{}/dto", src_path);
        if !Path::new(&dto_path).exists() {
            return Err("接口层缺少DTO定义目录".to_string());
        }

        Ok(())
    }

    /// 检查领域层边界
    async fn check_domain_boundaries(&self) -> Result<(), String> {
        // 验证领域层结构
        let domain_path = format!("{}/crates/app_domain/src", self.project_root);

        let required_modules = ["entities", "repositories", "services"];
        for module in &required_modules {
            let module_path = format!("{}/{}", domain_path, module);
            if !Path::new(&module_path).exists() {
                return Err(format!("领域层缺少{}模块", module));
            }
        }

        Ok(())
    }

    /// 检查应用层边界
    async fn check_application_boundaries(&self) -> Result<(), String> {
        let app_path = format!("{}/crates/app_application/src", self.project_root);

        if !Path::new(&app_path).exists() {
            return Err("应用层src目录不存在".to_string());
        }

        // 应用层应该包含服务实现
        let lib_rs_path = format!("{}/lib.rs", app_path);
        if !Path::new(&lib_rs_path).exists() {
            return Err("应用层缺少lib.rs文件".to_string());
        }

        Ok(())
    }

    /// 检查基础设施层边界
    async fn check_infrastructure_boundaries(&self) -> Result<(), String> {
        let infra_path = format!("{}/crates/app_infrastructure/src", self.project_root);

        if !Path::new(&infra_path).exists() {
            return Err("基础设施层src目录不存在".to_string());
        }

        // 基础设施层应该包含具体实现
        let domains_path = format!("{}/domains", infra_path);
        if !Path::new(&domains_path).exists() {
            return Err("基础设施层缺少domains目录".to_string());
        }

        Ok(())
    }

    /// 检查接口层边界
    async fn check_interface_boundaries(&self) -> Result<(), String> {
        let interfaces_path = format!("{}/crates/app_interfaces/src", self.project_root);

        if !Path::new(&interfaces_path).exists() {
            return Err("接口层src目录不存在".to_string());
        }

        Ok(())
    }

    /// 检查实体纯净性
    async fn check_entity_purity(&self) -> Result<(), String> {
        let entities_path = format!("{}/crates/app_domain/src/entities", self.project_root);

        if !Path::new(&entities_path).exists() {
            return Err("领域实体目录不存在".to_string());
        }

        // 检查实体文件是否存在
        let entity_files = ["user.rs", "task.rs", "message.rs"];
        for file in &entity_files {
            let file_path = format!("{}/{}", entities_path, file);
            if !Path::new(&file_path).exists() {
                return Err(format!("缺少实体文件: {}", file));
            }
        }

        Ok(())
    }

    /// 检查领域服务纯净性
    async fn check_domain_service_purity(&self) -> Result<(), String> {
        let services_path = format!("{}/crates/app_domain/src/services", self.project_root);

        if !Path::new(&services_path).exists() {
            return Err("领域服务目录不存在".to_string());
        }

        Ok(())
    }

    /// 检查值对象纯净性
    async fn check_value_object_purity(&self) -> Result<(), String> {
        // 值对象可能在各个实体文件中定义，这里做基本检查
        let domain_path = format!("{}/crates/app_domain/src", self.project_root);

        if !Path::new(&domain_path).exists() {
            return Err("领域层目录不存在".to_string());
        }

        Ok(())
    }

    /// 检查仓储接口
    async fn check_repository_interfaces(&self) -> Result<(), String> {
        let repo_path = format!("{}/crates/app_domain/src/repositories", self.project_root);

        if !Path::new(&repo_path).exists() {
            return Err("仓储接口目录不存在".to_string());
        }

        // 检查主要仓储接口文件
        let repo_files = [
            "user_repository.rs",
            "task_repository.rs",
            "message_repository.rs",
        ];
        for file in &repo_files {
            let file_path = format!("{}/{}", repo_path, file);
            if !Path::new(&file_path).exists() {
                return Err(format!("缺少仓储接口文件: {}", file));
            }
        }

        Ok(())
    }

    /// 检查仓储实现
    async fn check_repository_implementations(&self) -> Result<(), String> {
        let impl_path = format!(
            "{}/crates/app_infrastructure/src/domains",
            self.project_root
        );

        if !Path::new(&impl_path).exists() {
            return Err("仓储实现目录不存在".to_string());
        }

        // 检查主要仓储实现目录
        let impl_dirs = ["user", "task", "chat"];
        for dir in &impl_dirs {
            let dir_path = format!("{}/{}", impl_path, dir);
            if !Path::new(&dir_path).exists() {
                return Err(format!("缺少仓储实现目录: {}", dir));
            }
        }

        Ok(())
    }

    /// 检查聚合根映射
    async fn check_aggregate_mapping(&self) -> Result<(), String> {
        // 检查实体与仓储的一一对应关系
        let entities_path = format!("{}/crates/app_domain/src/entities", self.project_root);
        let repos_path = format!("{}/crates/app_domain/src/repositories", self.project_root);

        if !Path::new(&entities_path).exists() || !Path::new(&repos_path).exists() {
            return Err("实体或仓储目录不存在".to_string());
        }

        // 这里可以添加更详细的映射检查逻辑
        Ok(())
    }

    /// 检查编码规范
    async fn check_coding_standards(&self) -> Result<(), String> {
        // 检查是否有Cargo.toml配置
        let cargo_path = format!("{}/Cargo.toml", self.project_root);
        if !Path::new(&cargo_path).exists() {
            return Err("项目根目录缺少Cargo.toml".to_string());
        }

        // 检查是否使用workspace
        let content =
            fs::read_to_string(&cargo_path).map_err(|e| format!("读取Cargo.toml失败: {}", e))?;

        if !content.contains("[workspace]") {
            return Err("项目未使用workspace结构".to_string());
        }

        Ok(())
    }

    /// 检查文档完整性
    async fn check_documentation_completeness(&self) -> Result<(), String> {
        // 检查README文件
        let readme_path = format!("{}/README.md", self.project_root);
        if !Path::new(&readme_path).exists() {
            return Err("项目缺少README.md文件".to_string());
        }

        // 检查架构文档
        let arch_doc_path = format!("{}/docs", self.project_root);
        if !Path::new(&arch_doc_path).exists() {
            return Err("项目缺少docs目录".to_string());
        }

        Ok(())
    }

    /// 检查测试覆盖率
    async fn check_test_coverage(&self) -> Result<(), String> {
        // 检查是否有测试目录
        let tests_path = format!("{}/tests", self.project_root);
        if !Path::new(&tests_path).exists() {
            return Err("项目缺少tests目录".to_string());
        }

        // 检查各crate是否有测试
        let crates = [
            "app_domain",
            "app_application",
            "app_infrastructure",
            "app_common",
        ];
        for crate_name in &crates {
            let crate_tests_path = format!("{}/crates/{}/tests", self.project_root, crate_name);
            let crate_src_path = format!("{}/crates/{}/src", self.project_root, crate_name);

            // 检查是否有独立的tests目录或src中的测试
            if !Path::new(&crate_tests_path).exists() {
                // 检查src目录中是否有测试文件
                if Path::new(&crate_src_path).exists() {
                    // 这里可以进一步检查src中的测试代码
                    continue;
                } else {
                    return Err(format!("crate {} 缺少测试", crate_name));
                }
            }
        }

        Ok(())
    }
}

/// 主要的测试函数
#[tokio::test]
async fn test_ddd_architecture_compliance() {
    let project_root = std::env::current_dir()
        .expect("无法获取当前目录")
        .to_string_lossy()
        .to_string();

    let mut validator = DddArchitectureValidator::new(&project_root);

    match validator.validate_architecture().await {
        Ok(_) => {
            println!("✅ DDD架构合规性验证完成");

            // 检查是否所有验证都通过
            let all_passed = validator.validation_results.values().all(|r| r.passed);
            assert!(all_passed, "部分DDD架构验证未通过");
        }
        Err(e) => {
            panic!("DDD架构验证失败: {}", e);
        }
    }
}
