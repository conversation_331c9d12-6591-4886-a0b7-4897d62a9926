//! # 图表可视化模块
//!
//! 在HTML报告中嵌入图表，以直观展示测试覆盖率、成功率等指标
//!
//! ## 功能特性
//! - Chart.js集成
//! - 多种图表类型支持
//! - 响应式设计
//! - 交互式图表

use super::*;
use anyhow::{Result, Context};
use serde_json;

/// 图表可视化器
pub struct ChartVisualizer {
    /// 配置选项
    config: ReportConfig,
}

/// 图表类型枚举
#[derive(Debug, Clone)]
pub enum ChartType {
    /// 饼图
    Pie,
    /// 环形图
    Doughnut,
    /// 柱状图
    Bar,
    /// 折线图
    Line,
    /// 面积图
    Area,
    /// 雷达图
    Radar,
}

/// 图表配置
#[derive(Debug, Clone)]
pub struct ChartConfig {
    /// 图表类型
    pub chart_type: ChartType,
    /// 图表标题
    pub title: String,
    /// 宽度
    pub width: u32,
    /// 高度
    pub height: u32,
    /// 是否显示图例
    pub show_legend: bool,
    /// 是否响应式
    pub responsive: bool,
    /// 颜色主题
    pub color_scheme: ColorScheme,
}

/// 颜色主题
#[derive(Debug, Clone)]
pub enum ColorScheme {
    /// 默认主题
    Default,
    /// 成功/失败主题
    SuccessFailure,
    /// 彩虹主题
    Rainbow,
    /// 单色主题
    Monochrome(String),
    /// 自定义颜色
    Custom(Vec<String>),
}

impl Default for ChartConfig {
    fn default() -> Self {
        Self {
            chart_type: ChartType::Doughnut,
            title: "图表".to_string(),
            width: 400,
            height: 300,
            show_legend: true,
            responsive: true,
            color_scheme: ColorScheme::Default,
        }
    }
}

impl ChartVisualizer {
    /// 创建新的图表可视化器
    pub fn new(config: ReportConfig) -> Self {
        Self { config }
    }

    /// 生成测试状态分布图表
    pub fn generate_status_chart(&self, report: &TestReport) -> Result<String> {
        let chart_config = ChartConfig {
            chart_type: ChartType::Doughnut,
            title: "测试状态分布".to_string(),
            color_scheme: ColorScheme::SuccessFailure,
            ..Default::default()
        };

        let data = vec![
            ("通过", report.summary.passed_tests as f64),
            ("失败", report.summary.failed_tests as f64),
            ("跳过", report.summary.skipped_tests as f64),
            ("错误", report.summary.error_tests as f64),
        ];

        self.create_chart_js_code("statusChart", &chart_config, &data)
    }

    /// 生成测试套件执行时间图表
    pub fn generate_duration_chart(&self, report: &TestReport) -> Result<String> {
        let chart_config = ChartConfig {
            chart_type: ChartType::Bar,
            title: "测试套件执行时间".to_string(),
            color_scheme: ColorScheme::Default,
            ..Default::default()
        };

        let data: Vec<(&str, f64)> = report.test_suites
            .iter()
            .map(|suite| (suite.name.as_str(), suite.total_duration_ms as f64))
            .collect();

        self.create_chart_js_code("durationChart", &chart_config, &data)
    }

    /// 生成覆盖率图表
    pub fn generate_coverage_chart(&self, report: &TestReport) -> Result<String> {
        if let Some(coverage) = &report.coverage {
            let chart_config = ChartConfig {
                chart_type: ChartType::Radar,
                title: "代码覆盖率".to_string(),
                color_scheme: ColorScheme::Default,
                ..Default::default()
            };

            let data = vec![
                ("行覆盖率", coverage.line_coverage),
                ("分支覆盖率", coverage.branch_coverage),
                ("函数覆盖率", coverage.function_coverage),
            ];

            self.create_chart_js_code("coverageChart", &chart_config, &data)
        } else {
            Ok(String::new())
        }
    }

    /// 生成趋势图表
    pub fn generate_trend_chart(&self, trend_data: &TrendAnalysis) -> Result<String> {
        let chart_config = ChartConfig {
            chart_type: ChartType::Line,
            title: "测试趋势分析".to_string(),
            color_scheme: ColorScheme::Rainbow,
            ..Default::default()
        };

        // 准备时间序列数据
        let labels: Vec<String> = trend_data.data_points
            .iter()
            .map(|point| point.timestamp.format("%m-%d %H:%M").to_string())
            .collect();

        let success_rates: Vec<f64> = trend_data.data_points
            .iter()
            .map(|point| point.success_rate)
            .collect();

        self.create_time_series_chart("trendChart", &chart_config, &labels, &success_rates)
    }

    /// 创建Chart.js代码
    fn create_chart_js_code(&self, canvas_id: &str, config: &ChartConfig, data: &[(&str, f64)]) -> Result<String> {
        let chart_type = self.chart_type_to_string(&config.chart_type);
        let colors = self.get_color_palette(&config.color_scheme, data.len());
        
        let labels: Vec<String> = data.iter().map(|(label, _)| format!("'{}'", label)).collect();
        let values: Vec<String> = data.iter().map(|(_, value)| value.to_string()).collect();

        let chart_js = format!(
            r#"
            const {canvas_id}Ctx = document.getElementById('{canvas_id}').getContext('2d');
            new Chart({canvas_id}Ctx, {{
                type: '{chart_type}',
                data: {{
                    labels: [{labels}],
                    datasets: [{{
                        data: [{values}],
                        backgroundColor: [{colors}],
                        borderColor: [{border_colors}],
                        borderWidth: 2
                    }}]
                }},
                options: {{
                    responsive: {responsive},
                    maintainAspectRatio: false,
                    plugins: {{
                        title: {{
                            display: true,
                            text: '{title}',
                            font: {{
                                size: 16,
                                weight: 'bold'
                            }}
                        }},
                        legend: {{
                            display: {show_legend},
                            position: 'bottom'
                        }}
                    }},
                    {additional_options}
                }}
            }});
            "#,
            canvas_id = canvas_id,
            chart_type = chart_type,
            labels = labels.join(", "),
            values = values.join(", "),
            colors = colors.join(", "),
            border_colors = colors.join(", "), // 简化处理，使用相同颜色
            responsive = config.responsive,
            title = config.title,
            show_legend = config.show_legend,
            additional_options = self.get_additional_options(&config.chart_type)
        );

        Ok(chart_js)
    }

    /// 创建时间序列图表
    fn create_time_series_chart(&self, canvas_id: &str, config: &ChartConfig, labels: &[String], data: &[f64]) -> Result<String> {
        let chart_type = self.chart_type_to_string(&config.chart_type);
        let colors = self.get_color_palette(&config.color_scheme, 1);

        let labels_str = labels.iter().map(|l| format!("'{}'", l)).collect::<Vec<_>>().join(", ");
        let data_str = data.iter().map(|d| d.to_string()).collect::<Vec<_>>().join(", ");

        let chart_js = format!(
            r#"
            const {canvas_id}Ctx = document.getElementById('{canvas_id}').getContext('2d');
            new Chart({canvas_id}Ctx, {{
                type: '{chart_type}',
                data: {{
                    labels: [{labels}],
                    datasets: [{{
                        label: '成功率 (%)',
                        data: [{data}],
                        backgroundColor: 'rgba(52, 152, 219, 0.2)',
                        borderColor: 'rgba(52, 152, 219, 1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }}]
                }},
                options: {{
                    responsive: {responsive},
                    maintainAspectRatio: false,
                    plugins: {{
                        title: {{
                            display: true,
                            text: '{title}',
                            font: {{
                                size: 16,
                                weight: 'bold'
                            }}
                        }}
                    }},
                    scales: {{
                        y: {{
                            beginAtZero: true,
                            max: 100,
                            title: {{
                                display: true,
                                text: '成功率 (%)'
                            }}
                        }},
                        x: {{
                            title: {{
                                display: true,
                                text: '时间'
                            }}
                        }}
                    }}
                }}
            }});
            "#,
            canvas_id = canvas_id,
            chart_type = chart_type,
            labels = labels_str,
            data = data_str,
            responsive = config.responsive,
            title = config.title
        );

        Ok(chart_js)
    }

    /// 将图表类型转换为字符串
    fn chart_type_to_string(&self, chart_type: &ChartType) -> &'static str {
        match chart_type {
            ChartType::Pie => "pie",
            ChartType::Doughnut => "doughnut",
            ChartType::Bar => "bar",
            ChartType::Line => "line",
            ChartType::Area => "line",
            ChartType::Radar => "radar",
        }
    }

    /// 获取颜色调色板
    fn get_color_palette(&self, scheme: &ColorScheme, count: usize) -> Vec<String> {
        match scheme {
            ColorScheme::Default => vec![
                "'rgba(52, 152, 219, 0.8)'".to_string(),
                "'rgba(231, 76, 60, 0.8)'".to_string(),
                "'rgba(243, 156, 18, 0.8)'".to_string(),
                "'rgba(142, 68, 173, 0.8)'".to_string(),
                "'rgba(39, 174, 96, 0.8)'".to_string(),
            ].into_iter().cycle().take(count).collect(),
            
            ColorScheme::SuccessFailure => vec![
                "'rgba(39, 174, 96, 0.8)'".to_string(),   // 绿色 - 通过
                "'rgba(231, 76, 60, 0.8)'".to_string(),   // 红色 - 失败
                "'rgba(243, 156, 18, 0.8)'".to_string(),  // 橙色 - 跳过
                "'rgba(142, 68, 173, 0.8)'".to_string(),  // 紫色 - 错误
            ].into_iter().cycle().take(count).collect(),
            
            ColorScheme::Rainbow => vec![
                "'rgba(255, 99, 132, 0.8)'".to_string(),
                "'rgba(54, 162, 235, 0.8)'".to_string(),
                "'rgba(255, 205, 86, 0.8)'".to_string(),
                "'rgba(75, 192, 192, 0.8)'".to_string(),
                "'rgba(153, 102, 255, 0.8)'".to_string(),
                "'rgba(255, 159, 64, 0.8)'".to_string(),
            ].into_iter().cycle().take(count).collect(),
            
            ColorScheme::Monochrome(base_color) => {
                (0..count).map(|i| {
                    let opacity = 0.3 + (0.7 * i as f64 / count.max(1) as f64);
                    format!("'rgba({}, {})'", base_color, opacity)
                }).collect()
            },
            
            ColorScheme::Custom(colors) => {
                colors.iter().cycle().take(count).map(|c| format!("'{}'", c)).collect()
            }
        }
    }

    /// 获取额外的图表选项
    fn get_additional_options(&self, chart_type: &ChartType) -> String {
        match chart_type {
            ChartType::Bar => {
                r#"scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '时间 (毫秒)'
                        }
                    }
                }"#.to_string()
            },
            ChartType::Radar => {
                r#"scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: '覆盖率 (%)'
                        }
                    }
                }"#.to_string()
            },
            _ => String::new()
        }
    }

    /// 生成所有图表的JavaScript代码
    pub fn generate_all_charts(&self, report: &TestReport, trend_data: Option<&TrendAnalysis>) -> Result<String> {
        let mut all_charts = String::new();

        // 状态分布图表
        all_charts.push_str(&self.generate_status_chart(report)?);
        all_charts.push('\n');

        // 执行时间图表
        all_charts.push_str(&self.generate_duration_chart(report)?);
        all_charts.push('\n');

        // 覆盖率图表（如果有数据）
        if report.coverage.is_some() {
            all_charts.push_str(&self.generate_coverage_chart(report)?);
            all_charts.push('\n');
        }

        // 趋势图表（如果有数据）
        if let Some(trend) = trend_data {
            all_charts.push_str(&self.generate_trend_chart(trend)?);
            all_charts.push('\n');
        }

        Ok(all_charts)
    }
}
