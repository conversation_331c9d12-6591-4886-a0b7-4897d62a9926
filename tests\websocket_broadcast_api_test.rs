use reqwest;
use serde_json::json;

#[tokio::test]
async fn test_websocket_broadcast_via_api() {
    println!("🔧 开始WebSocket广播API调试测试");

    // 首先登录获取JWT token
    let client = reqwest::Client::new();
    let login_response = client
        .post("http://127.0.0.1:3000/api/auth/login")
        .json(&json!({
            "username": "testuser456",
            "password": "password123"
        }))
        .send()
        .await
        .expect("登录请求失败");

    assert!(login_response.status().is_success(), "登录应该成功");

    let login_data: serde_json::Value = login_response.json().await.expect("解析登录响应失败");
    let token = login_data["data"]["token"].as_str().expect("获取token失败");

    println!("✅ 登录成功，获取到token: {}", &token[..20]);

    // 测试聊天消息发送API
    let chat_message = json!({
        "message_type": "Text",
        "content": "🔧 调试测试消息 - 来自API测试",
        "sender": {
            "username": "testuser456",
            "user_id": "a53f5347-1de0-4bd1-8e07-5728af30aae5"
        },
        "timestamp": "2025-07-13T17:15:00.000Z"
    });

    let chat_response = client
        .post("http://127.0.0.1:3000/api/chat/send")
        .header("Authorization", format!("Bearer {}", token))
        .json(&chat_message)
        .send()
        .await
        .expect("发送聊天消息失败");

    println!("📤 聊天消息发送状态: {}", chat_response.status());

    if chat_response.status().is_success() {
        let response_data: serde_json::Value = chat_response.json().await.expect("解析响应失败");
        println!("📨 聊天消息发送响应: {}", response_data);
        println!("🎉 聊天消息发送成功！");
    } else {
        let error_text = chat_response
            .text()
            .await
            .unwrap_or_else(|_| "无法读取错误信息".to_string());
        println!("❌ 聊天消息发送失败: {}", error_text);
    }

    println!("🔧 WebSocket广播API调试测试完成");
}
