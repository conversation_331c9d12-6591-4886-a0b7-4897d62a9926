// 项目依赖配置优化验证测试

use anyhow::Result;
use std::process::Command;

/// 测试Cargo.toml依赖版本一致性
#[tokio::test]
async fn test_dependency_version_consistency() -> Result<()> {
    println!("测试依赖版本一致性...");

    // 检查workspace依赖是否正确定义
    let cargo_toml = std::fs::read_to_string("Cargo.toml")?;

    // 验证关键依赖版本
    assert!(
        cargo_toml.contains("axum = { version = \"0.8.4\""),
        "Axum版本应该是0.8.4"
    );
    assert!(
        cargo_toml.contains("tokio = { version = \"1.45.1\""),
        "Tokio版本应该是1.45.1"
    );
    assert!(
        cargo_toml.contains("sea-orm = { version = \"1.1.12\""),
        "SeaORM版本应该是1.1.12"
    );
    assert!(
        cargo_toml.contains("serde = { version = \"1.0\""),
        "Serde版本应该是1.0"
    );

    println!("✅ 依赖版本一致性验证通过");
    Ok(())
}

/// 测试构建成功性
#[tokio::test]
async fn test_build_success() -> Result<()> {
    println!("测试项目构建成功性...");

    let output = Command::new("cargo")
        .args(&["check", "--workspace"])
        .output()?;

    assert!(
        output.status.success(),
        "Cargo check应该成功: {}",
        String::from_utf8_lossy(&output.stderr)
    );

    println!("✅ 项目构建成功性验证通过");
    Ok(())
}

/// 测试测试套件运行
#[tokio::test]
async fn test_test_suite_execution() -> Result<()> {
    println!("测试测试套件执行...");

    // 运行server包的测试
    let output = Command::new("cargo")
        .args(&["test", "-p", "server", "--lib"])
        .output()?;

    assert!(
        output.status.success(),
        "Server测试应该通过: {}",
        String::from_utf8_lossy(&output.stderr)
    );

    println!("✅ 测试套件执行验证通过");
    Ok(())
}

/// 测试依赖树分析
#[tokio::test]
async fn test_dependency_tree_analysis() -> Result<()> {
    println!("测试依赖树分析...");

    let output = Command::new("cargo")
        .args(&["tree", "--workspace"])
        .output()?;

    assert!(
        output.status.success(),
        "Cargo tree应该成功: {}",
        String::from_utf8_lossy(&output.stderr)
    );

    let tree_output = String::from_utf8_lossy(&output.stdout);

    // 验证关键依赖存在
    assert!(tree_output.contains("axum"), "依赖树应该包含axum");
    assert!(tree_output.contains("tokio"), "依赖树应该包含tokio");
    assert!(tree_output.contains("sea-orm"), "依赖树应该包含sea-orm");

    println!("✅ 依赖树分析验证通过");
    Ok(())
}

/// 测试重复依赖检查
#[tokio::test]
async fn test_duplicate_dependencies() -> Result<()> {
    println!("测试重复依赖检查...");

    let output = Command::new("cargo")
        .args(&["tree", "--workspace", "--duplicates"])
        .output()?;

    assert!(output.status.success(), "Cargo tree duplicates应该成功");

    let duplicates_output = String::from_utf8_lossy(&output.stdout);

    // 如果有重复依赖，输出会包含具体信息
    if !duplicates_output.trim().is_empty() {
        println!("发现重复依赖:");
        println!("{}", duplicates_output);
        // 注意：这里不直接失败，因为某些重复依赖可能是合理的
    } else {
        println!("没有发现重复依赖");
    }

    println!("✅ 重复依赖检查完成");
    Ok(())
}

/// 测试安全漏洞检查
#[tokio::test]
async fn test_security_audit() -> Result<()> {
    println!("测试安全漏洞检查...");

    // 首先尝试安装cargo-audit（如果没有安装）
    let audit_check = Command::new("cargo").args(&["audit", "--version"]).output();

    if audit_check.is_err() || !audit_check.unwrap().status.success() {
        println!("cargo-audit未安装，跳过安全审计");
        return Ok(());
    }

    let output = Command::new("cargo").args(&["audit"]).output()?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        let stdout = String::from_utf8_lossy(&output.stdout);

        println!("安全审计发现问题:");
        println!("STDOUT: {}", stdout);
        println!("STDERR: {}", stderr);

        // 如果有安全漏洞，我们应该知道但不一定要失败测试
        // 因为这可能需要手动处理
    } else {
        println!("✅ 没有发现安全漏洞");
    }

    Ok(())
}

/// 测试编译时间优化
#[tokio::test]
async fn test_compilation_performance() -> Result<()> {
    println!("测试编译性能...");

    let start = std::time::Instant::now();

    let output = Command::new("cargo")
        .args(&["check", "--workspace"])
        .output()?;

    let duration = start.elapsed();

    assert!(output.status.success(), "编译应该成功");

    println!("编译耗时: {:?}", duration);

    // 如果编译时间超过2分钟，给出警告
    if duration.as_secs() > 120 {
        println!("⚠️  编译时间较长，可能需要优化依赖");
    } else {
        println!("✅ 编译性能良好");
    }

    Ok(())
}

/// 测试workspace配置
#[tokio::test]
async fn test_workspace_configuration() -> Result<()> {
    println!("测试workspace配置...");

    let cargo_toml = std::fs::read_to_string("Cargo.toml")?;

    // 验证workspace成员
    assert!(cargo_toml.contains("members = ["), "应该定义workspace成员");
    assert!(cargo_toml.contains("\"server\""), "应该包含server成员");
    assert!(
        cargo_toml.contains("\"migration\""),
        "应该包含migration成员"
    );
    assert!(
        cargo_toml.contains("\"crates/app_common\""),
        "应该包含app_common成员"
    );
    assert!(
        cargo_toml.contains("\"crates/app_domain\""),
        "应该包含app_domain成员"
    );

    // 验证workspace依赖
    assert!(
        cargo_toml.contains("[workspace.dependencies]"),
        "应该定义workspace依赖"
    );

    println!("✅ Workspace配置验证通过");
    Ok(())
}

/// 测试特性标志配置
#[tokio::test]
async fn test_feature_flags() -> Result<()> {
    println!("测试特性标志配置...");

    let cargo_toml = std::fs::read_to_string("Cargo.toml")?;

    // 验证特性配置
    assert!(cargo_toml.contains("[features]"), "应该定义特性标志");
    assert!(cargo_toml.contains("testing"), "应该有testing特性");

    // 测试特性编译
    let output = Command::new("cargo")
        .args(&["check", "--features", "testing"])
        .output()?;

    assert!(
        output.status.success(),
        "testing特性编译应该成功: {}",
        String::from_utf8_lossy(&output.stderr)
    );

    println!("✅ 特性标志配置验证通过");
    Ok(())
}

/// 集成测试：完整的依赖配置验证
#[tokio::test]
async fn test_complete_dependency_optimization() -> Result<()> {
    println!("执行完整的依赖配置验证...");

    // 1. 版本一致性
    let cargo_toml = std::fs::read_to_string("Cargo.toml")?;
    assert!(
        cargo_toml.contains("edition = \"2024\""),
        "应该使用Rust 2024 edition"
    );
    println!("✅ 版本一致性检查通过");

    // 2. 构建成功
    let build_output = Command::new("cargo")
        .args(&["check", "--workspace"])
        .output()?;
    assert!(build_output.status.success(), "构建应该成功");
    println!("✅ 构建成功检查通过");

    // 3. 测试通过
    let test_output = Command::new("cargo")
        .args(&["test", "-p", "server", "--lib"])
        .output()?;
    assert!(test_output.status.success(), "测试应该通过");
    println!("✅ 测试通过检查完成");

    // 4. 依赖树健康
    let tree_output = Command::new("cargo")
        .args(&["tree", "--workspace"])
        .output()?;
    assert!(tree_output.status.success(), "依赖树分析应该成功");
    println!("✅ 依赖树健康检查通过");

    println!("🎉 完整的依赖配置验证通过！");
    println!("   所有依赖都已正确配置和优化");

    Ok(())
}
