//! # WebSocket消息收发功能测试
//!
//! 专门测试WebSocket消息的发送、接收、广播和各种消息类型处理
//! 遵循TDD原则，确保与Axum 0.8.4兼容

use anyhow::Result;
use futures_util::{SinkExt, StreamExt};
use reqwest::Client;
use serde_json::{Value, json};
use std::time::Duration;
use tokio::time::{sleep, timeout};
use tokio_tungstenite::{connect_async, tungstenite::Message as TungsteniteMessage};

/// 测试配置
const SERVER_URL: &str = "127.0.0.1:3000";
const WS_URL: &str = "ws://127.0.0.1:3000/ws";
const TEST_USER_USERNAME: &str = "testuser456";
const TEST_USER_PASSWORD: &str = "password123";

/// WebSocket消息测试辅助结构
struct WebSocketMessageTestHelper {
    client: Client,
    jwt_token: Option<String>,
}

impl WebSocketMessageTestHelper {
    /// 创建新的测试辅助实例
    fn new() -> Self {
        Self {
            client: Client::new(),
            jwt_token: None,
        }
    }

    /// 用户登录并获取JWT token
    async fn login(&mut self) -> Result<String> {
        let login_url = format!("http://{}/api/auth/login", SERVER_URL);
        let login_data = json!({
            "username": TEST_USER_USERNAME,
            "password": TEST_USER_PASSWORD
        });

        let response = self
            .client
            .post(&login_url)
            .json(&login_data)
            .send()
            .await?;

        if !response.status().is_success() {
            anyhow::bail!("登录失败: {}", response.status());
        }

        let response_json: Value = response.json().await?;
        let token = response_json["data"]["access_token"]
            .as_str()
            .ok_or_else(|| anyhow::anyhow!("响应中缺少access_token字段"))?
            .to_string();

        self.jwt_token = Some(token.clone());
        Ok(token)
    }

    /// 建立WebSocket连接
    async fn connect_websocket(
        &self,
    ) -> Result<
        tokio_tungstenite::WebSocketStream<
            tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>,
        >,
    > {
        let token = self
            .jwt_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("需要先登录获取JWT token"))?;

        let ws_url_with_token = format!("{}?token={}", WS_URL, token);
        let (ws_stream, _) = connect_async(&ws_url_with_token).await?;
        Ok(ws_stream)
    }

    /// 发送文本消息
    async fn send_text_message(
        &self,
        ws_stream: &mut tokio_tungstenite::WebSocketStream<
            tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>,
        >,
        message: &str,
    ) -> Result<()> {
        ws_stream
            .send(TungsteniteMessage::Text(message.to_string().into()))
            .await?;
        Ok(())
    }

    /// 发送二进制消息
    async fn send_binary_message(
        &self,
        ws_stream: &mut tokio_tungstenite::WebSocketStream<
            tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>,
        >,
        data: Vec<u8>,
    ) -> Result<()> {
        ws_stream
            .send(TungsteniteMessage::Binary(data.into()))
            .await?;
        Ok(())
    }

    /// 发送Ping消息
    async fn send_ping(
        &self,
        ws_stream: &mut tokio_tungstenite::WebSocketStream<
            tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>,
        >,
        data: Vec<u8>,
    ) -> Result<()> {
        ws_stream
            .send(TungsteniteMessage::Ping(data.into()))
            .await?;
        Ok(())
    }

    /// 接收消息（带超时）
    async fn receive_message_with_timeout(
        &self,
        ws_stream: &mut tokio_tungstenite::WebSocketStream<
            tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>,
        >,
        timeout_duration: Duration,
    ) -> Result<Option<TungsteniteMessage>> {
        match timeout(timeout_duration, ws_stream.next()).await {
            Ok(Some(Ok(message))) => Ok(Some(message)),
            Ok(Some(Err(e))) => Err(e.into()),
            Ok(None) => Ok(None),
            Err(_) => Ok(None), // 超时
        }
    }

    /// 等待特定类型的消息
    async fn wait_for_message_type(
        &self,
        ws_stream: &mut tokio_tungstenite::WebSocketStream<
            tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>,
        >,
        expected_type: MessageType,
        timeout_duration: Duration,
    ) -> Result<Option<TungsteniteMessage>> {
        let start_time = std::time::Instant::now();

        while start_time.elapsed() < timeout_duration {
            if let Some(message) = self
                .receive_message_with_timeout(ws_stream, Duration::from_millis(100))
                .await?
            {
                match (&message, &expected_type) {
                    (TungsteniteMessage::Text(_), MessageType::Text) => {
                        return Ok(Some(message));
                    }
                    (TungsteniteMessage::Binary(_), MessageType::Binary) => {
                        return Ok(Some(message));
                    }
                    (TungsteniteMessage::Ping(_), MessageType::Ping) => {
                        return Ok(Some(message));
                    }
                    (TungsteniteMessage::Pong(_), MessageType::Pong) => {
                        return Ok(Some(message));
                    }
                    (TungsteniteMessage::Close(_), MessageType::Close) => {
                        return Ok(Some(message));
                    }
                    _ => {
                        continue;
                    } // 继续等待期望的消息类型
                }
            }
        }

        Ok(None)
    }
}

/// 消息类型枚举
#[derive(Debug, Clone)]
enum MessageType {
    Text,
    Binary,
    Ping,
    Pong,
    Close,
}

/// 测试1: 基本文本消息收发测试
async fn test_basic_text_message_exchange() -> Result<()> {
    println!("🔧 开始测试: 基本文本消息收发");

    let mut helper = WebSocketMessageTestHelper::new();
    helper.login().await?;
    let mut ws_stream = helper.connect_websocket().await?;

    println!("✅ WebSocket连接建立成功");

    // 发送文本消息
    let test_message = "Hello, WebSocket! 这是一条测试消息。";
    helper
        .send_text_message(&mut ws_stream, test_message)
        .await?;
    println!("✅ 文本消息发送成功: {}", test_message);

    // 等待消息处理
    sleep(Duration::from_millis(100)).await;

    println!("🎉 基本文本消息收发测试通过");
    Ok(())
}

/// 测试2: 二进制消息收发测试
async fn test_binary_message_exchange() -> Result<()> {
    println!("🔧 开始测试: 二进制消息收发");

    let mut helper = WebSocketMessageTestHelper::new();
    helper.login().await?;
    let mut ws_stream = helper.connect_websocket().await?;

    println!("✅ WebSocket连接建立成功");

    // 发送二进制消息
    let binary_data = vec![0x48, 0x65, 0x6c, 0x6c, 0x6f]; // "Hello" in bytes
    helper
        .send_binary_message(&mut ws_stream, binary_data.clone())
        .await?;
    println!("✅ 二进制消息发送成功: {:?}", binary_data);

    // 等待消息处理
    sleep(Duration::from_millis(100)).await;

    println!("🎉 二进制消息收发测试通过");
    Ok(())
}

/// 测试3: Ping/Pong消息测试
async fn test_ping_pong_messages() -> Result<()> {
    println!("🔧 开始测试: Ping/Pong消息");

    let mut helper = WebSocketMessageTestHelper::new();
    helper.login().await?;
    let mut ws_stream = helper.connect_websocket().await?;

    println!("✅ WebSocket连接建立成功");

    // 发送Ping消息
    let ping_data = vec![1, 2, 3, 4, 5];
    helper.send_ping(&mut ws_stream, ping_data.clone()).await?;
    println!("✅ Ping消息发送成功: {:?}", ping_data);

    // 等待Pong响应
    if let Some(message) = helper
        .wait_for_message_type(&mut ws_stream, MessageType::Pong, Duration::from_secs(5))
        .await?
    {
        match message {
            TungsteniteMessage::Pong(pong_data) => {
                assert_eq!(pong_data, ping_data, "Pong数据应该与Ping数据匹配");
                println!("✅ 收到正确的Pong响应: {:?}", pong_data);
            }
            _ => anyhow::bail!("期望收到Pong消息，但收到其他类型消息"),
        }
    } else {
        anyhow::bail!("未在超时时间内收到Pong响应");
    }

    println!("🎉 Ping/Pong消息测试通过");
    Ok(())
}

/// 测试4: 大消息处理测试
async fn test_large_message_handling() -> Result<()> {
    println!("🔧 开始测试: 大消息处理");

    let mut helper = WebSocketMessageTestHelper::new();
    helper.login().await?;
    let mut ws_stream = helper.connect_websocket().await?;

    println!("✅ WebSocket连接建立成功");

    // 创建大文本消息 (10KB)
    let large_message = "A".repeat(10 * 1024);
    helper
        .send_text_message(&mut ws_stream, &large_message)
        .await?;
    println!("✅ 大文本消息发送成功: {}字节", large_message.len());

    // 创建大二进制消息 (50KB)
    let large_binary = vec![0xFF; 50 * 1024];
    helper
        .send_binary_message(&mut ws_stream, large_binary.clone())
        .await?;
    println!("✅ 大二进制消息发送成功: {}字节", large_binary.len());

    // 等待消息处理
    sleep(Duration::from_millis(500)).await;

    println!("🎉 大消息处理测试通过");
    Ok(())
}

/// 测试5: 快速连续消息发送测试
async fn test_rapid_message_sending() -> Result<()> {
    println!("🔧 开始测试: 快速连续消息发送");

    let mut helper = WebSocketMessageTestHelper::new();
    helper.login().await?;
    let mut ws_stream = helper.connect_websocket().await?;

    println!("✅ WebSocket连接建立成功");

    // 快速发送多条消息
    let message_count = 100;
    for i in 0..message_count {
        let message = format!("快速消息 #{}", i);
        helper.send_text_message(&mut ws_stream, &message).await?;

        if (i + 1) % 20 == 0 {
            println!("📤 已发送 {} 条消息", i + 1);
        }
    }

    println!("✅ 快速发送{}条消息完成", message_count);

    // 等待消息处理
    sleep(Duration::from_secs(1)).await;

    println!("🎉 快速连续消息发送测试通过");
    Ok(())
}

/// 运行所有WebSocket消息收发测试
async fn run_all_message_tests() -> Result<()> {
    println!("🚀 开始运行WebSocket消息收发测试套件");

    // 等待服务器启动
    sleep(Duration::from_secs(2)).await;

    println!("\n=== 测试1: 基本文本消息 ===");
    test_basic_text_message_exchange().await?;

    println!("\n=== 测试2: 二进制消息 ===");
    test_binary_message_exchange().await?;

    println!("\n=== 测试3: Ping/Pong消息 ===");
    test_ping_pong_messages().await?;

    println!("\n=== 测试4: 大消息处理 ===");
    test_large_message_handling().await?;

    println!("\n=== 测试5: 快速连续发送 ===");
    test_rapid_message_sending().await?;

    println!("\n🎉 所有WebSocket消息收发测试通过！");
    Ok(())
}

/// 主函数 - 用于二进制执行
#[tokio::main]
async fn main() -> Result<()> {
    run_all_message_tests().await
}
