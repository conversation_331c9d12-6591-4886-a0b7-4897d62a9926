//! # HTTP状态码验证单元测试
//!
//! 本模块包含HTTP状态码验证逻辑的单元测试，验证：
//! - 状态码验证函数的正确性
//! - 响应格式验证的准确性
//! - 错误处理的完整性
//! - 遵循Context7 MCP最佳实践和TDD开发模式

use anyhow::Result;
use reqwest::StatusCode;
use serde_json::{Value, json};

/// HTTP状态码验证器
pub struct HttpStatusCodeValidator;

impl HttpStatusCodeValidator {
    /// 验证HTTP状态码
    pub fn validate_status_code(response: &Value, expected_status: StatusCode) -> Result<()> {
        let actual_status = response["status"]
            .as_u64()
            .ok_or_else(|| anyhow::anyhow!("响应中缺少status字段"))?;

        if actual_status != (expected_status.as_u16() as u64) {
            return Err(anyhow::anyhow!(
                "状态码不匹配: 期望 {}, 实际 {}",
                expected_status.as_u16(),
                actual_status
            ));
        }

        Ok(())
    }

    /// 验证成功响应格式
    pub fn validate_success_response_format(response: &Value) -> Result<()> {
        // 验证顶级结构
        if !response.is_object() {
            return Err(anyhow::anyhow!("成功响应应为JSON对象"));
        }

        // 验证必需字段
        if response["data"].is_null() {
            return Err(anyhow::anyhow!("成功响应缺少data字段"));
        }

        if !response["message"].is_string() {
            return Err(anyhow::anyhow!("成功响应message字段应为字符串"));
        }

        Ok(())
    }

    /// 验证错误响应格式
    pub fn validate_error_response_format(response: &Value) -> Result<()> {
        // 验证顶级结构
        if !response.is_object() {
            return Err(anyhow::anyhow!("错误响应应为JSON对象"));
        }

        // 验证必需字段
        if response["error"].is_null() {
            return Err(anyhow::anyhow!("错误响应缺少error字段"));
        }

        if !response["message"].is_string() {
            return Err(anyhow::anyhow!("错误响应message字段应为字符串"));
        }

        Ok(())
    }

    /// 验证状态码与响应格式的一致性
    pub fn validate_status_response_consistency(response: &Value) -> Result<()> {
        let status_code = response["status"]
            .as_u64()
            .ok_or_else(|| anyhow::anyhow!("响应中缺少status字段"))?;

        let response_body = &response["body"];

        match status_code {
            200..=299 => {
                // 2xx状态码应该有成功响应格式
                Self::validate_success_response_format(response_body)?;
            }
            400..=599 => {
                // 4xx和5xx状态码应该有错误响应格式
                Self::validate_error_response_format(response_body)?;
            }
            _ => {
                return Err(anyhow::anyhow!("不支持的状态码: {}", status_code));
            }
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_validate_status_code_success() {
        let response = json!({
            "status": 200,
            "body": {}
        });

        let result = HttpStatusCodeValidator::validate_status_code(&response, StatusCode::OK);
        assert!(result.is_ok());
    }

    #[test]
    fn test_validate_status_code_mismatch() {
        let response = json!({
            "status": 404,
            "body": {}
        });

        let result = HttpStatusCodeValidator::validate_status_code(&response, StatusCode::OK);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("状态码不匹配"));
    }

    #[test]
    fn test_validate_status_code_missing_field() {
        let response = json!({
            "body": {}
        });

        let result = HttpStatusCodeValidator::validate_status_code(&response, StatusCode::OK);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("缺少status字段"));
    }

    #[test]
    fn test_validate_success_response_format_valid() {
        let response = json!({
            "data": {"id": "123", "title": "测试任务"},
            "message": "操作成功"
        });

        let result = HttpStatusCodeValidator::validate_success_response_format(&response);
        assert!(result.is_ok());
    }

    #[test]
    fn test_validate_success_response_format_missing_data() {
        let response = json!({
            "message": "操作成功"
        });

        let result = HttpStatusCodeValidator::validate_success_response_format(&response);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("缺少data字段"));
    }

    #[test]
    fn test_validate_success_response_format_invalid_message() {
        let response = json!({
            "data": {"id": "123"},
            "message": 123
        });

        let result = HttpStatusCodeValidator::validate_success_response_format(&response);
        assert!(result.is_err());
        assert!(
            result
                .unwrap_err()
                .to_string()
                .contains("message字段应为字符串")
        );
    }

    #[test]
    fn test_validate_error_response_format_valid() {
        let response = json!({
            "error": "VALIDATION_ERROR",
            "message": "请求验证失败"
        });

        let result = HttpStatusCodeValidator::validate_error_response_format(&response);
        assert!(result.is_ok());
    }

    #[test]
    fn test_validate_error_response_format_missing_error() {
        let response = json!({
            "message": "请求验证失败"
        });

        let result = HttpStatusCodeValidator::validate_error_response_format(&response);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("缺少error字段"));
    }

    #[test]
    fn test_validate_status_response_consistency_success() {
        let response = json!({
            "status": 200,
            "body": {
                "data": {"id": "123"},
                "message": "操作成功"
            }
        });

        let result = HttpStatusCodeValidator::validate_status_response_consistency(&response);
        assert!(result.is_ok());
    }

    #[test]
    fn test_validate_status_response_consistency_error() {
        let response = json!({
            "status": 400,
            "body": {
                "error": "BAD_REQUEST",
                "message": "请求无效"
            }
        });

        let result = HttpStatusCodeValidator::validate_status_response_consistency(&response);
        assert!(result.is_ok());
    }

    #[test]
    fn test_validate_status_response_consistency_mismatch() {
        let response = json!({
            "status": 200,
            "body": {
                "error": "SOME_ERROR",
                "message": "错误消息"
            }
        });

        let result = HttpStatusCodeValidator::validate_status_response_consistency(&response);
        assert!(result.is_err());
    }

    #[test]
    fn test_http_status_codes_coverage() {
        // 测试各种HTTP状态码
        let test_cases = vec![
            (200, StatusCode::OK),
            (201, StatusCode::CREATED),
            (204, StatusCode::NO_CONTENT),
            (400, StatusCode::BAD_REQUEST),
            (401, StatusCode::UNAUTHORIZED),
            (403, StatusCode::FORBIDDEN),
            (404, StatusCode::NOT_FOUND),
            (409, StatusCode::CONFLICT),
            (500, StatusCode::INTERNAL_SERVER_ERROR),
        ];

        for (status_num, status_code) in test_cases {
            let response = json!({
                "status": status_num,
                "body": {}
            });

            let result = HttpStatusCodeValidator::validate_status_code(&response, status_code);
            assert!(result.is_ok(), "状态码 {} 验证失败", status_num);
        }
    }

    #[test]
    fn test_response_format_edge_cases() {
        // 测试空对象
        let empty_response = json!({});
        let result = HttpStatusCodeValidator::validate_success_response_format(&empty_response);
        assert!(result.is_err());

        // 测试null值
        let null_response = json!(null);
        let result = HttpStatusCodeValidator::validate_success_response_format(&null_response);
        assert!(result.is_err());

        // 测试数组类型
        let array_response = json!([]);
        let result = HttpStatusCodeValidator::validate_success_response_format(&array_response);
        assert!(result.is_err());
    }
}
