//! # 简单缓存测试
//!
//! 用于验证基本缓存功能的简单测试

#[cfg(test)]
mod tests {
    use super::super::{client_manager::CacheClientManager, config::CacheConfig};
    use fred::interfaces::KeysInterface;

    use tokio;

    #[tokio::test]
    async fn test_simple_cache_connection() {
        // 【测试目标】: 验证基本的缓存连接功能
        println!("🔍 开始测试简单缓存连接...");

        let cache_config = CacheConfig::for_tests();
        println!("📋 使用缓存配置: {}", cache_config.cache_url);

        // 尝试创建缓存客户端管理器
        let manager_result = CacheClientManager::new(cache_config).await;

        match manager_result {
            Ok(manager) => {
                println!("✅ 缓存客户端管理器创建成功");

                // 执行健康检查
                let health_check = manager.health_check().await;
                assert!(health_check, "健康检查应该通过");
                println!("✅ 健康检查通过");

                // 测试基本的set/get操作
                let client = manager.get_client();

                // 设置测试键值
                let set_result: Result<(), fred::error::Error> = client
                    .set("test_key", "test_value", None, None, false)
                    .await;
                assert!(set_result.is_ok(), "设置键值应该成功");
                println!("✅ 设置键值成功");

                // 获取测试键值
                let get_result: Result<String, fred::error::Error> = client.get("test_key").await;
                assert!(get_result.is_ok(), "获取键值应该成功");
                assert_eq!(get_result.unwrap(), "test_value", "获取的值应该匹配");
                println!("✅ 获取键值成功");

                // 清理测试键
                let del_result: Result<i64, fred::error::Error> = client.del("test_key").await;
                assert!(del_result.is_ok(), "删除键应该成功");
                println!("✅ 删除键成功");

                println!("🎉 简单缓存连接测试完成");
            }
            Err(e) => {
                println!("❌ 缓存客户端管理器创建失败: {e}");
                panic!("缓存连接测试失败: {e}");
            }
        }
    }

    #[tokio::test]
    async fn test_cache_config_detection() {
        // 【测试目标】: 验证缓存配置检测功能
        println!("🔍 开始测试缓存配置检测...");

        let config = CacheConfig::for_tests();
        println!("📋 检测到的缓存URL: {}", config.cache_url);

        // 验证URL格式
        assert!(
            config.cache_url.starts_with("redis://"),
            "URL应该以redis://开头"
        );
        assert!(
            config.cache_url.contains("localhost")
                || config.cache_url.contains("127.0.0.1")
                || config.cache_url.contains("172."),
            "URL应该包含localhost、127.0.0.1或WSL2 IP地址"
        );
        assert!(config.cache_url.contains("6379"), "URL应该包含端口6379");
        assert!(
            config.cache_url.contains("dragonfly_secure_password_2025"),
            "URL应该包含密码"
        );

        println!("✅ 缓存配置检测测试完成");
    }
}
