//! # JWT认证中间件
//!
//! 提供基于JWT的认证中间件和用户提取器，适配新的模块化DDD架构。
//!
//! ## 核心功能
//! - **AuthenticatedUser提取器**: 从请求中提取认证用户信息
//! - **JWT验证**: 验证JWT token的有效性
//! - **错误处理**: 统一的认证错误处理
//!
//! ## 设计原则
//! - **单一职责**: 专注于JWT认证逻辑
//! - **类型安全**: 使用Uuid类型确保类型安全
//! - **可测试性**: 便于单元测试和集成测试

use crate::utils::jwt_utils::ExtendedClaims;
use crate::utils::{AuthService, Claims, JwtError, error_response::ErrorResponseBuilder};
use app_interfaces::auth::UserRole;
use axum::{
    extract::{FromRequestParts, Request},
    http::{StatusCode, request::Parts},
    middleware::Next,
    response::Response,
};
use sea_orm::prelude::Uuid;
use serde::{Deserialize, Serialize};
use std::str::FromStr;

/// 认证用户信息结构体
///
/// 用于在请求处理过程中传递认证后的用户信息
/// 支持基础认证和扩展RBAC功能
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct AuthenticatedUser {
    /// 用户ID (使用Uuid类型确保类型安全)
    pub user_id: Uuid,
    /// 用户名
    pub username: String,
    /// 用户角色 (可选，用于RBAC功能)
    pub role: Option<UserRole>,
    /// 权限级别 (可选，用于快速权限检查)
    pub permission_level: Option<u8>,
}

impl AuthenticatedUser {
    /// 创建新的认证用户实例（基础版本，向后兼容）
    pub fn new(user_id: Uuid, username: String) -> Self {
        Self {
            user_id,
            username,
            role: None,
            permission_level: None,
        }
    }

    /// 创建带角色信息的认证用户实例（扩展版本）
    pub fn new_with_role(user_id: Uuid, username: String, role: UserRole) -> Self {
        let permission_level = role.permission_level();
        Self {
            user_id,
            username,
            role: Some(role),
            permission_level: Some(permission_level),
        }
    }

    /// 从基础JWT Claims创建认证用户（向后兼容）
    pub fn from_claims(claims: Claims) -> Result<Self, JwtAuthError> {
        let user_id = Uuid::from_str(&claims.sub)
            .map_err(|_| JwtAuthError::InvalidUserId(claims.sub.clone()))?;

        Ok(Self {
            user_id,
            username: claims.username,
            role: None,
            permission_level: None,
        })
    }

    /// 从扩展JWT Claims创建认证用户（支持RBAC）
    pub fn from_extended_claims(claims: ExtendedClaims) -> Result<Self, JwtAuthError> {
        let user_id = Uuid::from_str(&claims.sub)
            .map_err(|_| JwtAuthError::InvalidUserId(claims.sub.clone()))?;

        // 解析角色信息
        let role = UserRole::parse_role(&claims.role)
            .map_err(|e| JwtAuthError::AuthenticationFailed(format!("角色解析失败: {e}")))?;

        let permission_level = role.permission_level();

        Ok(Self {
            user_id,
            username: claims.username,
            role: Some(role),
            permission_level: Some(permission_level),
        })
    }

    /// 获取用户角色，如果没有则返回默认User角色
    pub fn get_role(&self) -> UserRole {
        self.role.clone().unwrap_or(UserRole::User)
    }

    /// 获取权限级别，如果没有则返回User角色的权限级别
    pub fn get_permission_level(&self) -> u8 {
        self.permission_level
            .unwrap_or(UserRole::User.permission_level())
    }

    /// 检查是否有指定权限
    pub fn has_permission(&self, required_level: u8) -> bool {
        self.get_permission_level() >= required_level
    }

    /// 检查是否是管理员
    pub fn is_admin(&self) -> bool {
        self.get_role().is_admin()
    }

    /// 检查是否可以访问指定资源
    pub fn can_access_resource(&self, resource_owner_id: &str) -> bool {
        // 管理员可以访问所有资源
        if self.is_admin() {
            return true;
        }

        // 用户只能访问自己的资源
        self.user_id.to_string() == resource_owner_id
    }
}

/// JWT认证错误类型
#[derive(Debug, thiserror::Error, PartialEq)]
pub enum JwtAuthError {
    #[error("JWT token 缺失")]
    TokenMissing,
    #[error("JWT token 无效")]
    TokenInvalid,
    #[error("JWT token 已过期")]
    TokenExpired,
    #[error("用户ID格式无效: {0}")]
    InvalidUserId(String),
    #[error("认证失败: {0}")]
    AuthenticationFailed(String),
}

impl From<JwtError> for JwtAuthError {
    fn from(jwt_error: JwtError) -> Self {
        match jwt_error {
            JwtError::TokenMissing => JwtAuthError::TokenMissing,
            JwtError::TokenInvalid => JwtAuthError::TokenInvalid,
            JwtError::TokenExpired => JwtAuthError::TokenExpired,
            JwtError::TokenCreationFailed(msg) => JwtAuthError::AuthenticationFailed(msg),
            JwtError::RoleParsingFailed(msg) => JwtAuthError::AuthenticationFailed(msg),
        }
    }
}

impl From<JwtAuthError> for StatusCode {
    fn from(error: JwtAuthError) -> Self {
        match error {
            JwtAuthError::TokenMissing
            | JwtAuthError::TokenInvalid
            | JwtAuthError::TokenExpired
            | JwtAuthError::InvalidUserId(_)
            | JwtAuthError::AuthenticationFailed(_) => StatusCode::UNAUTHORIZED,
        }
    }
}

/// JWT认证状态
///
/// 用于在应用状态中存储JWT密钥
#[derive(Debug, Clone)]
pub struct JwtAuthState {
    pub jwt_secret: String,
}

impl JwtAuthState {
    /// 创建新的JWT认证状态
    pub fn new(jwt_secret: String) -> Self {
        Self { jwt_secret }
    }
}

/// JWT认证中间件函数
///
/// 用于在请求处理过程中注入JWT密钥，使AuthenticatedUser提取器能够正常工作
pub async fn jwt_auth_middleware(mut req: Request, next: Next) -> Result<Response, StatusCode> {
    // 从应用状态中获取JWT密钥
    let jwt_secret = req
        .extensions()
        .get::<JwtAuthState>()
        .ok_or(StatusCode::INTERNAL_SERVER_ERROR)?
        .jwt_secret
        .clone();

    // 将JWT密钥注入到请求扩展中，供AuthenticatedUser提取器使用
    req.extensions_mut().insert(jwt_secret);

    // 继续处理请求
    Ok(next.run(req).await)
}

/// AuthenticatedUser的FromRequestParts实现
///
/// 这个实现允许在Axum处理器中直接使用AuthenticatedUser作为参数，
/// 自动从请求中提取和验证JWT token
///
/// 支持两种模式：
/// 1. 基础模式：使用标准Claims（向后兼容）
/// 2. 扩展模式：使用ExtendedClaims（支持RBAC）
impl<S> FromRequestParts<S> for AuthenticatedUser
where
    S: Send + Sync,
{
    type Rejection = Response;

    async fn from_request_parts(parts: &mut Parts, _state: &S) -> Result<Self, Self::Rejection> {
        // 从请求扩展中获取JWT密钥
        // 注意：这需要在中间件中预先设置
        let jwt_secret = parts
            .extensions
            .get::<String>()
            .ok_or_else(|| {
                tracing::error!("JWT密钥未在请求扩展中找到");
                ErrorResponseBuilder::internal_server_error()
                    .with_details("JWT配置错误")
                    .build()
            })?
            .clone();

        // 创建认证服务
        let auth_service = AuthService::new(jwt_secret);

        // 尝试使用扩展Claims进行认证（优先）
        match auth_service.authenticate_http_request_with_role(&parts.headers) {
            Ok(extended_claims) => {
                // 成功获取扩展Claims，创建带角色信息的AuthenticatedUser
                tracing::debug!("使用扩展Claims创建认证用户，角色: {}", extended_claims.role);
                AuthenticatedUser::from_extended_claims(extended_claims).map_err(|e| {
                    tracing::warn!("从扩展Claims创建认证用户失败: {:?}", e);
                    ErrorResponseBuilder::authentication_error()
                        .with_details("用户信息解析失败")
                        .build()
                })
            }
            Err(jwt_error) => {
                // 扩展Claims认证失败，尝试使用基础Claims（向后兼容）
                tracing::debug!("扩展Claims认证失败: {:?}，尝试基础Claims", jwt_error);

                let claims = auth_service
                    .authenticate_http_request(&parts.headers)
                    .map_err(|e| {
                        tracing::warn!("基础Claims认证也失败: {:?}", e);
                        ErrorResponseBuilder::authentication_error()
                            .with_details("无效的认证凭据")
                            .build()
                    })?;

                // 从基础Claims创建AuthenticatedUser（无角色信息）
                tracing::debug!("使用基础Claims创建认证用户（无角色信息）");
                AuthenticatedUser::from_claims(claims).map_err(|e| {
                    tracing::warn!("从基础Claims创建认证用户失败: {:?}", e);
                    ErrorResponseBuilder::authentication_error()
                        .with_details("用户信息解析失败")
                        .build()
                })
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::utils::JwtUtils;

    #[allow(dead_code)]
    const TEST_SECRET: &str = "test-jwt-auth-middleware-secret";

    #[allow(dead_code)]
    fn create_test_jwt_token(user_id: &str, username: &str) -> String {
        let jwt_utils = JwtUtils::new(TEST_SECRET.to_string());
        jwt_utils.create_token(user_id, username, 1).unwrap()
    }

    #[test]
    fn test_authenticated_user_creation() {
        let user_id = Uuid::new_v4();
        let username = "testuser".to_string();

        let auth_user = AuthenticatedUser::new(user_id, username.clone());

        assert_eq!(auth_user.user_id, user_id);
        assert_eq!(auth_user.username, username);
        assert_eq!(auth_user.role, None);
        assert_eq!(auth_user.permission_level, None);
        assert_eq!(auth_user.get_role(), UserRole::User); // 默认角色
    }

    #[test]
    fn test_authenticated_user_with_role_creation() {
        let user_id = Uuid::new_v4();
        let username = "admin".to_string();
        let role = UserRole::Admin;

        let auth_user = AuthenticatedUser::new_with_role(user_id, username.clone(), role.clone());

        assert_eq!(auth_user.user_id, user_id);
        assert_eq!(auth_user.username, username);
        assert_eq!(auth_user.role, Some(role));
        assert_eq!(auth_user.permission_level, Some(100));
        assert!(auth_user.is_admin());
    }

    #[test]
    fn test_authenticated_user_from_claims() {
        let user_id = Uuid::new_v4();
        let claims = Claims {
            sub: user_id.to_string(),
            username: "testuser".to_string(),
            exp: 9999999999,
            iat: 1000000000,
        };

        let auth_user = AuthenticatedUser::from_claims(claims).unwrap();

        assert_eq!(auth_user.user_id, user_id);
        assert_eq!(auth_user.username, "testuser");
        assert_eq!(auth_user.role, None);
        assert_eq!(auth_user.permission_level, None);
    }

    #[test]
    fn test_authenticated_user_from_extended_claims() {
        let user_id = Uuid::new_v4();
        let extended_claims = ExtendedClaims {
            sub: user_id.to_string(),
            username: "manager".to_string(),
            role: "Manager".to_string(),
            exp: 9999999999,
            iat: 1000000000,
            nbf: None,
            iss: None,
            aud: None,
            jti: None,
        };

        let auth_user = AuthenticatedUser::from_extended_claims(extended_claims).unwrap();

        assert_eq!(auth_user.user_id, user_id);
        assert_eq!(auth_user.username, "manager");
        assert_eq!(auth_user.role, Some(UserRole::Manager));
        assert_eq!(auth_user.permission_level, Some(75));
        assert!(!auth_user.is_admin());
        assert!(auth_user.has_permission(50)); // Manager权限级别75 >= 50
    }

    #[test]
    fn test_permission_checking() {
        let user_id = Uuid::new_v4();

        // 测试普通用户权限
        let user = AuthenticatedUser::new_with_role(user_id, "user".to_string(), UserRole::User);
        assert!(user.has_permission(25)); // Read权限
        assert!(user.has_permission(50)); // Write权限
        assert!(!user.has_permission(75)); // Delete权限
        assert!(!user.has_permission(100)); // Admin权限

        // 测试管理员权限
        let admin = AuthenticatedUser::new_with_role(user_id, "admin".to_string(), UserRole::Admin);
        assert!(admin.has_permission(25));
        assert!(admin.has_permission(50));
        assert!(admin.has_permission(75));
        assert!(admin.has_permission(100));
        assert!(admin.is_admin());
    }

    #[test]
    fn test_resource_access() {
        let user_id = Uuid::new_v4();
        let other_user_id = Uuid::new_v4();

        // 普通用户只能访问自己的资源
        let user = AuthenticatedUser::new_with_role(user_id, "user".to_string(), UserRole::User);
        assert!(user.can_access_resource(&user_id.to_string()));
        assert!(!user.can_access_resource(&other_user_id.to_string()));

        // 管理员可以访问所有资源
        let admin = AuthenticatedUser::new_with_role(user_id, "admin".to_string(), UserRole::Admin);
        assert!(admin.can_access_resource(&user_id.to_string()));
        assert!(admin.can_access_resource(&other_user_id.to_string()));
    }

    #[test]
    fn test_backward_compatibility() {
        // 测试向后兼容性：使用旧的创建方法
        let user_id = Uuid::new_v4();
        let auth_user = AuthenticatedUser::new(user_id, "testuser".to_string());

        // 应该使用默认角色和权限级别
        assert_eq!(auth_user.role, None);
        assert_eq!(auth_user.permission_level, None);
        assert_eq!(auth_user.get_role(), UserRole::User);
        assert_eq!(auth_user.get_permission_level(), 50);

        // 基础权限检查应该正常工作
        assert!(auth_user.has_permission(25)); // Read
        assert!(auth_user.has_permission(50)); // Write
        assert!(!auth_user.has_permission(75)); // Delete
        assert!(!auth_user.is_admin());
    }

    #[test]
    fn test_role_upgrade_scenarios() {
        let user_id = Uuid::new_v4();

        // 测试不同角色的权限级别
        let guest = AuthenticatedUser::new_with_role(user_id, "guest".to_string(), UserRole::Guest);
        assert_eq!(guest.get_permission_level(), 25);
        assert!(guest.has_permission(25));
        assert!(!guest.has_permission(50));

        let user = AuthenticatedUser::new_with_role(user_id, "user".to_string(), UserRole::User);
        assert_eq!(user.get_permission_level(), 50);
        assert!(user.has_permission(50));
        assert!(!user.has_permission(75));

        let manager =
            AuthenticatedUser::new_with_role(user_id, "manager".to_string(), UserRole::Manager);
        assert_eq!(manager.get_permission_level(), 75);
        assert!(manager.has_permission(75));
        assert!(!manager.has_permission(100));

        let admin = AuthenticatedUser::new_with_role(user_id, "admin".to_string(), UserRole::Admin);
        assert_eq!(admin.get_permission_level(), 100);
        assert!(admin.has_permission(100));
        assert!(admin.is_admin());
    }

    #[test]
    fn test_authenticated_user_from_invalid_claims() {
        let claims = Claims {
            sub: "invalid-uuid".to_string(),
            username: "testuser".to_string(),
            exp: 9999999999,
            iat: 1000000000,
        };

        let result = AuthenticatedUser::from_claims(claims);

        assert!(matches!(result, Err(JwtAuthError::InvalidUserId(_))));
    }

    #[test]
    fn test_jwt_auth_error_from_jwt_error() {
        assert_eq!(
            JwtAuthError::from(JwtError::TokenMissing),
            JwtAuthError::TokenMissing
        );
        assert_eq!(
            JwtAuthError::from(JwtError::TokenInvalid),
            JwtAuthError::TokenInvalid
        );
        assert_eq!(
            JwtAuthError::from(JwtError::TokenExpired),
            JwtAuthError::TokenExpired
        );
    }

    #[test]
    fn test_jwt_auth_error_to_status_code() {
        assert_eq!(
            StatusCode::from(JwtAuthError::TokenMissing),
            StatusCode::UNAUTHORIZED
        );
        assert_eq!(
            StatusCode::from(JwtAuthError::TokenInvalid),
            StatusCode::UNAUTHORIZED
        );
        assert_eq!(
            StatusCode::from(JwtAuthError::TokenExpired),
            StatusCode::UNAUTHORIZED
        );
    }

    #[test]
    fn test_jwt_auth_state_creation() {
        let secret = "test-secret".to_string();
        let auth_state = JwtAuthState::new(secret.clone());

        assert_eq!(auth_state.jwt_secret, secret);
    }
}
