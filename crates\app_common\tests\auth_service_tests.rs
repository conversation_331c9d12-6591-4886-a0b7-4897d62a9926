//! 认证服务单元测试
//!
//! 本模块测试统一身份验证服务的所有核心功能，包括：
//! - HTTP请求认证
//! - WebSocket连接认证
//! - Token提取和验证
//! - 错误处理和边缘情况
//! - RBAC扩展功能

use app_common::utils::{AuthService, JwtError, TokenExtractionMethod};
use app_interfaces::auth::UserRole;
use axum::http::{HeaderMap, HeaderValue, Uri, header::AUTHORIZATION};

// ============================================================================
// 测试常量和辅助函数
// ============================================================================

const TEST_SECRET: &str = "test-auth-service-secret-key-for-testing";
const TEST_USER_ID: &str = "550e8400-e29b-41d4-a716-446655440000";
const TEST_USERNAME: &str = "test_user";

/// 创建测试用的认证服务
fn create_test_auth_service() -> AuthService {
    AuthService::new(TEST_SECRET.to_string())
}

/// 创建测试用的HTTP请求头（带Bearer token）
fn create_auth_headers(token: &str) -> HeaderMap {
    let mut headers = HeaderMap::new();
    headers.insert(
        AUTHORIZATION,
        HeaderValue::from_str(&format!("Bearer {token}")).unwrap(),
    );
    headers
}

/// 创建测试用的WebSocket协议头
fn create_websocket_protocol_headers(token: &str) -> HeaderMap {
    let mut headers = HeaderMap::new();
    headers.insert(
        "sec-websocket-protocol",
        HeaderValue::from_str(&format!("access_token.{token}")).unwrap(),
    );
    headers
}

// ============================================================================
// HTTP请求认证测试
// ============================================================================

#[cfg(test)]
mod http_authentication_tests {
    use super::*;

    /// 测试HTTP请求认证成功场景
    #[test]
    fn test_authenticate_http_request_success() {
        let auth_service = create_test_auth_service();

        // 创建有效的JWT token
        let token = auth_service
            .create_token(TEST_USER_ID, TEST_USERNAME, 1)
            .unwrap();
        let headers = create_auth_headers(&token);

        // 执行认证
        let result = auth_service.authenticate_http_request(&headers);

        // 验证结果
        assert!(result.is_ok());
        let claims = result.unwrap();
        assert_eq!(claims.sub, TEST_USER_ID);
        assert_eq!(claims.username, TEST_USERNAME);
    }

    /// 测试HTTP请求认证失败 - 缺少Authorization头
    #[test]
    fn test_authenticate_http_request_missing_header() {
        let auth_service = create_test_auth_service();
        let headers = HeaderMap::new(); // 空的请求头

        let result = auth_service.authenticate_http_request(&headers);

        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), JwtError::TokenMissing));
    }

    /// 测试HTTP请求认证失败 - 无效的token格式
    #[test]
    fn test_authenticate_http_request_invalid_token_format() {
        let auth_service = create_test_auth_service();
        let headers = create_auth_headers("invalid-token-format");

        let result = auth_service.authenticate_http_request(&headers);

        assert!(result.is_err());
        // 应该是token验证失败
        assert!(matches!(result.unwrap_err(), JwtError::TokenInvalid));
    }

    /// 测试HTTP请求认证失败 - 错误的Bearer格式
    #[test]
    fn test_authenticate_http_request_wrong_bearer_format() {
        let auth_service = create_test_auth_service();
        let mut headers = HeaderMap::new();
        headers.insert(
            AUTHORIZATION,
            HeaderValue::from_str("Basic abc123").unwrap(),
        );

        let result = auth_service.authenticate_http_request(&headers);

        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), JwtError::TokenInvalid));
    }
}

// ============================================================================
// WebSocket连接认证测试
// ============================================================================

#[cfg(test)]
mod websocket_authentication_tests {
    use super::*;

    /// 测试WebSocket认证成功 - 查询参数方式
    #[test]
    fn test_authenticate_websocket_request_query_param_success() {
        let auth_service = create_test_auth_service();

        // 创建有效的JWT token
        let token = auth_service
            .create_token(TEST_USER_ID, TEST_USERNAME, 1)
            .unwrap();
        let uri: Uri = format!("ws://localhost:3000/ws?token={token}")
            .parse()
            .unwrap();
        let headers = HeaderMap::new();

        // 执行认证
        let result = auth_service.authenticate_websocket_request(&uri, &headers);

        // 验证结果
        assert!(result.is_ok());
        let (claims, method) = result.unwrap();
        assert_eq!(claims.sub, TEST_USER_ID);
        assert_eq!(claims.username, TEST_USERNAME);
        assert_eq!(method, TokenExtractionMethod::WebSocketQuery);
    }

    /// 测试WebSocket认证成功 - Sec-WebSocket-Protocol头方式
    #[test]
    fn test_authenticate_websocket_request_protocol_header_success() {
        let auth_service = create_test_auth_service();

        // 创建有效的JWT token
        let token = auth_service
            .create_token(TEST_USER_ID, TEST_USERNAME, 1)
            .unwrap();
        let uri: Uri = "ws://localhost:3000/ws".parse().unwrap();
        let headers = create_websocket_protocol_headers(&token);

        // 执行认证
        let result = auth_service.authenticate_websocket_request(&uri, &headers);

        // 验证结果
        assert!(result.is_ok());
        let (claims, method) = result.unwrap();
        assert_eq!(claims.sub, TEST_USER_ID);
        assert_eq!(claims.username, TEST_USERNAME);
        assert_eq!(method, TokenExtractionMethod::WebSocketProtocol);
    }

    /// 测试WebSocket认证成功 - Authorization头方式（兼容性）
    #[test]
    fn test_authenticate_websocket_request_auth_header_success() {
        let auth_service = create_test_auth_service();

        // 创建有效的JWT token
        let token = auth_service
            .create_token(TEST_USER_ID, TEST_USERNAME, 1)
            .unwrap();
        let uri: Uri = "ws://localhost:3000/ws".parse().unwrap();
        let headers = create_auth_headers(&token);

        // 执行认证
        let result = auth_service.authenticate_websocket_request(&uri, &headers);

        // 验证结果
        assert!(result.is_ok());
        let (claims, method) = result.unwrap();
        assert_eq!(claims.sub, TEST_USER_ID);
        assert_eq!(claims.username, TEST_USERNAME);
        assert_eq!(method, TokenExtractionMethod::HttpBearer);
    }

    /// 测试WebSocket认证失败 - 所有方式都缺少token
    #[test]
    fn test_authenticate_websocket_request_no_token() {
        let auth_service = create_test_auth_service();

        let uri: Uri = "ws://localhost:3000/ws".parse().unwrap();
        let headers = HeaderMap::new();

        let result = auth_service.authenticate_websocket_request(&uri, &headers);

        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), JwtError::TokenMissing));
    }
}

// ============================================================================
// Token提取功能测试
// ============================================================================

#[cfg(test)]
mod token_extraction_tests {
    use super::*;

    /// 测试从HTTP请求头提取token成功
    #[test]
    fn test_extract_token_from_http_headers_success() {
        let auth_service = create_test_auth_service();
        let headers = create_auth_headers("test-token-123");

        let result = auth_service.extract_token_from_http_headers(&headers);

        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "test-token-123");
    }

    /// 测试从查询参数提取token - 支持多种参数名
    #[test]
    fn test_extract_token_from_query_params_multiple_names() {
        let auth_service = create_test_auth_service();

        // 测试 token 参数
        let uri1: Uri = "ws://localhost:3000/ws?token=abc123".parse().unwrap();
        let token1 = auth_service.extract_token_from_query_params(&uri1).unwrap();
        assert_eq!(token1, "abc123");

        // 测试 access_token 参数
        let uri2: Uri = "ws://localhost:3000/ws?access_token=def456"
            .parse()
            .unwrap();
        let token2 = auth_service.extract_token_from_query_params(&uri2).unwrap();
        assert_eq!(token2, "def456");

        // 测试 jwt 参数
        let uri3: Uri = "ws://localhost:3000/ws?jwt=ghi789".parse().unwrap();
        let token3 = auth_service.extract_token_from_query_params(&uri3).unwrap();
        assert_eq!(token3, "ghi789");
    }

    /// 测试从WebSocket协议头提取token
    #[test]
    fn test_extract_token_from_websocket_protocol_success() {
        let auth_service = create_test_auth_service();
        let headers = create_websocket_protocol_headers("test-ws-token");

        let result = auth_service.extract_token_from_websocket_protocol(&headers);

        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "test-ws-token");
    }

    /// 测试从查询参数提取token失败 - 无token参数
    #[test]
    fn test_extract_token_from_query_params_no_token() {
        let auth_service = create_test_auth_service();
        let uri: Uri = "ws://localhost:3000/ws?other_param=value".parse().unwrap();

        let result = auth_service.extract_token_from_query_params(&uri);

        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), JwtError::TokenMissing));
    }
}

// ============================================================================
// RBAC扩展功能测试
// ============================================================================

#[cfg(test)]
mod rbac_authentication_tests {
    use super::*;

    /// 测试HTTP请求RBAC认证成功
    #[test]
    fn test_authenticate_http_request_with_role_success() {
        let auth_service = create_test_auth_service();

        // 创建带角色信息的JWT token
        let token = auth_service
            .create_token_with_role(TEST_USER_ID, TEST_USERNAME, UserRole::Admin, 1)
            .unwrap();
        let headers = create_auth_headers(&token);

        // 执行RBAC认证
        let result = auth_service.authenticate_http_request_with_role(&headers);

        // 验证结果
        assert!(result.is_ok());
        let extended_claims = result.unwrap();
        assert_eq!(extended_claims.sub, TEST_USER_ID);
        assert_eq!(extended_claims.username, TEST_USERNAME);
        assert_eq!(extended_claims.role, UserRole::Admin.to_string());
    }

    /// 测试WebSocket RBAC认证成功 - 查询参数方式
    #[test]
    fn test_authenticate_websocket_request_with_role_success() {
        let auth_service = create_test_auth_service();

        // 创建带角色信息的JWT token
        let token = auth_service
            .create_token_with_role(TEST_USER_ID, TEST_USERNAME, UserRole::Manager, 1)
            .unwrap();
        let uri: Uri = format!("ws://localhost:3000/ws?token={token}")
            .parse()
            .unwrap();
        let headers = HeaderMap::new();

        // 执行RBAC认证
        let result = auth_service.authenticate_websocket_request_with_role(&uri, &headers);

        // 验证结果
        assert!(result.is_ok());
        let (extended_claims, method) = result.unwrap();
        assert_eq!(extended_claims.sub, TEST_USER_ID);
        assert_eq!(extended_claims.username, TEST_USERNAME);
        assert_eq!(extended_claims.role, UserRole::Manager.to_string());
        assert_eq!(method, TokenExtractionMethod::WebSocketQuery);
    }

    /// 测试自定义角色认证
    #[test]
    fn test_authenticate_with_custom_role() {
        let auth_service = create_test_auth_service();

        // 创建自定义角色
        let custom_role = UserRole::Custom {
            name: "CustomRole".to_string(),
            level: 60,
        };

        let token = auth_service
            .create_token_with_role(TEST_USER_ID, TEST_USERNAME, custom_role.clone(), 1)
            .unwrap();
        let headers = create_auth_headers(&token);

        // 执行认证
        let result = auth_service.authenticate_http_request_with_role(&headers);

        // 验证结果
        assert!(result.is_ok());
        let extended_claims = result.unwrap();
        assert_eq!(extended_claims.role, custom_role.to_string());
    }

    /// 测试RBAC认证向后兼容性 - 基础token也能被灵活解析
    #[test]
    fn test_rbac_authentication_backward_compatibility() {
        let auth_service = create_test_auth_service();

        // 创建基础JWT token（无角色信息）
        let token = auth_service
            .create_token(TEST_USER_ID, TEST_USERNAME, 1)
            .unwrap();
        let headers = create_auth_headers(&token);

        // 尝试RBAC认证（应该能够灵活解析）
        let result = auth_service.authenticate_http_request_with_role(&headers);

        // 验证结果 - 应该成功，但角色为默认User
        assert!(result.is_ok());
        let extended_claims = result.unwrap();
        assert_eq!(extended_claims.sub, TEST_USER_ID);
        assert_eq!(extended_claims.username, TEST_USERNAME);
        assert_eq!(extended_claims.role, UserRole::User.to_string()); // 默认角色
    }
}

// ============================================================================
// Token创建功能测试
// ============================================================================

#[cfg(test)]
mod token_creation_tests {
    use super::*;

    /// 测试基础token创建
    #[test]
    fn test_create_token_success() {
        let auth_service = create_test_auth_service();

        let result = auth_service.create_token(TEST_USER_ID, TEST_USERNAME, 1);

        assert!(result.is_ok());
        let token = result.unwrap();
        assert!(!token.is_empty());

        // 验证创建的token能够被正确解析
        let headers = create_auth_headers(&token);
        let claims = auth_service.authenticate_http_request(&headers).unwrap();
        assert_eq!(claims.sub, TEST_USER_ID);
        assert_eq!(claims.username, TEST_USERNAME);
    }

    /// 测试带角色信息的token创建
    #[test]
    fn test_create_token_with_role_success() {
        let auth_service = create_test_auth_service();

        let result =
            auth_service.create_token_with_role(TEST_USER_ID, TEST_USERNAME, UserRole::Admin, 1);

        assert!(result.is_ok());
        let token = result.unwrap();
        assert!(!token.is_empty());

        // 验证创建的token能够被正确解析
        let headers = create_auth_headers(&token);
        let extended_claims = auth_service
            .authenticate_http_request_with_role(&headers)
            .unwrap();
        assert_eq!(extended_claims.sub, TEST_USER_ID);
        assert_eq!(extended_claims.username, TEST_USERNAME);
        assert_eq!(extended_claims.role, UserRole::Admin.to_string());
    }

    /// 测试无效用户ID的token创建
    #[test]
    fn test_create_token_invalid_user_id() {
        let auth_service = create_test_auth_service();

        // 使用空字符串作为用户ID
        let result = auth_service.create_token("", TEST_USERNAME, 1);

        // 应该能创建成功，但用户ID为空
        assert!(result.is_ok());
    }

    /// 测试不同角色的token创建
    #[test]
    fn test_create_token_different_roles() {
        let auth_service = create_test_auth_service();

        let roles = vec![
            UserRole::Admin,
            UserRole::Manager,
            UserRole::User,
            UserRole::Guest,
        ];

        for role in roles {
            let result =
                auth_service.create_token_with_role(TEST_USER_ID, TEST_USERNAME, role.clone(), 1);

            assert!(result.is_ok(), "Failed to create token for role: {role:?}");

            // 验证token能正确解析角色
            let token = result.unwrap();
            let headers = create_auth_headers(&token);
            let extended_claims = auth_service
                .authenticate_http_request_with_role(&headers)
                .unwrap();
            assert_eq!(extended_claims.role, role.to_string());
        }
    }
}

// ============================================================================
// 错误处理和边缘情况测试
// ============================================================================

#[cfg(test)]
mod error_handling_tests {
    use super::*;

    /// 测试过期token的处理
    #[test]
    fn test_authenticate_expired_token() {
        let auth_service = create_test_auth_service();

        // 创建一个过期的token（这里我们使用一个已知的过期token格式）
        // 注意：实际测试中可能需要创建真正过期的token
        let expired_token =
            "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0IiwiZXhwIjoxfQ.invalid";
        let headers = create_auth_headers(expired_token);

        let result = auth_service.authenticate_http_request(&headers);

        assert!(result.is_err());
        // 应该是token验证失败
        assert!(matches!(result.unwrap_err(), JwtError::TokenInvalid));
    }

    /// 测试恶意构造的token
    #[test]
    fn test_authenticate_malicious_token() {
        let auth_service = create_test_auth_service();

        // 测试各种恶意构造的token
        let malicious_tokens = vec![
            "malicious.token.here",
            "eyJ0eXAiOiJKV1QiLCJhbGciOiJub25lIn0.eyJzdWIiOiJhZG1pbiJ9.",
            "../../etc/passwd",
            "<script>alert('xss')</script>",
            "' OR 1=1 --",
        ];

        for malicious_token in malicious_tokens {
            let headers = create_auth_headers(malicious_token);
            let result = auth_service.authenticate_http_request(&headers);

            assert!(
                result.is_err(),
                "Malicious token should be rejected: {malicious_token}"
            );
        }
    }

    /// 测试空token的处理
    #[test]
    fn test_authenticate_empty_token() {
        let auth_service = create_test_auth_service();
        let headers = create_auth_headers("");

        let result = auth_service.authenticate_http_request(&headers);

        assert!(result.is_err());
        // 空token可能被认为是缺失token
        let error = result.unwrap_err();
        assert!(matches!(
            error,
            JwtError::TokenMissing | JwtError::TokenInvalid
        ));
    }

    /// 测试非UTF-8字符的处理
    #[test]
    fn test_authenticate_non_utf8_header() {
        let auth_service = create_test_auth_service();

        let mut headers = HeaderMap::new();
        // 创建包含非UTF-8字节的header值
        let invalid_bytes = vec![0xff, 0xfe, 0xfd];
        if let Ok(header_value) = HeaderValue::from_bytes(&invalid_bytes) {
            headers.insert(AUTHORIZATION, header_value);

            let result = auth_service.authenticate_http_request(&headers);
            assert!(result.is_err());
        }
    }

    /// 测试WebSocket多种提取方式的优先级
    #[test]
    fn test_websocket_token_extraction_priority() {
        let auth_service = create_test_auth_service();

        // 创建三个不同的有效token
        let query_token = auth_service.create_token("user1", "query_user", 1).unwrap();
        let protocol_token = auth_service
            .create_token("user2", "protocol_user", 1)
            .unwrap();
        let auth_token = auth_service.create_token("user3", "auth_user", 1).unwrap();

        // 创建包含所有三种token的请求
        let uri: Uri = format!("ws://localhost:3000/ws?token={query_token}")
            .parse()
            .unwrap();
        let mut headers = HeaderMap::new();
        headers.insert(
            "sec-websocket-protocol",
            HeaderValue::from_str(&format!("access_token.{protocol_token}")).unwrap(),
        );
        headers.insert(
            AUTHORIZATION,
            HeaderValue::from_str(&format!("Bearer {auth_token}")).unwrap(),
        );

        // 执行认证
        let result = auth_service.authenticate_websocket_request(&uri, &headers);

        // 验证结果 - 应该优先使用查询参数中的token
        assert!(result.is_ok());
        let (claims, method) = result.unwrap();
        assert_eq!(claims.username, "query_user"); // 应该是查询参数的用户
        assert_eq!(method, TokenExtractionMethod::WebSocketQuery);
    }

    /// 测试WebSocket协议头格式错误
    #[test]
    fn test_websocket_protocol_header_invalid_format() {
        let auth_service = create_test_auth_service();

        let uri: Uri = "ws://localhost:3000/ws".parse().unwrap();
        let mut headers = HeaderMap::new();

        // 测试各种错误的协议头格式
        let invalid_protocols = vec![
            "invalid_format",
            "access_token",
            "access_token.",
            ".token_value",
            "other_protocol.token_value",
        ];

        for invalid_protocol in invalid_protocols {
            headers.clear();
            headers.insert(
                "sec-websocket-protocol",
                HeaderValue::from_str(invalid_protocol).unwrap(),
            );

            let result = auth_service.authenticate_websocket_request(&uri, &headers);
            assert!(
                result.is_err(),
                "Invalid protocol format should be rejected: {invalid_protocol}"
            );
        }
    }

    /// 测试极长token的处理
    #[test]
    fn test_authenticate_extremely_long_token() {
        let auth_service = create_test_auth_service();

        // 创建一个极长的token（模拟攻击）
        let long_token = "a".repeat(10000);
        let headers = create_auth_headers(&long_token);

        let result = auth_service.authenticate_http_request(&headers);

        assert!(result.is_err());
        // 应该是token验证失败
        assert!(matches!(result.unwrap_err(), JwtError::TokenInvalid));
    }

    /// 测试并发认证请求
    #[tokio::test]
    async fn test_concurrent_authentication() {
        let auth_service = std::sync::Arc::new(create_test_auth_service());

        // 创建有效token
        let token = auth_service
            .create_token(TEST_USER_ID, TEST_USERNAME, 1)
            .unwrap();

        // 并发执行多个认证请求
        let mut handles = vec![];
        for i in 0..10 {
            let auth_service_clone = auth_service.clone();
            let token_clone = token.clone();

            let handle = tokio::spawn(async move {
                let headers = create_auth_headers(&token_clone);
                let result = auth_service_clone.authenticate_http_request(&headers);
                (i, result)
            });

            handles.push(handle);
        }

        // 等待所有请求完成
        for handle in handles {
            let (i, result) = handle.await.unwrap();
            assert!(result.is_ok(), "Concurrent request {i} failed");

            let claims = result.unwrap();
            assert_eq!(claims.sub, TEST_USER_ID);
            assert_eq!(claims.username, TEST_USERNAME);
        }
    }
}
