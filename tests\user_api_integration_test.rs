//! # GET /api/users/{id} 接口集成测试
//!
//! 测试用户详情获取接口的完整功能，包括：
//! - 成功获取用户信息
//! - 404错误处理（用户不存在）
//! - 401错误处理（未认证）
//! - 参数验证

use reqwest::StatusCode;
use serde_json::Value;
use uuid::Uuid;

/// 测试配置常量
const TEST_USER_USERNAME: &str = "testuser456";
const TEST_USER_PASSWORD: &str = "password123";
const API_BASE_URL: &str = "http://127.0.0.1:3000/api";

/// 辅助函数：登录并获取用户信息
async fn login_and_get_user_info() -> Result<(String, String), Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();

    // 登录获取JWT令牌
    let login_payload = serde_json::json!({
        "username": TEST_USER_USERNAME,
        "password": TEST_USER_PASSWORD
    });

    let login_response = client
        .post(&format!("{}/auth/login", API_BASE_URL))
        .json(&login_payload)
        .send()
        .await?;

    if !login_response.status().is_success() {
        return Err("登录失败".into());
    }

    let login_body: Value = login_response.json().await?;
    let token = login_body["data"]["access_token"]
        .as_str()
        .ok_or("获取token失败")?
        .to_string();
    let user_id = login_body["data"]["user"]["id"]
        .as_str()
        .ok_or("获取用户ID失败")?
        .to_string();

    Ok((token, user_id))
}

/// 测试成功获取用户信息
#[tokio::test]
async fn test_get_user_by_id_success() {
    // 登录并获取用户信息
    let (token, user_id) = login_and_get_user_info().await.expect("登录失败");

    // 创建API客户端
    let client = reqwest::Client::new();

    // 发送GET请求获取用户信息
    let response = client
        .get(&format!("{}/users/{}", API_BASE_URL, user_id))
        .header("Authorization", format!("Bearer {}", token))
        .send()
        .await
        .expect("发送请求失败");

    // 验证响应状态码
    assert!(
        response.status().is_success(),
        "获取用户信息失败: {}",
        response.status()
    );

    // 验证响应内容
    let body: Value = response.json().await.expect("解析JSON失败");
    assert_eq!(body["success"], true);
    assert_eq!(body["data"]["id"], user_id);
    assert_eq!(body["data"]["username"], TEST_USER_USERNAME);
    assert!(
        body["message"]
            .as_str()
            .unwrap()
            .contains("获取用户信息成功")
    );

    println!("✅ 成功获取用户信息测试通过");
}

/// 测试获取不存在的用户（404错误）
#[tokio::test]
async fn test_get_user_by_id_not_found() {
    // 登录并获取用户信息
    let (token, _) = login_and_get_user_info().await.expect("登录失败");

    // 创建API客户端
    let client = reqwest::Client::new();

    // 使用不存在的用户ID
    let non_existent_id = Uuid::new_v4();

    // 发送GET请求
    let response = client
        .get(&format!("{}/users/{}", API_BASE_URL, non_existent_id))
        .header("Authorization", format!("Bearer {}", token))
        .send()
        .await
        .expect("发送请求失败");

    // 验证响应状态码
    assert_eq!(response.status(), StatusCode::NOT_FOUND);

    // 验证响应内容
    let body: Value = response.json().await.expect("解析JSON失败");
    assert_eq!(body["success"], false);
    assert!(
        body["error"]["message"]
            .as_str()
            .unwrap()
            .contains("用户不存在")
    );

    println!("✅ 404错误处理测试通过");
}

/// 测试未认证访问（401错误）
#[tokio::test]
async fn test_get_user_by_id_unauthorized() {
    // 创建API客户端
    let client = reqwest::Client::new();

    // 使用一个示例用户ID
    let user_id = Uuid::new_v4();

    // 发送GET请求（不带Authorization头）
    let response = client
        .get(&format!("{}/users/{}", API_BASE_URL, user_id))
        .send()
        .await
        .expect("发送请求失败");

    // 验证响应状态码
    assert_eq!(response.status(), StatusCode::UNAUTHORIZED);

    println!("✅ 401未认证错误处理测试通过");
}

/// 测试无效的用户ID格式
#[tokio::test]
async fn test_get_user_by_id_invalid_uuid() {
    // 登录并获取用户信息
    let (token, _) = login_and_get_user_info().await.expect("登录失败");

    // 创建API客户端
    let client = reqwest::Client::new();

    // 使用无效的UUID格式
    let invalid_id = "invalid-uuid-format";

    // 发送GET请求
    let response = client
        .get(&format!("{}/users/{}", API_BASE_URL, invalid_id))
        .header("Authorization", format!("Bearer {}", token))
        .send()
        .await
        .expect("发送请求失败");

    // 验证响应状态码（应该是400 Bad Request）
    assert_eq!(response.status(), StatusCode::BAD_REQUEST);

    println!("✅ 400无效UUID格式错误处理测试通过");
}

/// 测试响应时间性能
#[tokio::test]
async fn test_get_user_by_id_performance() {
    // 登录并获取用户信息
    let (token, user_id) = login_and_get_user_info().await.expect("登录失败");

    // 创建API客户端
    let client = reqwest::Client::new();

    // 记录开始时间
    let start_time = std::time::Instant::now();

    // 发送GET请求
    let response = client
        .get(&format!("{}/users/{}", API_BASE_URL, user_id))
        .header("Authorization", format!("Bearer {}", token))
        .send()
        .await
        .expect("发送请求失败");

    // 记录结束时间
    let elapsed = start_time.elapsed();

    // 验证响应状态码
    assert!(response.status().is_success());

    // 验证响应时间（应该在500ms以内）
    assert!(elapsed.as_millis() < 500, "响应时间过长: {:?}", elapsed);

    println!("✅ API响应时间性能测试通过: {:?}", elapsed);
}
