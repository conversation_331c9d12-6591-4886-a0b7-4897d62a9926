//! 边缘情况测试模块
//!
//! 本模块包含各种边缘情况的测试，包括：
//! - 网络中断恢复测试
//! - 大数据量处理测试
//! - 高并发压力测试
//! - 内存泄漏检测
//! - 长时间稳定性测试

use futures::future::join_all;
use reqwest::Client;
use serde_json::json;
use std::sync::Arc;
use std::sync::atomic::{AtomicUsize, Ordering};
use std::time::{Duration, Instant};
use tokio::sync::Semaphore;
use tokio::time::sleep;

/// 测试配置结构体
#[derive(Debug, Clone)]
pub struct EdgeTestConfig {
    pub base_url: String,
    pub test_user: String,
    pub test_password: String,
    pub timeout: Duration,
    pub max_retries: usize,
}

impl Default for EdgeTestConfig {
    fn default() -> Self {
        Self {
            base_url: "http://127.0.0.1:3000".to_string(),
            test_user: "testuser456".to_string(),
            test_password: "password123".to_string(),
            timeout: Duration::from_secs(30),
            max_retries: 3,
        }
    }
}

/// 测试结果统计
#[derive(Debug, Default)]
pub struct TestStats {
    pub total_requests: usize,
    pub successful_requests: usize,
    pub failed_requests: usize,
    pub average_response_time: Duration,
    pub max_response_time: Duration,
    pub min_response_time: Duration,
}

impl TestStats {
    pub fn success_rate(&self) -> f64 {
        if self.total_requests == 0 {
            0.0
        } else {
            ((self.successful_requests as f64) / (self.total_requests as f64)) * 100.0
        }
    }
}

/// 网络中断恢复测试
pub struct NetworkInterruptionTest {
    config: EdgeTestConfig,
    client: Client,
}

impl NetworkInterruptionTest {
    pub fn new(config: EdgeTestConfig) -> Self {
        let client = Client::builder()
            .timeout(config.timeout)
            .build()
            .expect("Failed to create HTTP client");

        Self { config, client }
    }

    /// 执行网络中断恢复测试
    pub async fn run_network_interruption_test(
        &self,
    ) -> Result<TestStats, Box<dyn std::error::Error>> {
        println!("🔄 开始网络中断恢复测试...");

        let mut stats = TestStats::default();
        let mut response_times = Vec::new();

        // 第一阶段：正常连接测试
        println!("📡 阶段1: 正常连接测试");
        for i in 0..10 {
            let start = Instant::now();
            match self.test_api_endpoint().await {
                Ok(_) => {
                    stats.successful_requests += 1;
                    let duration = start.elapsed();
                    response_times.push(duration);
                    println!("✅ 请求 {} 成功，响应时间: {:?}", i + 1, duration);
                }
                Err(e) => {
                    stats.failed_requests += 1;
                    println!("❌ 请求 {} 失败: {}", i + 1, e);
                }
            }
            stats.total_requests += 1;
            sleep(Duration::from_millis(100)).await;
        }

        // 第二阶段：模拟网络中断（通过超时模拟）
        println!("📡 阶段2: 模拟网络中断");
        let short_timeout_client = Client::builder()
            .timeout(Duration::from_millis(1)) // 极短超时模拟网络中断
            .build()?;

        for i in 0..5 {
            let start = Instant::now();
            match self
                .test_api_endpoint_with_client(&short_timeout_client)
                .await
            {
                Ok(_) => {
                    stats.successful_requests += 1;
                    let duration = start.elapsed();
                    response_times.push(duration);
                    println!("✅ 中断期间请求 {} 意外成功", i + 1);
                }
                Err(_) => {
                    stats.failed_requests += 1;
                    println!("❌ 中断期间请求 {} 失败（预期）", i + 1);
                }
            }
            stats.total_requests += 1;
            sleep(Duration::from_millis(200)).await;
        }

        // 第三阶段：网络恢复测试
        println!("📡 阶段3: 网络恢复测试");
        sleep(Duration::from_secs(2)).await; // 等待"网络恢复"

        for i in 0..10 {
            let start = Instant::now();
            match self.test_api_endpoint().await {
                Ok(_) => {
                    stats.successful_requests += 1;
                    let duration = start.elapsed();
                    response_times.push(duration);
                    println!("✅ 恢复后请求 {} 成功，响应时间: {:?}", i + 1, duration);
                }
                Err(e) => {
                    stats.failed_requests += 1;
                    println!("❌ 恢复后请求 {} 失败: {}", i + 1, e);
                }
            }
            stats.total_requests += 1;
            sleep(Duration::from_millis(100)).await;
        }

        // 计算统计信息
        if !response_times.is_empty() {
            stats.average_response_time = Duration::from_nanos(
                (response_times.iter().map(|d| d.as_nanos()).sum::<u128>() as u64)
                    / (response_times.len() as u64),
            );
            stats.max_response_time = *response_times.iter().max().unwrap();
            stats.min_response_time = *response_times.iter().min().unwrap();
        }

        println!("📊 网络中断恢复测试完成");
        println!("   总请求数: {}", stats.total_requests);
        println!("   成功请求数: {}", stats.successful_requests);
        println!("   失败请求数: {}", stats.failed_requests);
        println!("   成功率: {:.2}%", stats.success_rate());
        println!("   平均响应时间: {:?}", stats.average_response_time);

        Ok(stats)
    }

    /// 测试API端点
    async fn test_api_endpoint(&self) -> Result<(), Box<dyn std::error::Error>> {
        let response = self
            .client
            .get(&format!("{}/api/tasks", self.config.base_url))
            .send()
            .await?;

        if response.status().is_success() {
            Ok(())
        } else {
            Err(format!("API请求失败，状态码: {}", response.status()).into())
        }
    }

    /// 使用指定客户端测试API端点
    async fn test_api_endpoint_with_client(
        &self,
        client: &Client,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let response = client
            .get(&format!("{}/api/tasks", self.config.base_url))
            .send()
            .await?;

        if response.status().is_success() {
            Ok(())
        } else {
            Err(format!("API请求失败，状态码: {}", response.status()).into())
        }
    }
}

/// 大数据量处理测试
pub struct LargeDataTest {
    config: EdgeTestConfig,
    client: Client,
}

impl LargeDataTest {
    pub fn new(config: EdgeTestConfig) -> Self {
        let client = Client::builder()
            .timeout(config.timeout)
            .build()
            .expect("Failed to create HTTP client");

        Self { config, client }
    }

    /// 执行大数据量处理测试
    pub async fn run_large_data_test(&self) -> Result<TestStats, Box<dyn std::error::Error>> {
        println!("📊 开始大数据量处理测试...");

        let mut stats = TestStats::default();
        let mut response_times = Vec::new();

        // 生成大量测试数据
        let large_data_sizes = vec![1000, 5000, 10000, 50000]; // 不同数据量级

        for data_size in large_data_sizes {
            println!("📈 测试数据量: {} 条记录", data_size);

            let start = Instant::now();
            match self.test_large_data_processing(data_size).await {
                Ok(_) => {
                    stats.successful_requests += 1;
                    let duration = start.elapsed();
                    response_times.push(duration);
                    println!("✅ 处理 {} 条记录成功，耗时: {:?}", data_size, duration);
                }
                Err(e) => {
                    stats.failed_requests += 1;
                    println!("❌ 处理 {} 条记录失败: {}", data_size, e);
                }
            }
            stats.total_requests += 1;

            // 等待系统恢复
            sleep(Duration::from_secs(1)).await;
        }

        // 计算统计信息
        if !response_times.is_empty() {
            stats.average_response_time = Duration::from_nanos(
                (response_times.iter().map(|d| d.as_nanos()).sum::<u128>() as u64)
                    / (response_times.len() as u64),
            );
            stats.max_response_time = *response_times.iter().max().unwrap();
            stats.min_response_time = *response_times.iter().min().unwrap();
        }

        println!("📊 大数据量处理测试完成");
        println!("   总测试数: {}", stats.total_requests);
        println!("   成功测试数: {}", stats.successful_requests);
        println!("   失败测试数: {}", stats.failed_requests);
        println!("   成功率: {:.2}%", stats.success_rate());
        println!("   平均处理时间: {:?}", stats.average_response_time);
        println!("   最大处理时间: {:?}", stats.max_response_time);

        Ok(stats)
    }

    /// 测试大数据量处理
    async fn test_large_data_processing(
        &self,
        data_size: usize,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // 生成大量任务数据
        let mut tasks = Vec::new();
        for i in 0..data_size {
            tasks.push(
                json!({
                "title": format!("大数据测试任务 {}", i),
                "description": format!("这是第 {} 个大数据测试任务，用于测试系统处理大量数据的能力", i),
                "priority": if i % 3 == 0 { "high" } else if i % 3 == 1 { "medium" } else { "low" },
                "status": "pending"
            })
            );
        }

        // 批量创建任务（模拟大数据量处理）
        let batch_size = 100; // 每批处理100个任务
        for chunk in tasks.chunks(batch_size) {
            let response = self
                .client
                .post(&format!("{}/api/tasks/batch", self.config.base_url))
                .json(&json!({ "tasks": chunk }))
                .send()
                .await?;

            if !response.status().is_success() {
                return Err(format!("批量创建任务失败，状态码: {}", response.status()).into());
            }
        }

        // 验证数据是否正确存储
        let response = self
            .client
            .get(&format!(
                "{}/api/tasks?limit={}",
                self.config.base_url, data_size
            ))
            .send()
            .await?;

        if response.status().is_success() {
            let tasks: serde_json::Value = response.json().await?;
            if let Some(task_array) = tasks.as_array() {
                if task_array.len() >= data_size {
                    Ok(())
                } else {
                    Err(format!(
                        "数据验证失败：期望 {} 条记录，实际 {} 条",
                        data_size,
                        task_array.len()
                    )
                    .into())
                }
            } else {
                Err("响应格式错误".into())
            }
        } else {
            Err(format!("数据验证请求失败，状态码: {}", response.status()).into())
        }
    }
}

/// 高并发压力测试
pub struct ConcurrencyStressTest {
    config: EdgeTestConfig,
    client: Client,
}

impl ConcurrencyStressTest {
    pub fn new(config: EdgeTestConfig) -> Self {
        let client = Client::builder()
            .timeout(config.timeout)
            .pool_max_idle_per_host(100) // 增加连接池大小
            .build()
            .expect("Failed to create HTTP client");

        Self { config, client }
    }

    /// 执行高并发压力测试
    pub async fn run_concurrency_stress_test(
        &self,
    ) -> Result<TestStats, Box<dyn std::error::Error>> {
        println!("⚡ 开始高并发压力测试...");

        let concurrent_levels = vec![10, 50, 100, 200]; // 不同并发级别
        let mut overall_stats = TestStats::default();

        for concurrent_users in concurrent_levels {
            println!("🚀 测试并发级别: {} 个并发用户", concurrent_users);

            let stats = self.test_concurrent_requests(concurrent_users).await?;

            // 累计统计信息
            overall_stats.total_requests += stats.total_requests;
            overall_stats.successful_requests += stats.successful_requests;
            overall_stats.failed_requests += stats.failed_requests;

            // 更新最大最小响应时间
            if stats.max_response_time > overall_stats.max_response_time {
                overall_stats.max_response_time = stats.max_response_time;
            }
            if overall_stats.min_response_time == Duration::default()
                || (stats.min_response_time < overall_stats.min_response_time
                    && stats.min_response_time > Duration::default())
            {
                overall_stats.min_response_time = stats.min_response_time;
            }

            println!("   并发 {} 用户结果:", concurrent_users);
            println!("   - 成功率: {:.2}%", stats.success_rate());
            println!("   - 平均响应时间: {:?}", stats.average_response_time);
            println!("   - 最大响应时间: {:?}", stats.max_response_time);

            // 等待系统恢复
            sleep(Duration::from_secs(2)).await;
        }

        // 计算总体平均响应时间（简化计算）
        overall_stats.average_response_time = Duration::from_millis(
            ((overall_stats.max_response_time.as_millis()
                + overall_stats.min_response_time.as_millis())
                / 2) as u64,
        );

        println!("📊 高并发压力测试完成");
        println!("   总请求数: {}", overall_stats.total_requests);
        println!("   总成功数: {}", overall_stats.successful_requests);
        println!("   总失败数: {}", overall_stats.failed_requests);
        println!("   总体成功率: {:.2}%", overall_stats.success_rate());

        Ok(overall_stats)
    }

    /// 测试指定并发级别的请求
    async fn test_concurrent_requests(
        &self,
        concurrent_users: usize,
    ) -> Result<TestStats, Box<dyn std::error::Error>> {
        let semaphore = Arc::new(Semaphore::new(concurrent_users));
        let successful_count = Arc::new(AtomicUsize::new(0));
        let failed_count = Arc::new(AtomicUsize::new(0));
        let response_times = Arc::new(tokio::sync::Mutex::new(Vec::new()));

        let requests_per_user = 10; // 每个并发用户发送10个请求
        let total_requests = concurrent_users * requests_per_user;

        let mut tasks = Vec::new();

        for _user_id in 0..concurrent_users {
            for _request_id in 0..requests_per_user {
                let semaphore = semaphore.clone();
                let client = self.client.clone();
                let base_url = self.config.base_url.clone();
                let successful_count = successful_count.clone();
                let failed_count = failed_count.clone();
                let response_times = response_times.clone();

                let task = tokio::spawn(async move {
                    let _permit = semaphore.acquire().await.unwrap();

                    let start = Instant::now();
                    let result = client.get(&format!("{}/api/tasks", base_url)).send().await;

                    let duration = start.elapsed();

                    match result {
                        Ok(response) if response.status().is_success() => {
                            successful_count.fetch_add(1, Ordering::Relaxed);
                            response_times.lock().await.push(duration);
                        }
                        _ => {
                            failed_count.fetch_add(1, Ordering::Relaxed);
                        }
                    }

                    // 模拟用户思考时间
                    sleep(Duration::from_millis(10)).await;
                });

                tasks.push(task);
            }
        }

        // 等待所有请求完成
        join_all(tasks).await;

        let successful_requests = successful_count.load(Ordering::Relaxed);
        let failed_requests = failed_count.load(Ordering::Relaxed);
        let times = response_times.lock().await;

        let mut stats = TestStats {
            total_requests,
            successful_requests,
            failed_requests,
            ..Default::default()
        };

        if !times.is_empty() {
            stats.average_response_time = Duration::from_nanos(
                (times.iter().map(|d| d.as_nanos()).sum::<u128>() as u64) / (times.len() as u64),
            );
            stats.max_response_time = *times.iter().max().unwrap();
            stats.min_response_time = *times.iter().min().unwrap();
        }

        Ok(stats)
    }
}
