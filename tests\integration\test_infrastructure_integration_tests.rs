//! # 测试基础设施集成测试
//!
//! 验证测试框架基础设施的完整性和可靠性。
//! 包括数据库连接池、缓存连接、测试数据管理等功能的集成测试。

use std::time::Duration;
use anyhow::Result;
use tokio_test;
use tracing_test::traced_test;
use uuid::Uuid;

// 引入测试模块
use crate::test_infrastructure::{TestInfrastructure, TestInfrastructureConfig};
use crate::test_data_manager::{TestDataManager, TestDataConfig};
use crate::test_utils::{DatabaseConfig, CacheConfig};

/// 测试基础设施初始化和关闭
#[tokio::test]
#[traced_test]
async fn test_infrastructure_lifecycle() -> Result<()> {
    tracing::info!("开始测试基础设施生命周期");
    
    // 创建测试基础设施
    let mut infrastructure = TestInfrastructure::new();
    
    // 初始化
    infrastructure.initialize().await?;
    
    // 验证初始化状态
    let status = infrastructure.get_status().await;
    assert!(status.initialized, "基础设施应该已初始化");
    assert!(status.database_status.is_some(), "数据库状态应该可用");
    assert!(status.cache_status.is_some(), "缓存状态应该可用");
    
    // 执行健康检查
    let health_status = infrastructure.health_check().await?;
    assert!(health_status.errors.is_empty(), "健康检查不应有错误: {:?}", health_status.errors);
    
    // 关闭基础设施
    infrastructure.shutdown().await?;
    
    // 验证关闭状态
    let final_status = infrastructure.get_status().await;
    assert!(!final_status.initialized, "基础设施应该已关闭");
    
    tracing::info!("基础设施生命周期测试完成");
    Ok(())
}

/// 测试数据库连接池功能
#[tokio::test]
#[traced_test]
async fn test_database_connection_pool() -> Result<()> {
    tracing::info!("开始测试数据库连接池");
    
    use crate::test_utils::TestDatabaseManager;
    
    // 创建数据库管理器
    let db_manager = TestDatabaseManager::new().await?;
    
    // 验证连接
    db_manager.verify_connection().await?;
    
    // 获取连接状态
    let status = db_manager.get_pool_status().await?;
    assert!(status.is_healthy, "数据库连接应该健康");
    assert!(status.ping_latency < Duration::from_secs(1), "Ping延迟应该小于1秒");
    
    // 获取多个连接（测试连接池）
    let conn1 = db_manager.get_connection();
    let conn2 = db_manager.get_connection();
    
    // 验证连接可用性
    conn1.ping().await?;
    conn2.ping().await?;
    
    // 关闭连接池
    db_manager.close().await?;
    
    tracing::info!("数据库连接池测试完成");
    Ok(())
}

/// 测试缓存连接功能
#[tokio::test]
#[traced_test]
async fn test_cache_connection() -> Result<()> {
    tracing::info!("开始测试缓存连接");
    
    use crate::test_utils::TestCacheManager;
    
    // 创建缓存管理器
    let cache_manager = TestCacheManager::new().await?;
    
    // 验证连接
    cache_manager.verify_connection().await?;
    
    // 获取缓存状态
    let status = cache_manager.get_cache_status().await?;
    assert!(status.is_healthy, "缓存连接应该健康");
    assert!(status.ping_latency < Duration::from_secs(1), "Ping延迟应该小于1秒");
    
    // 测试缓存操作
    let client = cache_manager.get_client();
    
    // 设置测试键值
    let test_key = format!("test:integration:{}", Uuid::new_v4());
    let test_value = "test_value";
    
    let _: () = client.set(&test_key, test_value, None, None, false).await?;
    
    // 获取值
    let retrieved_value: String = client.get(&test_key).await?;
    assert_eq!(retrieved_value, test_value, "缓存值应该匹配");
    
    // 清理测试数据
    cache_manager.flush_test_data().await?;
    
    // 关闭连接
    cache_manager.close().await?;
    
    tracing::info!("缓存连接测试完成");
    Ok(())
}

/// 测试数据管理器功能
#[tokio::test]
#[traced_test]
async fn test_data_manager() -> Result<()> {
    tracing::info!("开始测试数据管理器");
    
    // 初始化基础设施
    let mut infrastructure = TestInfrastructure::new();
    infrastructure.initialize().await?;
    
    let db_connection = infrastructure.get_database_connection()
        .ok_or_else(|| anyhow::anyhow!("无法获取数据库连接"))?;
    let cache_client = infrastructure.get_cache_client()
        .ok_or_else(|| anyhow::anyhow!("无法获取缓存客户端"))?;
    
    // 创建数据管理器
    let data_manager = TestDataManager::new(db_connection, cache_client);
    
    // 生成测试数据集
    let dataset_id = data_manager.generate_message_search_dataset(100).await?;
    assert!(!dataset_id.is_empty(), "数据集ID不应为空");
    
    // 获取数据集
    let dataset = data_manager.get_dataset(&dataset_id).await?;
    assert!(dataset.is_some(), "应该能够获取数据集");
    
    let dataset = dataset.unwrap();
    assert_eq!(dataset.size, 100, "数据集大小应该匹配");
    assert_eq!(dataset.items.len(), 100, "数据项数量应该匹配");
    
    // 列出数据集
    let datasets = data_manager.list_datasets().await?;
    assert!(datasets.contains(&dataset_id), "数据集列表应该包含新创建的数据集");
    
    // 删除数据集
    data_manager.delete_dataset(&dataset_id).await?;
    
    // 验证删除
    let deleted_dataset = data_manager.get_dataset(&dataset_id).await?;
    assert!(deleted_dataset.is_none(), "数据集应该已被删除");
    
    // 清理基础设施
    infrastructure.shutdown().await?;
    
    tracing::info!("数据管理器测试完成");
    Ok(())
}

/// 测试自定义配置
#[tokio::test]
#[traced_test]
async fn test_custom_configuration() -> Result<()> {
    tracing::info!("开始测试自定义配置");
    
    // 创建自定义配置
    let config = TestInfrastructureConfig {
        database: DatabaseConfig {
            url: std::env::var("DATABASE_URL")
                .unwrap_or_else(|_| "postgres://axum_user:axum_secure_password_2025@localhost:5432/axum_tutorial_test".to_string()),
            max_connections: 5,
            min_connections: 1,
            connect_timeout: Duration::from_secs(5),
            acquire_timeout: Duration::from_secs(5),
            idle_timeout: Duration::from_secs(15),
            max_lifetime: Duration::from_secs(60),
        },
        cache: CacheConfig {
            url: std::env::var("DRAGONFLY_URL")
                .unwrap_or_else(|_| "redis://localhost:6379".to_string()),
            pool_size: 5,
            connect_timeout: Duration::from_secs(3),
            command_timeout: Duration::from_secs(3),
            max_retries: 2,
            retry_delay: Duration::from_millis(50),
        },
        auto_cleanup: true,
        health_check_interval: Duration::from_secs(10),
        test_timeout: Duration::from_secs(60),
    };
    
    // 使用自定义配置创建基础设施
    let mut infrastructure = TestInfrastructure::with_config(config);
    
    // 初始化和验证
    infrastructure.initialize().await?;
    
    let status = infrastructure.get_status().await;
    assert!(status.initialized, "基础设施应该已初始化");
    
    // 验证数据库连接池配置
    if let Some(db_status) = &status.database_status {
        assert_eq!(db_status.max_connections, 5, "最大连接数应该匹配配置");
    }
    
    // 关闭基础设施
    infrastructure.shutdown().await?;
    
    tracing::info!("自定义配置测试完成");
    Ok(())
}

/// 测试错误处理和恢复
#[tokio::test]
#[traced_test]
async fn test_error_handling() -> Result<()> {
    tracing::info!("开始测试错误处理");
    
    use crate::test_utils::{TestDatabaseManager, DatabaseConfig};
    
    // 测试无效数据库URL
    let invalid_config = DatabaseConfig {
        url: "*******************************************/invalid".to_string(),
        max_connections: 1,
        min_connections: 1,
        connect_timeout: Duration::from_secs(1),
        acquire_timeout: Duration::from_secs(1),
        idle_timeout: Duration::from_secs(1),
        max_lifetime: Duration::from_secs(1),
    };
    
    let result = TestDatabaseManager::with_config(invalid_config).await;
    assert!(result.is_err(), "无效配置应该返回错误");
    
    tracing::info!("错误处理测试完成");
    Ok(())
}

/// 性能基准测试
#[tokio::test]
#[traced_test]
async fn test_performance_benchmark() -> Result<()> {
    tracing::info!("开始性能基准测试");
    
    let mut infrastructure = TestInfrastructure::new();
    infrastructure.initialize().await?;
    
    // 测试数据库连接性能
    if let Some(db_connection) = infrastructure.get_database_connection() {
        let start_time = std::time::Instant::now();
        
        // 执行多次ping测试
        for _ in 0..10 {
            db_connection.ping().await?;
        }
        
        let elapsed = start_time.elapsed();
        let avg_latency = elapsed / 10;
        
        tracing::info!("数据库平均ping延迟: {:?}", avg_latency);
        assert!(avg_latency < Duration::from_millis(100), "平均延迟应该小于100ms");
    }
    
    // 测试缓存连接性能
    if let Some(cache_client) = infrastructure.get_cache_client() {
        let start_time = std::time::Instant::now();
        
        // 执行多次ping测试
        for _ in 0..10 {
            let _: String = cache_client.ping(None).await?;
        }
        
        let elapsed = start_time.elapsed();
        let avg_latency = elapsed / 10;
        
        tracing::info!("缓存平均ping延迟: {:?}", avg_latency);
        assert!(avg_latency < Duration::from_millis(50), "平均延迟应该小于50ms");
    }
    
    infrastructure.shutdown().await?;
    
    tracing::info!("性能基准测试完成");
    Ok(())
}
