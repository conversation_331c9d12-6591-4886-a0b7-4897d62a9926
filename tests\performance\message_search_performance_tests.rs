//! # 消息搜索性能基准测试
//!
//! 测试消息搜索功能的性能指标，包括响应时间、吞吐量、并发能力等。
//! 严格遵循TDD原则，确保系统满足企业级性能要求。

use std::time::{Duration, Instant};
use std::sync::{Arc, Mutex};
use std::sync::atomic::{AtomicU64, Ordering};
use tokio::time::sleep;
use tokio_test;
use uuid::Uuid;
use serde_json;
use anyhow::Result;
use criterion::{Criterion, BenchmarkId, Throughput};

use crate::message_search_test_framework::{
    MessageSearchTestFramework, MessageSearchTestConfig, TestMessage, 
    PerformanceMetrics, LatencyStats, MetricsCollector
};

/// 性能测试套件
pub struct MessageSearchPerformanceTests {
    framework: MessageSearchTestFramework,
    metrics_collector: Arc<Mutex<MetricsCollector>>,
}

impl MessageSearchPerformanceTests {
    /// 创建新的性能测试套件实例
    pub fn new() -> Self {
        let config = MessageSearchTestConfig {
            test_data_size: 100000, // 10万条测试数据
            ..Default::default()
        };
        
        Self {
            framework: MessageSearchTestFramework::new(config),
            metrics_collector: Arc::new(Mutex::new(MetricsCollector::new())),
        }
    }

    /// 设置性能测试环境
    pub async fn setup(&mut self) -> Result<()> {
        tracing::info!("设置消息搜索性能测试环境");
        
        // 初始化测试框架
        self.framework.initialize().await?;
        
        // 预热系统
        self.warmup_system().await?;
        
        tracing::info!("性能测试环境设置完成");
        Ok(())
    }

    /// 系统预热
    async fn warmup_system(&self) -> Result<()> {
        tracing::info!("开始系统预热");
        
        // 执行一些预热查询
        let warmup_queries = vec!["测试", "消息", "搜索", "性能"];
        
        for query in warmup_queries {
            self.simulate_search_request(query).await?;
            sleep(Duration::from_millis(10)).await;
        }
        
        tracing::info!("系统预热完成");
        Ok(())
    }

    /// 模拟搜索请求
    async fn simulate_search_request(&self, query: &str) -> Result<Duration> {
        let start_time = Instant::now();
        
        // 模拟搜索逻辑（实际实现中会调用真实的搜索服务）
        sleep(Duration::from_millis(50 + (query.len() as u64 * 5))).await;
        
        let elapsed = start_time.elapsed();
        Ok(elapsed)
    }

    /// 执行单次搜索性能测试
    pub async fn benchmark_single_search(&self, query: &str) -> Result<Duration> {
        let start_time = Instant::now();
        
        // 执行搜索
        let _result = self.simulate_search_request(query).await?;
        
        let elapsed = start_time.elapsed();
        
        // 记录指标
        if let Ok(mut collector) = self.metrics_collector.lock() {
            collector.record_latency(elapsed);
        }
        
        Ok(elapsed)
    }

    /// 执行并发搜索性能测试
    pub async fn benchmark_concurrent_search(&self, concurrent_users: usize, queries_per_user: usize) -> Result<PerformanceMetrics> {
        tracing::info!("开始并发搜索性能测试: {}用户 x {}查询", concurrent_users, queries_per_user);
        
        let start_time = Instant::now();
        let mut collector = MetricsCollector::new();
        collector.start();
        
        let mut tasks = Vec::new();
        
        for user_id in 0..concurrent_users {
            let queries = vec![
                format!("用户{}搜索测试", user_id),
                format!("消息内容{}", user_id % 10),
                format!("关键词{}", user_id % 5),
            ];
            
            let task = tokio::spawn(async move {
                let mut user_latencies = Vec::new();
                let mut user_errors = 0;
                
                for i in 0..queries_per_user {
                    let query = &queries[i % queries.len()];
                    let request_start = Instant::now();
                    
                    // 模拟搜索请求
                    match simulate_search_operation(query).await {
                        Ok(_) => {
                            let latency = request_start.elapsed();
                            user_latencies.push(latency);
                        }
                        Err(_) => {
                            user_errors += 1;
                        }
                    }
                    
                    // 模拟用户思考时间
                    sleep(Duration::from_millis(100)).await;
                }
                
                (user_id, user_latencies, user_errors)
            });
            
            tasks.push(task);
        }
        
        // 等待所有任务完成
        let results = futures::future::join_all(tasks).await;
        
        // 收集所有结果
        let mut all_latencies = Vec::new();
        let mut total_errors = 0;
        
        for result in results {
            let (user_id, latencies, errors) = result.expect("并发任务执行失败");
            all_latencies.extend(latencies);
            total_errors += errors;
            tracing::debug!("用户{}: {}次查询, {}次错误", user_id, queries_per_user, errors);
        }
        
        let total_elapsed = start_time.elapsed();
        
        // 计算性能指标
        let latency_stats = calculate_latency_statistics(&all_latencies);
        let throughput_qps = all_latencies.len() as f64 / total_elapsed.as_secs_f64();
        let error_rate = total_errors as f64 / (concurrent_users * queries_per_user) as f64;
        
        let metrics = PerformanceMetrics {
            latency_stats,
            throughput_qps,
            cache_hit_ratio: 0.0, // TODO: 实现缓存命中率统计
            error_rate,
            resource_usage: crate::message_search_test_framework::ResourceUsage {
                cpu_usage_percent: 0.0,
                memory_usage_mb: 0.0,
                network_io_mbps: 0.0,
                db_connections: 0,
            },
        };
        
        tracing::info!("并发搜索性能测试完成: QPS={:.2}, P99延迟={:.2}ms, 错误率={:.2}%", 
            throughput_qps, latency_stats.p99_ms, error_rate * 100.0);
        
        Ok(metrics)
    }
}

/// 模拟搜索操作
async fn simulate_search_operation(query: &str) -> Result<String> {
    // 模拟不同复杂度的搜索
    let complexity_factor = match query.len() {
        0..=5 => 1,
        6..=15 => 2,
        _ => 3,
    };
    
    let base_latency = 20; // 基础延迟20ms
    let variable_latency = complexity_factor * 10; // 复杂度相关延迟
    
    sleep(Duration::from_millis(base_latency + variable_latency)).await;
    
    // 模拟5%的错误率
    if rand::random::<f64>() < 0.05 {
        return Err(anyhow::anyhow!("模拟搜索错误"));
    }
    
    Ok(format!("搜索结果: {}", query))
}

/// 计算延迟统计信息
fn calculate_latency_statistics(latencies: &[Duration]) -> LatencyStats {
    if latencies.is_empty() {
        return LatencyStats {
            mean_ms: 0.0,
            p50_ms: 0.0,
            p95_ms: 0.0,
            p99_ms: 0.0,
            min_ms: 0.0,
            max_ms: 0.0,
        };
    }
    
    let mut sorted_latencies = latencies.to_vec();
    sorted_latencies.sort();
    
    let mean_ms = sorted_latencies.iter()
        .map(|d| d.as_millis() as f64)
        .sum::<f64>() / sorted_latencies.len() as f64;
    
    let p50_ms = percentile(&sorted_latencies, 0.5);
    let p95_ms = percentile(&sorted_latencies, 0.95);
    let p99_ms = percentile(&sorted_latencies, 0.99);
    let min_ms = sorted_latencies.first().unwrap().as_millis() as f64;
    let max_ms = sorted_latencies.last().unwrap().as_millis() as f64;
    
    LatencyStats {
        mean_ms,
        p50_ms,
        p95_ms,
        p99_ms,
        min_ms,
        max_ms,
    }
}

/// 计算百分位数
fn percentile(sorted_latencies: &[Duration], percentile: f64) -> f64 {
    if sorted_latencies.is_empty() {
        return 0.0;
    }
    
    let index = (sorted_latencies.len() as f64 * percentile) as usize;
    let index = index.min(sorted_latencies.len() - 1);
    sorted_latencies[index].as_millis() as f64
}

/// 搜索延迟P99测试
#[tokio::test]
async fn test_search_latency_p99() {
    tracing::info!("开始搜索延迟P99测试");
    
    let mut test_suite = MessageSearchPerformanceTests::new();
    test_suite.setup().await.expect("性能测试环境设置失败");
    
    let test_queries = vec![
        "简单搜索",
        "复杂的中文搜索查询包含多个关键词",
        "PostgreSQL全文搜索功能测试",
        "企业级聊天应用架构设计模式",
    ];
    
    let mut all_latencies = Vec::new();
    
    // 执行多次搜索测试
    for _ in 0..100 {
        for query in &test_queries {
            let latency = test_suite.benchmark_single_search(query).await
                .expect("单次搜索测试失败");
            all_latencies.push(latency);
        }
    }
    
    // 计算延迟统计
    let stats = calculate_latency_statistics(&all_latencies);
    
    // 验证P99延迟要求
    assert!(stats.p99_ms < 200.0, 
        "P99延迟不达标，实际: {:.2}ms, 要求: <200ms", stats.p99_ms);
    
    // 验证P95延迟要求
    assert!(stats.p95_ms < 100.0, 
        "P95延迟不达标，实际: {:.2}ms, 要求: <100ms", stats.p95_ms);
    
    tracing::info!("搜索延迟测试完成: P99={:.2}ms, P95={:.2}ms, 平均={:.2}ms", 
        stats.p99_ms, stats.p95_ms, stats.mean_ms);
}

/// 缓存命中性能测试
#[tokio::test]
async fn test_cache_hit_performance() {
    tracing::info!("开始缓存命中性能测试");
    
    let mut test_suite = MessageSearchPerformanceTests::new();
    test_suite.setup().await.expect("性能测试环境设置失败");
    
    let cache_query = "热门搜索词";
    
    // 第一次搜索（缓存未命中）
    let cold_start = Instant::now();
    let _ = test_suite.simulate_search_request(cache_query).await
        .expect("冷启动搜索失败");
    let cold_latency = cold_start.elapsed();
    
    // 模拟缓存预热
    sleep(Duration::from_millis(10)).await;
    
    // 后续搜索（缓存命中）
    let mut cache_hit_latencies = Vec::new();
    for _ in 0..10 {
        let hit_start = Instant::now();
        // 模拟缓存命中（更快的响应）
        sleep(Duration::from_millis(5)).await;
        let hit_latency = hit_start.elapsed();
        cache_hit_latencies.push(hit_latency);
    }
    
    let average_cache_hit_latency = cache_hit_latencies.iter()
        .map(|d| d.as_millis() as f64)
        .sum::<f64>() / cache_hit_latencies.len() as f64;
    
    // 验证缓存命中性能
    assert!(average_cache_hit_latency < 10.0, 
        "缓存命中响应时间不达标，实际: {:.2}ms, 要求: <10ms", average_cache_hit_latency);
    
    // 验证缓存效果
    assert!(average_cache_hit_latency < cold_latency.as_millis() as f64 / 2.0, 
        "缓存命中应该显著快于冷启动");
    
    tracing::info!("缓存命中性能测试完成: 冷启动={:.2}ms, 缓存命中={:.2}ms", 
        cold_latency.as_millis() as f64, average_cache_hit_latency);
}

/// 百万并发搜索测试
#[tokio::test]
async fn test_million_concurrent_searches() {
    tracing::info!("开始百万并发搜索测试（简化版）");
    
    let mut test_suite = MessageSearchPerformanceTests::new();
    test_suite.setup().await.expect("性能测试环境设置失败");
    
    // 由于测试环境限制，使用较小的并发数模拟
    let concurrent_users = 1000; // 1000个并发用户
    let queries_per_user = 10;   // 每用户10次查询
    
    let metrics = test_suite.benchmark_concurrent_search(concurrent_users, queries_per_user).await
        .expect("并发搜索测试失败");
    
    // 验证并发性能要求
    assert!(metrics.latency_stats.p99_ms < 500.0, 
        "并发P99延迟不达标，实际: {:.2}ms, 要求: <500ms", metrics.latency_stats.p99_ms);
    
    assert!(metrics.throughput_qps > 100.0, 
        "吞吐量不达标，实际: {:.2} QPS, 要求: >100 QPS", metrics.throughput_qps);
    
    assert!(metrics.error_rate < 0.01, 
        "错误率过高，实际: {:.2}%, 要求: <1%", metrics.error_rate * 100.0);
    
    tracing::info!("百万并发搜索测试完成: QPS={:.2}, P99={:.2}ms, 错误率={:.2}%", 
        metrics.throughput_qps, metrics.latency_stats.p99_ms, metrics.error_rate * 100.0);
}

/// 吞吐量基准测试
#[tokio::test]
async fn test_throughput_benchmark() {
    tracing::info!("开始吞吐量基准测试");
    
    let mut test_suite = MessageSearchPerformanceTests::new();
    test_suite.setup().await.expect("性能测试环境设置失败");
    
    let test_duration = Duration::from_secs(10); // 10秒测试
    let start_time = Instant::now();
    let mut request_count = 0;
    let mut error_count = 0;
    
    // 持续发送请求直到测试时间结束
    while start_time.elapsed() < test_duration {
        let batch_size = 10;
        let mut batch_tasks = Vec::new();
        
        for i in 0..batch_size {
            let query = format!("吞吐量测试查询{}", request_count + i);
            let task = tokio::spawn(async move {
                simulate_search_operation(&query).await
            });
            batch_tasks.push(task);
        }
        
        // 等待批次完成
        let batch_results = futures::future::join_all(batch_tasks).await;
        
        for result in batch_results {
            request_count += 1;
            if result.is_err() || result.unwrap().is_err() {
                error_count += 1;
            }
        }
        
        // 短暂休息避免过载
        sleep(Duration::from_millis(10)).await;
    }
    
    let actual_duration = start_time.elapsed();
    let throughput_qps = request_count as f64 / actual_duration.as_secs_f64();
    let error_rate = error_count as f64 / request_count as f64;
    
    // 验证吞吐量要求
    assert!(throughput_qps > 50.0, 
        "吞吐量不达标，实际: {:.2} QPS, 要求: >50 QPS", throughput_qps);
    
    assert!(error_rate < 0.1, 
        "错误率过高，实际: {:.2}%, 要求: <10%", error_rate * 100.0);
    
    tracing::info!("吞吐量基准测试完成: {:.2} QPS, 错误率: {:.2}%, 测试时长: {:.2}s", 
        throughput_qps, error_rate * 100.0, actual_duration.as_secs_f64());
}

/// 负载递增测试
#[tokio::test]
async fn test_load_ramp_up() {
    tracing::info!("开始负载递增测试");
    
    let mut test_suite = MessageSearchPerformanceTests::new();
    test_suite.setup().await.expect("性能测试环境设置失败");
    
    let load_levels = vec![10, 50, 100, 200, 500]; // 不同的并发级别
    let mut results = Vec::new();
    
    for concurrent_users in load_levels {
        tracing::info!("测试并发级别: {}", concurrent_users);
        
        let metrics = test_suite.benchmark_concurrent_search(concurrent_users, 5).await
            .expect("负载测试失败");
        
        results.push((concurrent_users, metrics));
        
        // 验证系统在各个负载级别下的表现
        assert!(metrics.latency_stats.p99_ms < 1000.0, 
            "负载{}下P99延迟过高: {:.2}ms", concurrent_users, metrics.latency_stats.p99_ms);
        
        assert!(metrics.error_rate < 0.05, 
            "负载{}下错误率过高: {:.2}%", concurrent_users, metrics.error_rate * 100.0);
        
        // 系统恢复时间
        sleep(Duration::from_secs(2)).await;
    }
    
    // 分析性能趋势
    for (i, (users, metrics)) in results.iter().enumerate() {
        tracing::info!("负载级别{}: QPS={:.2}, P99={:.2}ms, 错误率={:.2}%", 
            users, metrics.throughput_qps, metrics.latency_stats.p99_ms, metrics.error_rate * 100.0);
        
        if i > 0 {
            let prev_metrics = &results[i-1].1;
            // 验证性能退化是否在可接受范围内
            let latency_increase = metrics.latency_stats.p99_ms / prev_metrics.latency_stats.p99_ms;
            assert!(latency_increase < 3.0, 
                "延迟增长过快，从{}用户到{}用户增长了{:.2}倍", 
                results[i-1].0, users, latency_increase);
        }
    }
    
    tracing::info!("负载递增测试完成");
}
