// 高并发压力测试用例实现
// 基于docs/stress_test_scenarios.md中定义的测试场景

use futures_util::{SinkExt, StreamExt};
use reqwest::Client;
use serde_json::json;
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, AtomicU64, Ordering};
use std::time::{Duration, Instant};
use tokio::sync::Semaphore;
use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};

/// 测试配置常量
const BASE_URL: &str = "http://127.0.0.1:3000";
const WS_URL: &str = "ws://127.0.0.1:3000/ws";
const TEST_USER: &str = "testuser456";
const TEST_PASSWORD: &str = "password123";

/// 压力测试结果统计
#[derive(Debug, <PERSON><PERSON>)]
pub struct StressTestResult {
    pub test_name: String,
    pub total_connections: u64,
    pub successful_connections: u64,
    pub failed_connections: u64,
    pub total_messages_sent: u64,
    pub total_messages_received: u64,
    pub avg_connection_time_ms: f64,
    pub p95_connection_time_ms: f64,
    pub avg_message_latency_ms: f64,
    pub p95_message_latency_ms: f64,
    pub test_duration_ms: u64,
    pub peak_memory_usage_mb: u64,
    pub peak_cpu_usage_percent: f64,
    pub success_rate: f64,
}

/// 测试认证信息
#[derive(Clone)]
pub struct TestAuth {
    pub token: String,
    pub client: Client,
}

/// 获取测试认证token
pub async fn get_test_auth() -> Result<TestAuth, Box<dyn std::error::Error>> {
    let client = Client::builder()
        .timeout(Duration::from_secs(30))
        .pool_max_idle_per_host(100)
        .pool_idle_timeout(Duration::from_secs(90))
        .build()?;

    let login_response = client
        .post(&format!("{}/api/auth/login", BASE_URL))
        .json(&json!({
            "username": TEST_USER,
            "password": TEST_PASSWORD
        }))
        .send()
        .await?;

    if !login_response.status().is_success() {
        return Err(format!("登录失败: {}", login_response.status()).into());
    }

    let login_data: serde_json::Value = login_response.json().await?;
    let token = login_data["data"]["access_token"]
        .as_str()
        .ok_or("无法获取认证token")?
        .to_string();

    Ok(TestAuth { token, client })
}

/// 测试用例1: 渐进式并发连接测试
pub async fn test_progressive_concurrent_connections()
-> Result<StressTestResult, Box<dyn std::error::Error>> {
    println!("🚀 开始渐进式并发连接测试...");

    let auth = get_test_auth().await?;
    let start_time = Instant::now();

    // 性能指标收集
    let total_connections = Arc::new(AtomicU64::new(0));
    let successful_connections = Arc::new(AtomicU64::new(0));
    let failed_connections = Arc::new(AtomicU64::new(0));
    let connection_times = Arc::new(tokio::sync::Mutex::new(Vec::new()));

    // 渐进式增加连接数: 100 -> 500 -> 1000 -> 2500 -> 5000 -> 10000
    let connection_steps = vec![100, 500, 1000, 2500, 5000, 10000];

    for &target_connections in &connection_steps {
        println!("📊 测试 {} 个并发连接...", target_connections);

        let semaphore = Arc::new(Semaphore::new(target_connections.min(1000)));
        let mut handles = Vec::new();

        for i in 0..target_connections {
            let token = auth.token.clone();
            let semaphore = semaphore.clone();
            let total_connections = total_connections.clone();
            let successful_connections = successful_connections.clone();
            let failed_connections = failed_connections.clone();
            let connection_times = connection_times.clone();

            let handle = tokio::spawn(async move {
                let _permit = semaphore.acquire().await.expect("获取信号量失败");
                let connection_start = Instant::now();

                total_connections.fetch_add(1, Ordering::Relaxed);

                let ws_url = format!("{}?token={}", WS_URL, token);

                match connect_async(&ws_url).await {
                    Ok((mut ws_stream, _)) => {
                        let connection_time = connection_start.elapsed();
                        connection_times
                            .lock()
                            .await
                            .push(connection_time.as_millis() as f64);
                        successful_connections.fetch_add(1, Ordering::Relaxed);

                        // 发送测试消息
                        let test_message = json!({
                            "type": "chat_message",
                            "room_id": 1,
                            "content": format!("渐进式测试消息 {}", i)
                        });

                        if ws_stream
                            .send(Message::Text(test_message.to_string().into()))
                            .await
                            .is_ok()
                        {
                            // 等待响应
                            let _ = tokio::time::timeout(Duration::from_secs(2), ws_stream.next())
                                .await;
                        }

                        // 保持连接30秒
                        tokio::time::sleep(Duration::from_secs(30)).await;
                        let _ = ws_stream.close(None).await;
                    }
                    Err(_) => {
                        failed_connections.fetch_add(1, Ordering::Relaxed);
                    }
                }
            });

            handles.push(handle);
        }

        // 等待当前批次完成
        for handle in handles {
            let _ = handle.await;
        }

        // 等待30秒再进行下一批次
        tokio::time::sleep(Duration::from_secs(30)).await;
    }

    let test_duration = start_time.elapsed();
    let connection_times_vec = connection_times.lock().await;

    let avg_connection_time = if connection_times_vec.is_empty() {
        0.0
    } else {
        connection_times_vec.iter().sum::<f64>() / (connection_times_vec.len() as f64)
    };

    // 计算P95连接时间
    let mut sorted_times = connection_times_vec.clone();
    sorted_times.sort_by(|a, b| a.partial_cmp(b).unwrap());
    let p95_index = ((sorted_times.len() as f64) * 0.95) as usize;
    let p95_connection_time = sorted_times.get(p95_index).copied().unwrap_or(0.0);

    let total_conn = total_connections.load(Ordering::Relaxed);
    let success_conn = successful_connections.load(Ordering::Relaxed);
    let success_rate = if total_conn > 0 {
        ((success_conn as f64) / (total_conn as f64)) * 100.0
    } else {
        0.0
    };

    let result = StressTestResult {
        test_name: "渐进式并发连接测试".to_string(),
        total_connections: total_conn,
        successful_connections: success_conn,
        failed_connections: failed_connections.load(Ordering::Relaxed),
        total_messages_sent: success_conn, // 每个成功连接发送一条消息
        total_messages_received: 0,        // 简化版本不统计接收消息
        avg_connection_time_ms: avg_connection_time,
        p95_connection_time_ms: p95_connection_time,
        avg_message_latency_ms: 0.0,
        p95_message_latency_ms: 0.0,
        test_duration_ms: test_duration.as_millis() as u64,
        peak_memory_usage_mb: 0,     // 需要额外监控工具
        peak_cpu_usage_percent: 0.0, // 需要额外监控工具
        success_rate,
    };

    println!("✅ 渐进式并发连接测试完成");
    println!("   总连接数: {}", result.total_connections);
    println!("   成功连接数: {}", result.successful_connections);
    println!("   成功率: {:.2}%", result.success_rate);
    println!("   平均连接时间: {:.2}ms", result.avg_connection_time_ms);
    println!("   P95连接时间: {:.2}ms", result.p95_connection_time_ms);

    Ok(result)
}

/// 测试用例2: 消息广播性能测试
pub async fn test_message_broadcast_performance()
-> Result<StressTestResult, Box<dyn std::error::Error>> {
    println!("🚀 开始消息广播性能测试...");

    let auth = get_test_auth().await?;
    let start_time = Instant::now();

    // 建立5000个WebSocket连接
    let target_connections = 5000;
    let messages_per_second = 100;
    let test_duration_seconds = 600; // 10分钟

    let total_connections = Arc::new(AtomicU64::new(0));
    let successful_connections = Arc::new(AtomicU64::new(0));
    let failed_connections = Arc::new(AtomicU64::new(0));
    let messages_sent = Arc::new(AtomicU64::new(0));
    let messages_received = Arc::new(AtomicU64::new(0));

    println!("📊 建立 {} 个WebSocket连接...", target_connections);

    let semaphore = Arc::new(Semaphore::new(target_connections.min(1000)));
    let mut handles = Vec::new();
    let stop_flag = Arc::new(AtomicBool::new(false));

    // 建立连接并开始消息广播测试
    for i in 0..target_connections {
        let token = auth.token.clone();
        let semaphore = semaphore.clone();
        let total_connections = total_connections.clone();
        let successful_connections = successful_connections.clone();
        let failed_connections = failed_connections.clone();
        let messages_sent = messages_sent.clone();
        let messages_received = messages_received.clone();
        let stop_flag = stop_flag.clone();

        let handle = tokio::spawn(async move {
            let _permit = semaphore.acquire().await.expect("获取信号量失败");

            total_connections.fetch_add(1, Ordering::Relaxed);

            let ws_url = format!("{}?token={}", WS_URL, token);

            match connect_async(&ws_url).await {
                Ok((mut ws_stream, _)) => {
                    successful_connections.fetch_add(1, Ordering::Relaxed);

                    // 消息发送循环
                    let mut message_count = 0;
                    while !stop_flag.load(Ordering::Relaxed) {
                        let test_message = json!({
                            "type": "chat_message",
                            "room_id": 1,
                            "content": format!("广播测试消息 {} - {}", i, message_count)
                        });

                        if ws_stream
                            .send(Message::Text(test_message.to_string().into()))
                            .await
                            .is_ok()
                        {
                            messages_sent.fetch_add(1, Ordering::Relaxed);

                            // 尝试接收响应
                            match tokio::time::timeout(Duration::from_millis(100), ws_stream.next())
                                .await
                            {
                                Ok(Some(Ok(_))) => {
                                    messages_received.fetch_add(1, Ordering::Relaxed);
                                }
                                _ => {}
                            }
                        } else {
                            break;
                        }

                        message_count += 1;

                        // 控制消息发送频率
                        tokio::time::sleep(Duration::from_millis(
                            1000 / (messages_per_second as u64),
                        ))
                        .await;
                    }

                    let _ = ws_stream.close(None).await;
                }
                Err(_) => {
                    failed_connections.fetch_add(1, Ordering::Relaxed);
                }
            }
        });

        handles.push(handle);
    }

    // 等待连接建立完成
    tokio::time::sleep(Duration::from_secs(30)).await;

    // 运行测试指定时间
    tokio::time::sleep(Duration::from_secs(test_duration_seconds)).await;

    // 停止测试
    stop_flag.store(true, Ordering::Relaxed);

    // 等待所有任务完成
    for handle in handles {
        let _ = handle.await;
    }

    let test_duration = start_time.elapsed();
    let total_conn = total_connections.load(Ordering::Relaxed);
    let success_conn = successful_connections.load(Ordering::Relaxed);
    let success_rate = if total_conn > 0 {
        ((success_conn as f64) / (total_conn as f64)) * 100.0
    } else {
        0.0
    };

    let result = StressTestResult {
        test_name: "消息广播性能测试".to_string(),
        total_connections: total_conn,
        successful_connections: success_conn,
        failed_connections: failed_connections.load(Ordering::Relaxed),
        total_messages_sent: messages_sent.load(Ordering::Relaxed),
        total_messages_received: messages_received.load(Ordering::Relaxed),
        avg_connection_time_ms: 0.0,
        p95_connection_time_ms: 0.0,
        avg_message_latency_ms: 0.0,
        p95_message_latency_ms: 0.0,
        test_duration_ms: test_duration.as_millis() as u64,
        peak_memory_usage_mb: 0,
        peak_cpu_usage_percent: 0.0,
        success_rate,
    };

    println!("✅ 消息广播性能测试完成");
    println!("   总连接数: {}", result.total_connections);
    println!("   成功连接数: {}", result.successful_connections);
    println!("   成功率: {:.2}%", result.success_rate);
    println!("   发送消息数: {}", result.total_messages_sent);
    println!("   接收消息数: {}", result.total_messages_received);

    Ok(result)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    #[ignore] // 需要手动运行，因为需要服务器运行
    async fn test_progressive_connections() {
        let result = test_progressive_concurrent_connections().await;
        assert!(result.is_ok());

        let result = result.unwrap();
        assert!(result.success_rate > 90.0, "连接成功率应该大于90%");
        assert!(
            result.avg_connection_time_ms < 1000.0,
            "平均连接时间应该小于1秒"
        );
    }

    #[tokio::test]
    #[ignore] // 需要手动运行，因为需要服务器运行
    async fn test_message_broadcast() {
        let result = test_message_broadcast_performance().await;
        assert!(result.is_ok());

        let result = result.unwrap();
        assert!(result.success_rate > 95.0, "连接成功率应该大于95%");
        assert!(result.total_messages_sent > 0, "应该发送了消息");
    }
}
