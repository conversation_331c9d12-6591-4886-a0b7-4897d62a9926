//! # WebSocket多用户并发连接测试 - 任务8.4
//!
//! 测试WebSocket系统在多用户并发场景下的性能和稳定性
//!
//! 【功能特性】:
//! - 多用户并发连接测试
//! - 性能指标收集和分析
//! - 连接稳定性验证
//! - 消息吞吐量测试
//! - 系统资源监控
//! - 错误恢复机制测试

use axum::extract::ws::Message;
use futures_util::{SinkExt, StreamExt};
use serde_json::json;
use std::{
    collections::HashMap,
    sync::{
        Arc,
        atomic::{AtomicU64, Ordering},
    },
    time::{Duration, Instant},
};
use tokio::{
    sync::{Barrier, RwLock, mpsc},
    time::timeout,
};
use tokio_tungstenite::{connect_async, tungstenite::protocol::Message as TungsteniteMessage};
use tracing::{error, info, warn};
use uuid::Uuid;

/// 并发测试配置
///
/// 【功能】: 配置多用户并发WebSocket测试的各项参数
#[derive(Debug, Clone)]
pub struct ConcurrentTestConfig {
    /// 并发用户数量
    pub concurrent_users: usize,
    /// 每个用户发送的消息数量
    pub messages_per_user: usize,
    /// 测试持续时间（秒）
    pub test_duration_secs: u64,
    /// 连接超时时间（秒）
    pub connection_timeout_secs: u64,
    /// 消息发送间隔（毫秒）
    pub message_interval_ms: u64,
    /// 最大重连次数
    pub max_reconnect_attempts: u32,
    /// 连接建立延迟（毫秒）- 避免同时连接造成的服务器压力
    pub connection_stagger_ms: u64,
    /// 心跳间隔（秒）
    pub heartbeat_interval_secs: u64,
    /// 消息确认超时（毫秒）
    pub message_ack_timeout_ms: u64,
    /// 性能监控间隔（秒）
    pub metrics_collection_interval_secs: u64,
}

impl Default for ConcurrentTestConfig {
    fn default() -> Self {
        Self {
            concurrent_users: 10,
            messages_per_user: 50,
            test_duration_secs: 60,
            connection_timeout_secs: 10,
            message_interval_ms: 100,
            max_reconnect_attempts: 3,
            connection_stagger_ms: 50,
            heartbeat_interval_secs: 30,
            message_ack_timeout_ms: 5000,
            metrics_collection_interval_secs: 5,
        }
    }
}

impl ConcurrentTestConfig {
    /// 创建轻量级测试配置
    pub fn light() -> Self {
        Self {
            concurrent_users: 5,
            messages_per_user: 20,
            test_duration_secs: 30,
            ..Default::default()
        }
    }

    /// 创建中等强度测试配置
    pub fn medium() -> Self {
        Self {
            concurrent_users: 20,
            messages_per_user: 100,
            test_duration_secs: 120,
            ..Default::default()
        }
    }

    /// 创建高强度压力测试配置
    pub fn stress() -> Self {
        Self {
            concurrent_users: 50,
            messages_per_user: 200,
            test_duration_secs: 300,
            connection_timeout_secs: 15,
            message_interval_ms: 50,
            ..Default::default()
        }
    }
}

/// 性能指标收集器
///
/// 【功能】: 收集和统计WebSocket并发测试的各项性能指标
#[derive(Debug, Default)]
pub struct PerformanceMetrics {
    /// 成功连接数
    pub successful_connections: AtomicU64,
    /// 失败连接数
    pub failed_connections: AtomicU64,
    /// 发送的消息总数
    pub messages_sent: AtomicU64,
    /// 接收的消息总数
    pub messages_received: AtomicU64,
    /// 连接错误数
    pub connection_errors: AtomicU64,
    /// 消息错误数
    pub message_errors: AtomicU64,
    /// 重连次数
    pub reconnection_count: AtomicU64,
    /// 连接延迟统计（毫秒）
    pub connection_latencies: Arc<RwLock<Vec<u64>>>,
    /// 消息延迟统计（毫秒）
    pub message_latencies: Arc<RwLock<Vec<u64>>>,
    /// 心跳响应时间统计（毫秒）
    pub heartbeat_latencies: Arc<RwLock<Vec<u64>>>,
    /// 并发连接峰值
    pub peak_concurrent_connections: AtomicU64,
    /// 当前活跃连接数
    pub active_connections: AtomicU64,
    /// 消息丢失数
    pub messages_lost: AtomicU64,
    /// 连接断开数
    pub connections_dropped: AtomicU64,
    /// 系统资源使用情况
    pub memory_usage_mb: Arc<RwLock<Vec<f64>>>,
    /// CPU使用率
    pub cpu_usage_percent: Arc<RwLock<Vec<f64>>>,
    /// 网络带宽使用（字节/秒）
    pub bandwidth_usage_bps: Arc<RwLock<Vec<u64>>>,
}

impl PerformanceMetrics {
    /// 记录连接延迟
    pub async fn record_connection_latency(&self, latency_ms: u64) {
        let mut latencies = self.connection_latencies.write().await;
        latencies.push(latency_ms);

        // 限制存储的延迟数据量，避免内存过度使用
        if latencies.len() > 10000 {
            latencies.drain(0..1000);
        }
    }

    /// 记录消息延迟
    pub async fn record_message_latency(&self, latency_ms: u64) {
        let mut latencies = self.message_latencies.write().await;
        latencies.push(latency_ms);

        if latencies.len() > 10000 {
            latencies.drain(0..1000);
        }
    }

    /// 记录心跳延迟
    pub async fn record_heartbeat_latency(&self, latency_ms: u64) {
        let mut latencies = self.heartbeat_latencies.write().await;
        latencies.push(latency_ms);

        if latencies.len() > 1000 {
            latencies.drain(0..100);
        }
    }

    /// 更新并发连接峰值
    pub fn update_peak_connections(&self, current_connections: u64) {
        let mut peak = self.peak_concurrent_connections.load(Ordering::Relaxed);
        while current_connections > peak {
            match self.peak_concurrent_connections.compare_exchange_weak(
                peak,
                current_connections,
                Ordering::Relaxed,
                Ordering::Relaxed,
            ) {
                Ok(_) => {
                    break;
                }
                Err(new_peak) => {
                    peak = new_peak;
                }
            }
        }
    }

    /// 增加活跃连接数
    pub fn increment_active_connections(&self) -> u64 {
        let current = self.active_connections.fetch_add(1, Ordering::Relaxed) + 1;
        self.update_peak_connections(current);
        current
    }

    /// 减少活跃连接数
    pub fn decrement_active_connections(&self) -> u64 {
        self.active_connections
            .fetch_sub(1, Ordering::Relaxed)
            .saturating_sub(1)
    }

    /// 记录系统资源使用情况
    pub async fn record_system_metrics(
        &self,
        memory_mb: f64,
        cpu_percent: f64,
        bandwidth_bps: u64,
    ) {
        {
            let mut memory = self.memory_usage_mb.write().await;
            memory.push(memory_mb);
            if memory.len() > 1000 {
                memory.drain(0..100);
            }
        }

        {
            let mut cpu = self.cpu_usage_percent.write().await;
            cpu.push(cpu_percent);
            if cpu.len() > 1000 {
                cpu.drain(0..100);
            }
        }

        {
            let mut bandwidth = self.bandwidth_usage_bps.write().await;
            bandwidth.push(bandwidth_bps);
            if bandwidth.len() > 1000 {
                bandwidth.drain(0..100);
            }
        }
    }

    /// 计算统计信息
    pub async fn calculate_stats(&self) -> TestStatistics {
        let conn_latencies = self.connection_latencies.read().await;
        let msg_latencies = self.message_latencies.read().await;
        let heartbeat_latencies = self.heartbeat_latencies.read().await;
        let memory_usage = self.memory_usage_mb.read().await;
        let cpu_usage = self.cpu_usage_percent.read().await;
        let bandwidth_usage = self.bandwidth_usage_bps.read().await;

        TestStatistics {
            total_connections: self.successful_connections.load(Ordering::Relaxed),
            failed_connections: self.failed_connections.load(Ordering::Relaxed),
            total_messages_sent: self.messages_sent.load(Ordering::Relaxed),
            total_messages_received: self.messages_received.load(Ordering::Relaxed),
            connection_errors: self.connection_errors.load(Ordering::Relaxed),
            message_errors: self.message_errors.load(Ordering::Relaxed),
            reconnection_count: self.reconnection_count.load(Ordering::Relaxed),
            avg_connection_latency_ms: calculate_average(&conn_latencies),
            avg_message_latency_ms: calculate_average(&msg_latencies),
            p95_connection_latency_ms: calculate_percentile(&conn_latencies, 95.0),
            p95_message_latency_ms: calculate_percentile(&msg_latencies, 95.0),
            success_rate: self.calculate_success_rate(),
            peak_concurrent_connections: self.peak_concurrent_connections.load(Ordering::Relaxed),
            active_connections: self.active_connections.load(Ordering::Relaxed),
            messages_lost: self.messages_lost.load(Ordering::Relaxed),
            connections_dropped: self.connections_dropped.load(Ordering::Relaxed),
            avg_heartbeat_latency_ms: calculate_average(&heartbeat_latencies),
            p95_heartbeat_latency_ms: calculate_percentile(&heartbeat_latencies, 95.0),
            avg_memory_usage_mb: calculate_average_f64(&memory_usage),
            peak_memory_usage_mb: memory_usage.iter().fold(0.0, |a, &b| a.max(b)),
            avg_cpu_usage_percent: calculate_average_f64(&cpu_usage),
            peak_cpu_usage_percent: cpu_usage.iter().fold(0.0, |a, &b| a.max(b)),
            avg_bandwidth_usage_bps: calculate_average(&bandwidth_usage),
            peak_bandwidth_usage_bps: bandwidth_usage.iter().fold(0, |a, &b| a.max(b)),
            message_loss_rate: self.calculate_message_loss_rate(),
            connection_stability_rate: self.calculate_connection_stability_rate(),
        }
    }

    /// 计算成功率
    fn calculate_success_rate(&self) -> f64 {
        let successful = self.successful_connections.load(Ordering::Relaxed) as f64;
        let total = successful + (self.failed_connections.load(Ordering::Relaxed) as f64);
        if total > 0.0 {
            (successful / total) * 100.0
        } else {
            0.0
        }
    }

    /// 计算消息丢失率
    fn calculate_message_loss_rate(&self) -> f64 {
        let sent = self.messages_sent.load(Ordering::Relaxed) as f64;
        let lost = self.messages_lost.load(Ordering::Relaxed) as f64;
        if sent > 0.0 {
            (lost / sent) * 100.0
        } else {
            0.0
        }
    }

    /// 计算连接稳定性率
    fn calculate_connection_stability_rate(&self) -> f64 {
        let total = self.successful_connections.load(Ordering::Relaxed) as f64;
        let dropped = self.connections_dropped.load(Ordering::Relaxed) as f64;
        if total > 0.0 {
            ((total - dropped) / total) * 100.0
        } else {
            0.0
        }
    }
}

/// 测试统计结果
///
/// 【功能】: 包含WebSocket并发测试的完整统计信息
#[derive(Debug)]
pub struct TestStatistics {
    // 基础连接统计
    pub total_connections: u64,
    pub failed_connections: u64,
    pub peak_concurrent_connections: u64,
    pub active_connections: u64,
    pub connections_dropped: u64,

    // 消息统计
    pub total_messages_sent: u64,
    pub total_messages_received: u64,
    pub messages_lost: u64,

    // 错误统计
    pub connection_errors: u64,
    pub message_errors: u64,
    pub reconnection_count: u64,

    // 延迟统计
    pub avg_connection_latency_ms: f64,
    pub avg_message_latency_ms: f64,
    pub avg_heartbeat_latency_ms: f64,
    pub p95_connection_latency_ms: f64,
    pub p95_message_latency_ms: f64,
    pub p95_heartbeat_latency_ms: f64,

    // 系统资源统计
    pub avg_memory_usage_mb: f64,
    pub peak_memory_usage_mb: f64,
    pub avg_cpu_usage_percent: f64,
    pub peak_cpu_usage_percent: f64,
    pub avg_bandwidth_usage_bps: f64,
    pub peak_bandwidth_usage_bps: u64,

    // 计算指标
    pub success_rate: f64,
    pub message_loss_rate: f64,
    pub connection_stability_rate: f64,
}

/// 用户会话信息
#[derive(Debug, Clone)]
pub struct UserSession {
    pub user_id: Uuid,
    pub username: String,
    pub token: String,
}

/// WebSocket并发测试器
pub struct WebSocketConcurrentTester {
    config: ConcurrentTestConfig,
    metrics: Arc<PerformanceMetrics>,
    base_url: String,
}

impl WebSocketConcurrentTester {
    /// 创建新的并发测试器
    pub fn new(base_url: String, config: ConcurrentTestConfig) -> Self {
        Self {
            config,
            metrics: Arc::new(PerformanceMetrics::default()),
            base_url,
        }
    }

    /// 执行并发连接测试
    pub async fn run_concurrent_test(&self) -> Result<TestStatistics, Box<dyn std::error::Error>> {
        info!("开始WebSocket并发连接测试");
        info!("测试配置: {:?}", self.config);

        // 创建测试用户会话
        let user_sessions = self.create_test_users().await?;
        info!("创建了{}个测试用户", user_sessions.len());

        // 创建同步屏障，确保所有连接同时开始
        let barrier = Arc::new(Barrier::new(self.config.concurrent_users));

        // 启动并发连接任务
        let mut tasks = Vec::new();
        for (i, session) in user_sessions.into_iter().enumerate() {
            let task = self.spawn_user_task(i, session, barrier.clone()).await;
            tasks.push(task);
        }

        // 等待所有任务完成或超时
        let test_timeout = Duration::from_secs(self.config.test_duration_secs + 30);
        match timeout(test_timeout, futures_util::future::join_all(tasks)).await {
            Ok(results) => {
                info!("所有并发任务已完成");
                for (i, result) in results.into_iter().enumerate() {
                    if let Err(e) = result {
                        error!("用户任务{}失败: {}", i, e);
                    }
                }
            }
            Err(_) => {
                warn!("并发测试超时，强制结束");
            }
        }

        // 计算并返回统计结果
        let stats = self.metrics.calculate_stats().await;
        info!("并发测试完成，统计结果: {:?}", stats);
        Ok(stats)
    }

    /// 创建测试用户会话
    async fn create_test_users(&self) -> Result<Vec<UserSession>, Box<dyn std::error::Error>> {
        let mut sessions = Vec::new();

        for i in 0..self.config.concurrent_users {
            let user_id = Uuid::new_v4();
            // 所有测试用户都使用同一个已存在的测试用户testuser456
            let username = "testuser456".to_string();

            // 创建JWT token（使用测试用户testuser456的逻辑）
            let token = self.create_test_token(&user_id, &username).await?;

            sessions.push(UserSession {
                user_id,
                username: format!("testuser{}", i), // 保持不同的显示名称用于日志
                token,
            });
        }

        Ok(sessions)
    }

    /// 创建测试JWT token
    async fn create_test_token(
        &self,
        _user_id: &Uuid,
        username: &str,
    ) -> Result<String, Box<dyn std::error::Error>> {
        // 通过登录API获取真实的JWT token
        // 将WebSocket URL转换为HTTP URL
        let http_base_url = self.base_url.replace("ws://", "http://").replace("/ws", "");
        let login_url = format!("{}/api/auth/login", http_base_url);
        let login_data = json!({
            "username": username,
            "password": "password123"  // 测试用户的默认密码
        });

        let client = reqwest::Client::new();
        let response = client
            .post(&login_url)
            .header("Content-Type", "application/json")
            .json(&login_data)
            .send()
            .await?;

        let status = response.status();
        if status.is_success() {
            let login_response: serde_json::Value = response.json().await?;
            if let Some(data) = login_response.get("data") {
                if let Some(token) = data.get("access_token") {
                    return Ok(token.as_str().unwrap_or("").to_string());
                }
            }
        }

        Err(format!("无法验证测试用户{}: {}", username, status).into())
    }

    /// 生成WebSocket连接URL
    fn generate_websocket_url(&self, token: &str) -> String {
        // base_url已经是ws://格式，不需要再次转换
        format!("{}?token={}", self.base_url, token)
    }

    /// 启动单个用户任务
    async fn spawn_user_task(
        &self,
        user_index: usize,
        session: UserSession,
        barrier: Arc<Barrier>,
    ) -> tokio::task::JoinHandle<Result<(), Box<dyn std::error::Error + Send + Sync>>> {
        let config = self.config.clone();
        let metrics = self.metrics.clone();
        let ws_url = self.generate_websocket_url(&session.token);

        tokio::spawn(async move {
            // 等待所有用户准备就绪
            barrier.wait().await;

            info!("用户{}开始连接测试", user_index);

            // 执行用户连接和消息测试
            let result = Self::run_user_session(user_index, session, ws_url, config, metrics).await;

            if let Err(e) = &result {
                error!("用户{}测试失败: {}", user_index, e);
            } else {
                info!("用户{}测试完成", user_index);
            }

            result
        })
    }

    /// 执行单个用户的WebSocket会话测试
    async fn run_user_session(
        user_index: usize,
        session: UserSession,
        ws_url: String,
        config: ConcurrentTestConfig,
        metrics: Arc<PerformanceMetrics>,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut reconnect_attempts = 0;

        while reconnect_attempts <= config.max_reconnect_attempts {
            match Self::attempt_connection(user_index, &session, &ws_url, &config, &metrics).await {
                Ok(_) => {
                    info!("用户{}连接成功", user_index);
                    break;
                }
                Err(e) => {
                    error!(
                        "用户{}连接失败 (尝试{}): {}",
                        user_index,
                        reconnect_attempts + 1,
                        e
                    );
                    metrics.failed_connections.fetch_add(1, Ordering::Relaxed);

                    reconnect_attempts += 1;
                    if reconnect_attempts <= config.max_reconnect_attempts {
                        metrics.reconnection_count.fetch_add(1, Ordering::Relaxed);
                        tokio::time::sleep(Duration::from_millis(1000)).await;
                    }
                }
            }
        }

        Ok(())
    }

    /// 尝试建立WebSocket连接并执行消息测试
    async fn attempt_connection(
        user_index: usize,
        session: &UserSession,
        ws_url: &str,
        config: &ConcurrentTestConfig,
        metrics: &Arc<PerformanceMetrics>,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let connection_start = Instant::now();

        // 建立WebSocket连接
        let (ws_stream, _) = timeout(
            Duration::from_secs(config.connection_timeout_secs),
            connect_async(ws_url),
        )
        .await??;

        let connection_latency = connection_start.elapsed().as_millis() as u64;
        metrics.record_connection_latency(connection_latency).await;
        metrics
            .successful_connections
            .fetch_add(1, Ordering::Relaxed);

        info!("用户{}连接建立，延迟: {}ms", user_index, connection_latency);

        // 分割WebSocket流
        let (mut ws_sender, mut ws_receiver) = ws_stream.split();

        // 创建消息通道用于协调发送和接收
        let (_tx, mut _rx) = mpsc::unbounded_channel::<String>();

        // 启动消息接收任务
        let recv_metrics = metrics.clone();
        let recv_username = session.username.clone();
        let recv_task = tokio::spawn(async move {
            let mut received_count = 0u64;

            while let Some(msg_result) = ws_receiver.next().await {
                match msg_result {
                    Ok(TungsteniteMessage::Text(text)) => {
                        received_count += 1;
                        recv_metrics
                            .messages_received
                            .fetch_add(1, Ordering::Relaxed);

                        // 解析消息时间戳计算延迟（如果消息包含时间戳）
                        if let Ok(msg_data) = serde_json::from_str::<serde_json::Value>(&text) {
                            if let Some(timestamp) =
                                msg_data.get("timestamp").and_then(|t| t.as_u64())
                            {
                                let now = chrono::Utc::now().timestamp_millis() as u64;
                                if now > timestamp {
                                    let latency = now - timestamp;
                                    recv_metrics.record_message_latency(latency).await;
                                }
                            }
                        }

                        if received_count % 10 == 0 {
                            info!("用户{}已接收{}条消息", recv_username, received_count);
                        }
                    }
                    Ok(TungsteniteMessage::Ping(_)) => {
                        // Ping消息由库自动处理
                    }
                    Ok(TungsteniteMessage::Pong(_)) => {
                        // Pong响应
                    }
                    Ok(TungsteniteMessage::Close(_)) => {
                        info!("用户{}收到关闭消息", recv_username);
                        break;
                    }
                    Err(e) => {
                        error!("用户{}接收消息错误: {}", recv_username, e);
                        recv_metrics.message_errors.fetch_add(1, Ordering::Relaxed);
                        break;
                    }
                    _ => {}
                }
            }

            info!(
                "用户{}接收任务结束，共接收{}条消息",
                recv_username, received_count
            );
        });

        // 启动消息发送任务
        let send_metrics = metrics.clone();
        let send_username = session.username.clone();
        let send_user_index = user_index;
        let send_config = config.clone();
        let send_task = tokio::spawn(async move {
            let mut sent_count = 0u64;

            for i in 0..send_config.messages_per_user {
                let timestamp = chrono::Utc::now().timestamp_millis() as u64;
                let message_content = json!({
                    "type": "chat",
                    "content": format!("测试消息 {} 来自用户 {}", i + 1, send_username),
                    "timestamp": timestamp,
                    "user_index": send_user_index
                });

                let message = TungsteniteMessage::Text(message_content.to_string().into());

                match ws_sender.send(message).await {
                    Ok(_) => {
                        sent_count += 1;
                        send_metrics.messages_sent.fetch_add(1, Ordering::Relaxed);

                        if sent_count % 10 == 0 {
                            info!("用户{}已发送{}条消息", send_username, sent_count);
                        }
                    }
                    Err(e) => {
                        error!("用户{}发送消息失败: {}", send_username, e);
                        send_metrics.message_errors.fetch_add(1, Ordering::Relaxed);
                        break;
                    }
                }

                // 消息发送间隔
                if send_config.message_interval_ms > 0 {
                    tokio::time::sleep(Duration::from_millis(send_config.message_interval_ms))
                        .await;
                }
            }

            info!(
                "用户{}发送任务完成，共发送{}条消息",
                send_username, sent_count
            );

            // 发送关闭消息
            let _ = ws_sender.send(TungsteniteMessage::Close(None)).await;
        });

        // 等待发送任务完成或超时
        let test_timeout = Duration::from_secs(config.test_duration_secs);
        tokio::select! {
            _ = send_task => {
                info!("用户{}发送任务正常完成", user_index);
            }
            _ = tokio::time::sleep(test_timeout) => {
                warn!("用户{}测试超时", user_index);
            }
        }

        // 等待发送任务完成，然后发送关闭消息会在发送任务内部处理

        // 等待接收任务完成
        let _ = timeout(Duration::from_secs(5), recv_task).await;

        Ok(())
    }
}

/// 计算平均值（u64）
fn calculate_average(values: &[u64]) -> f64 {
    if values.is_empty() {
        0.0
    } else {
        (values.iter().sum::<u64>() as f64) / (values.len() as f64)
    }
}

/// 计算平均值（f64）
fn calculate_average_f64(values: &[f64]) -> f64 {
    if values.is_empty() {
        0.0
    } else {
        values.iter().sum::<f64>() / (values.len() as f64)
    }
}

/// 计算百分位数
fn calculate_percentile(values: &[u64], percentile: f64) -> f64 {
    if values.is_empty() {
        return 0.0;
    }

    let mut sorted_values = values.to_vec();
    sorted_values.sort_unstable();

    let index = ((percentile / 100.0) * ((sorted_values.len() - 1) as f64)).round() as usize;
    sorted_values[index.min(sorted_values.len() - 1)] as f64
}
