//! # 统一错误消息格式验证测试模块
//!
//! 本模块专门测试API错误响应的统一格式，确保所有错误类型都遵循统一的响应结构。
//! 遵循Context7 MCP最佳实践和rust_axum_Rules.md规范：
//! - 验证ErrorResponseBuilder生成的统一错误格式
//! - 测试所有HTTP错误状态码的响应格式
//! - 验证错误消息的本地化和一致性
//! - 确保错误跟踪信息的完整性
//! - 遵循TDD（Test-Driven Development）开发模式

use anyhow::{Context, Result};
use chrono;
use reqwest::Client;
use serde_json::{Value, json};
use std::time::Duration;
use tokio::time::sleep;

// 导入E2E测试辅助模块
#[path = "e2e/helpers/mod.rs"]
mod helpers;

use helpers::{
    AuthHelper, E2EConfig, TaskCrudHelper, TestServer, TestTaskData, ensure_dir_exists,
    test_server::TestServerConfig,
};

/// 统一错误格式验证测试套件
pub struct UnifiedErrorFormatTestSuite {
    config: E2EConfig,
    test_server: TestServer,
    auth_helper: AuthHelper,
    _task_crud_helper: TaskCrudHelper,
    client: Client,
    test_user_token: Option<String>,
}

impl UnifiedErrorFormatTestSuite {
    /// 创建新的统一错误格式验证测试套件实例
    pub async fn new() -> Result<Self> {
        println!("🔧 初始化统一错误格式验证测试套件...");

        // 加载配置
        let config = E2EConfig::from_env().context("无法加载E2E测试配置")?;

        // 创建测试服务器
        let server_config = TestServerConfig {
            host: config.server_host.clone(),
            port: config.server_port,
            startup_timeout: 30,
            health_check_interval: 1,
            project_root: std::env::current_dir()
                .unwrap_or_default()
                .to_string_lossy()
                .to_string(),
        };

        let test_server = TestServer::new(server_config);

        // 创建HTTP客户端
        let client = Client::builder()
            .timeout(Duration::from_secs(10))
            .build()
            .context("无法创建HTTP客户端")?;

        // 创建辅助工具
        let auth_helper = AuthHelper::new(config.clone());
        let task_crud_helper = TaskCrudHelper::new(config.clone());

        Ok(Self {
            config,
            test_server,
            auth_helper,
            _task_crud_helper: task_crud_helper,
            client,
            test_user_token: None,
        })
    }

    /// 设置测试环境
    pub async fn setup(&mut self) -> Result<()> {
        println!("🔧 设置统一错误格式验证测试环境...");

        // 确保报告目录存在
        ensure_dir_exists(&std::path::PathBuf::from("tests/reports"))
            .context("无法创建报告目录")?;

        // 启动测试服务器
        self.test_server
            .start()
            .await
            .context("无法启动测试服务器")?;

        // 等待服务器启动
        sleep(Duration::from_secs(2)).await;

        // 验证服务器连接
        self.test_server
            .health_check()
            .await
            .context("无法验证服务器连接")?;

        // 注册测试用户并获取认证令牌
        let test_username = "error_format_test_user";
        let test_email = "<EMAIL>";
        let test_password = "TestPassword123!";

        // 先注册用户
        let _register_result = self
            .auth_helper
            .register_user(test_username, test_email, test_password)
            .await
            .context("无法注册测试用户")?;

        // 然后登录获取令牌
        let auth_token = self
            .auth_helper
            .get_auth_token(test_username, test_password)
            .await
            .context("无法获取认证令牌")?;

        self.test_user_token = Some(auth_token);

        println!("✅ 统一错误格式验证测试环境设置完成");
        Ok(())
    }

    /// 清理测试环境
    pub async fn cleanup(&mut self) -> Result<()> {
        println!("🧹 清理统一错误格式验证测试环境...");

        // 停止测试服务器
        self.test_server.stop().context("无法停止测试服务器")?;

        println!("✅ 统一错误格式验证测试环境清理完成");
        Ok(())
    }

    /// 运行所有统一错误格式验证测试
    pub async fn run_all_tests(&self) -> Result<()> {
        println!("🚀 开始运行统一错误格式验证测试套件...");

        let mut test_results = Vec::new();

        // 测试400 Bad Request错误格式
        match self.test_400_bad_request_format().await {
            Ok(_) => {
                println!("✅ 400 Bad Request错误格式验证通过");
                test_results.push(("400 Bad Request错误格式", true));
            }
            Err(e) => {
                println!("❌ 400 Bad Request错误格式验证失败: {}", e);
                test_results.push(("400 Bad Request错误格式", false));
            }
        }

        // 测试401 Unauthorized错误格式
        match self.test_401_unauthorized_format().await {
            Ok(_) => {
                println!("✅ 401 Unauthorized错误格式验证通过");
                test_results.push(("401 Unauthorized错误格式", true));
            }
            Err(e) => {
                println!("❌ 401 Unauthorized错误格式验证失败: {}", e);
                test_results.push(("401 Unauthorized错误格式", false));
            }
        }

        // 测试404 Not Found错误格式
        match self.test_404_not_found_format().await {
            Ok(_) => {
                println!("✅ 404 Not Found错误格式验证通过");
                test_results.push(("404 Not Found错误格式", true));
            }
            Err(e) => {
                println!("❌ 404 Not Found错误格式验证失败: {}", e);
                test_results.push(("404 Not Found错误格式", false));
            }
        }

        // 测试错误响应一致性
        match self.test_error_response_consistency().await {
            Ok(_) => {
                println!("✅ 错误响应一致性验证通过");
                test_results.push(("错误响应一致性", true));
            }
            Err(e) => {
                println!("❌ 错误响应一致性验证失败: {}", e);
                test_results.push(("错误响应一致性", false));
            }
        }

        // 测试错误消息本地化
        match self.test_error_message_localization().await {
            Ok(_) => {
                println!("✅ 错误消息本地化验证通过");
                test_results.push(("错误消息本地化", true));
            }
            Err(e) => {
                println!("❌ 错误消息本地化验证失败: {}", e);
                test_results.push(("错误消息本地化", false));
            }
        }

        // 测试错误跟踪信息
        match self.test_error_tracing_info().await {
            Ok(_) => {
                println!("✅ 错误跟踪信息验证通过");
                test_results.push(("错误跟踪信息", true));
            }
            Err(e) => {
                println!("❌ 错误跟踪信息验证失败: {}", e);
                test_results.push(("错误跟踪信息", false));
            }
        }

        // 生成测试报告
        self.generate_test_report(&test_results).await?;

        // 检查是否所有测试都通过
        let all_passed = test_results.iter().all(|(_, passed)| *passed);
        if all_passed {
            println!("🎉 所有统一错误格式验证测试都通过了！");
        } else {
            println!("⚠️ 部分统一错误格式验证测试失败，请查看详细报告");
        }

        Ok(())
    }

    /// 测试400 Bad Request错误格式
    async fn test_400_bad_request_format(&self) -> Result<()> {
        println!("🧪 测试400 Bad Request错误格式...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 创建一个会触发验证错误的请求（空标题）
        let invalid_task = TestTaskData::new("");
        let response = self
            .create_task_with_validation_error(token, &invalid_task)
            .await?;

        // 打印实际响应以便调试
        println!(
            "🔍 实际400错误响应: {}",
            serde_json::to_string_pretty(&response)
                .unwrap_or_else(|_| "无法序列化响应".to_string())
        );

        // 验证统一错误格式
        self.validate_unified_error_format(&response, "VALIDATION_ERROR", 400)?;

        // 验证错误消息本地化
        self.validate_error_message_localization(&response)?;

        // 验证错误跟踪信息
        self.validate_error_tracing_info(&response)?;

        println!("✅ 400 Bad Request错误格式验证通过");
        Ok(())
    }

    /// 测试401 Unauthorized错误格式
    async fn test_401_unauthorized_format(&self) -> Result<()> {
        println!("🧪 测试401 Unauthorized错误格式...");

        // 使用无效令牌发送请求
        let response = self.fetch_tasks_with_invalid_token().await?;

        // 验证统一错误格式
        self.validate_unified_error_format(&response, "AUTHENTICATION_ERROR", 401)?;

        // 验证错误消息本地化
        self.validate_error_message_localization(&response)?;

        // 验证错误跟踪信息
        self.validate_error_tracing_info(&response)?;

        println!("✅ 401 Unauthorized错误格式验证通过");
        Ok(())
    }

    /// 测试404 Not Found错误格式
    async fn test_404_not_found_format(&self) -> Result<()> {
        println!("🧪 测试404 Not Found错误格式...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 请求一个不存在的任务
        let response = self.fetch_nonexistent_task(token).await?;

        // 验证统一错误格式
        self.validate_unified_error_format(&response, "NOT_FOUND", 404)?;

        // 验证错误消息本地化
        self.validate_error_message_localization(&response)?;

        // 验证错误跟踪信息
        self.validate_error_tracing_info(&response)?;

        println!("✅ 404 Not Found错误格式验证通过");
        Ok(())
    }

    /// 测试错误响应一致性
    async fn test_error_response_consistency(&self) -> Result<()> {
        println!("🧪 测试错误响应一致性...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 收集不同类型的错误响应
        let validation_error = self
            .create_task_with_validation_error(token, &TestTaskData::new(""))
            .await?;

        let auth_error = self.fetch_tasks_with_invalid_token().await?;

        let not_found_error = self.fetch_nonexistent_task(token).await?;

        // 验证所有错误响应的结构一致性
        self.validate_error_response_consistency(vec![
            &validation_error,
            &auth_error,
            &not_found_error,
        ])?;

        println!("✅ 错误响应一致性验证通过");
        Ok(())
    }

    /// 测试错误消息本地化
    async fn test_error_message_localization(&self) -> Result<()> {
        println!("🧪 测试错误消息本地化...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 测试多种错误类型的本地化
        let validation_error = self
            .create_task_with_validation_error(token, &TestTaskData::new(""))
            .await?;

        let auth_error = self.fetch_tasks_with_invalid_token().await?;

        let not_found_error = self.fetch_nonexistent_task(token).await?;

        // 验证每种错误的本地化
        self.validate_error_message_localization(&validation_error)?;
        self.validate_error_message_localization(&auth_error)?;
        self.validate_error_message_localization(&not_found_error)?;

        println!("✅ 错误消息本地化验证通过");
        Ok(())
    }

    /// 测试错误跟踪信息
    async fn test_error_tracing_info(&self) -> Result<()> {
        println!("🧪 测试错误跟踪信息...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 测试多种错误类型的跟踪信息
        let validation_error = self
            .create_task_with_validation_error(token, &TestTaskData::new(""))
            .await?;

        let auth_error = self.fetch_tasks_with_invalid_token().await?;

        let not_found_error = self.fetch_nonexistent_task(token).await?;

        // 验证每种错误的跟踪信息
        self.validate_error_tracing_info(&validation_error)?;
        self.validate_error_tracing_info(&auth_error)?;
        self.validate_error_tracing_info(&not_found_error)?;

        println!("✅ 错误跟踪信息验证通过");
        Ok(())
    }

    /// 创建验证失败的任务请求
    async fn create_task_with_validation_error(
        &self,
        token: &str,
        task_data: &TestTaskData,
    ) -> Result<Value> {
        let url = format!("{}/api/tasks", self.config.base_url);

        // 创建一个会触发应用层验证错误的JSON（空标题但有效JSON）
        let invalid_payload = json!({
            "title": "",  // 空标题会触发应用层验证错误
            "description": "测试验证错误"
        });

        let response = self
            .client
            .post(&url)
            .header("Authorization", format!("Bearer {}", token))
            .header("Content-Type", "application/json")
            .json(&invalid_payload)
            .send()
            .await
            .context("发送验证失败请求失败")?;

        let _status = response.status();
        let response_text = response.text().await.context("获取响应文本失败")?;

        // 尝试解析JSON，如果失败则返回错误信息
        let body: Value = if response_text.trim().is_empty() {
            json!({"error": "Empty response from server"})
        } else {
            match serde_json::from_str(&response_text) {
                Ok(json) => json,
                Err(_) => {
                    json!({"error": "Invalid JSON response", "raw_response": response_text})
                }
            }
        };

        Ok(body)
    }

    /// 使用无效令牌获取任务列表
    async fn fetch_tasks_with_invalid_token(&self) -> Result<Value> {
        let url = format!("{}/api/tasks", self.config.base_url);
        let response = self
            .client
            .get(&url)
            .header("Authorization", "Bearer invalid_token")
            .send()
            .await
            .context("发送无效令牌请求失败")?;

        let _status = response.status();
        let response_text = response.text().await.context("获取响应文本失败")?;

        // 尝试解析JSON，如果失败则返回错误信息
        let body: Value = if response_text.trim().is_empty() {
            json!({"error": "Empty response from server"})
        } else {
            match serde_json::from_str(&response_text) {
                Ok(json) => json,
                Err(_) => {
                    json!({"error": "Invalid JSON response", "raw_response": response_text})
                }
            }
        };

        Ok(body)
    }

    /// 获取不存在的任务
    async fn fetch_nonexistent_task(&self, token: &str) -> Result<Value> {
        let nonexistent_id = "00000000-0000-0000-0000-000000000000";
        let url = format!("{}/api/tasks/{}", self.config.base_url, nonexistent_id);
        let response = self
            .client
            .get(&url)
            .header("Authorization", format!("Bearer {}", token))
            .send()
            .await
            .context("发送获取不存在任务请求失败")?;

        let _status = response.status();
        let response_text = response.text().await.context("获取响应文本失败")?;

        // 尝试解析JSON，如果失败则返回错误信息
        let body: Value = if response_text.trim().is_empty() {
            json!({"error": "Empty response from server"})
        } else {
            match serde_json::from_str(&response_text) {
                Ok(json) => json,
                Err(_) => {
                    json!({"error": "Invalid JSON response", "raw_response": response_text})
                }
            }
        };

        Ok(body)
    }

    /// 验证统一错误格式
    ///
    /// 【功能】：验证错误响应是否符合统一的ErrorResponseBuilder格式
    fn validate_unified_error_format(
        &self,
        error_response: &Value,
        expected_code: &str,
        expected_status: u16,
    ) -> Result<()> {
        println!(
            "    🔍 验证统一错误格式 - 代码: {}, 状态: {}",
            expected_code, expected_status
        );

        // 1. 验证顶级结构
        if !error_response.is_object() {
            return Err(anyhow::anyhow!("错误响应应为JSON对象"));
        }

        // 2. 验证success字段为false
        if error_response["success"].as_bool() != Some(false) {
            return Err(anyhow::anyhow!("错误响应success字段应为false"));
        }

        // 3. 验证data字段为null
        if !error_response["data"].is_null() {
            return Err(anyhow::anyhow!("错误响应data字段应为null"));
        }

        // 4. 验证message字段存在且为字符串
        if !error_response["message"].is_string() {
            return Err(anyhow::anyhow!("错误响应缺少message字段或类型错误"));
        }

        // 5. 验证error字段存在且为对象
        if !error_response["error"].is_object() {
            return Err(anyhow::anyhow!("错误响应缺少error字段或类型错误"));
        }

        let error_obj = &error_response["error"];

        // 6. 验证error对象的必需字段
        let required_fields = ["code", "message", "status", "timestamp", "trace_id"];
        for field in required_fields {
            if error_obj[field].is_null() {
                return Err(anyhow::anyhow!("错误对象缺少{}字段", field));
            }
        }

        // 7. 验证错误代码
        if let Some(actual_code) = error_obj["code"].as_str() {
            if actual_code != expected_code {
                return Err(anyhow::anyhow!(
                    "错误代码不匹配: 期望 {}, 实际 {}",
                    expected_code,
                    actual_code
                ));
            }
        } else {
            return Err(anyhow::anyhow!("错误代码字段类型错误"));
        }

        // 8. 验证HTTP状态码
        if let Some(actual_status) = error_obj["status"].as_u64() {
            if actual_status != (expected_status as u64) {
                return Err(anyhow::anyhow!(
                    "HTTP状态码不匹配: 期望 {}, 实际 {}",
                    expected_status,
                    actual_status
                ));
            }
        } else {
            return Err(anyhow::anyhow!("HTTP状态码字段类型错误"));
        }

        // 9. 验证时间戳格式
        if let Some(timestamp) = error_obj["timestamp"].as_str() {
            chrono::DateTime::parse_from_rfc3339(timestamp).context("时间戳格式无效")?;
        } else {
            return Err(anyhow::anyhow!("时间戳字段类型错误"));
        }

        // 10. 验证跟踪ID格式（UUID）
        if let Some(trace_id) = error_obj["trace_id"].as_str() {
            uuid::Uuid::parse_str(trace_id).context("跟踪ID不是有效的UUID格式")?;
        } else {
            return Err(anyhow::anyhow!("跟踪ID字段类型错误"));
        }

        println!("    ✅ 统一错误格式验证通过");
        Ok(())
    }

    /// 验证错误消息本地化
    fn validate_error_message_localization(&self, error_response: &Value) -> Result<()> {
        println!("    🌐 验证错误消息本地化");

        // 验证主错误消息
        if let Some(message) = error_response["message"].as_str() {
            if message.is_empty() {
                return Err(anyhow::anyhow!("错误消息不能为空"));
            }

            // 检查是否包含中文字符
            let has_chinese = message.chars().any(|c| {
                let code = c as u32;
                // 中文字符的Unicode范围
                (code >= 0x4e00 && code <= 0x9fff) || // CJK统一汉字
                    (code >= 0x3400 && code <= 0x4dbf) || // CJK扩展A
                    (code >= 0x20000 && code <= 0x2a6df) || // CJK扩展B
                    (code >= 0x2a700 && code <= 0x2b73f) || // CJK扩展C
                    (code >= 0x2b740 && code <= 0x2b81f) || // CJK扩展D
                    (code >= 0x2b820 && code <= 0x2ceaf) // CJK扩展E
            });

            if !has_chinese {
                println!("    ⚠️ 警告：错误消息可能未本地化为中文: {}", message);
            } else {
                println!("    ✅ 错误消息已正确本地化为中文");
            }
        }

        // 验证错误对象中的消息
        if let Some(error_obj) = error_response["error"].as_object() {
            if let Some(error_message) = error_obj["message"].as_str() {
                let has_chinese = error_message.chars().any(|c| {
                    let code = c as u32;
                    code >= 0x4e00 && code <= 0x9fff
                });

                if !has_chinese {
                    println!(
                        "    ⚠️ 警告：错误对象消息可能未本地化为中文: {}",
                        error_message
                    );
                }
            }
        }

        Ok(())
    }

    /// 验证错误响应一致性
    fn validate_error_response_consistency(&self, error_responses: Vec<&Value>) -> Result<()> {
        println!("    🔄 验证错误响应结构一致性");

        if error_responses.len() < 2 {
            return Ok(()); // 至少需要两个响应才能比较一致性
        }

        let first_response = error_responses[0];

        // 获取第一个响应的结构作为基准
        let base_keys: std::collections::HashSet<String> =
            if let Some(obj) = first_response.as_object() {
                obj.keys().cloned().collect()
            } else {
                return Err(anyhow::anyhow!("第一个错误响应不是JSON对象"));
            };

        let base_error_keys: std::collections::HashSet<String> =
            if let Some(error_obj) = first_response["error"].as_object() {
                error_obj.keys().cloned().collect()
            } else {
                return Err(anyhow::anyhow!("第一个错误响应的error字段不是对象"));
            };

        // 验证其他响应的结构一致性
        for (index, response) in error_responses.iter().enumerate().skip(1) {
            // 验证顶级字段一致性
            if let Some(obj) = response.as_object() {
                let current_keys: std::collections::HashSet<String> = obj.keys().cloned().collect();
                if current_keys != base_keys {
                    return Err(anyhow::anyhow!(
                        "第{}个错误响应的顶级字段与基准不一致",
                        index + 1
                    ));
                }
            } else {
                return Err(anyhow::anyhow!("第{}个错误响应不是JSON对象", index + 1));
            }

            // 验证error对象字段一致性
            if let Some(error_obj) = response["error"].as_object() {
                let current_error_keys: std::collections::HashSet<String> =
                    error_obj.keys().cloned().collect();
                if current_error_keys != base_error_keys {
                    return Err(anyhow::anyhow!(
                        "第{}个错误响应的error字段与基准不一致",
                        index + 1
                    ));
                }
            } else {
                return Err(anyhow::anyhow!(
                    "第{}个错误响应的error字段不是对象",
                    index + 1
                ));
            }
        }

        println!("    ✅ 所有错误响应结构一致");
        Ok(())
    }

    /// 验证错误跟踪信息
    fn validate_error_tracing_info(&self, error_response: &Value) -> Result<()> {
        println!("    🔍 验证错误跟踪信息");

        if let Some(error_obj) = error_response["error"].as_object() {
            // 验证跟踪ID存在且有效
            if let Some(trace_id) = error_obj["trace_id"].as_str() {
                if trace_id.is_empty() {
                    return Err(anyhow::anyhow!("跟踪ID不能为空"));
                }

                // 验证UUID格式
                uuid::Uuid::parse_str(trace_id).context("跟踪ID格式无效")?;

                println!("    ✅ 跟踪ID格式有效: {}", trace_id);
            } else {
                return Err(anyhow::anyhow!("缺少跟踪ID"));
            }

            // 验证时间戳存在且有效
            if let Some(timestamp) = error_obj["timestamp"].as_str() {
                if timestamp.is_empty() {
                    return Err(anyhow::anyhow!("时间戳不能为空"));
                }

                // 验证RFC3339格式
                let parsed_time =
                    chrono::DateTime::parse_from_rfc3339(timestamp).context("时间戳格式无效")?;

                // 验证时间戳是否合理（不能是未来时间，不能太久以前）
                let now = chrono::Utc::now();
                let time_diff = now.signed_duration_since(parsed_time.with_timezone(&chrono::Utc));

                if time_diff.num_seconds() < 0 {
                    return Err(anyhow::anyhow!("时间戳不能是未来时间"));
                }

                if time_diff.num_minutes() > 5 {
                    println!("    ⚠️ 警告：时间戳距离当前时间超过5分钟");
                }

                println!("    ✅ 时间戳格式有效: {}", timestamp);
            } else {
                return Err(anyhow::anyhow!("缺少时间戳"));
            }

            // 验证错误详情（如果存在）
            if let Some(details) = error_obj["details"].as_str() {
                if !details.is_empty() {
                    println!("    ✅ 错误详情存在: {}", details);
                }
            }
        } else {
            return Err(anyhow::anyhow!("错误响应缺少error对象"));
        }

        Ok(())
    }

    /// 生成测试报告
    async fn generate_test_report(&self, test_results: &[(&str, bool)]) -> Result<()> {
        println!("📊 生成统一错误格式验证测试报告...");

        let report_path = "tests/reports/unified_error_format_validation_report.md";

        let mut report_content = String::new();
        report_content.push_str("# 统一错误消息格式验证测试报告\n\n");
        report_content.push_str(&format!(
            "**生成时间**: {}\n\n",
            chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC")
        ));

        // 测试概览
        let total_tests = test_results.len();
        let passed_tests = test_results.iter().filter(|(_, passed)| *passed).count();
        let failed_tests = total_tests - passed_tests;

        report_content.push_str("## 测试概览\n\n");
        report_content.push_str(&format!("- **总测试数**: {}\n", total_tests));
        report_content.push_str(&format!("- **通过测试**: {}\n", passed_tests));
        report_content.push_str(&format!("- **失败测试**: {}\n", failed_tests));
        report_content.push_str(&format!(
            "- **通过率**: {:.1}%\n\n",
            ((passed_tests as f64) / (total_tests as f64)) * 100.0
        ));

        // 详细测试结果
        report_content.push_str("## 详细测试结果\n\n");
        for (test_name, passed) in test_results {
            let status = if *passed { "✅ 通过" } else { "❌ 失败" };
            report_content.push_str(&format!("- **{}**: {}\n", test_name, status));
        }

        report_content.push_str("\n## 统一错误格式规范\n\n");
        report_content.push_str("### ErrorResponseBuilder格式\n");
        report_content.push_str("所有API错误响应都应遵循以下统一格式：\n\n");
        report_content.push_str("```json\n");
        report_content.push_str("{\n");
        report_content.push_str("  \"success\": false,\n");
        report_content.push_str("  \"data\": null,\n");
        report_content.push_str("  \"message\": \"用户友好的错误消息\",\n");
        report_content.push_str("  \"error\": {\n");
        report_content.push_str("    \"code\": \"ERROR_CODE\",\n");
        report_content.push_str("    \"message\": \"详细错误消息\",\n");
        report_content.push_str("    \"status\": 400,\n");
        report_content.push_str("    \"timestamp\": \"2024-01-01T00:00:00Z\",\n");
        report_content.push_str("    \"trace_id\": \"uuid-v4-format\",\n");
        report_content.push_str("    \"details\": \"可选的错误详情\"\n");
        report_content.push_str("  }\n");
        report_content.push_str("}\n");
        report_content.push_str("```\n\n");

        // 写入报告文件
        tokio::fs::write(report_path, report_content)
            .await
            .context("无法写入测试报告文件")?;

        println!("✅ 测试报告已生成: {}", report_path);
        Ok(())
    }
}

/// 主测试函数
#[tokio::main]
async fn main() -> Result<()> {
    println!("🚀 启动统一错误消息格式验证测试套件");

    // 初始化测试套件
    let mut test_suite = UnifiedErrorFormatTestSuite::new()
        .await
        .context("无法初始化统一错误格式验证测试套件")?;

    // 设置测试环境
    test_suite.setup().await.context("无法设置测试环境")?;

    // 运行所有测试
    let test_result = test_suite.run_all_tests().await;

    // 清理测试环境
    test_suite.cleanup().await.context("无法清理测试环境")?;

    // 处理测试结果
    match test_result {
        Ok(_) => {
            println!("🎉 统一错误消息格式验证测试套件执行完成");
            Ok(())
        }
        Err(e) => {
            println!("❌ 统一错误消息格式验证测试套件执行失败: {}", e);
            Err(e)
        }
    }
}
