{"name": "axum-tutorial-e2e-tests", "version": "1.0.0", "description": "Axum项目前端UI交互测试套件", "private": true, "scripts": {"test": "playwright test", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "test:headed": "playwright test --headed", "test:report": "playwright show-report", "test:install": "playwright install", "test:install-deps": "playwright install-deps", "test:codegen": "playwright codegen http://127.0.0.1:3000", "test:trace": "playwright test --trace on", "test:screenshot": "playwright test --screenshot=only-on-failure", "test:video": "playwright test --video=retain-on-failure", "test:unit": "jest", "test:unit:watch": "jest --watch", "test:unit:coverage": "jest --coverage", "test:unit:ci": "jest --ci --coverage --watchAll=false", "test:all": "npm run test:unit && npm run test", "test:performance": "playwright test tests/frontend_performance_tests.js", "test:performance:headed": "playwright test tests/frontend_performance_tests.js --headed", "test:performance:report": "playwright test tests/frontend_performance_tests.js --reporter=html", "lighthouse": "lighthouse http://127.0.0.1:3000 --config-path=lighthouse.config.js --output=html --output-path=./reports/lighthouse-report.html", "lighthouse:mobile": "lighthouse http://127.0.0.1:3000 --config-path=lighthouse.config.js --preset=perf --form-factor=mobile --output=html --output-path=./reports/lighthouse-mobile-report.html", "lighthouse:desktop": "lighthouse http://127.0.0.1:3000 --config-path=lighthouse.config.js --preset=perf --form-factor=desktop --output=html --output-path=./reports/lighthouse-desktop-report.html", "performance:audit": "npm run lighthouse && npm run test:performance", "performance:ci": "npm run lighthouse:mobile && npm run lighthouse:desktop && npm run test:performance:report"}, "devDependencies": {"@playwright/test": "^1.40.0", "playwright": "^1.40.0", "@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "@babel/plugin-transform-class-properties": "^7.23.0", "@babel/plugin-transform-optional-chaining": "^7.23.0", "@babel/plugin-transform-nullish-coalescing-operator": "^7.23.0", "babel-jest": "^29.7.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-extended": "^4.0.0"}, "engines": {"node": ">=16.0.0"}, "keywords": ["playwright", "e2e", "testing", "axum", "rust", "ui-testing"], "author": "Axum Tutorial Team", "license": "MIT", "dependencies": {"ws": "^8.18.3"}}