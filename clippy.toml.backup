# Clippy配置文件 - 2025年最新最佳实践
# 适用于Axum企业级项目的严格代码质量标准

# 认知复杂度阈值 - 降低到20以提高代码可读性
cognitive-complexity-threshold = 20

# 函数参数数量限制 - 降低到5个以提高可维护性
too-many-arguments-threshold = 5

# 函数行数限制 - 降低到80行以提高可读性
too-many-lines-threshold = 80

# 类型复杂度阈值 - 降低到200以简化类型设计
type-complexity-threshold = 200

# 栈大小阈值 - 512KB，适合高性能应用
stack-size-threshold = 524288

# 大型对象栈分配阈值 - 降低到150字节
too-large-for-stack = 150

# 传值大小限制 - 降低到200字节以优化性能
pass-by-value-size-limit = 200

# 最小标识符字符数 - 要求至少2个字符
min-ident-chars-threshold = 2

# 结构体布尔字段数量限制
max-struct-bools = 2

# 函数布尔参数数量限制
max-fn-params-bools = 2

# trait边界数量限制
max-trait-bounds = 2

# 详细位掩码阈值
verbose-bit-mask-threshold = 1

# 嵌套层级限制
excessive-nesting-threshold = 4

# 禁止的方法列表 - 2025年安全最佳实践
disallowed-methods = [
    { path = "std::mem::transmute", reason = "使用更安全的替代方案如std::mem::transmute_copy或类型转换" },
    { path = "std::ptr::null", reason = "使用Option<T>代替裸指针" },
    { path = "std::ptr::null_mut", reason = "使用Option<T>代替裸指针" },
    { path = "std::slice::from_raw_parts", reason = "使用安全的切片创建方法" },
    { path = "std::slice::from_raw_parts_mut", reason = "使用安全的切片创建方法" },
]

# 禁止的类型列表
disallowed-types = [
    { path = "std::collections::HashMap", reason = "在企业级应用中考虑使用IndexMap以获得确定性迭代顺序" },
]

# 禁止的名称
disallowed-names = ["foo", "bar", "baz", "test", "tmp", "temp", "data", "info"]

# 允许的前缀
allowed-prefixes = ["to", "as", "into", "from", "try_into", "try_from", "with", "without"]

# 允许通配符导入的路径
allowed-wildcard-imports = ["serde_json::json", "tokio::prelude"]

# 忽略内部可变性的类型
ignore-interior-mutability = ["bytes::Bytes", "std::sync::Arc"]

# 允许重复的crate
allowed-duplicate-crates = []

# 算术副作用允许的类型
arithmetic-side-effects-allowed = []

# 算术副作用允许的二元操作类型对
arithmetic-side-effects-allowed-binary = []

# 算术副作用允许的一元操作类型
arithmetic-side-effects-allowed-unary = []

# 是否在测试中允许dbg!宏
allow-dbg-in-tests = false

# 是否在测试中允许expect方法
allow-expect-in-tests = false

# 是否在测试中允许panic宏
allow-panic-in-tests = false

# 是否在测试中允许unwrap方法
allow-unwrap-in-tests = false

# 是否在常量中允许expect方法
allow-expect-in-consts = true

# 是否在常量中允许unwrap方法
allow-unwrap-in-consts = true

# 是否允许精确重复
allow-exact-repetitions = false

# 是否允许重命名参数的trait
allow-renamed-params-for = []

# 是否避免破坏导出的API
avoid-breaking-exported-api = true

# 是否检查私有项目
check-private-items = true

# 是否在测试中检查MSRV兼容性
check-incompatible-msrv-in-tests = false

# 是否接受属性上方的注释
accept-comment-above-attributes = true

# 是否接受语句上方的注释
accept-comment-above-statement = true

# 是否在const中抑制限制性lint
suppress-restriction-lint-in-const = false

# 是否对所有通配符导入发出警告
warn-on-all-wildcard-imports = false

# 是否强制隐式迭代重借用值
enforce-iter-loop-reborrow = true

# 是否检查注释代码的可折叠性
lint-commented-code = false

# 缺少文档是否仅检查crate项目
missing-docs-in-crate-items = false

# 是否允许未使用的字段跳过文档要求（已移除不支持的配置项）
# missing-docs-allow-unused = false

# let-else的匹配模式
matches-for-let-else = "WellKnownTypes"

# 源项目排序
source-item-ordering = ["enum", "impl", "module", "struct", "trait"]

# trait关联项目类型顺序
trait-assoc-item-kinds-order = ["const", "type", "fn"]

# 分号在块内忽略单行
semicolon-inside-block-ignore-singleline = false

# 分号在块外忽略多行
semicolon-outside-block-ignore-multiline = false

# 是否建议重新排序不一致的结构字段初始化器（已移除不支持的配置项）
# suggest-reordering-inconsistent-struct-constructor = false

# MSRV（最小支持的Rust版本）
msrv = "1.82.0"
