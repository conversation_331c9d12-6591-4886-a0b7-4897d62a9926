pub use sea_orm_migration::prelude::*;

mod m20250610_035426_create_task_table;
mod m20250615_075512_create_users_table;
mod m20250615_081240_add_user_id_to_tasks;
// 聊天室相关迁移
mod m20250624_120000_create_chat_rooms_table;
mod m20250624_120001_create_messages_table;
mod m20250624_120002_create_user_sessions_table;
mod m20250717_120000_optimize_message_indexes;
mod m20250720_120000_add_user_profile_fields;
// 全文搜索迁移
mod m20250728_120000_add_fulltext_search;
pub mod task_entity;
pub mod user_entity;
// 聊天室相关实体模块
pub mod chat_room_entity;
pub mod message_entity;
pub mod user_session_entity;

pub struct Migrator;

impl MigratorTrait for Migrator {
    fn migrations() -> Vec<Box<dyn MigrationTrait>> {
        vec![
            Box::new(m20250610_035426_create_task_table::Migration),
            Box::new(m20250615_075512_create_users_table::Migration),
            Box::new(m20250615_081240_add_user_id_to_tasks::Migration),
            // 聊天室相关迁移
            Box::new(m20250624_120000_create_chat_rooms_table::Migration),
            Box::new(m20250624_120001_create_messages_table::Migration),
            Box::new(m20250624_120002_create_user_sessions_table::Migration),
            // 性能优化迁移
            Box::new(m20250717_120000_optimize_message_indexes::Migration),
            // 用户资料扩展迁移
            Box::new(m20250720_120000_add_user_profile_fields::Migration),
            // 全文搜索迁移
            Box::new(m20250728_120000_add_fulltext_search::Migration),
        ]
    }
}
