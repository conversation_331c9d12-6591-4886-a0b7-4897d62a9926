//! # 用户资料查看API的E2E测试
//!
//! 本测试文件实现了用户资料查看API的端到端测试，遵循TDD原则：
//! 1. 用户资料查看功能测试（GET /api/users/{id}）
//! 2. 认证和权限验证测试
//! 3. 边界情况和错误处理测试
//! 4. 性能和安全测试
//!
//! 遵循rust_axum_Rules.md规范：
//! - 使用清晰的函数命名（test_fetch_user_profile_success等）
//! - 详细的中文注释
//! - 完整的错误处理
//! - 遵循DRY和SOLID原则

use anyhow::Result;
use reqwest::Client;
use std::time::Duration;
use uuid::Uuid;

// 导入E2E测试辅助模块
mod e2e {
    pub mod helpers;
}
use e2e::helpers::{ApiHelper, AuthHelper, E2EConfig};

/// 测试配置常量
const TEST_USER_USERNAME: &str = "testuser456";
const TEST_USER_PASSWORD: &str = "password123";
const API_TIMEOUT_SECONDS: u64 = 30;

/// 初始化测试环境
async fn setup_test_environment() -> Result<E2EConfig> {
    // 启动测试服务器
    e2e::helpers::test_server::start_global_test_server().await?;

    // 加载测试配置
    let config = E2EConfig::from_env()?;

    // 确保测试目录存在
    e2e::helpers::ensure_dir_exists(&config.report_dir)?;
    e2e::helpers::ensure_dir_exists(&config.screenshot_dir)?;
    e2e::helpers::ensure_dir_exists(&config.video_dir)?;

    // 清理之前的测试数据
    e2e::helpers::cleanup_test_data()?;

    println!("✅ 测试环境初始化完成");
    Ok(config)
}

/// 创建测试用户并返回用户ID和JWT令牌
async fn create_test_user_and_login(config: &E2EConfig) -> Result<(Uuid, String)> {
    let auth_helper = AuthHelper::new(config.clone());

    // 注册测试用户
    let register_response = auth_helper
        .register_user(TEST_USER_USERNAME, TEST_USER_PASSWORD, "<EMAIL>")
        .await?;

    // 登录获取JWT令牌
    let login_response = auth_helper
        .login_user(TEST_USER_USERNAME, TEST_USER_PASSWORD)
        .await?;

    // 调试：打印完整的登录响应结构
    println!(
        "🔍 完整登录响应: {}",
        serde_json::to_string_pretty(&login_response)?
    );

    // 从登录响应中获取用户ID（在body.data.user.id字段中）
    let user_id = login_response["body"]["data"]["user"]["id"]
        .as_str()
        .ok_or_else(|| anyhow::anyhow!("无法获取用户ID，响应结构: {}", login_response))?;
    let user_id = Uuid::parse_str(user_id)?;

    // 从登录响应中获取JWT令牌（在body.data.access_token字段中）
    let token = login_response["body"]["data"]["access_token"]
        .as_str()
        .ok_or_else(|| anyhow::anyhow!("无法获取JWT令牌，响应结构: {}", login_response))?
        .to_string();

    Ok((user_id, token))
}

/// 测试1：成功获取用户资料
#[tokio::test]
async fn test_fetch_user_profile_success() -> Result<()> {
    println!("🧪 开始测试：成功获取用户资料");

    let config = setup_test_environment().await?;
    let (user_id, token) = create_test_user_and_login(&config).await?;

    // 发送获取用户资料请求
    let client = Client::new();
    let response = client
        .get(&format!("{}/api/users/{}", config.base_url, user_id))
        .header("Authorization", format!("Bearer {}", token))
        .timeout(Duration::from_secs(API_TIMEOUT_SECONDS))
        .send()
        .await?;

    // 验证响应状态码
    assert_eq!(response.status(), 200, "应该返回200状态码");

    // 解析响应体
    let response_body: serde_json::Value = response.json().await?;
    println!(
        "📋 响应体: {}",
        serde_json::to_string_pretty(&response_body)?
    );

    // 验证响应结构
    assert!(
        response_body["success"].as_bool().unwrap_or(false),
        "响应应该标记为成功"
    );
    assert!(response_body["data"].is_object(), "应该包含用户数据");

    let user_data = &response_body["data"];

    // 验证用户基本信息
    assert_eq!(
        user_data["id"].as_str().unwrap(),
        user_id.to_string(),
        "用户ID应该匹配"
    );
    assert_eq!(
        user_data["username"].as_str().unwrap(),
        TEST_USER_USERNAME,
        "用户名应该匹配"
    );

    // 验证必需字段存在
    assert!(user_data["created_at"].is_string(), "应该包含创建时间");
    assert!(user_data["updated_at"].is_string(), "应该包含更新时间");

    // 验证敏感信息不被暴露
    assert!(user_data["password_hash"].is_null(), "不应该暴露密码哈希");

    println!("✅ 测试通过：成功获取用户资料");
    Ok(())
}

/// 测试2：未认证用户访问被拒绝
#[tokio::test]
async fn test_fetch_user_profile_unauthorized() -> Result<()> {
    println!("🧪 开始测试：未认证用户访问被拒绝");

    let config = setup_test_environment().await?;
    let (user_id, _) = create_test_user_and_login(&config).await?;

    let client = Client::new();

    // 不提供认证令牌发送请求
    let response = client
        .get(&format!("{}/api/users/{}", config.base_url, user_id))
        .timeout(Duration::from_secs(API_TIMEOUT_SECONDS))
        .send()
        .await?;

    // 验证返回401未授权状态码
    assert_eq!(response.status(), 401, "应该返回401未授权状态码");

    println!("✅ 测试通过：未认证用户访问被拒绝");
    Ok(())
}

/// 测试3：无效JWT令牌被拒绝
#[tokio::test]
async fn test_fetch_user_profile_invalid_token() -> Result<()> {
    println!("🧪 开始测试：无效JWT令牌被拒绝");

    let config = setup_test_environment().await?;
    let (user_id, _) = create_test_user_and_login(&config).await?;

    let client = Client::new();

    // 使用无效的JWT令牌
    let invalid_token = "invalid.jwt.token";
    let response = client
        .get(&format!("{}/api/users/{}", config.base_url, user_id))
        .header("Authorization", format!("Bearer {}", invalid_token))
        .timeout(Duration::from_secs(API_TIMEOUT_SECONDS))
        .send()
        .await?;

    // 验证返回401未授权状态码
    assert_eq!(response.status(), 401, "应该返回401未授权状态码");

    println!("✅ 测试通过：无效JWT令牌被拒绝");
    Ok(())
}

/// 测试4：用户不存在返回404
#[tokio::test]
async fn test_fetch_user_profile_not_found() -> Result<()> {
    println!("🧪 开始测试：用户不存在返回404");

    let config = setup_test_environment().await?;
    let (_, token) = create_test_user_and_login(&config).await?;

    // 使用不存在的用户ID
    let non_existent_user_id = Uuid::new_v4();
    let client = Client::new();
    let response = client
        .get(&format!(
            "{}/api/users/{}",
            config.base_url, non_existent_user_id
        ))
        .header("Authorization", format!("Bearer {}", token))
        .timeout(Duration::from_secs(API_TIMEOUT_SECONDS))
        .send()
        .await?;

    // 验证返回404状态码
    assert_eq!(response.status(), 404, "应该返回404状态码");

    // 验证错误响应格式
    let response_body: serde_json::Value = response.json().await?;
    println!(
        "🔍 404响应体: {}",
        serde_json::to_string_pretty(&response_body)?
    );
    assert!(
        !response_body["success"].as_bool().unwrap_or(true),
        "响应应该标记为失败"
    );
    assert!(
        response_body["error"].is_object() || response_body["error"].is_string(),
        "应该包含错误信息"
    );

    println!("✅ 测试通过：用户不存在返回404");
    Ok(())
}

/// 测试5：无效用户ID格式返回400
#[tokio::test]
async fn test_fetch_user_profile_invalid_id_format() -> Result<()> {
    println!("🧪 开始测试：无效用户ID格式返回400");

    let config = setup_test_environment().await?;
    let (_, token) = create_test_user_and_login(&config).await?;

    // 使用无效的用户ID格式
    let invalid_user_id = "invalid-uuid-format";
    let client = Client::new();
    let response = client
        .get(&format!(
            "{}/api/users/{}",
            config.base_url, invalid_user_id
        ))
        .header("Authorization", format!("Bearer {}", token))
        .timeout(Duration::from_secs(API_TIMEOUT_SECONDS))
        .send()
        .await?;

    // 验证返回400状态码
    assert_eq!(response.status(), 400, "应该返回400状态码");

    println!("✅ 测试通过：无效用户ID格式返回400");
    Ok(())
}

/// 测试6：响应时间性能测试
#[tokio::test]
async fn test_fetch_user_profile_performance() -> Result<()> {
    println!("🧪 开始测试：响应时间性能测试");

    let config = setup_test_environment().await?;
    let (user_id, token) = create_test_user_and_login(&config).await?;

    // 记录开始时间
    let start_time = std::time::Instant::now();

    // 发送获取用户资料请求
    let client = Client::new();
    let response = client
        .get(&format!("{}/api/users/{}", config.base_url, user_id))
        .header("Authorization", format!("Bearer {}", token))
        .timeout(Duration::from_secs(API_TIMEOUT_SECONDS))
        .send()
        .await?;

    // 计算响应时间
    let response_time = start_time.elapsed();

    // 验证响应成功
    assert_eq!(response.status(), 200, "应该返回200状态码");

    // 验证响应时间在合理范围内（小于1秒）
    assert!(
        response_time < Duration::from_secs(1),
        "响应时间应该小于1秒，实际: {:?}",
        response_time
    );

    println!(
        "✅ 测试通过：响应时间性能测试，响应时间: {:?}",
        response_time
    );
    Ok(())
}

/// 清理测试环境
async fn cleanup_test_environment() -> Result<()> {
    // 清理测试数据
    e2e::helpers::cleanup_test_data()?;
    println!("🧹 测试环境清理完成");
    Ok(())
}
