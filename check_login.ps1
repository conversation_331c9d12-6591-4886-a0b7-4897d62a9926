# Check login response structure
$baseUrl = "http://127.0.0.1:3000"

$loginBody = @{
    username = "testuser456"
    password = "password123"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/auth/login" -Method Post -ContentType "application/json" -Body $loginBody
    Write-Host "Login response structure:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 5 | Write-Host
    
    # Try to access token in different ways
    if ($response.data) {
        Write-Host "response.data exists" -ForegroundColor Cyan
        if ($response.data.token) {
            Write-Host "Token found: $($response.data.token.Substring(0,30))..." -ForegroundColor Green
        } else {
            Write-Host "No token in response.data" -ForegroundColor Red
        }
    } else {
        Write-Host "No response.data" -ForegroundColor Red
        if ($response.token) {
            Write-Host "Token found at root: $($response.token.Substring(0,30))..." -ForegroundColor Green
        }
    }
    
} catch {
    Write-Host "Login failed: $($_.Exception.Message)" -ForegroundColor Red
}
