//! # 查询优化功能集成测试
//!
//! 遵循TDD原则，先编写测试验证查询优化API的各项功能

use reqwest::Client;
use serde_json::{Value, json};
use std::time::Duration;
use tokio::time::sleep;

/// 测试配置
struct TestConfig {
    base_url: String,
    auth_token: Option<String>,
}

impl TestConfig {
    fn new() -> Self {
        Self {
            base_url: "http://127.0.0.1:3000".to_string(),
            auth_token: None,
        }
    }
}

/// 测试用户登录并获取JWT令牌
async fn login_and_get_token(
    client: &Client,
    config: &mut TestConfig,
) -> Result<(), Box<dyn std::error::Error>> {
    println!("🔐 正在登录获取认证令牌...");

    let login_payload = json!({
        "username": "testuser456",
        "password": "password123"
    });

    let response = client
        .post(&format!("{}/api/auth/login", config.base_url))
        .json(&login_payload)
        .send()
        .await?;

    let status = response.status();
    if status.is_success() {
        let login_response: Value = response.json().await?;
        println!(
            "登录响应: {}",
            serde_json::to_string_pretty(&login_response)?
        );

        if let Some(token) = login_response
            .get("data")
            .and_then(|data| data.get("access_token"))
            .and_then(|t| t.as_str())
        {
            config.auth_token = Some(token.to_string());
            println!("✅ 登录成功，获取到认证令牌");
            return Ok(());
        } else {
            println!("❌ 响应中未找到access_token字段");
        }
    }

    println!("❌ 登录失败: {}", status);
    Err("登录失败".into())
}

/// 测试1: 单个查询优化API
#[tokio::test]
async fn test_single_query_optimization() {
    println!("\n🧪 测试1: 单个查询优化API");

    let client = Client::new();
    let mut config = TestConfig::new();

    // 等待服务器启动
    sleep(Duration::from_secs(2)).await;

    // 登录获取令牌
    if let Err(e) = login_and_get_token(&client, &mut config).await {
        panic!("登录失败: {}", e);
    }

    // 测试查询优化
    let query_payload = json!({
        "query_sql": "SELECT * FROM messages WHERE chat_room_id = '123e4567-e89b-12d3-a456-426614174000' ORDER BY created_at DESC LIMIT 50",
        "apply_recommendations": false
    });

    let mut request = client
        .post(&format!("{}/api/query/optimize", config.base_url))
        .json(&query_payload);

    if let Some(token) = &config.auth_token {
        request = request.bearer_auth(token);
    }

    let response = request.send().await.expect("请求发送失败");

    // 验证响应状态
    assert!(
        response.status().is_success(),
        "查询优化API应该返回成功状态"
    );

    // 验证响应结构
    let optimization_result: Value = response.json().await.expect("响应解析失败");

    // 验证必要字段存在
    assert!(
        optimization_result.get("performance_metrics").is_some(),
        "应该包含性能指标"
    );
    assert!(
        optimization_result.get("optimization_strategies").is_some(),
        "应该包含优化策略"
    );
    assert!(optimization_result.get("summary").is_some(), "应该包含摘要");
    assert!(
        optimization_result.get("is_slow_query").is_some(),
        "应该包含慢查询标识"
    );

    println!("✅ 单个查询优化API测试通过");
}

/// 测试2: 批量查询优化API
#[tokio::test]
async fn test_batch_query_optimization() {
    println!("\n🧪 测试2: 批量查询优化API");

    let client = Client::new();
    let mut config = TestConfig::new();

    // 等待服务器启动
    sleep(Duration::from_secs(2)).await;

    // 登录获取令牌
    if let Err(e) = login_and_get_token(&client, &mut config).await {
        panic!("登录失败: {}", e);
    }

    // 测试批量查询优化
    let batch_payload = json!({
        "queries": [
            "SELECT * FROM messages WHERE sender_id = '123e4567-e89b-12d3-a456-426614174001' ORDER BY created_at DESC",
            "SELECT * FROM users WHERE status = 'active' AND last_login_at > NOW() - INTERVAL '1 day'",
            "SELECT COUNT(*) FROM messages WHERE chat_room_id = '123e4567-e89b-12d3-a456-426614174000'"
        ],
        "max_concurrent": 3
    });

    let mut request = client
        .post(&format!("{}/api/query/batch-optimize", config.base_url))
        .json(&batch_payload);

    if let Some(token) = &config.auth_token {
        request = request.bearer_auth(token);
    }

    let response = request.send().await.expect("请求发送失败");

    // 验证响应状态
    assert!(
        response.status().is_success(),
        "批量查询优化API应该返回成功状态"
    );

    // 验证响应结构
    let batch_result: Value = response.json().await.expect("响应解析失败");

    // 验证必要字段存在
    assert!(batch_result.get("results").is_some(), "应该包含优化结果");
    assert!(batch_result.get("summary").is_some(), "应该包含摘要");
    assert!(
        batch_result.get("processed_queries").is_some(),
        "应该包含处理的查询数量"
    );
    assert!(
        batch_result.get("successful_optimizations").is_some(),
        "应该包含成功优化数量"
    );

    // 验证处理的查询数量
    let processed_queries = batch_result["processed_queries"].as_u64().unwrap();
    assert_eq!(processed_queries, 3, "应该处理3个查询");

    println!("✅ 批量查询优化API测试通过");
}

/// 测试3: 数据库统计信息API
#[tokio::test]
async fn test_database_stats_api() {
    println!("\n🧪 测试3: 数据库统计信息API");

    let client = Client::new();
    let mut config = TestConfig::new();

    // 等待服务器启动
    sleep(Duration::from_secs(2)).await;

    // 登录获取令牌
    if let Err(e) = login_and_get_token(&client, &mut config).await {
        panic!("登录失败: {}", e);
    }

    // 测试获取数据库统计信息
    let mut request = client.get(&format!(
        "{}/api/query/database-stats?include_details=true",
        config.base_url
    ));

    if let Some(token) = &config.auth_token {
        request = request.bearer_auth(token);
    }

    let response = request.send().await.expect("请求发送失败");

    // 验证响应状态
    assert!(
        response.status().is_success(),
        "数据库统计信息API应该返回成功状态"
    );

    // 验证响应结构
    let stats_result: Value = response.json().await.expect("响应解析失败");

    // 验证必要字段存在
    assert!(stats_result.get("stats").is_some(), "应该包含统计信息");
    assert!(
        stats_result.get("collected_at").is_some(),
        "应该包含收集时间"
    );
    assert!(stats_result.get("details").is_some(), "应该包含详细信息");

    // 验证统计信息结构
    let stats = &stats_result["stats"];
    assert!(
        stats.get("database_size_bytes").is_some(),
        "应该包含数据库大小"
    );
    assert!(
        stats.get("active_connections").is_some(),
        "应该包含活跃连接数"
    );
    assert!(stats.get("cache_hit_ratio").is_some(), "应该包含缓存命中率");
    assert!(stats.get("total_queries").is_some(), "应该包含总查询数");
    assert!(stats.get("slow_queries").is_some(), "应该包含慢查询数");

    println!("✅ 数据库统计信息API测试通过");
}

/// 测试4: 索引推荐API
#[tokio::test]
async fn test_index_recommendations_api() {
    println!("\n🧪 测试4: 索引推荐API");

    let client = Client::new();
    let mut config = TestConfig::new();

    // 等待服务器启动
    sleep(Duration::from_secs(2)).await;

    // 登录获取令牌
    if let Err(e) = login_and_get_token(&client, &mut config).await {
        panic!("登录失败: {}", e);
    }

    // 测试获取索引推荐
    let mut request = client.get(&format!(
        "{}/api/query/index-recommendations",
        config.base_url
    ));

    if let Some(token) = &config.auth_token {
        request = request.bearer_auth(token);
    }

    let response = request.send().await.expect("请求发送失败");

    // 验证响应状态
    assert!(
        response.status().is_success(),
        "索引推荐API应该返回成功状态"
    );

    // 验证响应结构
    let recommendations_result: Value = response.json().await.expect("响应解析失败");

    // 验证必要字段存在
    assert!(
        recommendations_result.get("recommendations").is_some(),
        "应该包含推荐列表"
    );
    assert!(
        recommendations_result
            .get("total_recommendations")
            .is_some(),
        "应该包含推荐总数"
    );
    assert!(
        recommendations_result
            .get("expected_total_improvement")
            .is_some(),
        "应该包含预期总体性能提升"
    );

    // 验证推荐列表是数组
    assert!(
        recommendations_result["recommendations"].is_array(),
        "推荐列表应该是数组"
    );

    println!("✅ 索引推荐API测试通过");
}

/// 测试5: 错误处理 - 无效查询
#[tokio::test]
async fn test_invalid_query_error_handling() {
    println!("\n🧪 测试5: 错误处理 - 无效查询");

    let client = Client::new();
    let mut config = TestConfig::new();

    // 等待服务器启动
    sleep(Duration::from_secs(2)).await;

    // 登录获取令牌
    if let Err(e) = login_and_get_token(&client, &mut config).await {
        panic!("登录失败: {}", e);
    }

    // 测试空查询
    let empty_query_payload = json!({
        "query_sql": "",
        "apply_recommendations": false
    });

    let mut request = client
        .post(&format!("{}/api/query/optimize", config.base_url))
        .json(&empty_query_payload);

    if let Some(token) = &config.auth_token {
        request = request.bearer_auth(token);
    }

    let response = request.send().await.expect("请求发送失败");

    // 空查询可能会被处理，但不应该崩溃
    // 这里主要测试API的健壮性
    println!("空查询响应状态: {}", response.status());

    println!("✅ 错误处理测试通过");
}

/// 测试6: 性能测试 - 并发查询优化
#[tokio::test]
async fn test_concurrent_query_optimization() {
    println!("\n🧪 测试6: 性能测试 - 并发查询优化");

    let client = Client::new();
    let mut config = TestConfig::new();

    // 等待服务器启动
    sleep(Duration::from_secs(2)).await;

    // 登录获取令牌
    if let Err(e) = login_and_get_token(&client, &mut config).await {
        panic!("登录失败: {}", e);
    }

    let start_time = std::time::Instant::now();

    // 并发执行多个优化请求
    let mut handles = Vec::new();

    for i in 0..5 {
        let client = client.clone();
        let base_url = config.base_url.clone();
        let token = config.auth_token.clone();
        let query_sql = format!(
            "SELECT * FROM messages WHERE chat_room_id = '123e4567-e89b-12d3-a456-42661417400{}' ORDER BY created_at DESC LIMIT 10",
            i
        );

        let handle = tokio::spawn(async move {
            let query_payload = json!({
                "query_sql": query_sql,
                "apply_recommendations": false
            });

            let mut request = client
                .post(&format!("{}/api/query/optimize", base_url))
                .json(&query_payload);

            if let Some(token) = &token {
                request = request.bearer_auth(token);
            }

            request.send().await
        });

        handles.push(handle);
    }

    // 等待所有请求完成
    let mut success_count = 0;
    for handle in handles {
        match handle.await {
            Ok(Ok(response)) => {
                if response.status().is_success() {
                    success_count += 1;
                }
            }
            _ => {}
        }
    }

    let duration = start_time.elapsed();

    // 验证性能要求
    assert!(success_count >= 4, "至少应该有4个请求成功");
    assert!(duration.as_secs() < 10, "并发请求应该在10秒内完成");

    println!(
        "✅ 并发性能测试通过 - 成功请求数: {}, 耗时: {:?}",
        success_count, duration
    );
}
