//! # 任务23：错误处理机制简化测试
//!
//! 基于Context7 MCP最佳实践，验证核心错误处理功能

use app_common::{
    error::AppError,
    utils::{
        <PERSON>rror<PERSON>ate<PERSON>y, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rror<PERSON><PERSON>lerTool as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON>rrorRecoveryTool as <PERSON>rrorR<PERSON><PERSON>yManager, ErrorSeverity, RecoveryStrategy,
    },
};
use uuid::Uuid;

/// 测试thiserror错误定义
#[tokio::test]
async fn test_thiserror_error_definition() {
    // 测试任务未找到错误
    let task_id = Uuid::new_v4();
    let error = AppError::TaskNotFound(task_id);

    // 验证错误消息格式
    let error_message = error.to_string();
    assert!(error_message.contains(&task_id.to_string()));
    assert!(error_message.contains("未找到ID为"));

    // 测试用户已存在错误
    let username = "testuser";
    let error = AppError::UserAlreadyExists(username.to_string());
    let error_message = error.to_string();
    assert!(error_message.contains(username));
    assert!(error_message.contains("已存在"));

    println!("✅ thiserror错误定义测试通过");
}

/// 测试错误处理器
#[tokio::test]
async fn test_error_handler() {
    // 测试不同类型的错误处理
    let test_cases = vec![
        (
            AppError::TaskNotFound(Uuid::new_v4()),
            axum::http::StatusCode::NOT_FOUND,
            ErrorCategory::UserInput,
            ErrorSeverity::Low,
        ),
        (
            AppError::Forbidden("权限不足".to_string()),
            axum::http::StatusCode::FORBIDDEN,
            ErrorCategory::Authentication,
            ErrorSeverity::Medium,
        ),
        (
            AppError::DatabaseError("连接失败".to_string()),
            axum::http::StatusCode::INTERNAL_SERVER_ERROR,
            ErrorCategory::Database,
            ErrorSeverity::High,
        ),
        (
            AppError::RateLimited,
            axum::http::StatusCode::TOO_MANY_REQUESTS,
            ErrorCategory::System,
            ErrorSeverity::Medium,
        ),
    ];

    for (error, expected_status, expected_category, expected_severity) in test_cases {
        let (status_code, context) = ErrorHandler::handle_error(&error);

        assert_eq!(status_code, expected_status);
        assert_eq!(context.category, expected_category);
        assert_eq!(context.severity, expected_severity);
        assert!(!context.error_id.is_empty());
        assert!(!context.user_message.is_empty());
    }

    println!("✅ 错误处理器测试通过");
}

/// 测试错误恢复策略
#[tokio::test]
async fn test_error_recovery_strategies() {
    let test_cases = vec![
        (AppError::Timeout, "Retry"),
        (AppError::ServiceUnavailable, "Retry"),
        (AppError::RateLimited, "Retry"),
        (AppError::InvalidCredentials, "FailFast"),
        (AppError::Forbidden("test".to_string()), "FailFast"),
    ];

    for (error, expected_strategy_type) in test_cases {
        let strategy = ErrorRecoveryManager::get_recovery_strategy(&error);

        match (&strategy, expected_strategy_type) {
            (RecoveryStrategy::Retry { .. }, "Retry") => {
                // 验证重试策略
                if let RecoveryStrategy::Retry {
                    max_attempts,
                    delay_ms,
                } = strategy
                {
                    assert!(max_attempts > 0);
                    assert!(delay_ms > 0);
                }
            }
            (RecoveryStrategy::FailFast, "FailFast") => {
                // 验证快速失败策略
            }
            (RecoveryStrategy::Fallback, "Fallback") => {
                // 验证降级策略
            }
            _ => panic!(
                "错误恢复策略不匹配: 期望 {}, 得到 {:?}",
                expected_strategy_type, strategy
            ),
        }
    }

    println!("✅ 错误恢复策略测试通过");
}

/// 测试错误上下文创建
#[tokio::test]
async fn test_error_context_creation() {
    let context = ErrorContext::new(ErrorCategory::UserInput, ErrorSeverity::Low, "测试错误消息")
        .with_technical_details("技术详细信息")
        .with_suggested_action("建议的解决方案")
        .with_request_id("req-123");

    assert_eq!(context.category, ErrorCategory::UserInput);
    assert_eq!(context.severity, ErrorSeverity::Low);
    assert_eq!(context.user_message, "测试错误消息");
    assert_eq!(context.technical_details, Some("技术详细信息".to_string()));
    assert_eq!(context.suggested_action, Some("建议的解决方案".to_string()));
    assert_eq!(context.request_id, Some("req-123".to_string()));
    assert!(!context.error_id.is_empty());

    println!("✅ 错误上下文创建测试通过");
}

/// 测试基本的错误转换
#[tokio::test]
async fn test_basic_error_conversion() {
    // 测试从sea_orm::DbErr转换
    let db_error = sea_orm::DbErr::ConnectionAcquire(sea_orm::error::ConnAcquireErr::Timeout);
    let app_error = AppError::from(db_error);

    match app_error {
        AppError::DatabaseConnection(_) => {
            println!("✅ 数据库错误转换成功");
        }
        _ => panic!("数据库错误转换失败"),
    }

    // 测试从ValidationErrors转换
    let validation_errors = validator::ValidationErrors::new();
    let app_error = AppError::from(validation_errors);

    match app_error {
        AppError::InputValidation(_) => {
            println!("✅ 验证错误转换成功");
        }
        _ => panic!("验证错误转换失败"),
    }

    println!("✅ 基本错误转换测试通过");
}

/// 性能测试：错误处理性能
#[tokio::test]
async fn test_error_handling_performance() {
    let start = std::time::Instant::now();

    // 创建大量错误并处理
    for _ in 0..1000 {
        let error = AppError::TaskNotFound(Uuid::new_v4());
        let (_status, _context) = ErrorHandler::handle_error(&error);
    }

    let duration = start.elapsed();

    // 验证性能要求（1000个错误处理应该在100ms内完成）
    assert!(
        duration < std::time::Duration::from_millis(100),
        "错误处理性能不达标: {:?}",
        duration
    );

    println!("✅ 错误处理性能测试通过 (耗时: {:?})", duration);
}
