//! # 测试库模块
//!
//! 为Axum企业级消息搜索功能提供完整的测试基础设施和工具。
//! 包括数据库连接池、缓存管理、测试数据生成、性能测试等功能。
//!
//! ## 模块组织
//! - `test_utils`: 通用测试工具和宏
//! - `test_infrastructure`: 测试基础设施管理
//! - `test_data_manager`: 测试数据管理
//! - `message_search_test_framework`: 消息搜索测试框架
//!
//! ## 使用示例
//! ```rust
//! use tests::{TestInfrastructure, TestDataManager};
//!
//! #[tokio::test]
//! async fn test_example() -> anyhow::Result<()> {
//!     let mut infrastructure = TestInfrastructure::new();
//!     infrastructure.initialize().await?;
//!
//!     // 执行测试...
//!
//!     infrastructure.shutdown().await?;
//!     Ok(())
//! }
//! ```

// 公共模块
pub mod message_search_integration_test;
pub mod message_search_test_framework;
pub mod resilience_validation_test;
pub mod task_52_8_integration_test_runner;
pub mod test_data_manager;
pub mod test_infrastructure;
pub mod test_utils;

// 重新导出主要类型
pub use test_infrastructure::{InfrastructureStatus, TestInfrastructure, TestInfrastructureConfig};

pub use test_data_manager::{
    TestDataConfig, TestDataItem, TestDataManager, TestDataType, TestDataset,
};

pub use test_utils::{
    CacheConfig, CacheStatus, DatabaseConfig, DatabasePoolStatus, TestAssertions, TestCacheManager,
    TestDatabaseManager, TestEnvironment, TestTask, TestTaskBuilder, TestUser, TestUserBuilder,
};

pub use message_search_test_framework::{
    ConcurrencyConfig, CoverageReport, LatencyStats, MessageSearchTestConfig,
    MessageSearchTestFramework, MessageSearchTestReport, MetricsCollector, PerformanceMetrics,
    PerformanceThresholds, ResourceUsage, SearchTestScenario, TestExecutionResult, TestMessage,
    TestMessageType,
};

// 测试宏重新导出 - 这些宏需要在test_utils中实际定义
// pub use test_utils::{
//     assert_error_contains,
//     assert_ok_and_extract,
//     create_test_user,
//     create_test_task
// };

/// 测试常量
pub mod constants {
    use std::time::Duration;

    /// 默认测试超时时间
    pub const DEFAULT_TEST_TIMEOUT: Duration = Duration::from_secs(300);

    /// 默认数据库连接超时
    pub const DEFAULT_DB_TIMEOUT: Duration = Duration::from_secs(10);

    /// 默认缓存连接超时
    pub const DEFAULT_CACHE_TIMEOUT: Duration = Duration::from_secs(5);

    /// 默认测试数据大小
    pub const DEFAULT_TEST_DATA_SIZE: usize = 1000;

    /// 默认批量操作大小
    pub const DEFAULT_BATCH_SIZE: usize = 100;

    /// 性能测试阈值
    pub mod performance {
        use std::time::Duration;

        /// 数据库ping延迟阈值
        pub const DB_PING_THRESHOLD: Duration = Duration::from_millis(100);

        /// 缓存ping延迟阈值
        pub const CACHE_PING_THRESHOLD: Duration = Duration::from_millis(50);

        /// 搜索P99延迟阈值
        pub const SEARCH_P99_THRESHOLD: Duration = Duration::from_millis(200);

        /// 搜索P95延迟阈值
        pub const SEARCH_P95_THRESHOLD: Duration = Duration::from_millis(100);

        /// 最小缓存命中率
        pub const MIN_CACHE_HIT_RATIO: f64 = 0.8;

        /// 最大错误率
        pub const MAX_ERROR_RATE: f64 = 0.01;
    }
}

/// 测试辅助函数
pub mod helpers {
    use anyhow::Result;
    use std::time::Duration;
    use tracing::info;

    /// 初始化测试日志
    pub fn init_test_logging() {
        let _ = tracing_subscriber::fmt()
            .with_env_filter("debug")
            .with_test_writer()
            .try_init();
    }

    /// 等待指定时间
    pub async fn wait_for(duration: Duration) {
        tokio::time::sleep(duration).await;
    }

    /// 重试执行函数直到成功或达到最大重试次数
    pub async fn retry_until_success<F, Fut, T>(
        mut operation: F,
        max_retries: usize,
        delay: Duration,
    ) -> Result<T>
    where
        F: FnMut() -> Fut,
        Fut: std::future::Future<Output = Result<T>>,
    {
        let mut last_error = None;

        for attempt in 0..=max_retries {
            match operation().await {
                Ok(result) => {
                    return Ok(result);
                }
                Err(e) => {
                    last_error = Some(e);
                    if attempt < max_retries {
                        info!("重试操作 {}/{}", attempt + 1, max_retries);
                        tokio::time::sleep(delay).await;
                    }
                }
            }
        }

        Err(last_error.unwrap_or_else(|| anyhow::anyhow!("操作失败")))
    }

    /// 生成随机测试ID
    pub fn generate_test_id() -> String {
        format!("test_{}", uuid::Uuid::new_v4().simple())
    }

    /// 生成测试用的随机字符串
    pub fn generate_random_string(length: usize) -> String {
        use rand::Rng;
        const CHARSET: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZ\
                                abcdefghijklmnopqrstuvwxyz\
                                0123456789";
        let mut rng = rand::thread_rng();

        (0..length)
            .map(|_| {
                let idx = rng.gen_range(0..CHARSET.len());
                CHARSET[idx] as char
            })
            .collect()
    }

    /// 验证环境变量是否设置
    pub fn verify_test_environment() -> Result<()> {
        let required_vars = ["DATABASE_URL", "DRAGONFLY_URL"];

        for var in &required_vars {
            if std::env::var(var).is_err() {
                return Err(anyhow::anyhow!("缺少必需的环境变量: {}", var));
            }
        }

        Ok(())
    }
}

/// 测试配置预设
pub mod presets {
    use super::*;
    use std::time::Duration;

    /// 快速测试配置（用于单元测试）
    pub fn fast_test_config() -> TestInfrastructureConfig {
        TestInfrastructureConfig {
            database: test_infrastructure::DatabaseConfig {
                url: std::env
                    ::var("DATABASE_URL")
                    .unwrap_or_else(|_|
                        "postgres://axum_user:axum_secure_password_2025@localhost:5432/axum_tutorial_test".to_string()
                    ),
                max_connections: 5,
                timeout: Duration::from_secs(30),
            },
            cache: test_infrastructure::CacheConfig {
                url: std::env
                    ::var("DRAGONFLY_URL")
                    .unwrap_or_else(|_| "redis://localhost:6379".to_string()),
                max_connections: 5,
                timeout: Duration::from_secs(30),
            },
            auto_cleanup: true,
            health_check_interval: Duration::from_secs(10),
            test_timeout: Duration::from_secs(60),
        }
    }

    /// 性能测试配置（用于压力测试）
    pub fn performance_test_config() -> TestInfrastructureConfig {
        TestInfrastructureConfig {
            database: test_infrastructure::DatabaseConfig {
                url: std::env
                    ::var("DATABASE_URL")
                    .unwrap_or_else(|_|
                        "postgres://axum_user:axum_secure_password_2025@localhost:5432/axum_tutorial_test".to_string()
                    ),
                max_connections: 50,
                timeout: Duration::from_secs(30),
            },
            cache: test_infrastructure::CacheConfig {
                url: std::env
                    ::var("DRAGONFLY_URL")
                    .unwrap_or_else(|_| "redis://localhost:6379".to_string()),
                max_connections: 20,
                timeout: Duration::from_secs(30),
            },
            auto_cleanup: true,
            health_check_interval: Duration::from_secs(30),
            test_timeout: Duration::from_secs(600),
        }
    }
}
