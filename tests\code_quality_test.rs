//! 代码质量检查测试
//!
//! 本测试模块验证项目的代码质量标准，包括：
//! - Clippy静态分析检查
//! - 代码格式化检查
//! - 依赖版本一致性检查
//! - 架构模式合规性检查

use std::path::Path;
use std::process::Command;

/// 测试clippy静态分析检查 - 2025年最新最佳实践
#[test]
fn test_clippy_analysis() {
    println!("🔍 运行Clippy静态分析检查（2025年最新标准）...");

    let output = Command::new("cargo")
        .args([
            "clippy",
            "--all-targets",
            "--all-features",
            "--workspace",
            "--",
            "-D",
            "warnings",
            "-D",
            "clippy::all",
            "-D",
            "clippy::pedantic",
            "-W",
            "clippy::nursery",
            "-D",
            "clippy::cognitive_complexity",
            "-D",
            "clippy::too_many_arguments",
            "-D",
            "clippy::type_complexity",
            "-D",
            "clippy::unwrap_used",
            "-D",
            "clippy::expect_used",
            "-D",
            "clippy::panic",
            "-D",
            "clippy::missing_docs_in_private_items",
        ])
        .output()
        .expect("Failed to execute cargo clippy");

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        let stdout = String::from_utf8_lossy(&output.stdout);

        println!("❌ Clippy检查失败:");
        println!("STDOUT: {stdout}");
        println!("STDERR: {stderr}");

        panic!("Clippy检查发现代码质量问题，请修复后重试");
    }

    println!("✅ Clippy静态分析检查通过（2025年最新标准）");
}

/// 测试代码格式化检查
#[test]
fn test_code_formatting() {
    println!("🎨 检查代码格式化...");

    let output = Command::new("cargo")
        .args(["fmt", "--all", "--", "--check"])
        .output()
        .expect("Failed to execute cargo fmt");

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        let stdout = String::from_utf8_lossy(&output.stdout);

        println!("❌ 代码格式化检查失败:");
        println!("STDOUT: {stdout}");
        println!("STDERR: {stderr}");

        panic!("代码格式不符合标准，请运行 'cargo fmt' 修复");
    }

    println!("✅ 代码格式化检查通过");
}

/// 测试依赖版本一致性
#[test]
fn test_dependency_consistency() {
    println!("📦 检查依赖版本一致性...");

    // 检查workspace Cargo.toml是否存在
    assert!(
        Path::new("Cargo.toml").exists(),
        "Workspace Cargo.toml不存在"
    );

    // 检查关键依赖版本
    let cargo_toml = std::fs::read_to_string("Cargo.toml").expect("无法读取Cargo.toml文件");

    // 验证关键依赖版本
    assert!(
        cargo_toml.contains("edition = \"2024\""),
        "Rust edition应为2024"
    );
    assert!(
        cargo_toml.contains("axum = { version = \"0.8.4\""),
        "Axum版本应为0.8.4"
    );
    assert!(
        cargo_toml.contains("sea-orm = { version = \"1.1.12\""),
        "SeaORM版本应为1.1.12"
    );
    assert!(
        cargo_toml.contains("tokio = { version = \"1.45.1\""),
        "Tokio版本应为1.45.1"
    );

    println!("✅ 依赖版本一致性检查通过");
}

/// 测试架构模式合规性
#[test]
fn test_architecture_compliance() {
    println!("🏗️ 检查架构模式合规性...");

    // 检查DDD架构目录结构
    let required_dirs = [
        "crates/app_common",
        "crates/app_domain",
        "crates/app_application",
        "crates/app_infrastructure",
    ];

    for dir in &required_dirs {
        assert!(Path::new(dir).exists(), "缺少必需的架构目录: {dir}");

        // 检查每个crate是否有Cargo.toml
        let cargo_path = format!("{dir}/Cargo.toml");
        assert!(
            Path::new(&cargo_path).exists(),
            "缺少Cargo.toml: {cargo_path}"
        );

        // 检查每个crate是否有src目录
        let src_path = format!("{dir}/src");
        assert!(Path::new(&src_path).exists(), "缺少src目录: {src_path}");

        // 检查每个crate是否有lib.rs
        let lib_path = format!("{dir}/src/lib.rs");
        assert!(Path::new(&lib_path).exists(), "缺少lib.rs: {lib_path}");
    }

    println!("✅ 架构模式合规性检查通过");
}

/// 测试模块导出完整性
#[test]
fn test_module_exports() {
    println!("📤 检查模块导出完整性...");

    // 检查各个crate的lib.rs是否正确导出模块
    let crates = [
        (
            "crates/app_common/src/lib.rs",
            vec!["error", "utils", "config"],
        ),
        (
            "crates/app_domain/src/lib.rs",
            vec!["entities", "repositories", "services"],
        ),
        (
            "crates/app_application/src/lib.rs",
            vec!["user_service", "task_service"],
        ),
        (
            "crates/app_infrastructure/src/lib.rs",
            vec!["database", "user_repository"],
        ),
    ];

    for (lib_path, expected_modules) in &crates {
        if Path::new(lib_path).exists() {
            let content = std::fs::read_to_string(lib_path)
                .unwrap_or_else(|_| panic!("无法读取文件: {lib_path}"));

            for module in expected_modules {
                let pub_mod = format!("pub mod {module}");
                let pub_use = format!("pub use {module}::");

                assert!(
                    content.contains(&pub_mod) || content.contains(&pub_use),
                    "在{lib_path}中缺少模块导出: {module}"
                );
            }
        }
    }

    println!("✅ 模块导出完整性检查通过");
}

/// 测试编译检查
#[test]
fn test_compilation() {
    println!("🔨 运行编译检查...");

    let output = Command::new("cargo")
        .args(["check", "--all-targets", "--all-features", "--workspace"])
        .output()
        .expect("Failed to execute cargo check");

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        let stdout = String::from_utf8_lossy(&output.stdout);

        println!("❌ 编译检查失败:");
        println!("STDOUT: {stdout}");
        println!("STDERR: {stderr}");

        panic!("项目编译失败，请修复编译错误");
    }

    println!("✅ 编译检查通过");
}

/// 测试单元测试覆盖率
#[test]
fn test_unit_tests() {
    println!("🧪 运行单元测试...");

    let output = Command::new("cargo")
        .args(["test", "--workspace", "--lib"])
        .output()
        .expect("Failed to execute cargo test");

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        let stdout = String::from_utf8_lossy(&output.stdout);

        println!("❌ 单元测试失败:");
        println!("STDOUT: {stdout}");
        println!("STDERR: {stderr}");

        panic!("单元测试失败，请修复测试错误");
    }

    println!("✅ 单元测试通过");
}

/// 测试DDD架构边界验证
#[test]
fn test_ddd_architecture_boundaries() {
    println!("🏛️ 验证DDD架构边界...");

    // 验证领域层不依赖接口层
    verify_domain_independence();

    // 验证公共层不依赖接口层
    verify_common_independence();

    // 验证依赖方向正确性
    verify_dependency_direction();

    // 验证migration crate依赖一致性
    verify_migration_dependencies();

    println!("✅ DDD架构边界验证通过");
}

/// 验证领域层独立性 - 不应依赖接口层
fn verify_domain_independence() {
    let domain_cargo_path = Path::new("crates/app_domain/Cargo.toml");
    if !domain_cargo_path.exists() {
        return;
    }

    let content = std::fs::read_to_string(domain_cargo_path).unwrap();

    // 检查是否依赖app_interfaces
    assert!(
        !content.contains("app_interfaces"),
        "领域层(app_domain)不应该依赖接口层(app_interfaces)"
    );
}

/// 验证公共层独立性 - 不应依赖接口层
fn verify_common_independence() {
    let common_cargo_path = Path::new("crates/app_common/Cargo.toml");
    if !common_cargo_path.exists() {
        return;
    }

    let content = std::fs::read_to_string(common_cargo_path).unwrap();

    // 检查是否依赖app_interfaces
    assert!(
        !content.contains("app_interfaces"),
        "公共层(app_common)不应该依赖接口层(app_interfaces)"
    );
}

/// 验证依赖方向正确性
fn verify_dependency_direction() {
    // 验证应用层依赖关系
    let app_cargo_path = Path::new("crates/app_application/Cargo.toml");
    if app_cargo_path.exists() {
        let content = std::fs::read_to_string(app_cargo_path).unwrap();
        assert!(
            content.contains("app_domain")
                && content.contains("app_common")
                && content.contains("app_interfaces"),
            "应用层应该依赖领域层、公共层和接口层"
        );
    }

    // 验证基础设施层依赖关系
    let infra_cargo_path = Path::new("crates/app_infrastructure/Cargo.toml");
    if infra_cargo_path.exists() {
        let content = std::fs::read_to_string(infra_cargo_path).unwrap();
        assert!(
            content.contains("app_domain")
                && content.contains("app_common")
                && content.contains("app_interfaces"),
            "基础设施层应该依赖领域层、公共层和接口层"
        );
    }

    // 验证服务器层依赖关系
    let server_cargo_path = Path::new("server/Cargo.toml");
    if server_cargo_path.exists() {
        let content = std::fs::read_to_string(server_cargo_path).unwrap();
        assert!(
            content.contains("app_application")
                && content.contains("app_infrastructure")
                && content.contains("app_interfaces"),
            "服务器层应该依赖应用层、基础设施层和接口层"
        );
    }
}

/// 验证migration crate依赖一致性
fn verify_migration_dependencies() {
    let migration_cargo_path = Path::new("migration/Cargo.toml");
    if !migration_cargo_path.exists() {
        return;
    }

    let content = std::fs::read_to_string(migration_cargo_path).unwrap();

    // 检查是否使用workspace依赖继承
    assert!(
        content.contains("edition = \"2024\""),
        "migration crate应该使用Rust edition 2024"
    );

    // 检查SeaORM版本一致性
    assert!(
        content.contains("sea-orm = { version = \"1.1.12\"")
            || content.contains("sea-orm = { workspace = true }"),
        "migration crate的SeaORM版本应该与workspace保持一致"
    );
}

/// 测试Cargo Deny安全检查 - 2025年最新安全标准
#[test]
fn test_cargo_deny_security() {
    println!("🛡️ 执行Cargo Deny安全检查（2025年最新标准）...");

    // 首先检查cargo-deny是否已安装
    let check_install = Command::new("cargo").args(["deny", "--version"]).output();

    if check_install.is_err() {
        println!("⚠️ cargo-deny未安装，跳过安全检查");
        println!("💡 请运行 'cargo install cargo-deny' 安装工具");
        return;
    }

    let output = Command::new("cargo")
        .args(["deny", "check", "--all-features"])
        .output()
        .expect("Failed to execute cargo deny");

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        let stdout = String::from_utf8_lossy(&output.stdout);

        println!("❌ Cargo deny检查失败:");
        println!("STDOUT: {stdout}");
        println!("STDERR: {stderr}");

        panic!("发现安全漏洞或许可证问题，请修复后重试");
    }

    println!("✅ Cargo Deny安全检查通过");
}

/// 测试Cargo Audit漏洞检查 - 2025年最新安全标准
#[test]
fn test_cargo_audit_vulnerabilities() {
    println!("🔒 执行Cargo Audit漏洞检查（2025年最新标准）...");

    // 首先检查cargo-audit是否已安装
    let check_install = Command::new("cargo").args(["audit", "--version"]).output();

    if check_install.is_err() {
        println!("⚠️ cargo-audit未安装，跳过漏洞检查");
        println!("💡 请运行 'cargo install cargo-audit' 安装工具");
        return;
    }

    let output = Command::new("cargo")
        .args(["audit", "--deny", "warnings"])
        .output()
        .expect("Failed to execute cargo audit");

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        let stdout = String::from_utf8_lossy(&output.stdout);

        // 检查是否是因为没有漏洞数据库而失败
        if stderr.contains("advisory database") || stderr.contains("database") {
            println!("⚠️ 安全漏洞数据库需要更新，尝试更新...");
            let _update = Command::new("cargo").args(["audit", "--update"]).output();
            return;
        }

        println!("❌ Cargo audit检查失败:");
        println!("STDOUT: {stdout}");
        println!("STDERR: {stderr}");

        panic!("发现安全漏洞，请修复后重试");
    }

    println!("✅ Cargo Audit漏洞检查通过");
}

/// 测试代码质量配置文件完整性 - 2025年最新标准
#[test]
fn test_quality_configuration_files() {
    println!("📋 检查代码质量配置文件（2025年最新标准）...");

    // 检查必需的配置文件
    assert!(
        Path::new("deny.toml").exists(),
        "❌ deny.toml配置文件不存在"
    );
    println!("✅ deny.toml配置文件存在");

    assert!(Path::new("Cargo.toml").exists(), "❌ Cargo.toml文件不存在");
    println!("✅ Cargo.toml文件存在");

    // 检查deny.toml配置内容
    let deny_content = std::fs::read_to_string("deny.toml").expect("无法读取deny.toml文件");

    // 验证关键安全配置
    assert!(
        deny_content.contains("[advisories]"),
        "deny.toml缺少advisories配置"
    );
    assert!(
        deny_content.contains("[licenses]"),
        "deny.toml缺少licenses配置"
    );
    assert!(deny_content.contains("[bans]"), "deny.toml缺少bans配置");
    assert!(
        deny_content.contains("[sources]"),
        "deny.toml缺少sources配置"
    );

    // 检查是否有clippy配置（可选但推荐）
    let has_clippy_config = Path::new("clippy.toml").exists() || Path::new(".clippy.toml").exists();
    if has_clippy_config {
        println!("✅ Clippy配置文件存在");
    } else {
        println!("⚠️ Clippy配置文件不存在（将使用默认配置）");
    }

    // 检查rustfmt配置（可选）
    if Path::new("rustfmt.toml").exists() || Path::new(".rustfmt.toml").exists() {
        println!("✅ Rustfmt配置文件存在");
    } else {
        println!("⚠️ Rustfmt配置文件不存在（将使用默认配置）");
    }

    println!("✅ 配置文件检查完成");
}
