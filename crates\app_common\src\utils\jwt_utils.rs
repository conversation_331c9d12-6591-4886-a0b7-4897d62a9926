//! JWT 工具模块 - 统一认证系统
//!
//! 本模块提供统一的 JWT 相关工具函数，支持基础认证和RBAC权限控制。
//! 包含 JWT token 的创建、验证、解析等通用功能。
//!
//! ## 核心功能
//! - **统一的 JWT 验证逻辑**：HTTP 和 WebSocket 共用
//! - **Token 创建工具**：支持基础Claims和扩展Claims（含角色信息）
//! - **错误处理统一**：统一的错误类型和处理
//! - **向后兼容性**：保持现有API不变，新增RBAC功能
//!
//! ## 设计原则
//! - **DRY 原则**：避免重复代码
//! - **单一职责**：专注于 JWT 相关操作
//! - **可测试性**：便于单元测试
//! - **向后兼容**：保持现有接口稳定

use chrono::{Duration, Utc};
use jsonwebtoken::{DecodingKey, EncodingKey, Header, Validation, decode, encode};
use serde::{Deserialize, Serialize};

// JWT Claims 结构体已移至 app_interfaces 模块以避免重复定义
// 这里重新导出以保持向后兼容性
pub use app_interfaces::Claims;

// 重新导出角色相关类型
use app_interfaces::auth::UserRole;

/// 扩展的JWT Claims，包含角色信息
///
/// 用于支持RBAC权限控制的JWT token
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct ExtendedClaims {
    /// 用户ID (Subject)
    pub sub: String,
    /// 用户名
    pub username: String,
    /// 用户角色
    pub role: String,
    /// 过期时间 (Expiration Time)
    pub exp: i64,
    /// 签发时间 (Issued At)
    pub iat: i64,
    /// 生效时间 (Not Before) - 可选
    pub nbf: Option<i64>,
    /// 签发者 (Issuer) - 可选
    pub iss: Option<String>,
    /// 受众 (Audience) - 可选
    pub aud: Option<String>,
    /// JWT ID - 用于token撤销
    pub jti: Option<String>,
}

impl ExtendedClaims {
    /// 创建新的扩展Claims
    pub fn new(user_id: &str, username: &str, role: UserRole, expires_in_hours: i64) -> Self {
        let now = Utc::now();
        Self {
            sub: user_id.to_string(),
            username: username.to_string(),
            role: role.as_str().to_string(),
            iat: now.timestamp(),
            exp: (now + Duration::hours(expires_in_hours)).timestamp(),
            nbf: Some(now.timestamp()),
            iss: Some("axum-tutorial".to_string()),
            aud: Some("axum-tutorial-users".to_string()),
            jti: Some(uuid::Uuid::new_v4().to_string()),
        }
    }

    /// 获取用户角色
    pub fn get_role(&self) -> crate::error::Result<UserRole> {
        match self.role.as_str() {
            "Admin" => Ok(UserRole::Admin),
            "Manager" => Ok(UserRole::Manager),
            "User" => Ok(UserRole::User),
            "Guest" => Ok(UserRole::Guest),
            _ => {
                // 尝试解析自定义角色
                UserRole::parse_role(&self.role.to_lowercase())
                    .map_err(crate::error::AppError::ValidationError)
            }
        }
    }

    /// 检查token是否即将过期
    pub fn is_expiring_soon(&self, threshold_minutes: i64) -> bool {
        let now = Utc::now().timestamp();
        let threshold = threshold_minutes * 60;
        self.exp - now <= threshold
    }

    /// 检查token是否已过期
    pub fn is_expired(&self) -> bool {
        Utc::now().timestamp() > self.exp
    }

    /// 获取token剩余有效时间（秒）
    pub fn remaining_validity_seconds(&self) -> i64 {
        self.exp - Utc::now().timestamp()
    }

    /// 转换为基础Claims（向后兼容）
    pub fn to_basic_claims(&self) -> Claims {
        Claims {
            sub: self.sub.clone(),
            username: self.username.clone(),
            exp: self.exp,
            iat: self.iat,
        }
    }
}

/// JWT 错误类型
#[derive(Debug, PartialEq, thiserror::Error)]
pub enum JwtError {
    #[error("JWT token 缺失")]
    TokenMissing,
    #[error("JWT token 无效")]
    TokenInvalid,
    #[error("JWT token 已过期")]
    TokenExpired,
    #[error("JWT token 创建失败: {0}")]
    TokenCreationFailed(String),
    #[error("角色解析失败: {0}")]
    RoleParsingFailed(String),
}

/// JWT 工具结构体
///
/// 封装了 JWT 相关的操作，包括创建和验证 token
pub struct JwtUtils {
    secret: String,
}

impl JwtUtils {
    /// 创建新的 JWT 工具实例
    pub fn new(secret: String) -> Self {
        Self { secret }
    }

    /// 创建 JWT token
    ///
    /// # 参数
    /// - `user_id`: 用户 ID
    /// - `username`: 用户名
    /// - `expires_in_hours`: 过期时间（小时）
    ///
    /// # 返回
    /// 成功时返回 JWT token 字符串，失败时返回错误
    pub fn create_token(
        &self,
        user_id: &str,
        username: &str,
        expires_in_hours: i64,
    ) -> Result<String, JwtError> {
        let now = Utc::now();
        let claims = Claims {
            sub: user_id.to_string(),
            username: username.to_string(),
            iat: now.timestamp(),
            exp: (now + Duration::hours(expires_in_hours)).timestamp(),
        };

        encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(self.secret.as_ref()),
        )
        .map_err(|e| JwtError::TokenCreationFailed(e.to_string()))
    }

    /// 验证 JWT token
    ///
    /// # 参数
    /// - `token`: JWT token 字符串
    ///
    /// # 返回
    /// 成功时返回解析后的 Claims，失败时返回错误
    pub fn validate_token(&self, token: &str) -> Result<Claims, JwtError> {
        if token.is_empty() {
            return Err(JwtError::TokenMissing);
        }

        match decode::<Claims>(
            token,
            &DecodingKey::from_secret(self.secret.as_ref()),
            &Validation::default(),
        ) {
            Ok(token_data) => Ok(token_data.claims),
            Err(err) => match err.kind() {
                jsonwebtoken::errors::ErrorKind::ExpiredSignature => Err(JwtError::TokenExpired),
                _ => Err(JwtError::TokenInvalid),
            },
        }
    }

    /// 从 Bearer token 中提取 JWT
    ///
    /// # 参数
    /// - `bearer_token`: Bearer token 字符串（格式：Bearer <token>）
    ///
    /// # 返回
    /// 成功时返回提取的 token，失败时返回错误
    pub fn extract_token_from_bearer(&self, bearer_token: &str) -> Result<String, JwtError> {
        if !bearer_token.starts_with("Bearer ") {
            return Err(JwtError::TokenInvalid);
        }

        let token = bearer_token.strip_prefix("Bearer ").unwrap_or("");
        if token.is_empty() {
            return Err(JwtError::TokenMissing);
        }

        Ok(token.to_string())
    }

    /// 验证 Bearer token
    ///
    /// 组合了提取和验证的功能
    ///
    /// # 参数
    /// - `bearer_token`: Bearer token 字符串
    ///
    /// # 返回
    /// 成功时返回解析后的 Claims，失败时返回错误
    pub fn validate_bearer_token(&self, bearer_token: &str) -> Result<Claims, JwtError> {
        let token = self.extract_token_from_bearer(bearer_token)?;
        self.validate_token(&token)
    }

    /// 检查 token 是否即将过期
    ///
    /// # 参数
    /// - `claims`: JWT Claims
    /// - `threshold_minutes`: 过期阈值（分钟）
    ///
    /// # 返回
    /// 如果 token 在指定时间内过期则返回 true
    pub fn is_token_expiring_soon(&self, claims: &Claims, threshold_minutes: i64) -> bool {
        let now = Utc::now().timestamp();
        let threshold = threshold_minutes * 60;
        claims.exp - now <= threshold
    }

    // ============================================================================
    // 扩展Claims支持方法（RBAC功能）
    // ============================================================================

    /// 创建包含角色信息的JWT token
    ///
    /// # 参数
    /// - `user_id`: 用户 ID
    /// - `username`: 用户名
    /// - `role`: 用户角色
    /// - `expires_in_hours`: 过期时间（小时）
    ///
    /// # 返回
    /// 成功时返回 JWT token 字符串，失败时返回错误
    pub fn create_token_with_role(
        &self,
        user_id: &str,
        username: &str,
        role: UserRole,
        expires_in_hours: i64,
    ) -> Result<String, JwtError> {
        let claims = ExtendedClaims::new(user_id, username, role, expires_in_hours);

        encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(self.secret.as_ref()),
        )
        .map_err(|e| JwtError::TokenCreationFailed(e.to_string()))
    }

    /// 验证包含角色信息的JWT token
    ///
    /// # 参数
    /// - `token`: JWT token 字符串
    ///
    /// # 返回
    /// 成功时返回解析后的 ExtendedClaims，失败时返回错误
    pub fn validate_token_with_role(&self, token: &str) -> Result<ExtendedClaims, JwtError> {
        if token.is_empty() {
            return Err(JwtError::TokenMissing);
        }

        let mut validation = Validation::default();
        validation.validate_aud = false; // 不验证audience字段
        validation.validate_nbf = false; // 不验证not before字段

        match decode::<ExtendedClaims>(
            token,
            &DecodingKey::from_secret(self.secret.as_ref()),
            &validation,
        ) {
            Ok(token_data) => Ok(token_data.claims),
            Err(err) => match err.kind() {
                jsonwebtoken::errors::ErrorKind::ExpiredSignature => Err(JwtError::TokenExpired),
                _ => Err(JwtError::TokenInvalid),
            },
        }
    }

    /// 验证Bearer token并返回扩展Claims
    ///
    /// # 参数
    /// - `bearer_token`: Bearer token 字符串
    ///
    /// # 返回
    /// 成功时返回解析后的 ExtendedClaims，失败时返回错误
    pub fn validate_bearer_token_with_role(
        &self,
        bearer_token: &str,
    ) -> Result<ExtendedClaims, JwtError> {
        let token = self.extract_token_from_bearer(bearer_token)?;
        self.validate_token_with_role(&token)
    }

    /// 尝试验证token，优先返回ExtendedClaims，失败时返回基础Claims
    ///
    /// 用于向后兼容，支持同时处理新旧token格式
    ///
    /// # 参数
    /// - `token`: JWT token 字符串
    ///
    /// # 返回
    /// 成功时返回ExtendedClaims或基础Claims，失败时返回错误
    pub fn validate_token_flexible(&self, token: &str) -> Result<ExtendedClaims, JwtError> {
        // 首先尝试解析为ExtendedClaims
        if let Ok(extended_claims) = self.validate_token_with_role(token) {
            return Ok(extended_claims);
        }

        // 如果失败，尝试解析为基础Claims并转换
        let basic_claims = self.validate_token(token)?;

        // 将基础Claims转换为ExtendedClaims，使用默认角色
        Ok(ExtendedClaims {
            sub: basic_claims.sub,
            username: basic_claims.username,
            role: UserRole::User.as_str().to_string(), // 默认角色
            exp: basic_claims.exp,
            iat: basic_claims.iat,
            nbf: Some(basic_claims.iat),
            iss: Some("axum-tutorial".to_string()),
            aud: Some("axum-tutorial-users".to_string()),
            jti: Some(uuid::Uuid::new_v4().to_string()),
        })
    }

    /// 创建用于测试的过期 token
    ///
    /// 仅用于测试目的
    #[cfg(test)]
    pub fn create_expired_test_token(&self, user_id: &str, username: &str) -> String {
        let past_time = Utc::now() - Duration::hours(1);
        let claims = Claims {
            sub: user_id.to_string(),
            username: username.to_string(),
            iat: past_time.timestamp(),
            exp: past_time.timestamp(),
        };

        encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(self.secret.as_ref()),
        )
        .expect("Failed to create test token")
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    const TEST_SECRET: &str = "test-jwt-utils-secret";

    #[test]
    fn test_create_and_validate_token() {
        let jwt_utils = JwtUtils::new(TEST_SECRET.to_string());

        // 创建 token
        let token = jwt_utils.create_token("user123", "testuser", 1).unwrap();
        assert!(!token.is_empty());

        // 验证 token
        let claims = jwt_utils.validate_token(&token).unwrap();
        assert_eq!(claims.sub, "user123");
        assert_eq!(claims.username, "testuser");
    }

    #[test]
    fn test_validate_expired_token() {
        let jwt_utils = JwtUtils::new(TEST_SECRET.to_string());

        let expired_token = jwt_utils.create_expired_test_token("user123", "testuser");
        let result = jwt_utils.validate_token(&expired_token);

        assert_eq!(result, Err(JwtError::TokenExpired));
    }

    #[test]
    fn test_extract_token_from_bearer() {
        let jwt_utils = JwtUtils::new(TEST_SECRET.to_string());

        let bearer_token = "Bearer abc123";
        let token = jwt_utils.extract_token_from_bearer(bearer_token).unwrap();
        assert_eq!(token, "abc123");

        // 测试无效格式
        let invalid_bearer = "Invalid abc123";
        let result = jwt_utils.extract_token_from_bearer(invalid_bearer);
        assert_eq!(result, Err(JwtError::TokenInvalid));
    }

    #[test]
    fn test_validate_bearer_token() {
        let jwt_utils = JwtUtils::new(TEST_SECRET.to_string());

        // 创建有效的 token
        let token = jwt_utils.create_token("user123", "testuser", 1).unwrap();
        let bearer_token = format!("Bearer {token}");

        // 验证 Bearer token
        let claims = jwt_utils.validate_bearer_token(&bearer_token).unwrap();
        assert_eq!(claims.sub, "user123");
        assert_eq!(claims.username, "testuser");
    }

    #[test]
    fn test_is_token_expiring_soon() {
        let jwt_utils = JwtUtils::new(TEST_SECRET.to_string());

        // 创建一个1小时后过期的 token
        let token = jwt_utils.create_token("user123", "testuser", 1).unwrap();
        let claims = jwt_utils.validate_token(&token).unwrap();

        // 检查是否在2小时内过期（应该返回 true）
        assert!(jwt_utils.is_token_expiring_soon(&claims, 120));

        // 检查是否在30分钟内过期（应该返回 false）
        assert!(!jwt_utils.is_token_expiring_soon(&claims, 30));
    }
}
