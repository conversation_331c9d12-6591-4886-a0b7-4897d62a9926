/**
 * Jest配置文件
 * 用于API客户端单元测试
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-23
 */

module.exports = {
  // 测试环境
  testEnvironment: 'jsdom',
  
  // 测试文件匹配模式
  testMatch: [
    '**/tests/**/*.test.js',
    '**/tests/**/*_tests.js',
    '**/__tests__/**/*.js'
  ],
  
  // 模块文件扩展名
  moduleFileExtensions: ['js', 'json'],
  
  // 模块名映射
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/static/js/$1'
  },
  
  // 转换配置
  transform: {
    '^.+\\.js$': 'babel-jest'
  },
  
  // 设置文件
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  
  // 覆盖率配置
  collectCoverage: true,
  collectCoverageFrom: [
    'static/js/modules/**/*.js',
    '!static/js/modules/**/*.test.js',
    '!**/node_modules/**'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  
  // 忽略的路径
  testPathIgnorePatterns: [
    '/node_modules/',
    '/target/',
    '/static/css/'
  ],
  
  // 清理mock
  clearMocks: true,
  
  // 详细输出
  verbose: true,
  
  // 错误时停止
  bail: false,
  
  // 最大并发数
  maxConcurrency: 5
};
