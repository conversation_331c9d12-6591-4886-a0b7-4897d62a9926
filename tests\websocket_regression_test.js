/**
 * WebSocket回归测试
 * 
 * 验证所有之前修复的WebSocket问题仍然正常工作
 */

const { chromium } = require('playwright');

async function runWebSocketRegressionTest() {
    console.log('🔄 开始WebSocket回归测试...');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 500
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        if (msg.type() === 'error') {
            console.log('❌ 浏览器错误:', msg.text());
        }
    });
    
    let testResults = {
        passed: 0,
        failed: 0,
        tests: []
    };
    
    try {
        // 导航和登录
        await page.goto('http://127.0.0.1:3000');
        await page.waitForLoadState('networkidle');
        
        await page.click('#loginTab');
        await page.fill('#loginUsername', 'testuser456');
        await page.fill('#loginPassword', 'password123');
        await page.click('#loginForm button[type="submit"]');
        await page.waitForSelector('#currentUser:has-text("testuser456")', { timeout: 10000 });
        
        const connectBtn = page.locator('#wsConnectBtn');
        const disconnectBtn = page.locator('#wsDisconnectBtn');
        const connectionStatus = page.locator('#connectionStatus');
        const messageInput = page.locator('#messageInput');
        const sendBtn = page.locator('#sendBtn');
        const chatMessages = page.locator('#chatMessages');
        
        // 测试1: 基本连接功能
        console.log('🧪 测试1: 基本连接功能');
        await connectBtn.click();
        await page.waitForTimeout(2000);
        const status1 = await connectionStatus.textContent();
        if (status1.includes('已连接')) {
            console.log('✅ 基本连接功能正常');
            testResults.passed++;
            testResults.tests.push({ name: '基本连接功能', status: 'PASSED' });
        } else {
            console.log('❌ 基本连接功能失败');
            testResults.failed++;
            testResults.tests.push({ name: '基本连接功能', status: 'FAILED' });
        }
        
        // 测试2: 消息发送功能
        console.log('🧪 测试2: 消息发送功能');
        await messageInput.fill('回归测试消息');
        await sendBtn.click();
        await page.waitForTimeout(1000);
        
        const messageExists = await chatMessages.locator(':has-text("回归测试消息")').count() > 0;
        if (messageExists) {
            console.log('✅ 消息发送功能正常');
            testResults.passed++;
            testResults.tests.push({ name: '消息发送功能', status: 'PASSED' });
        } else {
            console.log('❌ 消息发送功能失败');
            testResults.failed++;
            testResults.tests.push({ name: '消息发送功能', status: 'FAILED' });
        }
        
        // 测试3: 断开连接功能
        console.log('🧪 测试3: 断开连接功能');
        await disconnectBtn.click();
        await page.waitForTimeout(2000);
        const status3 = await connectionStatus.textContent();
        if (status3.includes('未连接')) {
            console.log('✅ 断开连接功能正常');
            testResults.passed++;
            testResults.tests.push({ name: '断开连接功能', status: 'PASSED' });
        } else {
            console.log('❌ 断开连接功能失败');
            testResults.failed++;
            testResults.tests.push({ name: '断开连接功能', status: 'FAILED' });
        }
        
        // 测试4: 重连功能
        console.log('🧪 测试4: 重连功能');
        await connectBtn.click();
        await page.waitForTimeout(2000);
        const status4 = await connectionStatus.textContent();
        if (status4.includes('已连接')) {
            console.log('✅ 重连功能正常');
            testResults.passed++;
            testResults.tests.push({ name: '重连功能', status: 'PASSED' });
        } else {
            console.log('❌ 重连功能失败');
            testResults.failed++;
            testResults.tests.push({ name: '重连功能', status: 'FAILED' });
        }
        
        // 测试5: 频繁连接/断开稳定性
        console.log('🧪 测试5: 频繁连接/断开稳定性');
        let stabilityPassed = true;
        
        for (let i = 0; i < 3; i++) {
            // 断开
            if (await disconnectBtn.isEnabled()) {
                await disconnectBtn.click();
                await page.waitForTimeout(500);
            }
            
            // 连接
            if (await connectBtn.isEnabled()) {
                await connectBtn.click();
                await page.waitForTimeout(500);
            }
        }
        
        // 最终检查连接状态
        await page.waitForTimeout(2000);
        const finalStatus = await connectionStatus.textContent();
        if (!finalStatus.includes('已连接')) {
            stabilityPassed = false;
        }
        
        // 检查连接稳定性（5秒）
        if (stabilityPassed) {
            for (let i = 1; i <= 5; i++) {
                await page.waitForTimeout(1000);
                const currentStatus = await connectionStatus.textContent();
                if (!currentStatus.includes('已连接')) {
                    stabilityPassed = false;
                    break;
                }
            }
        }
        
        if (stabilityPassed) {
            console.log('✅ 频繁连接/断开稳定性正常');
            testResults.passed++;
            testResults.tests.push({ name: '频繁连接/断开稳定性', status: 'PASSED' });
        } else {
            console.log('❌ 频繁连接/断开稳定性失败');
            testResults.failed++;
            testResults.tests.push({ name: '频繁连接/断开稳定性', status: 'FAILED' });
        }
        
        // 测试6: 消息持续发送
        console.log('🧪 测试6: 消息持续发送');
        let messagingPassed = true;
        
        for (let i = 1; i <= 3; i++) {
            await messageInput.fill(`持续测试消息 ${i}`);
            await sendBtn.click();
            await page.waitForTimeout(500);
            
            const messageExists = await chatMessages.locator(`:has-text("持续测试消息 ${i}")`).count() > 0;
            if (!messageExists) {
                messagingPassed = false;
                break;
            }
        }
        
        if (messagingPassed) {
            console.log('✅ 消息持续发送正常');
            testResults.passed++;
            testResults.tests.push({ name: '消息持续发送', status: 'PASSED' });
        } else {
            console.log('❌ 消息持续发送失败');
            testResults.failed++;
            testResults.tests.push({ name: '消息持续发送', status: 'FAILED' });
        }
        
    } catch (error) {
        console.error('❌ 回归测试执行失败:', error);
        testResults.failed++;
        testResults.tests.push({ name: '测试执行', status: 'ERROR', error: error.message });
    } finally {
        await browser.close();
    }
    
    // 生成测试报告
    console.log('\n📊 WebSocket回归测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${testResults.passed + testResults.failed}`);
    console.log(`通过: ${testResults.passed}`);
    console.log(`失败: ${testResults.failed}`);
    console.log(`成功率: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
    
    console.log('\n📋 详细结果:');
    testResults.tests.forEach((test, index) => {
        const icon = test.status === 'PASSED' ? '✅' : test.status === 'FAILED' ? '❌' : '⚠️';
        console.log(`  ${index + 1}. ${icon} ${test.name}: ${test.status}`);
        if (test.error) {
            console.log(`     错误: ${test.error}`);
        }
    });
    
    const success = testResults.failed === 0;
    console.log(`\n${success ? '🎉 所有回归测试通过！' : '⚠️ 部分回归测试失败，需要进一步检查。'}`);
    console.log('🏁 回归测试完成');
    
    return success;
}

// 运行测试
if (require.main === module) {
    runWebSocketRegressionTest().catch(console.error);
}

module.exports = { runWebSocketRegressionTest };
