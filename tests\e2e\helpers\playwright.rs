// Playwright MCP 辅助函数

use super::E2EConfig;
use anyhow::{Context, Result};
use std::process::{Command, Stdio};
use std::time::Duration;
use tokio::time::sleep;

/// Playwright MCP 客户端包装器
pub struct PlaywrightClient {
    config: E2EConfig,
    server_process: Option<std::process::Child>,
    server_port: u16,
}

impl PlaywrightClient {
    /// 创建新的Playwright客户端
    pub fn new(config: E2EConfig) -> Self {
        Self {
            config,
            server_process: None,
            server_port: 8931, // 默认MCP服务器端口
        }
    }

    /// 启动Playwright MCP服务器
    pub async fn start_server(&mut self) -> Result<()> {
        println!("正在启动Playwright MCP服务器...");

        // 构建启动命令
        let mut cmd = Command::new("npx");
        cmd.args(&[
            "@playwright/mcp@latest",
            "--port",
            &self.server_port.to_string(),
            "--host",
            "localhost",
            "--config",
            "tests/e2e/config/playwright.config.json",
        ]);

        // 根据配置添加额外参数
        if self.config.playwright_headless {
            cmd.arg("--headless");
        }

        cmd.arg("--viewport-size").arg(&format!(
            "{},{}",
            self.config.viewport_width, self.config.viewport_height
        ));

        cmd.arg("--output-dir")
            .arg(self.config.report_dir.to_string_lossy().as_ref());

        // 启动进程
        let child = cmd
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .spawn()
            .context("无法启动Playwright MCP服务器")?;

        self.server_process = Some(child);

        // 等待服务器启动
        self.wait_for_server().await?;

        println!("Playwright MCP服务器已启动，端口: {}", self.server_port);
        Ok(())
    }

    /// 等待服务器启动
    async fn wait_for_server(&self) -> Result<()> {
        let max_attempts = 30;
        let mut attempts = 0;

        while attempts < max_attempts {
            if self.is_server_ready().await {
                return Ok(());
            }

            attempts += 1;
            sleep(Duration::from_millis(1000)).await;
        }

        Err(anyhow::anyhow!("Playwright MCP服务器启动超时"))
    }

    /// 检查服务器是否就绪
    async fn is_server_ready(&self) -> bool {
        // 尝试连接到MCP服务器的SSE端点
        let url = format!("http://localhost:{}/sse", self.server_port);

        match reqwest::get(&url).await {
            Ok(response) => response.status().is_success() || response.status().as_u16() == 404,
            Err(_) => false,
        }
    }

    /// 停止Playwright MCP服务器
    pub fn stop_server(&mut self) -> Result<()> {
        if let Some(mut child) = self.server_process.take() {
            println!("正在停止Playwright MCP服务器...");

            // 尝试优雅关闭
            if let Err(_) = child.kill() {
                println!("警告: 无法优雅关闭Playwright MCP服务器");
            }

            // 等待进程结束
            if let Err(_) = child.wait() {
                println!("警告: 等待Playwright MCP服务器进程结束时出错");
            }

            println!("Playwright MCP服务器已停止");
        }
        Ok(())
    }

    /// 获取服务器URL
    pub fn get_server_url(&self) -> String {
        format!("http://localhost:{}", self.server_port)
    }

    /// 获取SSE端点URL
    pub fn get_sse_url(&self) -> String {
        format!("http://localhost:{}/sse", self.server_port)
    }

    /// 安装浏览器（如果需要）
    pub async fn install_browser(&self) -> Result<()> {
        println!("正在检查并安装浏览器...");

        let output = Command::new("npx")
            .args(&["@playwright/mcp@latest", "--help"])
            .output()
            .context("无法执行Playwright MCP命令")?;

        if !output.status.success() {
            return Err(anyhow::anyhow!("Playwright MCP不可用"));
        }

        // 尝试安装浏览器
        let install_output = Command::new("npx")
            .args(&["playwright", "install", "chromium"])
            .output()
            .context("无法安装Chromium浏览器")?;

        if install_output.status.success() {
            println!("浏览器安装成功");
        } else {
            println!("浏览器可能已经安装或安装失败");
        }

        Ok(())
    }

    /// 验证Playwright MCP配置
    pub fn validate_config(&self) -> Result<()> {
        // 检查配置文件是否存在
        let config_path = std::path::Path::new("tests/e2e/config/playwright.config.json");
        if !config_path.exists() {
            return Err(anyhow::anyhow!(
                "Playwright配置文件不存在: {:?}",
                config_path
            ));
        }

        // 检查输出目录
        if !self.config.report_dir.exists() {
            std::fs::create_dir_all(&self.config.report_dir).context("无法创建报告目录")?;
        }

        if !self.config.screenshot_dir.exists() {
            std::fs::create_dir_all(&self.config.screenshot_dir).context("无法创建截图目录")?;
        }

        if !self.config.video_dir.exists() {
            std::fs::create_dir_all(&self.config.video_dir).context("无法创建视频目录")?;
        }

        println!("Playwright配置验证通过");
        Ok(())
    }

    /// 生成测试报告
    pub async fn generate_test_report(&self, test_results: &str) -> Result<()> {
        let report_path = self.config.report_dir.join("playwright_report.html");

        let html_content = format!(
            r#"<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Playwright E2E 测试报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .results {{ margin-top: 20px; }}
        .success {{ color: green; }}
        .error {{ color: red; }}
        pre {{ background-color: #f5f5f5; padding: 10px; border-radius: 3px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Playwright E2E 测试报告</h1>
        <p>生成时间: {}</p>
        <p>测试服务器: {}</p>
    </div>
    <div class="results">
        <h2>测试结果</h2>
        <pre>{}</pre>
    </div>
</body>
</html>"#,
            "测试时间", self.config.base_url, test_results
        );

        std::fs::write(&report_path, html_content).context("无法写入测试报告")?;

        println!("测试报告已生成: {:?}", report_path);
        Ok(())
    }
}

impl Drop for PlaywrightClient {
    fn drop(&mut self) {
        if let Err(e) = self.stop_server() {
            eprintln!("清理Playwright服务器时出错: {}", e);
        }
    }
}
