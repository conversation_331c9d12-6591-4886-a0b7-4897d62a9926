import { Page, Locator, expect } from '@playwright/test';

/**
 * 基础页面类 - 遵循Context7 MCP最佳实践
 * 提供通用的页面操作方法和用户友好的定位器
 */
export class BasePage {
  readonly page: Page;
  readonly baseUrl: string;

  constructor(page: Page, baseUrl: string = 'http://127.0.0.1:3000') {
    this.page = page;
    this.baseUrl = baseUrl;
  }

  /**
   * 导航到指定路径
   * @param path 相对路径
   */
  async goto(path: string = '') {
    const url = path ? `${this.baseUrl}${path}` : this.baseUrl;
    await this.page.goto(url);
    await this.waitForPageLoad();
  }

  /**
   * 等待页面加载完成
   */
  async waitForPageLoad() {
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForLoadState('domcontentloaded');
  }

  /**
   * 等待元素可见
   * @param locator 定位器
   * @param timeout 超时时间（毫秒）
   */
  async waitForVisible(locator: Locator, timeout: number = 5000) {
    await expect(locator).toBeVisible({ timeout });
  }

  /**
   * 等待元素隐藏
   * @param locator 定位器
   * @param timeout 超时时间（毫秒）
   */
  async waitForHidden(locator: Locator, timeout: number = 5000) {
    await expect(locator).toBeHidden({ timeout });
  }

  /**
   * 安全点击 - 等待元素可见且可点击后再点击
   * @param locator 定位器
   */
  async safeClick(locator: Locator) {
    await expect(locator).toBeVisible();
    await expect(locator).toBeEnabled();
    await locator.click();
  }

  /**
   * 安全填写 - 等待元素可见且可编辑后再填写
   * @param locator 定位器
   * @param text 要填写的文本
   */
  async safeFill(locator: Locator, text: string) {
    await expect(locator).toBeVisible();
    await expect(locator).toBeEditable();
    await locator.clear();
    await locator.fill(text);
  }

  /**
   * 获取页面标题
   */
  async getTitle(): Promise<string> {
    return await this.page.title();
  }

  /**
   * 获取当前URL
   */
  async getCurrentUrl(): Promise<string> {
    return this.page.url();
  }

  /**
   * 截图
   * @param name 截图文件名
   */
  async takeScreenshot(name: string) {
    await this.page.screenshot({ 
      path: `tests/ui/test-results/${name}.png`,
      fullPage: true 
    });
  }

  /**
   * 等待网络请求完成
   * @param urlPattern URL模式
   */
  async waitForResponse(urlPattern: string | RegExp) {
    return await this.page.waitForResponse(urlPattern);
  }

  /**
   * 检查控制台错误
   */
  async checkConsoleErrors(): Promise<string[]> {
    const errors: string[] = [];
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    return errors;
  }

  /**
   * 模拟键盘按键
   * @param key 按键名称
   */
  async pressKey(key: string) {
    await this.page.keyboard.press(key);
  }

  /**
   * 滚动到元素
   * @param locator 定位器
   */
  async scrollToElement(locator: Locator) {
    await locator.scrollIntoViewIfNeeded();
  }

  /**
   * 等待指定时间
   * @param ms 毫秒数
   */
  async wait(ms: number) {
    await this.page.waitForTimeout(ms);
  }

  /**
   * 检查元素是否存在
   * @param locator 定位器
   */
  async isElementPresent(locator: Locator): Promise<boolean> {
    try {
      await expect(locator).toBeVisible({ timeout: 1000 });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取元素文本内容
   * @param locator 定位器
   */
  async getElementText(locator: Locator): Promise<string> {
    await expect(locator).toBeVisible();
    return await locator.textContent() || '';
  }

  /**
   * 获取元素属性值
   * @param locator 定位器
   * @param attribute 属性名
   */
  async getElementAttribute(locator: Locator, attribute: string): Promise<string | null> {
    await expect(locator).toBeVisible();
    return await locator.getAttribute(attribute);
  }
}
