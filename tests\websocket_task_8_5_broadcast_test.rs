//! # 任务8.5 - WebSocket广播功能测试
//!
//! 验证服务器能够将消息广播给所有已连接的客户端

use app_common::utils::JwtUtils;
use futures_util::{SinkExt, StreamExt};
use serde_json::json;
use std::{
    collections::HashMap,
    sync::{
        Arc,
        atomic::{AtomicU64, Ordering},
    },
    time::{Duration, Instant},
};
use tokio::{
    sync::{<PERSON>ier, Mutex},
    time::timeout,
};
use tokio_tungstenite::{connect_async, tungstenite::protocol::Message as TungsteniteMessage};
use tracing::{error, info, warn};
use uuid;

/// 任务8.5测试配置
#[derive(Debug, Clone)]
pub struct Task85Config {
    /// 客户端数量
    pub client_count: usize,
    /// 广播消息数量
    pub broadcast_messages: usize,
    /// 测试持续时间（秒）
    pub test_duration_secs: u64,
    /// 连接超时时间（秒）
    pub connection_timeout_secs: u64,
    /// 消息发送间隔（毫秒）
    pub message_interval_ms: u64,
    /// 服务器URL
    pub server_url: String,
}

impl Default for Task85Config {
    fn default() -> Self {
        Self {
            client_count: 4,
            broadcast_messages: 3,
            test_duration_secs: 15,
            connection_timeout_secs: 5,
            message_interval_ms: 1000,
            server_url: "ws://127.0.0.1:3000/ws".to_string(),
        }
    }
}

/// 任务8.5测试指标
#[derive(Debug, Default)]
pub struct Task85Metrics {
    /// 成功连接数
    pub successful_connections: AtomicU64,
    /// 失败连接数
    pub failed_connections: AtomicU64,
    /// 发送的广播消息数
    pub broadcast_messages_sent: AtomicU64,
    /// 接收的消息总数
    pub total_messages_received: AtomicU64,
    /// 广播成功率
    pub broadcast_success_count: AtomicU64,
}

/// 客户端接收统计
#[derive(Debug, Default, Clone)]
pub struct ClientReceiveStats {
    /// 客户端ID
    pub client_id: usize,
    /// 接收的消息数量
    pub received_count: u64,
    /// 接收的广播消息内容
    pub received_messages: Vec<String>,
}

/// 任务8.5测试结果
#[derive(Debug)]
pub struct Task85Results {
    pub total_test_duration: Duration,
    pub successful_connections: u64,
    pub failed_connections: u64,
    pub broadcast_messages_sent: u64,
    pub total_messages_received: u64,
    pub broadcast_success_rate: f64,
    pub client_receive_stats: Vec<ClientReceiveStats>,
}

/// 任务8.5测试器
pub struct Task85Tester {
    config: Task85Config,
    metrics: Arc<Task85Metrics>,
    client_stats: Arc<Mutex<HashMap<usize, ClientReceiveStats>>>,
}

impl Task85Tester {
    /// 创建新的测试器
    pub fn new(config: Task85Config) -> Self {
        Self {
            config,
            metrics: Arc::new(Task85Metrics::default()),
            client_stats: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    /// 执行任务8.5广播功能测试
    pub async fn run_task_8_5_test(&self) -> Result<Task85Results, Box<dyn std::error::Error>> {
        info!("🚀 开始执行任务8.5 - WebSocket广播功能测试");
        info!("📋 测试配置: {:?}", self.config);

        let start_time = Instant::now();

        // 第一阶段：建立所有客户端连接
        info!("🔗 第一阶段：建立{}个客户端连接", self.config.client_count);
        let connections = self.establish_all_connections().await?;

        if connections.is_empty() {
            return Err("没有成功建立任何连接".into());
        }

        info!("✅ 成功建立{}个连接", connections.len());

        // 第二阶段：执行广播测试
        info!("📡 第二阶段：执行广播功能测试");
        self.execute_broadcast_test(connections).await?;

        // 计算并返回统计结果
        let total_duration = start_time.elapsed();
        let client_stats = self.client_stats.lock().await;
        let client_receive_stats: Vec<ClientReceiveStats> =
            client_stats.values().cloned().collect();

        // 计算广播成功率
        let expected_total_receives =
            (self.config.broadcast_messages as u64) * ((self.config.client_count - 1) as u64);
        let actual_total_receives = self.metrics.total_messages_received.load(Ordering::Relaxed);
        let broadcast_success_rate = if expected_total_receives > 0 {
            ((actual_total_receives as f64) / (expected_total_receives as f64)) * 100.0
        } else {
            0.0
        };

        let results = Task85Results {
            total_test_duration: total_duration,
            successful_connections: self.metrics.successful_connections.load(Ordering::Relaxed),
            failed_connections: self.metrics.failed_connections.load(Ordering::Relaxed),
            broadcast_messages_sent: self.metrics.broadcast_messages_sent.load(Ordering::Relaxed),
            total_messages_received: actual_total_receives,
            broadcast_success_rate,
            client_receive_stats,
        };

        info!("📊 任务8.5测试完成，统计结果: {:?}", results);
        Ok(results)
    }

    /// 建立所有客户端连接
    async fn establish_all_connections(
        &self,
    ) -> Result<
        Vec<(
            usize,
            tokio_tungstenite::WebSocketStream<
                tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>,
            >,
        )>,
        Box<dyn std::error::Error>,
    > {
        let mut connections = Vec::new();
        let barrier = Arc::new(Barrier::new(self.config.client_count));

        // 并发建立连接
        let mut connection_tasks = Vec::new();
        for client_id in 0..self.config.client_count {
            let task = self.spawn_connection_task(client_id, barrier.clone()).await;
            connection_tasks.push(task);
        }

        // 等待所有连接建立
        let connection_results = futures_util::future::join_all(connection_tasks).await;

        for (client_id, result) in connection_results.into_iter().enumerate() {
            match result {
                Ok(Ok(stream)) => {
                    connections.push((client_id, stream));
                    self.metrics
                        .successful_connections
                        .fetch_add(1, Ordering::Relaxed);
                }
                Ok(Err(e)) => {
                    error!("❌ 客户端{}连接失败: {}", client_id, e);
                    self.metrics
                        .failed_connections
                        .fetch_add(1, Ordering::Relaxed);
                }
                Err(e) => {
                    error!("❌ 客户端{}任务失败: {}", client_id, e);
                    self.metrics
                        .failed_connections
                        .fetch_add(1, Ordering::Relaxed);
                }
            }
        }

        Ok(connections)
    }

    /// 启动连接任务
    async fn spawn_connection_task(
        &self,
        client_id: usize,
        barrier: Arc<Barrier>,
    ) -> tokio::task::JoinHandle<
        Result<
            tokio_tungstenite::WebSocketStream<
                tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>,
            >,
            Box<dyn std::error::Error + Send + Sync>,
        >,
    > {
        let config = self.config.clone();

        tokio::spawn(async move {
            // 等待所有客户端准备就绪
            barrier.wait().await;

            info!("🔗 客户端{}开始建立连接", client_id);

            // 创建有效的JWT token (使用UUID格式的用户ID)
            let jwt_utils = JwtUtils::new("your-secret-key-change-in-production".to_string());
            let user_uuid = uuid::Uuid::new_v4(); // 生成有效的UUID
            let user_id = user_uuid.to_string(); // 转换为字符串格式
            let username = format!("testuser{}", client_id);
            let valid_token = jwt_utils
                .create_token(&user_id, &username, 1)
                .map_err(|e| {
                    Box::new(std::io::Error::new(
                        std::io::ErrorKind::Other,
                        format!("JWT创建失败: {}", e),
                    )) as Box<dyn std::error::Error + Send + Sync>
                })?;

            // 构建WebSocket URL with valid JWT token
            let ws_url = format!("{}?token={}", config.server_url, valid_token);

            // 建立WebSocket连接
            let (ws_stream, _) = match timeout(
                Duration::from_secs(config.connection_timeout_secs),
                connect_async(&ws_url),
            )
            .await
            {
                Ok(Ok(stream)) => {
                    info!("✅ 客户端{}连接成功", client_id);
                    stream
                }
                Ok(Err(e)) => {
                    error!("❌ 客户端{}连接失败: {}", client_id, e);
                    return Err(Box::new(e) as Box<dyn std::error::Error + Send + Sync>);
                }
                Err(_) => {
                    error!("⏰ 客户端{}连接超时", client_id);
                    return Err(Box::new(std::io::Error::new(
                        std::io::ErrorKind::TimedOut,
                        "连接超时",
                    ))
                        as Box<dyn std::error::Error + Send + Sync>);
                }
            };

            Ok(ws_stream)
        })
    }

    /// 执行广播测试
    async fn execute_broadcast_test(
        &self,
        connections: Vec<(
            usize,
            tokio_tungstenite::WebSocketStream<
                tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>,
            >,
        )>,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // 为每个连接启动接收任务
        let mut receive_tasks = Vec::new();
        let mut senders = Vec::new();

        for (client_id, stream) in connections {
            let (sender, receiver) = stream.split();
            senders.push((client_id, sender));

            // 启动接收任务
            let recv_task = self.spawn_receive_task(client_id, receiver).await;
            receive_tasks.push(recv_task);
        }

        // 等待一小段时间确保所有接收器准备就绪
        tokio::time::sleep(Duration::from_millis(500)).await;

        // 执行广播测试：让第一个客户端发送广播消息
        if let Some((sender_id, mut sender)) = senders.into_iter().next() {
            info!("📡 客户端{}开始发送广播消息", sender_id);

            for i in 0..self.config.broadcast_messages {
                let broadcast_message = json!({
                    "type": "chat",
                    "content": format!("🔊 任务8.5广播测试消息 {} 来自客户端 {}", i + 1, sender_id),
                    "timestamp": chrono::Utc::now().timestamp_millis(),
                    "sender_id": sender_id,
                    "task": "8.5",
                    "broadcast_test": true
                });

                let message = TungsteniteMessage::Text(broadcast_message.to_string().into());

                match sender.send(message).await {
                    Ok(_) => {
                        self.metrics
                            .broadcast_messages_sent
                            .fetch_add(1, Ordering::Relaxed);
                        info!("📤 客户端{}发送广播消息 {}", sender_id, i + 1);
                    }
                    Err(e) => {
                        error!("❌ 客户端{}发送广播消息失败: {}", sender_id, e);
                        break;
                    }
                }

                // 消息发送间隔
                if self.config.message_interval_ms > 0 {
                    tokio::time::sleep(Duration::from_millis(self.config.message_interval_ms))
                        .await;
                }
            }

            // 发送关闭消息
            let _ = sender.send(TungsteniteMessage::Close(None)).await;
        }

        // 等待接收任务完成
        let receive_timeout = Duration::from_secs(self.config.test_duration_secs);
        let _ = timeout(
            receive_timeout,
            futures_util::future::join_all(receive_tasks),
        )
        .await;

        Ok(())
    }

    /// 启动消息接收任务
    async fn spawn_receive_task(
        &self,
        client_id: usize,
        mut receiver: futures_util::stream::SplitStream<
            tokio_tungstenite::WebSocketStream<
                tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>,
            >,
        >,
    ) -> tokio::task::JoinHandle<()> {
        let metrics = self.metrics.clone();
        let client_stats = self.client_stats.clone();

        tokio::spawn(async move {
            let mut received_count = 0u64;
            let mut received_messages = Vec::new();

            info!("👂 客户端{}开始监听消息", client_id);

            while let Some(msg_result) = receiver.next().await {
                match msg_result {
                    Ok(TungsteniteMessage::Text(text)) => {
                        received_count += 1;
                        metrics
                            .total_messages_received
                            .fetch_add(1, Ordering::Relaxed);

                        // 解析消息内容
                        if let Ok(msg_json) = serde_json::from_str::<serde_json::Value>(&text) {
                            if let Some(content) = msg_json.get("content").and_then(|c| c.as_str())
                            {
                                received_messages.push(content.to_string());
                                info!("📨 客户端{}接收到广播消息: {}", client_id, content);
                            }
                        } else {
                            // 处理非JSON格式的消息
                            received_messages.push(text.to_string());
                            info!("📨 客户端{}接收到消息: {}", client_id, text);
                        }
                    }
                    Ok(TungsteniteMessage::Close(_)) => {
                        info!("🔚 客户端{}收到关闭消息", client_id);
                        break;
                    }
                    Ok(TungsteniteMessage::Ping(_)) => {
                        // Ping消息由框架自动处理
                    }
                    Ok(TungsteniteMessage::Pong(_)) => {
                        // Pong响应
                    }
                    Err(e) => {
                        error!("❌ 客户端{}接收消息错误: {}", client_id, e);
                        break;
                    }
                    _ => {}
                }
            }

            // 更新客户端统计信息
            {
                let mut stats = client_stats.lock().await;
                stats.insert(
                    client_id,
                    ClientReceiveStats {
                        client_id,
                        received_count,
                        received_messages,
                    },
                );
            }

            info!(
                "📥 客户端{}接收任务结束，共接收{}条消息",
                client_id, received_count
            );
        })
    }

    /// 打印任务8.5测试报告
    pub fn print_task_8_5_report(&self, results: &Task85Results) {
        println!("\n🎯 === 任务8.5 - WebSocket广播功能测试报告 ===");
        println!("⏱️  测试持续时间: {:?}", results.total_test_duration);
        println!("🔗 成功连接数: {}", results.successful_connections);
        println!("❌ 失败连接数: {}", results.failed_connections);
        println!("📡 发送广播消息数: {}", results.broadcast_messages_sent);
        println!("📨 总接收消息数: {}", results.total_messages_received);
        println!("📊 广播成功率: {:.2}%", results.broadcast_success_rate);

        // 详细的客户端接收统计
        println!("\n📋 客户端接收详情:");
        for stats in &results.client_receive_stats {
            println!(
                "  👤 客户端{}: 接收{}条消息",
                stats.client_id, stats.received_count
            );
            for (i, msg) in stats.received_messages.iter().enumerate() {
                println!("    📨 消息{}: {}", i + 1, msg);
            }
        }

        // 测试结论
        if results.broadcast_success_rate >= 90.0 {
            println!("\n🎉 测试结论: 优秀 - WebSocket广播功能表现出色");
        } else if results.broadcast_success_rate >= 70.0 {
            println!("\n✅ 测试结论: 良好 - WebSocket广播功能表现良好");
        } else if results.broadcast_success_rate >= 50.0 {
            println!("\n⚠️  测试结论: 一般 - WebSocket广播功能需要优化");
        } else {
            println!("\n❌ 测试结论: 较差 - WebSocket广播功能需要重点改进");
        }

        println!("=== 任务8.5测试报告结束 ===\n");
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tracing_test::traced_test;

    #[tokio::test]
    #[traced_test]
    async fn test_task_8_5_websocket_broadcast_functionality() {
        let config = Task85Config {
            client_count: 3,
            broadcast_messages: 2,
            test_duration_secs: 10,
            connection_timeout_secs: 5,
            message_interval_ms: 1000,
            server_url: "ws://127.0.0.1:3000/ws".to_string(),
        };

        let tester = Task85Tester::new(config);

        match tester.run_task_8_5_test().await {
            Ok(results) => {
                tester.print_task_8_5_report(&results);

                // 验证测试结果
                info!("🎯 任务8.5 WebSocket广播功能测试完成");
                info!("📊 广播成功率: {:.2}%", results.broadcast_success_rate);
                info!("📡 发送广播消息数: {}", results.broadcast_messages_sent);
                info!("📨 总接收消息数: {}", results.total_messages_received);

                // 基本验证：至少尝试了连接
                assert!(
                    results.successful_connections + results.failed_connections > 0,
                    "应该有连接尝试记录"
                );

                // 如果有成功连接，验证广播功能
                if results.successful_connections > 1 {
                    assert!(results.broadcast_messages_sent > 0, "应该发送了广播消息");

                    // 验证广播效果：如果有多个客户端连接，应该有消息接收
                    if results.successful_connections > 1 {
                        // 期望的最小接收数量（至少有一些消息被接收）
                        let expected_min_receives = results.broadcast_messages_sent;
                        assert!(
                            results.total_messages_received >= expected_min_receives,
                            "广播消息应该被其他客户端接收到"
                        );
                    }
                }

                info!("✅ 任务8.5测试验证通过");
            }
            Err(e) => {
                // 测试失败也是可以接受的，因为可能没有运行的服务器
                warn!("⚠️ 任务8.5测试失败（可能是因为服务器未运行）: {}", e);
                info!("💡 提示：请确保WebSocket服务器在127.0.0.1:3000运行");
            }
        }
    }

    #[tokio::test]
    #[traced_test]
    async fn test_task_8_5_broadcast_with_larger_group() {
        let config = Task85Config {
            client_count: 5,
            broadcast_messages: 3,
            test_duration_secs: 15,
            connection_timeout_secs: 5,
            message_interval_ms: 800,
            server_url: "ws://127.0.0.1:3000/ws".to_string(),
        };

        let tester = Task85Tester::new(config);

        match tester.run_task_8_5_test().await {
            Ok(results) => {
                tester.print_task_8_5_report(&results);

                info!("🎯 任务8.5大组广播测试完成");
                info!("📊 广播成功率: {:.2}%", results.broadcast_success_rate);

                // 验证大组广播效果
                if results.successful_connections >= 3 {
                    // 在大组测试中，期望更高的广播覆盖率
                    assert!(
                        results.broadcast_success_rate >= 50.0,
                        "大组广播测试中，成功率应该至少达到50%"
                    );
                }

                info!("✅ 任务8.5大组广播测试验证通过");
            }
            Err(e) => {
                warn!("⚠️ 任务8.5大组广播测试失败: {}", e);
            }
        }
    }
}
