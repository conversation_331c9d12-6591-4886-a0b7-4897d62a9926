//! # 健康检查API集成测试
//!
//! 测试任务36：集成健康检查API的7个接口
//! 验证前端API客户端与后端健康检查服务的完整集成

use serde_json::{Value, json};
use std::time::Duration;
use tokio::time::sleep;

/// 测试配置常量
const BASE_URL: &str = "http://127.0.0.1:3000";
const TEST_TIMEOUT: Duration = Duration::from_secs(30);

/// 健康检查API端点列表
const HEALTH_ENDPOINTS: &[&str] = &[
    "/api/health",                 // 基础健康检查
    "/api/health/deep",            // 深度健康检查
    "/api/health/database",        // 数据库健康检查
    "/api/health/database/config", // 数据库配置信息
    "/api/performance/health",     // 性能健康检查
    "/api/performance/ready",      // 就绪检查
    "/api/performance/live",       // 存活检查
];

/// HTTP客户端
fn create_http_client() -> reqwest::Client {
    reqwest::Client::builder()
        .timeout(TEST_TIMEOUT)
        .build()
        .expect("创建HTTP客户端失败")
}

/// 检查服务器是否运行
async fn check_server_health() -> Result<bool, Box<dyn std::error::Error>> {
    let client = create_http_client();

    match client.get(&format!("{}/api/health", BASE_URL)).send().await {
        Ok(response) if response.status().is_success() => {
            println!("✅ 服务器健康检查通过");
            Ok(true)
        }
        Ok(response) => {
            println!("⚠️ 服务器响应异常: 状态码 {}", response.status());
            Ok(false)
        }
        Err(e) => {
            println!("❌ 服务器连接失败: {}", e);
            Ok(false)
        }
    }
}

/// 等待服务器启动
async fn wait_for_server() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔄 等待服务器启动...");

    for attempt in 1..=10 {
        if check_server_health().await? {
            println!("✅ 服务器已就绪 (尝试 {}/10)", attempt);
            return Ok(());
        }

        println!("⏳ 服务器未就绪，等待2秒后重试... (尝试 {}/10)", attempt);
        sleep(Duration::from_secs(2)).await;
    }

    Err("服务器启动超时".into())
}

/// 验证健康检查响应结构
fn validate_health_response(response: &Value, endpoint: &str) -> Result<(), String> {
    // 基本字段验证
    if !response.is_object() {
        return Err(format!("响应不是JSON对象: {}", endpoint));
    }

    let obj = response.as_object().unwrap();

    // 检查必需字段
    match endpoint {
        "/api/health" => {
            let required_fields = ["status", "timestamp", "version", "uptime", "services"];
            for field in required_fields {
                if !obj.contains_key(field) {
                    return Err(format!("缺少必需字段 '{}': {}", field, endpoint));
                }
            }

            // 验证services字段结构
            if let Some(services) = obj.get("services") {
                let services_obj = services
                    .as_object()
                    .ok_or_else(|| format!("services字段不是对象: {}", endpoint))?;

                let service_fields = ["database", "cache", "external_apis"];
                for field in service_fields {
                    if !services_obj.contains_key(field) {
                        return Err(format!("services缺少字段 '{}': {}", field, endpoint));
                    }
                }
            }
        }

        "/api/health/deep" => {
            let required_fields = [
                "status",
                "timestamp",
                "version",
                "uptime",
                "system",
                "services",
                "metrics",
            ];
            for field in required_fields {
                if !obj.contains_key(field) {
                    return Err(format!("缺少必需字段 '{}': {}", field, endpoint));
                }
            }
        }

        "/api/health/database" => {
            let required_fields = ["status", "timestamp", "pool_status"];
            for field in required_fields {
                if !obj.contains_key(field) {
                    return Err(format!("缺少必需字段 '{}': {}", field, endpoint));
                }
            }
        }

        "/api/performance/ready" => {
            let required_fields = ["status", "timestamp", "checks"];
            for field in required_fields {
                if !obj.contains_key(field) {
                    return Err(format!("缺少必需字段 '{}': {}", field, endpoint));
                }
            }
        }

        "/api/performance/live" => {
            let required_fields = ["status", "timestamp", "uptime"];
            for field in required_fields {
                if !obj.contains_key(field) {
                    return Err(format!("缺少必需字段 '{}': {}", field, endpoint));
                }
            }
        }

        _ => {
            // 通用验证：至少包含status和timestamp
            if !obj.contains_key("status") {
                return Err(format!("缺少status字段: {}", endpoint));
            }
            if !obj.contains_key("timestamp") {
                return Err(format!("缺少timestamp字段: {}", endpoint));
            }
        }
    }

    Ok(())
}

/// 测试单个健康检查端点
async fn test_health_endpoint(endpoint: &str) -> Result<Value, Box<dyn std::error::Error>> {
    let client = create_http_client();
    let url = format!("{}{}", BASE_URL, endpoint);

    println!("🔍 测试端点: {}", endpoint);

    let response = client.get(&url).send().await?;
    let status = response.status();

    if !status.is_success() {
        return Err(format!("端点 {} 返回错误状态码: {}", endpoint, status).into());
    }

    let body = response.text().await?;
    let json_response: Value =
        serde_json::from_str(&body).map_err(|e| format!("解析JSON失败 {}: {}", endpoint, e))?;

    // 验证响应结构
    validate_health_response(&json_response, endpoint)?;

    println!("✅ 端点测试通过: {}", endpoint);
    Ok(json_response)
}

/// 主测试函数：测试所有健康检查端点
#[tokio::test]
async fn test_all_health_check_endpoints() {
    println!("🚀 开始健康检查API集成测试");

    // 等待服务器启动
    wait_for_server().await.expect("服务器启动失败");

    let mut success_count = 0;
    let mut failed_endpoints = Vec::new();

    // 测试所有端点
    for endpoint in HEALTH_ENDPOINTS {
        match test_health_endpoint(endpoint).await {
            Ok(_) => {
                success_count += 1;
                println!("✅ 端点成功: {}", endpoint);
            }
            Err(e) => {
                failed_endpoints.push((endpoint, e.to_string()));
                println!("❌ 端点失败: {} - {}", endpoint, e);
            }
        }
    }

    // 输出测试结果
    println!("\n📊 健康检查API测试结果:");
    println!("总端点数: {}", HEALTH_ENDPOINTS.len());
    println!("成功端点数: {}", success_count);
    println!("失败端点数: {}", failed_endpoints.len());

    if !failed_endpoints.is_empty() {
        println!("\n❌ 失败的端点:");
        for (endpoint, error) in &failed_endpoints {
            println!("  - {}: {}", endpoint, error);
        }
    }

    // 断言所有端点都成功
    assert_eq!(
        failed_endpoints.len(),
        0,
        "有 {} 个健康检查端点测试失败",
        failed_endpoints.len()
    );

    println!("🎉 所有健康检查API端点测试通过！");
}

/// 测试健康检查响应时间性能
#[tokio::test]
async fn test_health_check_performance() {
    println!("⚡ 开始健康检查性能测试");

    wait_for_server().await.expect("服务器启动失败");

    let client = create_http_client();
    let mut performance_results = Vec::new();

    for endpoint in HEALTH_ENDPOINTS {
        let url = format!("{}{}", BASE_URL, endpoint);
        let start_time = std::time::Instant::now();

        match client.get(&url).send().await {
            Ok(response) if response.status().is_success() => {
                let duration = start_time.elapsed();
                performance_results.push((endpoint, duration));
                println!("⚡ {} 响应时间: {:?}", endpoint, duration);
            }
            Ok(response) => {
                println!("⚠️ {} 返回状态码: {}", endpoint, response.status());
            }
            Err(e) => {
                println!("❌ {} 请求失败: {}", endpoint, e);
            }
        }
    }

    // 验证性能要求（所有端点应在1秒内响应）
    let slow_endpoints: Vec<_> = performance_results
        .iter()
        .filter(|(_, duration)| *duration > Duration::from_secs(1))
        .collect();

    if !slow_endpoints.is_empty() {
        println!("⚠️ 响应时间超过1秒的端点:");
        for (endpoint, duration) in slow_endpoints {
            println!("  - {}: {:?}", endpoint, duration);
        }
    }

    println!("✅ 健康检查性能测试完成");
}

/// 测试健康检查状态分类
#[tokio::test]
async fn test_health_status_classification() {
    println!("🏷️ 开始健康检查状态分类测试");

    wait_for_server().await.expect("服务器启动失败");

    let client = create_http_client();

    // 测试基础健康检查的状态分类
    let response = client
        .get(&format!("{}/api/health", BASE_URL))
        .send()
        .await
        .expect("健康检查请求失败");

    let json_response: Value = response.json().await.expect("解析JSON失败");

    // 验证状态字段
    let status = json_response["status"]
        .as_str()
        .expect("status字段不是字符串");

    assert!(
        ["healthy", "unhealthy", "degraded"].contains(&status),
        "无效的健康状态: {}",
        status
    );

    // 验证服务状态分类
    if let Some(services) = json_response.get("services") {
        let services_obj = services.as_object().expect("services不是对象");

        for (service_name, service_status) in services_obj {
            let status_str = service_status
                .as_str()
                .expect(&format!("{}服务状态不是字符串", service_name));

            assert!(
                ["healthy", "unhealthy", "degraded"].contains(&status_str),
                "服务 {} 状态无效: {}",
                service_name,
                status_str
            );
        }
    }

    println!("✅ 健康检查状态分类测试通过");
}
