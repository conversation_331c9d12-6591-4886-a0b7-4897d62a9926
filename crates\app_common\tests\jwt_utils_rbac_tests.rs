//! JWT工具类RBAC扩展功能单元测试
//!
//! 本测试套件专门测试jwt_utils.rs中的RBAC扩展功能，包括：
//! - ExtendedClaims结构体功能
//! - 角色token创建和验证
//! - 向后兼容性处理
//! - 错误处理和边缘情况
//! - 角色解析和转换

use app_common::utils::jwt_utils::ExtendedClaims;
use app_common::utils::{JwtError, JwtUtils};
use app_interfaces::auth::UserRole;
use chrono::{Duration, Utc};
use std::thread;
use std::time::Duration as StdDuration;

// ============================================================================
// 测试常量和辅助函数
// ============================================================================

const TEST_SECRET: &str = "test_jwt_secret_key_for_rbac_testing";
const TEST_USER_ID: &str = "test_user_123";
const TEST_USERNAME: &str = "testuser";
const DEFAULT_EXPIRY_HOURS: i64 = 24;

/// 创建测试用的JWT工具实例
fn create_test_jwt_utils() -> JwtUtils {
    JwtUtils::new(TEST_SECRET.to_string())
}

/// 创建测试用的ExtendedClaims
fn create_test_extended_claims(role: UserRole, expires_in_hours: i64) -> ExtendedClaims {
    ExtendedClaims::new(TEST_USER_ID, TEST_USERNAME, role, expires_in_hours)
}

/// 创建过期的ExtendedClaims（用于测试过期token）
#[allow(dead_code)]
fn create_expired_extended_claims(role: UserRole) -> ExtendedClaims {
    let now = Utc::now();
    let past_time = now - Duration::hours(1); // 1小时前过期

    ExtendedClaims {
        sub: TEST_USER_ID.to_string(),
        username: TEST_USERNAME.to_string(),
        role: role.to_string(),
        iat: past_time.timestamp(),
        exp: past_time.timestamp(), // 设置为过去时间
        nbf: Some(past_time.timestamp()),
        iss: Some("axum-tutorial".to_string()),
        aud: Some("axum-tutorial-users".to_string()),
        jti: Some(uuid::Uuid::new_v4().to_string()),
    }
}

// ============================================================================
// ExtendedClaims结构体功能测试
// ============================================================================

#[cfg(test)]
mod extended_claims_tests {
    use super::*;

    /// 测试ExtendedClaims的创建
    #[test]
    fn test_extended_claims_creation() {
        let role = UserRole::Admin;
        let claims = create_test_extended_claims(role.clone(), DEFAULT_EXPIRY_HOURS);

        // 验证基础字段
        assert_eq!(claims.sub, TEST_USER_ID);
        assert_eq!(claims.username, TEST_USERNAME);
        assert_eq!(claims.role, role.to_string());

        // 验证时间字段
        let now = Utc::now().timestamp();
        assert!(claims.iat <= now);
        assert!(claims.exp > now);
        assert!(claims.exp - claims.iat == DEFAULT_EXPIRY_HOURS * 3600);

        // 验证可选字段
        assert!(claims.nbf.is_some());
        assert_eq!(claims.iss, Some("axum-tutorial".to_string()));
        assert_eq!(claims.aud, Some("axum-tutorial-users".to_string()));
        assert!(claims.jti.is_some());
    }

    /// 测试不同角色的ExtendedClaims创建
    #[test]
    fn test_extended_claims_different_roles() {
        let roles = vec![
            UserRole::Admin,
            UserRole::Manager,
            UserRole::User,
            UserRole::Guest,
        ];

        for role in roles {
            let claims = create_test_extended_claims(role.clone(), DEFAULT_EXPIRY_HOURS);
            assert_eq!(claims.role, role.to_string());
        }
    }

    /// 测试ExtendedClaims转换为基础Claims
    #[test]
    fn test_extended_claims_to_basic_claims() {
        let extended_claims = create_test_extended_claims(UserRole::Manager, DEFAULT_EXPIRY_HOURS);
        let basic_claims = extended_claims.to_basic_claims();

        // 验证转换后的字段
        assert_eq!(basic_claims.sub, extended_claims.sub);
        assert_eq!(basic_claims.username, extended_claims.username);
        assert_eq!(basic_claims.exp, extended_claims.exp);
        assert_eq!(basic_claims.iat, extended_claims.iat);
    }

    /// 测试ExtendedClaims的克隆和相等性
    #[test]
    fn test_extended_claims_clone_and_equality() {
        let claims1 = create_test_extended_claims(UserRole::User, DEFAULT_EXPIRY_HOURS);
        let claims2 = claims1.clone();

        assert_eq!(claims1, claims2);
        assert_eq!(claims1.jti, claims2.jti); // UUID应该相同
    }
}

// ============================================================================
// 角色token创建功能测试
// ============================================================================

#[cfg(test)]
mod role_token_creation_tests {
    use super::*;

    /// 测试创建包含角色信息的JWT token
    #[test]
    fn test_create_token_with_role_success() {
        let jwt_utils = create_test_jwt_utils();
        let role = UserRole::Admin;

        let result = jwt_utils.create_token_with_role(
            TEST_USER_ID,
            TEST_USERNAME,
            role,
            DEFAULT_EXPIRY_HOURS,
        );

        assert!(result.is_ok());
        let token = result.unwrap();
        assert!(!token.is_empty());
        assert!(token.contains('.')); // JWT格式检查

        // 验证token可以被正确解析
        let validation_result = jwt_utils.validate_token_with_role(&token);
        assert!(validation_result.is_ok());

        let claims = validation_result.unwrap();
        assert_eq!(claims.sub, TEST_USER_ID);
        assert_eq!(claims.username, TEST_USERNAME);
        assert_eq!(claims.role, UserRole::Admin.to_string());
    }

    /// 测试创建不同角色的token
    #[test]
    fn test_create_token_different_roles() {
        let jwt_utils = create_test_jwt_utils();
        let roles = vec![
            UserRole::Admin,
            UserRole::Manager,
            UserRole::User,
            UserRole::Guest,
        ];

        for role in roles {
            let result = jwt_utils.create_token_with_role(
                TEST_USER_ID,
                TEST_USERNAME,
                role.clone(),
                DEFAULT_EXPIRY_HOURS,
            );

            assert!(result.is_ok());
            let token = result.unwrap();

            // 验证token包含正确的角色信息
            let claims = jwt_utils.validate_token_with_role(&token).unwrap();
            assert_eq!(claims.role, role.to_string());
        }
    }

    /// 测试创建不同过期时间的token
    #[test]
    fn test_create_token_different_expiry_times() {
        let jwt_utils = create_test_jwt_utils();
        let expiry_hours = vec![1, 12, 24, 48, 168]; // 1小时到1周

        for hours in expiry_hours {
            let result = jwt_utils.create_token_with_role(
                TEST_USER_ID,
                TEST_USERNAME,
                UserRole::User,
                hours,
            );

            assert!(result.is_ok());
            let token = result.unwrap();
            let claims = jwt_utils.validate_token_with_role(&token).unwrap();

            // 验证过期时间设置正确
            let expected_exp = claims.iat + hours * 3600;
            assert_eq!(claims.exp, expected_exp);
        }
    }

    /// 测试创建token时的边缘情况
    #[test]
    fn test_create_token_edge_cases() {
        let jwt_utils = create_test_jwt_utils();

        // 测试空用户ID
        let result = jwt_utils.create_token_with_role(
            "",
            TEST_USERNAME,
            UserRole::User,
            DEFAULT_EXPIRY_HOURS,
        );
        assert!(result.is_ok()); // 空用户ID应该被允许，由业务逻辑决定

        // 测试空用户名
        let result = jwt_utils.create_token_with_role(
            TEST_USER_ID,
            "",
            UserRole::User,
            DEFAULT_EXPIRY_HOURS,
        );
        assert!(result.is_ok()); // 空用户名应该被允许，由业务逻辑决定

        // 测试极短过期时间
        let result =
            jwt_utils.create_token_with_role(TEST_USER_ID, TEST_USERNAME, UserRole::User, 0);
        assert!(result.is_ok()); // 0小时过期时间应该被允许

        // 测试极长过期时间
        let result =
            jwt_utils.create_token_with_role(TEST_USER_ID, TEST_USERNAME, UserRole::User, 8760); // 1年
        assert!(result.is_ok());
    }
}

// ============================================================================
// 角色token验证功能测试
// ============================================================================

#[cfg(test)]
mod role_token_validation_tests {
    use super::*;

    /// 测试验证包含角色信息的JWT token
    #[test]
    fn test_validate_token_with_role_success() {
        let jwt_utils = create_test_jwt_utils();
        let role = UserRole::Manager;

        // 创建token
        let token = jwt_utils
            .create_token_with_role(
                TEST_USER_ID,
                TEST_USERNAME,
                role.clone(),
                DEFAULT_EXPIRY_HOURS,
            )
            .unwrap();

        // 验证token
        let result = jwt_utils.validate_token_with_role(&token);
        assert!(result.is_ok());

        let claims = result.unwrap();
        assert_eq!(claims.sub, TEST_USER_ID);
        assert_eq!(claims.username, TEST_USERNAME);
        assert_eq!(claims.role, role.to_string());
        assert!(claims.jti.is_some());
    }

    /// 测试验证Bearer token格式
    #[test]
    fn test_validate_bearer_token_with_role() {
        let jwt_utils = create_test_jwt_utils();
        let role = UserRole::User;

        // 创建token
        let token = jwt_utils
            .create_token_with_role(
                TEST_USER_ID,
                TEST_USERNAME,
                role.clone(),
                DEFAULT_EXPIRY_HOURS,
            )
            .unwrap();

        // 测试Bearer格式验证
        let bearer_token = format!("Bearer {token}");
        let result = jwt_utils.validate_bearer_token_with_role(&bearer_token);

        assert!(result.is_ok());
        let claims = result.unwrap();
        assert_eq!(claims.role, role.to_string());
    }

    /// 测试验证空token
    #[test]
    fn test_validate_empty_token() {
        let jwt_utils = create_test_jwt_utils();

        let result = jwt_utils.validate_token_with_role("");
        assert!(result.is_err());
        assert_eq!(result.unwrap_err(), JwtError::TokenMissing);
    }

    /// 测试验证无效token
    #[test]
    fn test_validate_invalid_token() {
        let jwt_utils = create_test_jwt_utils();

        let result = jwt_utils.validate_token_with_role("invalid.token.here");
        assert!(result.is_err());
        assert_eq!(result.unwrap_err(), JwtError::TokenInvalid);
    }

    /// 测试验证过期token
    #[test]
    fn test_validate_expired_token() {
        let jwt_utils = create_test_jwt_utils();

        // 创建一个立即过期的token
        let token = jwt_utils
            .create_token_with_role(
                TEST_USER_ID,
                TEST_USERNAME,
                UserRole::User,
                0, // 0小时过期
            )
            .unwrap();

        // 等待一小段时间确保token过期
        thread::sleep(StdDuration::from_millis(100));

        let result = jwt_utils.validate_token_with_role(&token);
        // 注意：由于时间精度问题，这个测试可能不稳定，实际应用中需要更精确的过期时间控制
        // 这里主要测试过期检查逻辑的存在
        if result.is_err() {
            assert_eq!(result.unwrap_err(), JwtError::TokenExpired);
        }
    }

    /// 测试验证错误的密钥
    #[test]
    fn test_validate_token_wrong_secret() {
        let jwt_utils1 = create_test_jwt_utils();
        let jwt_utils2 = JwtUtils::new("different_secret".to_string());

        // 用第一个密钥创建token
        let token = jwt_utils1
            .create_token_with_role(
                TEST_USER_ID,
                TEST_USERNAME,
                UserRole::Admin,
                DEFAULT_EXPIRY_HOURS,
            )
            .unwrap();

        // 用第二个密钥验证token（应该失败）
        let result = jwt_utils2.validate_token_with_role(&token);
        assert!(result.is_err());
        assert_eq!(result.unwrap_err(), JwtError::TokenInvalid);
    }
}

// ============================================================================
// 向后兼容性测试
// ============================================================================

#[cfg(test)]
mod backward_compatibility_tests {
    use super::*;

    /// 测试灵活token验证（向后兼容）
    #[test]
    fn test_validate_token_flexible_with_extended_claims() {
        let jwt_utils = create_test_jwt_utils();
        let role = UserRole::Manager;

        // 创建扩展Claims token
        let token = jwt_utils
            .create_token_with_role(
                TEST_USER_ID,
                TEST_USERNAME,
                role.clone(),
                DEFAULT_EXPIRY_HOURS,
            )
            .unwrap();

        // 使用灵活验证方法
        let result = jwt_utils.validate_token_flexible(&token);
        assert!(result.is_ok());

        let claims = result.unwrap();
        assert_eq!(claims.sub, TEST_USER_ID);
        assert_eq!(claims.username, TEST_USERNAME);
        assert_eq!(claims.role, role.to_string());
    }

    /// 测试灵活token验证回退到基础Claims
    #[test]
    fn test_validate_token_flexible_fallback_to_basic() {
        let jwt_utils = create_test_jwt_utils();

        // 创建基础Claims token
        let basic_token = jwt_utils
            .create_token(TEST_USER_ID, TEST_USERNAME, DEFAULT_EXPIRY_HOURS)
            .unwrap();

        // 使用灵活验证方法（应该回退到基础Claims并转换）
        let result = jwt_utils.validate_token_flexible(&basic_token);
        assert!(result.is_ok());

        let claims = result.unwrap();
        assert_eq!(claims.sub, TEST_USER_ID);
        assert_eq!(claims.username, TEST_USERNAME);
        assert_eq!(claims.role, UserRole::User.to_string()); // 默认角色
        assert!(claims.jti.is_some()); // 应该生成新的JTI
    }

    /// 测试基础Claims转换为ExtendedClaims
    #[test]
    fn test_basic_to_extended_claims_conversion() {
        let jwt_utils = create_test_jwt_utils();

        // 创建基础token
        let basic_token = jwt_utils
            .create_token(TEST_USER_ID, TEST_USERNAME, DEFAULT_EXPIRY_HOURS)
            .unwrap();

        // 验证基础token
        let basic_claims = jwt_utils.validate_token(&basic_token).unwrap();

        // 使用灵活验证转换为扩展Claims
        let extended_claims = jwt_utils.validate_token_flexible(&basic_token).unwrap();

        // 验证转换正确性
        assert_eq!(extended_claims.sub, basic_claims.sub);
        assert_eq!(extended_claims.username, basic_claims.username);
        assert_eq!(extended_claims.exp, basic_claims.exp);
        assert_eq!(extended_claims.iat, basic_claims.iat);
        assert_eq!(extended_claims.role, UserRole::User.to_string());
        assert_eq!(extended_claims.iss, Some("axum-tutorial".to_string()));
        assert_eq!(extended_claims.aud, Some("axum-tutorial-users".to_string()));
    }

    /// 测试向后兼容性错误处理
    #[test]
    fn test_flexible_validation_error_handling() {
        let jwt_utils = create_test_jwt_utils();

        // 测试无效token
        let result = jwt_utils.validate_token_flexible("invalid.token.format");
        assert!(result.is_err());
        assert_eq!(result.unwrap_err(), JwtError::TokenInvalid);

        // 测试空token
        let result = jwt_utils.validate_token_flexible("");
        assert!(result.is_err());
        assert_eq!(result.unwrap_err(), JwtError::TokenMissing);
    }
}

// ============================================================================
// 错误处理和边缘情况测试
// ============================================================================

#[cfg(test)]
mod error_handling_tests {
    use super::*;

    /// 测试JwtError的所有变体
    #[test]
    fn test_jwt_error_variants() {
        // 测试错误消息格式
        assert_eq!(JwtError::TokenMissing.to_string(), "JWT token 缺失");
        assert_eq!(JwtError::TokenInvalid.to_string(), "JWT token 无效");
        assert_eq!(JwtError::TokenExpired.to_string(), "JWT token 已过期");

        let creation_error = JwtError::TokenCreationFailed("test error".to_string());
        assert_eq!(creation_error.to_string(), "JWT token 创建失败: test error");

        let role_error = JwtError::RoleParsingFailed("invalid role".to_string());
        assert_eq!(role_error.to_string(), "角色解析失败: invalid role");
    }

    /// 测试JwtError的相等性比较
    #[test]
    fn test_jwt_error_equality() {
        assert_eq!(JwtError::TokenMissing, JwtError::TokenMissing);
        assert_eq!(JwtError::TokenInvalid, JwtError::TokenInvalid);
        assert_eq!(JwtError::TokenExpired, JwtError::TokenExpired);

        let error1 = JwtError::TokenCreationFailed("same message".to_string());
        let error2 = JwtError::TokenCreationFailed("same message".to_string());
        assert_eq!(error1, error2);

        let error3 = JwtError::TokenCreationFailed("different message".to_string());
        assert_ne!(error1, error3);
    }

    /// 测试极端输入情况
    #[test]
    fn test_extreme_input_cases() {
        let jwt_utils = create_test_jwt_utils();

        // 测试极长的用户ID和用户名
        let long_user_id = "a".repeat(1000);
        let long_username = "b".repeat(1000);

        let result = jwt_utils.create_token_with_role(
            &long_user_id,
            &long_username,
            UserRole::User,
            DEFAULT_EXPIRY_HOURS,
        );
        assert!(result.is_ok());

        // 验证创建的token可以正确解析
        let token = result.unwrap();
        let claims = jwt_utils.validate_token_with_role(&token).unwrap();
        assert_eq!(claims.sub, long_user_id);
        assert_eq!(claims.username, long_username);
    }

    /// 测试特殊字符处理
    #[test]
    fn test_special_characters_handling() {
        let jwt_utils = create_test_jwt_utils();

        // 测试包含特殊字符的用户信息
        let special_user_id = "<EMAIL>";
        let special_username = "用户名_with_中文_and_symbols!@#$%";

        let result = jwt_utils.create_token_with_role(
            special_user_id,
            special_username,
            UserRole::Admin,
            DEFAULT_EXPIRY_HOURS,
        );
        assert!(result.is_ok());

        let token = result.unwrap();
        let claims = jwt_utils.validate_token_with_role(&token).unwrap();
        assert_eq!(claims.sub, special_user_id);
        assert_eq!(claims.username, special_username);
    }

    /// 测试并发token创建和验证
    #[test]
    fn test_concurrent_token_operations() {
        use std::sync::Arc;
        use std::thread;

        let jwt_utils = Arc::new(create_test_jwt_utils());
        let mut handles = vec![];

        // 创建10个并发线程进行token操作
        for i in 0..10 {
            let jwt_utils_clone = Arc::clone(&jwt_utils);
            let handle = thread::spawn(move || {
                let user_id = format!("user_{i}");
                let username = format!("username_{i}");
                let role = match i % 4 {
                    0 => UserRole::Admin,
                    1 => UserRole::Manager,
                    2 => UserRole::User,
                    _ => UserRole::Guest,
                };

                // 创建token
                let token = jwt_utils_clone
                    .create_token_with_role(&user_id, &username, role.clone(), DEFAULT_EXPIRY_HOURS)
                    .unwrap();

                // 验证token
                let claims = jwt_utils_clone.validate_token_with_role(&token).unwrap();
                assert_eq!(claims.sub, user_id);
                assert_eq!(claims.username, username);
                assert_eq!(claims.role, role.to_string());
            });
            handles.push(handle);
        }

        // 等待所有线程完成
        for handle in handles {
            handle.join().unwrap();
        }
    }
}
