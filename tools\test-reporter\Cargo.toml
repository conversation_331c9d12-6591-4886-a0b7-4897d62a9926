[package]
name = "test-reporter"
version = "0.1.0"
edition = "2024"
authors = ["Rust学习者"]
description = "测试报告生成工具"

[dependencies]
# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 时间处理
chrono = { version = "0.4.41", features = ["serde"] }

# 错误处理
anyhow = "1.0.82"
thiserror = "1.0"

# 模板引擎
tera = "1.19"

# 图表生成
plotters = "0.3"

# 文件操作
# std库是内置的，不需要在dependencies中声明

[[bin]]
name = "test-report-generator"
path = "src/bin/main.rs"
