# 新对话连续性提示词模板 - 任务52.2

## 📋 上下文信息

### 项目基本信息
- **项目名称**: Axum企业级聊天应用后端
- **项目根目录**: `d:\ceshi\ceshi\axum-tutorial`
- **技术栈**: Rust 2024 Edition + Axum 0.8.4 + SeaORM + PostgreSQL 17 + DragonflyDB
- **架构模式**: 模块化领域驱动设计(Modular DDD) + 整洁架构
- **开发原则**: 严格遵循TDD原则，先写测试再实现功能

### 当前任务状态
- **已完成任务**: 任务52.1 - 设计消息搜索功能的测试用例框架 ✅
- **当前任务**: 任务52.2 - 实现测试框架基础设施 🔄
- **项目进度**: 企业级消息搜索功能TDD开发计划 (任务#52) 进行中

### 任务52.1完成成果
已成功完成消息搜索功能的TDD测试用例框架设计，包括：

1. **核心测试框架** (`tests/message_search_test_framework.rs`)
   - 测试配置管理系统
   - 性能指标收集器
   - 测试数据生成器
   - 测试场景管理器

2. **PostgreSQL全文搜索测试** (`tests/unit/postgres_fulltext_search_tests.rs`)
   - GIN索引性能测试
   - tsvector优化测试
   - 中文全文搜索测试
   - 搜索排序算法测试

3. **DragonflyDB缓存测试** (`tests/unit/dragonfly_cache_tests.rs`)
   - L1/L2多级缓存测试
   - TTL管理策略测试
   - 缓存预热和失效测试
   - 高并发访问测试

4. **防雪崩机制测试** (`tests/unit/circuit_breaker_tests.rs`)
   - 熔断器实现和测试
   - 限流器实现和测试
   - 降级策略测试

5. **性能基准测试** (`tests/performance/message_search_performance_tests.rs`)
   - 百万并发测试框架
   - P99/P95延迟测试
   - 吞吐量基准测试

6. **集成测试框架** (`tests/integration/message_search_integration_tests.rs`)
   - 端到端测试
   - API集成测试
   - 数据库缓存集成测试

7. **测试工具和配置** (`tests/fixtures/test_config.rs`, `tests/test_report_generator.rs`)
   - 测试环境配置管理
   - 多格式测试报告生成

## 🎯 任务52.2目标

### 主要目标
实现测试框架的基础设施，让任务52.1设计的测试用例能够实际运行。

### 具体要求
1. **数据库连接实现**
   - 实现PostgreSQL测试数据库连接
   - 配置测试数据库模式和表结构
   - 实现数据库连接池管理
   - 集成SeaORM测试支持

2. **缓存连接实现**
   - 实现DragonflyDB测试连接
   - 配置缓存连接池
   - 实现缓存键命名空间管理
   - 集成Fred客户端测试支持

3. **测试数据管理**
   - 实现测试数据的创建和清理
   - 实现测试数据的隔离机制
   - 实现测试数据的版本管理
   - 支持并行测试的数据隔离

4. **CI/CD集成**
   - 配置GitHub Actions测试流水线
   - 实现测试报告自动生成
   - 配置测试覆盖率收集
   - 实现测试结果通知

5. **测试环境配置**
   - 实现多环境配置管理
   - 支持本地开发环境测试
   - 支持CI环境测试
   - 实现环境变量管理

### 性能要求
- 测试框架启动时间 < 5秒
- 测试数据生成速度 > 1000条/秒
- 测试环境清理时间 < 3秒
- 支持并行测试执行

### 技术约束
- 严格遵循`rust_axum_Rules.md`编码规范
- 使用SeaORM（不使用SQLx）
- 维护API向后兼容性
- 确保每步可编译：`cargo check --workspace`
- 遵循DRY、SOLID原则、TDD模式
- 所有代码注释必须使用中文

## 📁 相关文件位置

### 已完成的测试框架文件
- `tests/message_search_test_framework.rs` - 核心测试框架
- `tests/unit/postgres_fulltext_search_tests.rs` - PostgreSQL测试
- `tests/unit/dragonfly_cache_tests.rs` - DragonflyDB测试
- `tests/unit/circuit_breaker_tests.rs` - 防雪崩测试
- `tests/performance/message_search_performance_tests.rs` - 性能测试
- `tests/integration/message_search_integration_tests.rs` - 集成测试
- `tests/fixtures/test_config.rs` - 测试配置
- `tests/test_report_generator.rs` - 报告生成器

### 需要实现的文件
- `tests/infrastructure/database_test_setup.rs` - 数据库测试设置
- `tests/infrastructure/cache_test_setup.rs` - 缓存测试设置
- `tests/infrastructure/test_data_manager.rs` - 测试数据管理
- `tests/infrastructure/test_environment.rs` - 测试环境管理
- `.github/workflows/message_search_tests.yml` - CI/CD配置

### 项目核心文件
- `migration/src/message_entity.rs` - 消息实体定义
- `crates/app_infrastructure/src/domains/chat/message_repository.rs` - 消息仓库
- `server/src/routes/handlers/chat.rs` - 搜索API处理器
- `Cargo.toml` - 项目依赖配置

## 🔧 开发指导

### 1. 开发流程
1. 首先调用Context7 MCP工具查询最新的测试框架最佳实践
2. 分析现有测试框架代码，理解架构设计
3. 实现数据库测试基础设施
4. 实现缓存测试基础设施
5. 实现测试数据管理系统
7. 运行测试验证实现

### 2. 质量要求
- 所有新增代码必须有详细中文注释
- 必须编写对应的单元测试
- 必须通过`cargo check --workspace`编译检查
- 必须遵循项目编码规范
- 必须支持并行测试执行

### 3. 测试验证
- 验证测试框架能够正常启动
- 验证数据库连接和表创建
- 验证缓存连接和键管理
- 验证测试数据生成和清理


## 🎯 期望输出

1. **完整的测试基础设施实现**
2. **可运行的测试框架**
3. **CI/CD集成配置**
4. **测试环境管理系统**
5. **详细的实现文档**

## 📝 提示词模板

请使用以下提示词开始任务52.2：

---

# 企业级消息搜索功能TDD开发 - 任务52.2，实现测试框架基础设施

## 📋 当前任务状态
- **主任务**: 实现企业级消息搜索功能的完整TDD开发计划 (任务#52)
- **当前子任务**: 实现测试框架基础设施 (任务#52.2) - **开始执行**
- **项目根目录**: `d:\ceshi\ceshi\axum-tutorial`
- **技术栈**: Rust 2024 Edition + Axum 0.8.4 + SeaORM + PostgreSQL 17 + DragonflyDB

## 🎯 任务目标
基于任务52.1完成的TDD测试用例框架设计，实现测试框架的基础设施，包括数据库连接、缓存连接、测试数据管理、CI/CD集成等，让设计的测试用例能够实际运行。

## 📝 具体要求
1. **数据库测试基础设施**: PostgreSQL连接、表结构、连接池管理
2. **缓存测试基础设施**: DragonflyDB连接、键管理、连接池
3. **测试数据管理**: 数据创建、清理、隔离、版本管理
4. **CI/CD集成**: GitHub Actions配置、报告生成、覆盖率收集
5. **测试环境配置**: 多环境支持、环境变量管理

## 🔧 技术约束
- 严格遵循`rust_axum_Rules.md`编码规范
- 使用SeaORM（不使用SQLx），维护API向后兼容性
- 确保每步可编译：`cargo check --workspace`
- 遵循DRY、SOLID原则、TDD模式
- 所有代码注释必须使用中文
- 遇到问题时先调用Context7 MCP工具查询最新最佳实践

请开始执行任务52.2，实现测试框架基础设施。记住严格遵循TDD原则，确保测试框架的可靠性和性能。

---

## 📞 联系方式
如需查看任务52.1的详细完成情况，请查看：
- `docs/task_52_1_completion_summary.md` - 任务完成总结
- `docs/message_search_tdd_test_framework.md` - 测试框架设计文档

**对话结束提醒**：完成任务52.2后提醒我开启新对话以避免上下文过长，并提供任务52.3的新对话框连续性提示词模版。
