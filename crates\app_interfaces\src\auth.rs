//! 认证相关的API数据传输对象

use serde::{Deserialize, Serialize};
use std::fmt;
use validator::Validate;

/// 用户角色枚举
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum UserRole {
    /// 系统管理员 - 最高权限
    Admin,
    /// 部门经理 - 高级权限
    Manager,
    /// 普通用户 - 基础权限
    User,
    /// 访客用户 - 只读权限
    Guest,
    /// 自定义角色
    Custom { name: String, level: u8 },
}

impl UserRole {
    /// 获取角色权限级别（数字越大权限越高）
    pub fn permission_level(&self) -> u8 {
        match self {
            UserRole::Admin => 100,
            UserRole::Manager => 75,
            UserRole::User => 50,
            UserRole::Guest => 25,
            UserRole::Custom { level, .. } => *level,
        }
    }

    /// 获取角色名称
    pub fn name(&self) -> String {
        match self {
            UserRole::Admin => "Admin".to_string(),
            UserRole::Manager => "Manager".to_string(),
            UserRole::User => "User".to_string(),
            UserRole::Guest => "Guest".to_string(),
            UserRole::Custom { name, .. } => name.clone(),
        }
    }

    /// 获取角色描述
    pub fn description(&self) -> String {
        match self {
            UserRole::Admin => "系统管理员，拥有所有权限".to_string(),
            UserRole::Manager => "部门经理，拥有管理权限".to_string(),
            UserRole::User => "普通用户，拥有基础操作权限".to_string(),
            UserRole::Guest => "访客用户，只有只读权限".to_string(),
            UserRole::Custom { name, level } => {
                format!("自定义角色：{name}，权限级别：{level}")
            }
        }
    }

    /// 从字符串创建角色
    pub fn parse_role(s: &str) -> Result<Self, String> {
        match s.to_lowercase().as_str() {
            "admin" => Ok(UserRole::Admin),
            "manager" => Ok(UserRole::Manager),
            "user" => Ok(UserRole::User),
            "guest" => Ok(UserRole::Guest),
            _ => Err(format!("无效的用户角色: {s}")),
        }
    }

    /// 获取角色名称
    pub fn as_str(&self) -> &str {
        match self {
            UserRole::Admin => "Admin",
            UserRole::Manager => "Manager",
            UserRole::User => "User",
            UserRole::Guest => "Guest",
            UserRole::Custom { name, .. } => name,
        }
    }

    /// 检查是否可以升级到指定角色
    pub fn can_upgrade_to(&self, target_role: &UserRole) -> bool {
        self.permission_level() < target_role.permission_level()
    }

    /// 检查是否可以降级到指定角色
    pub fn can_downgrade_to(&self, target_role: &UserRole) -> bool {
        self.permission_level() > target_role.permission_level()
    }

    /// 检查是否是管理员角色
    pub fn is_admin(&self) -> bool {
        matches!(self, UserRole::Admin) || self.permission_level() >= 100
    }

    /// 检查是否是管理级别角色（Manager及以上）
    pub fn is_manager_or_above(&self) -> bool {
        self.permission_level() >= 75
    }

    /// 获取所有标准角色
    pub fn all_standard_roles() -> Vec<UserRole> {
        vec![
            UserRole::Admin,
            UserRole::Manager,
            UserRole::User,
            UserRole::Guest,
        ]
    }
}

impl fmt::Display for UserRole {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.name())
    }
}

/// 权限操作枚举
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum Permission {
    /// 读取权限
    Read,
    /// 写入权限
    Write,
    /// 删除权限
    Delete,
    /// 管理员权限
    Admin,
}

impl Permission {
    /// 获取操作所需的最低权限级别
    pub fn required_level(&self) -> u8 {
        match self {
            Permission::Read => 25,
            Permission::Write => 50,
            Permission::Delete => 75,
            Permission::Admin => 100,
        }
    }

    /// 获取权限名称
    pub fn name(&self) -> String {
        match self {
            Permission::Read => "Read".to_string(),
            Permission::Write => "Write".to_string(),
            Permission::Delete => "Delete".to_string(),
            Permission::Admin => "Admin".to_string(),
        }
    }

    /// 获取权限描述
    pub fn description(&self) -> String {
        match self {
            Permission::Read => "读取权限，可以查看资源".to_string(),
            Permission::Write => "写入权限，可以创建和修改资源".to_string(),
            Permission::Delete => "删除权限，可以删除资源".to_string(),
            Permission::Admin => "管理员权限，可以执行所有操作".to_string(),
        }
    }

    /// 从字符串创建权限
    pub fn parse_permission(s: &str) -> Result<Self, String> {
        match s.to_lowercase().as_str() {
            "read" => Ok(Permission::Read),
            "write" => Ok(Permission::Write),
            "delete" => Ok(Permission::Delete),
            "admin" => Ok(Permission::Admin),
            _ => Err(format!("无效的权限类型: {s}")),
        }
    }

    /// 获取权限名称
    pub fn as_str(&self) -> &'static str {
        match self {
            Permission::Read => "Read",
            Permission::Write => "Write",
            Permission::Delete => "Delete",
            Permission::Admin => "Admin",
        }
    }

    /// 获取所有权限
    pub fn all_permissions() -> Vec<Permission> {
        vec![
            Permission::Read,
            Permission::Write,
            Permission::Delete,
            Permission::Admin,
        ]
    }
}

/// 用户注册请求
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct RegisterRequest {
    #[validate(length(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间"))]
    pub username: String,

    #[validate(length(min = 6, message = "密码长度至少6个字符"))]
    pub password: String,

    #[validate(must_match(other = "password", message = "两次输入的密码不一致"))]
    pub confirm_password: String,

    /// 邮箱地址（可选，学习项目中暂不使用）
    #[validate(email(message = "请输入有效的邮箱地址"))]
    #[serde(skip_serializing_if = "Option::is_none")]
    pub email: Option<String>,
}

/// 用户登录请求
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct LoginRequest {
    #[validate(length(min = 1, message = "用户名不能为空"))]
    pub username: String,

    #[validate(length(min = 1, message = "密码不能为空"))]
    pub password: String,
}

/// 认证响应
#[derive(Debug, Serialize, Deserialize)]
pub struct AuthResponse {
    /// JWT访问令牌
    pub access_token: String,
    /// 令牌类型
    pub token_type: String,
    /// 令牌过期时间（秒）
    pub expires_in: i64,
    /// 用户信息
    pub user: UserInfo,
}

/// 完整认证响应（包含更多令牌信息）
#[derive(Debug, Serialize, Deserialize)]
pub struct FullAuthResponse {
    /// JWT访问令牌
    pub access_token: String,
    /// 令牌类型
    pub token_type: String,
    /// 令牌过期时间（秒）
    pub expires_in: i64,
    /// 用户信息
    pub user: UserInfo,
}

/// 用户信息（用于认证响应）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserInfo {
    pub id: uuid::Uuid,
    pub username: String,
    pub email: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

/// JWT令牌刷新请求
#[derive(Debug, Serialize, Deserialize)]
pub struct RefreshTokenRequest {
    pub refresh_token: String,
}

/// JWT令牌刷新响应
#[derive(Debug, Serialize, Deserialize)]
pub struct RefreshTokenResponse {
    pub access_token: String,
    pub refresh_token: String,
}

/// 密码重置请求
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct PasswordResetRequest {
    #[validate(email(message = "请输入有效的邮箱地址"))]
    pub email: String,
}

/// 密码重置确认请求
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct PasswordResetConfirmRequest {
    pub token: String,

    #[validate(length(min = 6, message = "密码长度至少6个字符"))]
    pub new_password: String,

    #[validate(must_match(other = "new_password", message = "两次输入的密码不一致"))]
    pub confirm_password: String,
}

/// 用户登录凭据（用于领域服务）
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct UserCredentials {
    /// 用户名
    #[validate(length(min = 1, message = "用户名不能为空"))]
    pub username: String,

    /// 密码
    #[validate(length(min = 1, message = "密码不能为空"))]
    pub password: String,
}

/// 认证结果（包含完整令牌信息）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthenticationResult {
    /// 访问令牌
    pub access_token: String,

    /// 刷新令牌
    pub refresh_token: String,

    /// 令牌类型
    pub token_type: String,

    /// 访问令牌过期时间（秒）
    pub expires_in: u64,

    /// 用户信息
    pub user: UserInfo,
}

/// JWT 声明结构体
///
/// 注意：此结构体必须与 app_common::utils::jwt_utils::Claims 保持完全一致
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct Claims {
    /// 用户 ID (Subject)
    pub sub: String,
    /// 用户名
    pub username: String,
    /// 令牌过期时间（Unix 时间戳）
    pub exp: i64,
    /// 令牌签发时间（Unix 时间戳）
    pub iat: i64,
}
