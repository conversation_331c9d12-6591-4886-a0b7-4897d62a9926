/**
 * WebSocket连接稳定性测试
 * 
 * 测试场景：
 * 1. 连续点击"连接"按钮3次
 * 2. 连续点击"断开"按钮3次  
 * 3. 最后点击"连接"按钮，使WebSocket处于连接状态
 * 4. 等待约2秒后，检查连接是否自动断开
 */

const { chromium } = require('playwright');

async function runWebSocketStabilityTest() {
    console.log('🚀 开始WebSocket连接稳定性测试...');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 500 // 减慢操作速度以便观察
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
        if (msg.type() === 'error') {
            console.log('❌ 浏览器错误:', msg.text());
        } else if (msg.text().includes('WebSocket') || msg.text().includes('websocket')) {
            console.log('📡 WebSocket日志:', msg.text());
        }
    });
    
    try {
        // 1. 导航到页面
        console.log('📍 导航到测试页面...');
        await page.goto('http://127.0.0.1:3000');
        await page.waitForLoadState('networkidle');
        
        // 2. 登录测试用户
        console.log('🔐 登录测试用户...');

        // 确保在登录标签页
        await page.click('#loginTab');
        await page.waitForTimeout(500);

        await page.fill('#loginUsername', 'testuser456');
        await page.fill('#loginPassword', 'password123');

        // 点击登录表单的提交按钮
        await page.click('#loginForm button[type="submit"]');

        // 等待登录完成 - 检查用户信息显示
        await page.waitForSelector('#currentUser:has-text("testuser456")', { timeout: 10000 });
        console.log('✅ 登录成功');
        
        // 3. 获取WebSocket控制按钮
        const connectBtn = page.locator('#wsConnectBtn');
        const disconnectBtn = page.locator('#wsDisconnectBtn');
        const connectionStatus = page.locator('#connectionStatus');
        
        // 4. 执行连接稳定性测试序列
        console.log('🔄 开始执行连接稳定性测试序列...');
        
        // 记录每次操作的连接状态
        const statusHistory = [];
        
        // 连续点击"连接"按钮3次
        console.log('📡 连续点击"连接"按钮3次...');
        for (let i = 1; i <= 3; i++) {
            console.log(`  第${i}次点击连接按钮`);

            // 检查按钮是否可点击
            const isEnabled = await connectBtn.isEnabled();
            if (isEnabled) {
                await connectBtn.click();
                await page.waitForTimeout(500); // 等待状态更新
            } else {
                console.log(`    按钮已禁用，跳过点击`);
            }

            const status = await connectionStatus.textContent();
            statusHistory.push(`连接-${i}: ${status} (按钮${isEnabled ? '启用' : '禁用'})`);
            console.log(`    状态: ${status} (按钮${isEnabled ? '启用' : '禁用'})`);
        }
        
        // 连续点击"断开"按钮3次
        console.log('📡 连续点击"断开"按钮3次...');
        for (let i = 1; i <= 3; i++) {
            console.log(`  第${i}次点击断开按钮`);

            // 检查按钮是否可点击
            const isEnabled = await disconnectBtn.isEnabled();
            if (isEnabled) {
                await disconnectBtn.click();
                await page.waitForTimeout(500); // 等待状态更新
            } else {
                console.log(`    按钮已禁用，跳过点击`);
            }

            const status = await connectionStatus.textContent();
            statusHistory.push(`断开-${i}: ${status} (按钮${isEnabled ? '启用' : '禁用'})`);
            console.log(`    状态: ${status} (按钮${isEnabled ? '启用' : '禁用'})`);
        }
        
        // 最后点击"连接"按钮
        console.log('📡 最后点击"连接"按钮...');
        await connectBtn.click();
        await page.waitForTimeout(500);
        
        const finalConnectStatus = await connectionStatus.textContent();
        statusHistory.push(`最终连接: ${finalConnectStatus}`);
        console.log(`    最终连接状态: ${finalConnectStatus}`);
        
        // 5. 监控连接稳定性 - 等待30秒检查是否自动断开
        console.log('⏱️  监控连接稳定性30秒...');
        
        let connectionLost = false;
        let connectionLostTime = null;
        
        for (let second = 1; second <= 30; second++) {
            await page.waitForTimeout(1000);
            
            const currentStatus = await connectionStatus.textContent();
            
            // 检查是否从连接状态变为断开状态
            if (currentStatus.includes('未连接') || currentStatus.includes('断开')) {
                if (!connectionLost) {
                    connectionLost = true;
                    connectionLostTime = second;
                    console.log(`❌ 连接在第${second}秒时自动断开！`);
                    statusHistory.push(`自动断开: 第${second}秒 - ${currentStatus}`);
                    break;
                }
            }
            
            // 每5秒报告一次状态
            if (second % 5 === 0) {
                console.log(`    第${second}秒: ${currentStatus}`);
            }
        }
        
        // 6. 生成测试报告
        console.log('\n📊 测试报告:');
        console.log('='.repeat(50));
        
        console.log('\n📋 状态变化历史:');
        statusHistory.forEach((status, index) => {
            console.log(`  ${index + 1}. ${status}`);
        });
        
        if (connectionLost) {
            console.log(`\n❌ 测试失败: 连接在${connectionLostTime}秒后自动断开`);
            console.log('🔍 问题分析:');
            console.log('  - 频繁的连接/断开操作可能导致连接池泄漏');
            console.log('  - 服务器端可能存在连接状态管理问题');
            console.log('  - 心跳机制可能存在异常');
        } else {
            console.log('\n✅ 测试通过: 连接在30秒内保持稳定');
        }
        
        // 7. 检查服务器日志（如果可能）
        console.log('\n📝 建议检查服务器日志中的以下内容:');
        console.log('  - WebSocket连接建立/断开事件');
        console.log('  - 连接池状态变化');
        console.log('  - 心跳消息发送/接收');
        console.log('  - 任何错误或警告信息');
        
    } catch (error) {
        console.error('❌ 测试执行失败:', error);
    } finally {
        await browser.close();
        console.log('🏁 测试完成');
    }
}

// 运行测试
if (require.main === module) {
    runWebSocketStabilityTest().catch(console.error);
}

module.exports = { runWebSocketStabilityTest };
