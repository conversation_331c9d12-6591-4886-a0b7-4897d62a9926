//! # 权限检查中间件模块
//!
//! 提供基于角色的权限检查中间件，支持：
//! - 细粒度权限控制
//! - 角色验证
//! - 资源访问控制
//! - 权限缓存优化
//!
//! ## 设计原则
//! - **统一权限模型**：整合auth模块的权限检查功能到middleware系统
//! - **中间件优先**：遵循Axum最佳实践，使用中间件模式
//! - **向后兼容**：保持现有API不变
//! - **性能优化**：支持权限缓存和批量检查

use crate::middleware::AuthenticatedUser;
use app_interfaces::auth::{Permission, UserRole};
use axum::{
    extract::{FromRequestParts, Request, State},
    http::StatusCode,
    middleware::Next,
    response::Response,
};
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use thiserror::Error;

/// 权限检查错误类型
#[derive(Debug, Error)]
pub enum PermissionError {
    #[error("权限不足：需要 {required} 权限")]
    InsufficientPermission { required: String },
    #[error("角色验证失败：需要 {required} 角色")]
    InsufficientRole { required: String },
    #[error("资源访问被拒绝")]
    ResourceAccessDenied,
    #[error("权限检查失败：{0}")]
    CheckFailed(String),
}

impl From<PermissionError> for StatusCode {
    fn from(error: PermissionError) -> Self {
        match error {
            PermissionError::InsufficientPermission { .. } => StatusCode::FORBIDDEN,
            PermissionError::InsufficientRole { .. } => StatusCode::FORBIDDEN,
            PermissionError::ResourceAccessDenied => StatusCode::FORBIDDEN,
            PermissionError::CheckFailed(_) => StatusCode::INTERNAL_SERVER_ERROR,
        }
    }
}

/// 权限缓存条目
#[derive(Debug, Clone)]
struct PermissionCacheEntry {
    result: bool,
    expires_at: DateTime<Utc>,
}

impl PermissionCacheEntry {
    fn new(result: bool, ttl_seconds: u64) -> Self {
        Self {
            result,
            expires_at: Utc::now() + chrono::Duration::seconds(ttl_seconds as i64),
        }
    }

    fn is_expired(&self) -> bool {
        Utc::now() > self.expires_at
    }
}

/// 权限检查器配置
#[derive(Debug, Clone)]
pub struct PermissionCheckerConfig {
    /// 是否启用缓存
    pub cache_enabled: bool,
    /// 缓存TTL（秒）
    pub cache_ttl: u64,
    /// 是否启用详细日志
    pub verbose_logging: bool,
}

impl Default for PermissionCheckerConfig {
    fn default() -> Self {
        Self {
            cache_enabled: true,
            cache_ttl: 300, // 5分钟
            verbose_logging: false,
        }
    }
}

/// 统一权限检查器
///
/// 整合了auth模块的权限检查功能，提供中间件友好的API
#[derive(Debug)]
pub struct UnifiedPermissionChecker {
    /// 权限缓存
    cache: Arc<RwLock<HashMap<String, PermissionCacheEntry>>>,
    /// 配置
    config: PermissionCheckerConfig,
}

impl Default for UnifiedPermissionChecker {
    fn default() -> Self {
        Self::new()
    }
}

impl UnifiedPermissionChecker {
    /// 创建新的权限检查器
    pub fn new() -> Self {
        Self::with_config(PermissionCheckerConfig::default())
    }

    /// 创建带配置的权限检查器
    pub fn with_config(config: PermissionCheckerConfig) -> Self {
        Self {
            cache: Arc::new(RwLock::new(HashMap::new())),
            config,
        }
    }

    /// 检查用户是否有执行指定操作的权限
    pub fn has_permission(&self, user_role: &UserRole, permission: &Permission) -> bool {
        // 如果启用缓存，先检查缓存
        if self.config.cache_enabled {
            let cache_key = format!("{}:{}", user_role.as_str(), permission.as_str());

            if let Ok(cache) = self.cache.read() {
                if let Some(entry) = cache.get(&cache_key) {
                    if !entry.is_expired() {
                        if self.config.verbose_logging {
                            tracing::debug!("权限检查缓存命中: {} -> {}", cache_key, entry.result);
                        }
                        return entry.result;
                    }
                }
            }
        }

        // 执行实际的权限检查
        let result = user_role.permission_level() >= permission.required_level();

        if self.config.verbose_logging {
            tracing::debug!(
                "权限检查: 角色={}, 权限={}, 结果={}",
                user_role.as_str(),
                permission.as_str(),
                result
            );
        }

        // 如果启用缓存，存储结果
        if self.config.cache_enabled {
            let cache_key = format!("{}:{}", user_role.as_str(), permission.as_str());
            let entry = PermissionCacheEntry::new(result, self.config.cache_ttl);

            if let Ok(mut cache) = self.cache.write() {
                cache.insert(cache_key, entry);
            }
        }

        result
    }

    /// 检查用户是否可以访问指定资源
    pub fn can_access_resource(
        &self,
        user_role: &UserRole,
        resource_owner_id: &str,
        current_user_id: &str,
    ) -> bool {
        // 管理员可以访问所有资源
        if matches!(user_role, UserRole::Admin) {
            return true;
        }

        // 用户只能访问自己的资源
        resource_owner_id == current_user_id
    }

    /// 检查用户是否可以访问指定资源（带权限要求）
    pub fn can_access_resource_with_permission(
        &self,
        user_role: &UserRole,
        permission: &Permission,
        resource_owner_id: &str,
        current_user_id: &str,
    ) -> bool {
        // 首先检查基本权限
        if !self.has_permission(user_role, permission) {
            return false;
        }

        // 然后检查资源访问权限
        self.can_access_resource(user_role, resource_owner_id, current_user_id)
    }

    /// 批量检查权限
    pub fn check_multiple_permissions(
        &self,
        user_role: &UserRole,
        permissions: &[Permission],
    ) -> Vec<bool> {
        permissions
            .iter()
            .map(|permission| self.has_permission(user_role, permission))
            .collect()
    }

    /// 获取用户拥有的所有权限
    pub fn get_user_permissions(&self, user_role: &UserRole) -> Vec<Permission> {
        let all_permissions = vec![
            Permission::Read,
            Permission::Write,
            Permission::Delete,
            Permission::Admin,
        ];

        all_permissions
            .into_iter()
            .filter(|permission| self.has_permission(user_role, permission))
            .collect()
    }

    /// 清理过期的缓存条目
    pub fn cleanup_expired_cache(&self) {
        if !self.config.cache_enabled {
            return;
        }

        if let Ok(mut cache) = self.cache.write() {
            let before_count = cache.len();
            cache.retain(|_, entry| !entry.is_expired());
            let after_count = cache.len();

            if self.config.verbose_logging && before_count != after_count {
                tracing::debug!(
                    "权限缓存清理完成: 清理前={}, 清理后={}",
                    before_count,
                    after_count
                );
            }
        }
    }

    /// 清空所有缓存
    pub fn clear_cache(&self) {
        if let Ok(mut cache) = self.cache.write() {
            cache.clear();
            if self.config.verbose_logging {
                tracing::debug!("权限缓存已清空");
            }
        }
    }
}

/// 权限检查中间件状态
#[derive(Debug, Clone)]
pub struct PermissionMiddlewareState {
    pub checker: Arc<UnifiedPermissionChecker>,
}

impl PermissionMiddlewareState {
    pub fn new(checker: UnifiedPermissionChecker) -> Self {
        Self {
            checker: Arc::new(checker),
        }
    }
}

// ============================================================================
// 中间件函数和装饰器
// ============================================================================

/// 要求特定权限的中间件
///
/// 这是一个符合Axum最佳实践的中间件函数，用于检查用户是否具有指定权限
///
/// # 参数
/// - `required_permission`: 需要的权限
///
/// # 返回
/// 返回一个中间件函数，检查用户是否具有指定权限
///
/// # 使用示例
/// ```rust
/// use axum::{Router, routing::get};
/// use app_common::middleware::{require_permission, Permission};
///
/// let app = Router::new()
///     .route("/admin", get(admin_handler))
///     .layer(axum::middleware::from_fn(require_permission(Permission::Admin)));
/// ```
pub fn require_permission(
    required_permission: Permission,
) -> impl (Fn(
    Request,
    Next,
)
    -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>>)
+ Clone {
    move |request: Request, next: Next| {
        let permission = required_permission.clone();
        Box::pin(async move {
            // 从请求中提取认证用户信息
            let (parts, body) = request.into_parts();

            // 提取PermissionMiddlewareState
            let state = parts
                .extensions
                .get::<PermissionMiddlewareState>()
                .ok_or_else(|| {
                    tracing::error!("PermissionMiddlewareState未在请求扩展中找到");
                    StatusCode::INTERNAL_SERVER_ERROR
                })?
                .clone();

            // 提取AuthenticatedUser
            let user = parts
                .extensions
                .get::<AuthenticatedUser>()
                .ok_or_else(|| {
                    tracing::warn!("用户未认证，需要先通过认证中间件");
                    StatusCode::UNAUTHORIZED
                })?
                .clone();

            // 重新构建请求
            let request = Request::from_parts(parts, body);

            // 从AuthenticatedUser获取实际角色信息
            let user_role = user.get_role();

            if !state.checker.has_permission(&user_role, &permission) {
                tracing::warn!(
                    "用户 {} (角色: {}) 权限不足，需要 {} 权限",
                    user.username,
                    user_role.as_str(),
                    permission.as_str()
                );
                return Err(StatusCode::FORBIDDEN);
            }

            tracing::debug!(
                "用户 {} (角色: {}) 权限检查通过，权限: {}",
                user.username,
                user_role.as_str(),
                permission.as_str()
            );

            Ok(next.run(request).await)
        })
    }
}

/// 要求特定角色的中间件
///
/// 这是一个符合Axum最佳实践的中间件函数，用于检查用户是否具有指定角色或更高级别的角色
///
/// # 参数
/// - `required_role`: 需要的角色
///
/// # 返回
/// 返回一个中间件函数，检查用户是否具有指定角色或更高级别的角色
///
/// # 使用示例
/// ```rust
/// use axum::{Router, routing::get};
/// use app_common::middleware::{require_role, UserRole};
///
/// let app = Router::new()
///     .route("/manager", get(manager_handler))
///     .layer(axum::middleware::from_fn(require_role(UserRole::Manager)));
/// ```
pub fn require_role(
    required_role: UserRole,
) -> impl (Fn(
    Request,
    Next,
)
    -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>>)
+ Clone {
    move |request: Request, next: Next| {
        let role = required_role.clone();
        Box::pin(async move {
            // 从请求中提取认证用户信息
            let (parts, body) = request.into_parts();

            // 提取AuthenticatedUser
            let user = parts
                .extensions
                .get::<AuthenticatedUser>()
                .ok_or_else(|| {
                    tracing::warn!("用户未认证，需要先通过认证中间件");
                    StatusCode::UNAUTHORIZED
                })?
                .clone();

            // 重新构建请求
            let request = Request::from_parts(parts, body);

            // 从AuthenticatedUser获取实际角色信息
            let user_role = user.get_role();

            // 检查角色等级是否足够
            if user_role.permission_level() < role.permission_level() {
                tracing::warn!(
                    "用户 {} 角色不足，当前角色: {} (级别: {}), 需要角色: {} (级别: {})",
                    user.username,
                    user_role.as_str(),
                    user_role.permission_level(),
                    role.as_str(),
                    role.permission_level()
                );
                return Err(StatusCode::FORBIDDEN);
            }

            tracing::debug!(
                "用户 {} 角色检查通过，当前角色: {} (级别: {})",
                user.username,
                user_role.as_str(),
                user_role.permission_level()
            );

            Ok(next.run(request).await)
        })
    }
}

/// 要求管理员权限的中间件
///
/// 这是一个便捷的中间件函数，等同于 `require_role(UserRole::Admin)`
///
/// # 使用示例
/// ```rust
/// use axum::{Router, routing::get};
/// use app_common::middleware::require_admin;
///
/// let app = Router::new()
///     .route("/admin", get(admin_handler))
///     .layer(axum::middleware::from_fn(require_admin()));
/// ```
pub fn require_admin() -> impl (Fn(
    Request,
    Next,
) -> std::pin::Pin<
    Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>,
>) + Clone {
    require_role(UserRole::Admin)
}

/// 要求管理者权限的中间件
///
/// 这是一个便捷的中间件函数，等同于 `require_role(UserRole::Manager)`
pub fn require_manager() -> impl (Fn(
    Request,
    Next,
) -> std::pin::Pin<
    Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>,
>) + Clone {
    require_role(UserRole::Manager)
}

/// 要求读权限的中间件
///
/// 这是一个便捷的中间件函数，等同于 `require_permission(Permission::Read)`
pub fn require_read_permission() -> impl (Fn(
    Request,
    Next,
) -> std::pin::Pin<
    Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>,
>) + Clone {
    require_permission(Permission::Read)
}

/// 要求写权限的中间件
///
/// 这是一个便捷的中间件函数，等同于 `require_permission(Permission::Write)`
///
/// # 使用示例
/// ```rust
/// use axum::{Router, routing::post};
/// use app_common::middleware::require_write_permission;
///
/// let app = Router::new()
///     .route("/create", post(create_handler))
///     .layer(axum::middleware::from_fn(require_write_permission()));
/// ```
pub fn require_write_permission() -> impl (Fn(
    Request,
    Next,
) -> std::pin::Pin<
    Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>,
>) + Clone {
    require_permission(Permission::Write)
}

/// 要求删除权限的中间件
///
/// 这是一个便捷的中间件函数，等同于 `require_permission(Permission::Delete)`
///
/// # 使用示例
/// ```rust
/// use axum::{Router, routing::delete};
/// use app_common::middleware::require_delete_permission;
///
/// let app = Router::new()
///     .route("/delete/:id", delete(delete_handler))
///     .layer(axum::middleware::from_fn(require_delete_permission()));
/// ```
pub fn require_delete_permission() -> impl (Fn(
    Request,
    Next,
) -> std::pin::Pin<
    Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>,
>) + Clone {
    require_permission(Permission::Delete)
}

// ============================================================================
// 认证用户注入中间件
// ============================================================================

/// 认证用户注入中间件
///
/// 这个中间件将AuthenticatedUser注入到请求扩展中，使权限中间件能够正常工作
/// 必须在权限中间件之前使用
///
/// # 使用示例
/// ```rust
/// use axum::{Router, routing::get};
/// use app_common::middleware::{inject_authenticated_user, require_admin};
///
/// let app = Router::new()
///     .route("/admin", get(admin_handler))
///     .layer(axum::middleware::from_fn(require_admin()))
///     .layer(axum::middleware::from_fn(inject_authenticated_user()));
/// ```
pub fn inject_authenticated_user() -> impl (Fn(
    Request,
    Next,
) -> std::pin::Pin<
    Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>,
>) + Clone {
    |request: Request, next: Next| {
        Box::pin(async move {
            // 尝试从请求中提取AuthenticatedUser
            let (mut parts, body) = request.into_parts();

            // 使用FromRequestParts提取AuthenticatedUser
            match AuthenticatedUser::from_request_parts(&mut parts, &()).await {
                Ok(user) => {
                    // 将用户信息注入到请求扩展中
                    parts.extensions.insert(user);

                    // 重新构建请求
                    let request = Request::from_parts(parts, body);

                    // 继续处理请求
                    Ok(next.run(request).await)
                }
                Err(response) => {
                    // 认证失败，返回错误响应
                    Ok(response)
                }
            }
        })
    }
}

/// 权限中间件状态注入中间件
///
/// 这个中间件将PermissionMiddlewareState注入到请求扩展中，使权限中间件能够正常工作
/// 必须在权限中间件之前使用
///
/// # 参数
/// - `state`: 权限中间件状态
///
/// # 使用示例
/// ```rust
/// use axum::{Router, routing::get};
/// use app_common::middleware::{inject_permission_state, require_admin, create_default_permission_checker};
///
/// let permission_state = create_permission_middleware_state(create_default_permission_checker());
/// let app = Router::new()
///     .route("/admin", get(admin_handler))
///     .layer(axum::middleware::from_fn(require_admin()))
///     .layer(axum::middleware::from_fn_with_state(permission_state.clone(), inject_permission_state));
/// ```
pub async fn inject_permission_state(
    State(state): State<PermissionMiddlewareState>,
    mut request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // 将权限状态注入到请求扩展中
    request.extensions_mut().insert(state);

    // 继续处理请求
    Ok(next.run(request).await)
}

// ============================================================================
// 辅助函数
// ============================================================================

/// 创建默认的权限检查器
pub fn create_default_permission_checker() -> UnifiedPermissionChecker {
    UnifiedPermissionChecker::new()
}

/// 创建带配置的权限检查器
pub fn create_permission_checker_with_config(
    cache_enabled: bool,
    cache_ttl: u64,
    verbose_logging: bool,
) -> UnifiedPermissionChecker {
    let config = PermissionCheckerConfig {
        cache_enabled,
        cache_ttl,
        verbose_logging,
    };
    UnifiedPermissionChecker::with_config(config)
}

/// 创建权限中间件状态
pub fn create_permission_middleware_state(
    checker: UnifiedPermissionChecker,
) -> PermissionMiddlewareState {
    PermissionMiddlewareState::new(checker)
}

/// 创建企业级权限检查器（启用所有功能）
pub fn create_enterprise_permission_checker() -> UnifiedPermissionChecker {
    create_permission_checker_with_config(
        true, // 启用缓存
        600,  // 10分钟缓存
        true, // 启用详细日志
    )
}

/// 创建高性能权限检查器（禁用日志，启用缓存）
pub fn create_high_performance_permission_checker() -> UnifiedPermissionChecker {
    create_permission_checker_with_config(
        true,  // 启用缓存
        300,   // 5分钟缓存
        false, // 禁用详细日志
    )
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_permission_checker_creation() {
        let checker = create_default_permission_checker();
        assert!(checker.config.cache_enabled);
        assert_eq!(checker.config.cache_ttl, 300);
        assert!(!checker.config.verbose_logging);
    }

    #[test]
    fn test_permission_checking() {
        let checker = create_default_permission_checker();

        // 测试管理员权限
        assert!(checker.has_permission(&UserRole::Admin, &Permission::Read));
        assert!(checker.has_permission(&UserRole::Admin, &Permission::Write));
        assert!(checker.has_permission(&UserRole::Admin, &Permission::Delete));
        assert!(checker.has_permission(&UserRole::Admin, &Permission::Admin));

        // 测试普通用户权限
        assert!(checker.has_permission(&UserRole::User, &Permission::Read));
        assert!(!checker.has_permission(&UserRole::User, &Permission::Admin));

        // 测试访客权限
        assert!(checker.has_permission(&UserRole::Guest, &Permission::Read));
        assert!(!checker.has_permission(&UserRole::Guest, &Permission::Write));
    }

    #[test]
    fn test_resource_access() {
        let checker = create_default_permission_checker();

        // 管理员可以访问所有资源
        assert!(checker.can_access_resource(&UserRole::Admin, "user123", "user456"));

        // 用户只能访问自己的资源
        assert!(checker.can_access_resource(&UserRole::User, "user123", "user123"));
        assert!(!checker.can_access_resource(&UserRole::User, "user123", "user456"));
    }

    #[test]
    fn test_multiple_permissions() {
        let checker = create_default_permission_checker();
        let permissions = vec![Permission::Read, Permission::Write, Permission::Admin];

        let admin_results = checker.check_multiple_permissions(&UserRole::Admin, &permissions);
        assert_eq!(admin_results, vec![true, true, true]);

        // User角色权限级别是50，Write权限需要50，所以User有Write权限
        let user_results = checker.check_multiple_permissions(&UserRole::User, &permissions);
        assert_eq!(user_results, vec![true, true, false]); // Read=true, Write=true, Admin=false
    }

    #[test]
    fn test_user_permissions() {
        let checker = create_default_permission_checker();

        let admin_permissions = checker.get_user_permissions(&UserRole::Admin);
        assert_eq!(admin_permissions.len(), 4); // 所有权限

        let user_permissions = checker.get_user_permissions(&UserRole::User);
        assert!(user_permissions.contains(&Permission::Read));
        assert!(!user_permissions.contains(&Permission::Admin));
    }

    #[test]
    fn test_middleware_function_creation() {
        // 测试中间件函数的创建
        let _admin_middleware = require_admin();
        let _manager_middleware = require_manager();
        let _write_middleware = require_write_permission();
        let _delete_middleware = require_delete_permission();
        let _read_middleware = require_read_permission();
        let _permission_middleware = require_permission(Permission::Write);
        let _role_middleware = require_role(UserRole::Manager);

        // 测试辅助中间件
        let _auth_injector = inject_authenticated_user();
        let _permission_state =
            create_permission_middleware_state(create_default_permission_checker());

        // 如果能创建成功，说明中间件函数签名正确
        // 测试通过
    }

    #[test]
    fn test_permission_checker_configurations() {
        // 测试不同配置的权限检查器
        let default_checker = create_default_permission_checker();
        let enterprise_checker = create_enterprise_permission_checker();
        let high_perf_checker = create_high_performance_permission_checker();
        let custom_checker = create_permission_checker_with_config(true, 300, false);

        // 所有检查器都应该有相同的权限检查逻辑
        assert!(default_checker.has_permission(&UserRole::Admin, &Permission::Admin));
        assert!(enterprise_checker.has_permission(&UserRole::Admin, &Permission::Admin));
        assert!(high_perf_checker.has_permission(&UserRole::Admin, &Permission::Admin));
        assert!(custom_checker.has_permission(&UserRole::Admin, &Permission::Admin));

        assert!(!default_checker.has_permission(&UserRole::User, &Permission::Admin));
        assert!(!enterprise_checker.has_permission(&UserRole::User, &Permission::Admin));
        assert!(!high_perf_checker.has_permission(&UserRole::User, &Permission::Admin));
        assert!(!custom_checker.has_permission(&UserRole::User, &Permission::Admin));
    }
}
