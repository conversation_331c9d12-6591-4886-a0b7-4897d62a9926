// 测试服务器管理模块
//
// 本模块负责在E2E测试中启动和管理Axum测试服务器
// 遵循Context7 MCP最佳实践和rust_axum_Rules.md规范：
// - 使用清晰的函数命名（start_test_server、stop_test_server等）
// - 详细的中文注释
// - 完整的错误处理
// - 遵循DRY和SOLID原则

use anyhow::{Context, Result};
use reqwest::Client;
use serde_json::Value;
use std::process::{Child, Command, Stdio};
use std::sync::Arc;
use std::time::Duration;
use tokio::time::sleep;

/// 测试服务器配置
#[derive(Debug, Clone)]
pub struct TestServerConfig {
    /// 服务器监听地址
    pub host: String,
    /// 服务器监听端口
    pub port: u16,
    /// 服务器启动超时时间（秒）
    pub startup_timeout: u64,
    /// 健康检查间隔（秒）
    pub health_check_interval: u64,
    /// 项目根目录
    pub project_root: String,
}

impl Default for TestServerConfig {
    fn default() -> Self {
        Self {
            host: "127.0.0.1".to_string(),
            port: 3000,
            startup_timeout: 60,
            health_check_interval: 1,
            project_root: std::env::current_dir()
                .unwrap_or_default()
                .to_string_lossy()
                .to_string(),
        }
    }
}

impl TestServerConfig {
    /// 获取服务器基础URL
    pub fn base_url(&self) -> String {
        format!("http://{}:{}", self.host, self.port)
    }

    /// 获取健康检查URL
    pub fn health_url(&self) -> String {
        format!("{}/api/health", self.base_url())
    }
}

/// 测试服务器管理器
pub struct TestServer {
    config: TestServerConfig,
    process: Option<Child>,
    client: Client,
}

impl TestServer {
    /// 创建新的测试服务器实例
    pub fn new(config: TestServerConfig) -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .expect("Failed to create HTTP client");

        Self {
            config,
            process: None,
            client,
        }
    }

    /// 使用默认配置创建测试服务器
    pub fn with_default_config() -> Self {
        Self::new(TestServerConfig::default())
    }

    /// 启动测试服务器
    pub async fn start(&mut self) -> Result<()> {
        println!("🚀 启动测试服务器...");

        // 检查端口是否已被占用
        if self.is_server_running().await {
            println!("✅ 服务器已在运行: {}", self.config.base_url());
            return Ok(());
        }

        // 启动服务器进程
        self.spawn_server_process()?;

        // 等待服务器启动
        self.wait_for_server_ready().await?;

        println!("✅ 测试服务器启动成功: {}", self.config.base_url());
        Ok(())
    }

    /// 停止测试服务器
    pub fn stop(&mut self) -> Result<()> {
        if let Some(mut process) = self.process.take() {
            println!("🛑 停止测试服务器...");

            // 尝试优雅关闭
            if let Err(e) = process.kill() {
                eprintln!("警告: 无法终止服务器进程: {}", e);
            }

            // 等待进程结束
            if let Err(e) = process.wait() {
                eprintln!("警告: 等待进程结束时出错: {}", e);
            }

            println!("✅ 测试服务器已停止");
        }

        Ok(())
    }

    /// 检查服务器是否正在运行
    pub async fn is_server_running(&self) -> bool {
        match self.health_check().await {
            Ok(_) => true,
            Err(_) => false,
        }
    }

    /// 执行健康检查
    pub async fn health_check(&self) -> Result<Value> {
        let response = self
            .client
            .get(&self.config.health_url())
            .timeout(Duration::from_secs(5))
            .send()
            .await
            .context("健康检查请求失败")?;

        if response.status().is_success() {
            let body = response.text().await.context("读取健康检查响应失败")?;

            // 尝试解析为JSON，如果失败则返回简单的成功响应
            match serde_json::from_str::<Value>(&body) {
                Ok(json) => Ok(json),
                Err(_) => Ok(serde_json::json!({
                    "status": "ok",
                    "message": body
                })),
            }
        } else {
            Err(anyhow::anyhow!(
                "健康检查失败，状态码: {}",
                response.status()
            ))
        }
    }

    /// 获取服务器配置
    pub fn config(&self) -> &TestServerConfig {
        &self.config
    }

    /// 获取HTTP客户端
    pub fn client(&self) -> &Client {
        &self.client
    }

    /// 生成服务器进程
    fn spawn_server_process(&mut self) -> Result<()> {
        println!("📦 启动Axum服务器进程...");

        let mut cmd = Command::new("cargo");
        cmd.args(&["run", "-p", "server"])
            .current_dir(&self.config.project_root)
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .env("RUST_LOG", "info")
            .env("SERVER_HOST", &self.config.host)
            .env("SERVER_PORT", self.config.port.to_string());

        let child = cmd.spawn().context("无法启动服务器进程")?;

        self.process = Some(child);

        // 给进程一些时间来初始化
        std::thread::sleep(Duration::from_secs(2));

        Ok(())
    }

    /// 等待服务器准备就绪
    async fn wait_for_server_ready(&self) -> Result<()> {
        let max_attempts = self.config.startup_timeout / self.config.health_check_interval;
        let mut attempts = 0;

        println!("⏳ 等待服务器启动...");

        while attempts < max_attempts {
            match self.health_check().await {
                Ok(_) => {
                    println!("✅ 服务器健康检查通过");
                    return Ok(());
                }
                Err(_) => {
                    attempts += 1;
                    if attempts % 5 == 0 {
                        println!("⏳ 等待服务器启动... ({}/{})", attempts, max_attempts);
                    }
                    sleep(Duration::from_secs(self.config.health_check_interval)).await;
                }
            }
        }

        Err(anyhow::anyhow!(
            "服务器启动超时: {} 秒内未响应健康检查",
            self.config.startup_timeout
        ))
    }
}

impl Drop for TestServer {
    fn drop(&mut self) {
        if let Err(e) = self.stop() {
            eprintln!("警告: 清理测试服务器时出错: {}", e);
        }
    }
}

/// 全局测试服务器管理器
use std::sync::OnceLock;
static GLOBAL_TEST_SERVER: OnceLock<Arc<tokio::sync::Mutex<TestServer>>> = OnceLock::new();

/// 获取全局测试服务器实例
pub async fn get_global_test_server() -> Arc<tokio::sync::Mutex<TestServer>> {
    GLOBAL_TEST_SERVER
        .get_or_init(|| {
            let server = TestServer::with_default_config();
            Arc::new(tokio::sync::Mutex::new(server))
        })
        .clone()
}

/// 启动全局测试服务器
pub async fn start_global_test_server() -> Result<()> {
    let server = get_global_test_server().await;
    let mut server_guard = server.lock().await;
    server_guard.start().await
}

/// 停止全局测试服务器
pub async fn stop_global_test_server() -> Result<()> {
    let server = get_global_test_server().await;
    let mut server_guard = server.lock().await;
    server_guard.stop()
}

/// 检查全局测试服务器是否运行
pub async fn is_global_test_server_running() -> bool {
    let server = get_global_test_server().await;
    let server_guard = server.lock().await;
    server_guard.is_server_running().await
}
