/**
 * 测试数据夹具 - 用于UI测试
 * 遵循Context7 MCP最佳实践，提供一致的测试数据
 */

export interface TestUser {
  username: string;
  email: string;
  password: string;
}

export interface TestTask {
  title: string;
  description: string;
}

export interface TestMessage {
  content: string;
  type: 'text' | 'system' | 'user-joined' | 'user-left';
}

/**
 * 测试用户数据
 */
export const testUsers: Record<string, TestUser> = {
  validUser: {
    username: 'testuser456',
    email: '<EMAIL>',
    password: 'password123'
  },
  
  newUser: {
    username: 'newuser789',
    email: '<EMAIL>',
    password: 'newpassword456'
  },
  
  invalidUser: {
    username: 'invalid',
    email: '<EMAIL>',
    password: 'wrongpassword'
  },
  
  emptyUser: {
    username: '',
    email: '',
    password: ''
  },
  
  shortPassword: {
    username: 'shortpass',
    email: '<EMAIL>',
    password: '123'
  },
  
  invalidEmail: {
    username: 'invalidemail',
    email: 'not-an-email',
    password: 'password123'
  }
};

/**
 * 测试任务数据
 */
export const testTasks: Record<string, TestTask> = {
  simpleTask: {
    title: '简单任务',
    description: '这是一个简单的测试任务'
  },
  
  complexTask: {
    title: '复杂任务',
    description: '这是一个包含详细描述的复杂任务，用于测试长文本处理能力和界面响应性。'
  },
  
  urgentTask: {
    title: '紧急任务',
    description: '需要立即处理的紧急任务'
  },
  
  emptyTask: {
    title: '',
    description: ''
  },
  
  longTitleTask: {
    title: '这是一个非常长的任务标题，用于测试界面在处理长标题时的表现和响应性',
    description: '长标题任务的描述'
  },
  
  specialCharsTask: {
    title: '特殊字符任务 !@#$%^&*()',
    description: '包含特殊字符的任务描述：<script>alert("test")</script>'
  },
  
  unicodeTask: {
    title: '多语言任务 🚀 📝 ✅',
    description: '支持Unicode字符的任务：中文、English、日本語、한국어、العربية'
  }
};

/**
 * 测试消息数据
 */
export const testMessages: Record<string, TestMessage> = {
  simpleMessage: {
    content: '这是一条简单的测试消息',
    type: 'text'
  },
  
  longMessage: {
    content: '这是一条很长的测试消息，用于验证聊天界面在处理长消息时的表现。消息内容包含多个句子，用于测试文本换行和显示效果。',
    type: 'text'
  },
  
  specialCharsMessage: {
    content: '特殊字符消息 !@#$%^&*()_+-=[]{}|;:,.<>?',
    type: 'text'
  },
  
  unicodeMessage: {
    content: '多语言消息 🎉 Hello 你好 こんにちは 안녕하세요',
    type: 'text'
  },
  
  emptyMessage: {
    content: '',
    type: 'text'
  },
  
  systemMessage: {
    content: '系统消息：用户已连接',
    type: 'system'
  }
};

/**
 * 测试配置数据
 */
export const testConfig = {
  // 超时配置
  timeouts: {
    short: 1000,
    medium: 5000,
    long: 10000,
    veryLong: 30000
  },
  
  // 重试配置
  retries: {
    default: 3,
    network: 5,
    auth: 2
  },
  
  // 视口配置
  viewports: {
    mobile: { width: 375, height: 667 },
    tablet: { width: 768, height: 1024 },
    desktop: { width: 1280, height: 720 },
    largeDesktop: { width: 1920, height: 1080 }
  },
  
  // URL配置
  urls: {
    base: 'http://127.0.0.1:3000',
    api: 'http://127.0.0.1:3000/api',
    ws: 'ws://127.0.0.1:3000/ws'
  },
  
  // 测试标签
  tags: {
    smoke: '@smoke',
    regression: '@regression',
    auth: '@auth',
    tasks: '@tasks',
    chat: '@chat',
    responsive: '@responsive',
    performance: '@performance'
  }
};

/**
 * 错误消息常量
 */
export const errorMessages = {
  auth: {
    invalidCredentials: '用户名或密码错误',
    userNotFound: '用户不存在',
    userExists: '用户已存在',
    invalidEmail: '邮箱格式不正确',
    passwordTooShort: '密码长度不能少于6位',
    fieldRequired: '此字段为必填项'
  },
  
  tasks: {
    titleRequired: '任务标题不能为空',
    createFailed: '创建任务失败',
    deleteFailed: '删除任务失败',
    updateFailed: '更新任务失败',
    notFound: '任务不存在'
  },
  
  chat: {
    connectionFailed: '连接失败',
    sendFailed: '发送消息失败',
    messageEmpty: '消息不能为空'
  },
  
  network: {
    timeout: '请求超时',
    serverError: '服务器错误',
    networkError: '网络错误'
  }
};

/**
 * 成功消息常量
 */
export const successMessages = {
  auth: {
    loginSuccess: '登录成功',
    registerSuccess: '注册成功',
    logoutSuccess: '登出成功'
  },
  
  tasks: {
    createSuccess: '任务创建成功',
    deleteSuccess: '任务删除成功',
    updateSuccess: '任务更新成功'
  },
  
  chat: {
    connected: '连接成功',
    disconnected: '已断开连接',
    messageSent: '消息发送成功'
  }
};

/**
 * 生成随机测试数据的工具函数
 */
export const generateTestData = {
  /**
   * 生成随机用户名
   */
  randomUsername: (): string => {
    return `testuser_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
  },
  
  /**
   * 生成随机邮箱
   */
  randomEmail: (): string => {
    const username = generateTestData.randomUsername();
    return `${username}@example.com`;
  },
  
  /**
   * 生成随机任务标题
   */
  randomTaskTitle: (): string => {
    const adjectives = ['重要', '紧急', '简单', '复杂', '日常'];
    const nouns = ['任务', '工作', '项目', '计划', '目标'];
    const adj = adjectives[Math.floor(Math.random() * adjectives.length)];
    const noun = nouns[Math.floor(Math.random() * nouns.length)];
    return `${adj}${noun}_${Date.now()}`;
  },
  
  /**
   * 生成随机任务描述
   */
  randomTaskDescription: (): string => {
    const descriptions = [
      '这是一个测试任务的描述',
      '需要完成的重要工作项',
      '项目相关的具体任务内容',
      '日常工作中的常规任务',
      '需要特别关注的任务项目'
    ];
    return descriptions[Math.floor(Math.random() * descriptions.length)];
  },
  
  /**
   * 生成随机消息内容
   */
  randomMessage: (): string => {
    const messages = [
      '这是一条测试消息',
      '大家好，我是新用户',
      '今天天气不错',
      '项目进展如何？',
      '感谢大家的帮助'
    ];
    return messages[Math.floor(Math.random() * messages.length)];
  }
};
