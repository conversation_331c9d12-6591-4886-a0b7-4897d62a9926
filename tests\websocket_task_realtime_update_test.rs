//! # WebSocket任务实时更新测试
//!
//! 测试WebSocket实时任务更新功能的完整性和正确性

use axum::extract::ws::Message;
use serde_json::{Value, json};
use std::time::Duration;
use tokio::time::timeout;
use uuid::Uuid;

use crate::test_utils::{TestWebSocketClient, create_test_app, create_test_user, login_test_user};

/// 测试任务创建的实时广播
#[tokio::test]
async fn test_task_create_realtime_broadcast() {
    let app = create_test_app().await;

    // 创建测试用户并登录
    let user = create_test_user("testuser_task_create", "password123").await;
    let token = login_test_user(&user.username, "password123").await;

    // 建立两个WebSocket连接模拟多用户
    let mut client1 = TestWebSocketClient::connect_with_auth(&token).await;
    let mut client2 = TestWebSocketClient::connect_with_auth(&token).await;

    // 等待连接稳定
    tokio::time::sleep(Duration::from_millis(100)).await;

    // 通过HTTP API创建任务
    let task_data = json!({
        "title": "测试任务实时创建",
        "description": "这是一个测试任务",
        "priority": "high"
    });

    let response = app
        .post("/api/tasks")
        .header("Authorization", format!("Bearer {}", token))
        .json(&task_data)
        .send()
        .await;

    assert_eq!(response.status(), 201);
    let created_task: Value = response.json().await;

    // 验证两个客户端都收到任务创建广播
    let msg1 = timeout(Duration::from_secs(2), client1.receive_message())
        .await
        .expect("客户端1应该收到任务创建消息")
        .expect("消息解析应该成功");

    let msg2 = timeout(Duration::from_secs(2), client2.receive_message())
        .await
        .expect("客户端2应该收到任务创建消息")
        .expect("消息解析应该成功");

    // 验证消息内容
    verify_task_broadcast_message(&msg1, "task_created", &created_task);
    verify_task_broadcast_message(&msg2, "task_created", &created_task);
}

/// 测试任务更新的实时广播
#[tokio::test]
async fn test_task_update_realtime_broadcast() {
    let app = create_test_app().await;

    // 创建测试用户并登录
    let user = create_test_user("testuser_task_update", "password123").await;
    let token = login_test_user(&user.username, "password123").await;

    // 先创建一个任务
    let task_data = json!({
        "title": "待更新的任务",
        "description": "原始描述",
        "priority": "medium"
    });

    let create_response = app
        .post("/api/tasks")
        .header("Authorization", format!("Bearer {}", token))
        .json(&task_data)
        .send()
        .await;

    let created_task: Value = create_response.json().await;
    let task_id = created_task["id"].as_u64().unwrap();

    // 建立WebSocket连接
    let mut client = TestWebSocketClient::connect_with_auth(&token).await;

    // 清空创建任务时的广播消息
    let _ = timeout(Duration::from_millis(500), client.receive_message()).await;

    // 更新任务
    let update_data = json!({
        "title": "已更新的任务",
        "description": "更新后的描述",
        "status": "in_progress"
    });

    let update_response = app
        .put(&format!("/api/tasks/{}", task_id))
        .header("Authorization", format!("Bearer {}", token))
        .json(&update_data)
        .send()
        .await;

    assert_eq!(update_response.status(), 200);
    let updated_task: Value = update_response.json().await;

    // 验证收到任务更新广播
    let msg = timeout(Duration::from_secs(2), client.receive_message())
        .await
        .expect("应该收到任务更新消息")
        .expect("消息解析应该成功");

    verify_task_broadcast_message(&msg, "task_updated", &updated_task);
}

/// 测试任务删除的实时广播
#[tokio::test]
async fn test_task_delete_realtime_broadcast() {
    let app = create_test_app().await;

    // 创建测试用户并登录
    let user = create_test_user("testuser_task_delete", "password123").await;
    let token = login_test_user(&user.username, "password123").await;

    // 先创建一个任务
    let task_data = json!({
        "title": "待删除的任务",
        "description": "将被删除的任务",
        "priority": "low"
    });

    let create_response = app
        .post("/api/tasks")
        .header("Authorization", format!("Bearer {}", token))
        .json(&task_data)
        .send()
        .await;

    let created_task: Value = create_response.json().await;
    let task_id = created_task["id"].as_u64().unwrap();

    // 建立WebSocket连接
    let mut client = TestWebSocketClient::connect_with_auth(&token).await;

    // 清空创建任务时的广播消息
    let _ = timeout(Duration::from_millis(500), client.receive_message()).await;

    // 删除任务
    let delete_response = app
        .delete(&format!("/api/tasks/{}", task_id))
        .header("Authorization", format!("Bearer {}", token))
        .send()
        .await;

    assert_eq!(delete_response.status(), 204);

    // 验证收到任务删除广播
    let msg = timeout(Duration::from_secs(2), client.receive_message())
        .await
        .expect("应该收到任务删除消息")
        .expect("消息解析应该成功");

    let parsed_msg: Value = serde_json::from_str(&msg).expect("消息应该是有效的JSON");
    assert_eq!(parsed_msg["type"], "task_deleted");
    assert_eq!(parsed_msg["data"]["id"], task_id);
}

/// 测试任务状态切换的实时广播
#[tokio::test]
async fn test_task_status_toggle_realtime_broadcast() {
    let app = create_test_app().await;

    // 创建测试用户并登录
    let user = create_test_user("testuser_task_toggle", "password123").await;
    let token = login_test_user(&user.username, "password123").await;

    // 先创建一个任务
    let task_data = json!({
        "title": "状态切换测试任务",
        "description": "用于测试状态切换",
        "priority": "medium"
    });

    let create_response = app
        .post("/api/tasks")
        .header("Authorization", format!("Bearer {}", token))
        .json(&task_data)
        .send()
        .await;

    let created_task: Value = create_response.json().await;
    let task_id = created_task["id"].as_u64().unwrap();

    // 建立WebSocket连接
    let mut client = TestWebSocketClient::connect_with_auth(&token).await;

    // 清空创建任务时的广播消息
    let _ = timeout(Duration::from_millis(500), client.receive_message()).await;

    // 切换任务状态为完成
    let toggle_response = app
        .patch(&format!("/api/tasks/{}/toggle", task_id))
        .header("Authorization", format!("Bearer {}", token))
        .send()
        .await;

    assert_eq!(toggle_response.status(), 200);
    let toggled_task: Value = toggle_response.json().await;

    // 验证收到任务状态更新广播
    let msg = timeout(Duration::from_secs(2), client.receive_message())
        .await
        .expect("应该收到任务状态更新消息")
        .expect("消息解析应该成功");

    verify_task_broadcast_message(&msg, "task_updated", &toggled_task);

    // 验证状态确实改变了
    let parsed_msg: Value = serde_json::from_str(&msg).expect("消息应该是有效的JSON");
    assert_eq!(parsed_msg["data"]["status"], "completed");
}

/// 测试多用户任务实时同步
#[tokio::test]
async fn test_multi_user_task_sync() {
    let app = create_test_app().await;

    // 创建两个测试用户
    let user1 = create_test_user("user1_sync", "password123").await;
    let user2 = create_test_user("user2_sync", "password123").await;

    let token1 = login_test_user(&user1.username, "password123").await;
    let token2 = login_test_user(&user2.username, "password123").await;

    // 建立两个WebSocket连接
    let mut client1 = TestWebSocketClient::connect_with_auth(&token1).await;
    let mut client2 = TestWebSocketClient::connect_with_auth(&token2).await;

    // 等待连接稳定
    tokio::time::sleep(Duration::from_millis(100)).await;

    // 用户1创建任务
    let task_data = json!({
        "title": "多用户同步测试任务",
        "description": "测试多用户实时同步",
        "priority": "high"
    });

    let response = app
        .post("/api/tasks")
        .header("Authorization", format!("Bearer {}", token1))
        .json(&task_data)
        .send()
        .await;

    assert_eq!(response.status(), 201);
    let created_task: Value = response.json().await;

    // 验证两个用户都收到任务创建广播
    let msg1 = timeout(Duration::from_secs(2), client1.receive_message())
        .await
        .expect("用户1应该收到任务创建消息");

    let msg2 = timeout(Duration::from_secs(2), client2.receive_message())
        .await
        .expect("用户2应该收到任务创建消息");

    // 验证消息内容一致
    assert_eq!(msg1, msg2);
    verify_task_broadcast_message(&msg1.unwrap(), "task_created", &created_task);
}

/// 验证任务广播消息的格式和内容
fn verify_task_broadcast_message(message: &str, expected_type: &str, expected_task: &Value) {
    let parsed_msg: Value = serde_json::from_str(message).expect("消息应该是有效的JSON");

    assert_eq!(parsed_msg["type"], expected_type);
    assert_eq!(parsed_msg["data"]["id"], expected_task["id"]);
    assert_eq!(parsed_msg["data"]["title"], expected_task["title"]);
    assert_eq!(
        parsed_msg["data"]["description"],
        expected_task["description"]
    );
    assert_eq!(parsed_msg["data"]["priority"], expected_task["priority"]);

    // 验证时间戳存在
    assert!(parsed_msg["timestamp"].is_string());
}
