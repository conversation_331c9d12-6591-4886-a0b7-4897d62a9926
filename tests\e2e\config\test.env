# E2E测试环境配置

# 服务器配置
SERVER_HOST=127.0.0.1
SERVER_PORT=3000
BASE_URL=http://127.0.0.1:3000
HTTP_ADDR=127.0.0.1:3000

# WebSocket配置
WS_URL=ws://127.0.0.1:3000/ws

# 测试用户配置
TEST_USERNAME=testuser456
TEST_PASSWORD=password123
TEST_EMAIL=<EMAIL>

# 数据库配置
DATABASE_URL=sqlite:./task_manager.db
TEST_DATABASE_URL=sqlite:./test_task_manager.db

# JWT配置
JWT_SECRET=test_jwt_secret_key_for_e2e_testing_only

# 测试配置
TEST_TIMEOUT=30000
RETRY_ATTEMPTS=3
PARALLEL_TESTS=1
TEST_CONCURRENCY=1

# Playwright配置
PLAYWRIGHT_HEADLESS=false
PLAYWRIGHT_SLOW_MO=100
PLAYWRIGHT_VIEWPORT_WIDTH=1280
PLAYWRIGHT_VIEWPORT_HEIGHT=720
PLAYWRIGHT_TIMEOUT=30000

# 报告配置
REPORT_DIR=./tests/e2e/reports
SCREENSHOT_DIR=./tests/e2e/reports/screenshots
VIDEO_DIR=./tests/e2e/reports/videos

# 日志配置
LOG_LEVEL=info
TEST_LOG_FILE=./tests/e2e/reports/test.log
RUST_LOG=info

# 性能配置
MAX_RESPONSE_TIME=1000
MAX_CONCURRENT_CONNECTIONS=100

# 安全配置
ENABLE_CORS=true
CORS_ORIGINS=http://127.0.0.1:3000,http://localhost:3000

# 开发模式配置
DEV_MODE=true
DEBUG_MODE=false

# 任务管理CRUD测试配置
CRUD_TEST_TIMEOUT=45
CRUD_TEST_RETRY_COUNT=3
CRUD_TEST_BATCH_SIZE=10

# API端点配置
API_AUTH_REGISTER=/api/auth/register
API_AUTH_LOGIN=/api/auth/login
API_AUTH_LOGOUT=/api/auth/logout
API_AUTH_VERIFY=/api/auth/verify
API_TASKS_BASE=/api/tasks
API_TASKS_GET=/api/tasks/{id}
API_TASKS_UPDATE=/api/tasks/{id}
API_TASKS_DELETE=/api/tasks/{id}

# 测试数据配置
TEST_TASK_TITLE_PREFIX=E2E测试任务
TEST_TASK_DESCRIPTION_PREFIX=这是一个E2E测试任务描述
TEST_TASK_COUNT=5

# 并发测试配置
CONCURRENT_USERS=3
CONCURRENT_REQUESTS_PER_USER=5
LOAD_TEST_DURATION=30

# 认证流程测试配置
AUTH_TOKEN_EXPIRY_TEST=true
AUTH_REFRESH_TOKEN_TEST=true
AUTH_INVALID_TOKEN_TEST=true
AUTH_MALFORMED_TOKEN_TEST=true

# 数据持久化测试配置
DB_TRANSACTION_TEST=true
DB_ROLLBACK_TEST=true
DB_CONSTRAINT_TEST=true
