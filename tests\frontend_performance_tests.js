/**
 * 前端性能优化测试套件
 * 基于Web Vitals和Lighthouse最佳实践
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-28
 */

import { test, expect } from '@playwright/test';

// 性能指标阈值配置（基于2025年最新Web Vitals标准）
const PERFORMANCE_THRESHOLDS = {
    // Core Web Vitals
    LCP: 2500,      // Largest Contentful Paint - 最大内容绘制
    FID: 100,       // First Input Delay - 首次输入延迟
    CLS: 0.1,       // Cumulative Layout Shift - 累积布局偏移
    
    // 其他重要指标
    FCP: 1800,      // First Contentful Paint - 首次内容绘制
    TTI: 3800,      // Time to Interactive - 可交互时间
    TBT: 300,       // Total Blocking Time - 总阻塞时间
    
    // 资源加载指标
    RESOURCE_COUNT: 50,     // 最大资源数量
    TOTAL_SIZE: 2048,       // 总资源大小(KB)
    JS_SIZE: 512,           // JavaScript大小(KB)
    CSS_SIZE: 128,          // CSS大小(KB)
    IMAGE_SIZE: 1024,       // 图片大小(KB)
    
    // 缓存指标
    CACHE_HIT_RATE: 0.8,    // 缓存命中率
    
    // 网络指标
    NETWORK_REQUESTS: 30,   // 最大网络请求数
};

// 测试配置
const TEST_CONFIG = {
    baseURL: 'http://127.0.0.1:3000',
    timeout: 30000,
    retries: 2,
};

test.describe('前端性能优化测试套件', () => {
    
    test.beforeEach(async ({ page }) => {
        // 启用性能监控
        await page.addInitScript(() => {
            // 注入Web Vitals监控脚本
            window.performanceMetrics = {
                lcp: null,
                fid: null,
                cls: null,
                fcp: null,
                tti: null,
                tbt: null
            };
            
            // 监听性能指标
            new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.entryType === 'largest-contentful-paint') {
                        window.performanceMetrics.lcp = entry.startTime;
                    }
                    if (entry.entryType === 'first-input') {
                        window.performanceMetrics.fid = entry.processingStart - entry.startTime;
                    }
                    if (entry.entryType === 'layout-shift' && !entry.hadRecentInput) {
                        window.performanceMetrics.cls = (window.performanceMetrics.cls || 0) + entry.value;
                    }
                }
            }).observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
            
            // 监听导航时间
            window.addEventListener('load', () => {
                const navigation = performance.getEntriesByType('navigation')[0];
                window.performanceMetrics.fcp = navigation.responseEnd - navigation.fetchStart;
                
                // 计算TTI和TBT（简化版本）
                setTimeout(() => {
                    const paintEntries = performance.getEntriesByType('paint');
                    const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint');
                    if (fcpEntry) {
                        window.performanceMetrics.fcp = fcpEntry.startTime;
                    }
                }, 1000);
            });
        });
    });

    test('Core Web Vitals性能指标测试', async ({ page }) => {
        console.log('🚀 开始Core Web Vitals性能测试...');
        
        // 导航到主页
        const startTime = Date.now();
        await page.goto(TEST_CONFIG.baseURL);
        
        // 等待页面完全加载
        await page.waitForLoadState('networkidle');
        const loadTime = Date.now() - startTime;
        
        console.log(`📊 页面加载时间: ${loadTime}ms`);
        
        // 获取性能指标
        const metrics = await page.evaluate(() => window.performanceMetrics);
        
        // 验证LCP (Largest Contentful Paint)
        if (metrics.lcp !== null) {
            console.log(`🎯 LCP: ${metrics.lcp}ms (阈值: ${PERFORMANCE_THRESHOLDS.LCP}ms)`);
            expect(metrics.lcp).toBeLessThan(PERFORMANCE_THRESHOLDS.LCP);
        }
        
        // 验证FCP (First Contentful Paint)
        if (metrics.fcp !== null) {
            console.log(`🎯 FCP: ${metrics.fcp}ms (阈值: ${PERFORMANCE_THRESHOLDS.FCP}ms)`);
            expect(metrics.fcp).toBeLessThan(PERFORMANCE_THRESHOLDS.FCP);
        }
        
        // 验证CLS (Cumulative Layout Shift)
        if (metrics.cls !== null) {
            console.log(`🎯 CLS: ${metrics.cls} (阈值: ${PERFORMANCE_THRESHOLDS.CLS})`);
            expect(metrics.cls).toBeLessThan(PERFORMANCE_THRESHOLDS.CLS);
        }
        
        // 验证页面加载时间
        expect(loadTime).toBeLessThan(5000); // 5秒内完成加载
    });

    test('资源加载优化测试', async ({ page }) => {
        console.log('📦 开始资源加载优化测试...');
        
        // 监听网络请求
        const requests = [];
        const responses = [];
        
        page.on('request', request => {
            requests.push({
                url: request.url(),
                method: request.method(),
                resourceType: request.resourceType(),
                size: 0
            });
        });
        
        page.on('response', response => {
            responses.push({
                url: response.url(),
                status: response.status(),
                headers: response.headers(),
                size: parseInt(response.headers()['content-length'] || '0')
            });
        });
        
        await page.goto(TEST_CONFIG.baseURL);
        await page.waitForLoadState('networkidle');
        
        // 分析资源类型和大小
        const resourceStats = {
            total: 0,
            js: 0,
            css: 0,
            images: 0,
            fonts: 0,
            other: 0
        };
        
        responses.forEach(response => {
            const size = response.size;
            resourceStats.total += size;
            
            const url = response.url.toLowerCase();
            if (url.includes('.js')) {
                resourceStats.js += size;
            } else if (url.includes('.css')) {
                resourceStats.css += size;
            } else if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/)) {
                resourceStats.images += size;
            } else if (url.match(/\.(woff|woff2|ttf|eot)$/)) {
                resourceStats.fonts += size;
            } else {
                resourceStats.other += size;
            }
        });
        
        // 转换为KB
        Object.keys(resourceStats).forEach(key => {
            resourceStats[key] = Math.round(resourceStats[key] / 1024);
        });
        
        console.log('📊 资源统计:');
        console.log(`   总大小: ${resourceStats.total}KB (阈值: ${PERFORMANCE_THRESHOLDS.TOTAL_SIZE}KB)`);
        console.log(`   JavaScript: ${resourceStats.js}KB (阈值: ${PERFORMANCE_THRESHOLDS.JS_SIZE}KB)`);
        console.log(`   CSS: ${resourceStats.css}KB (阈值: ${PERFORMANCE_THRESHOLDS.CSS_SIZE}KB)`);
        console.log(`   图片: ${resourceStats.images}KB (阈值: ${PERFORMANCE_THRESHOLDS.IMAGE_SIZE}KB)`);
        console.log(`   字体: ${resourceStats.fonts}KB`);
        console.log(`   其他: ${resourceStats.other}KB`);
        console.log(`   请求数量: ${requests.length} (阈值: ${PERFORMANCE_THRESHOLDS.NETWORK_REQUESTS})`);
        
        // 验证资源大小
        expect(resourceStats.total).toBeLessThan(PERFORMANCE_THRESHOLDS.TOTAL_SIZE);
        expect(resourceStats.js).toBeLessThan(PERFORMANCE_THRESHOLDS.JS_SIZE);
        expect(resourceStats.css).toBeLessThan(PERFORMANCE_THRESHOLDS.CSS_SIZE);
        expect(resourceStats.images).toBeLessThan(PERFORMANCE_THRESHOLDS.IMAGE_SIZE);
        expect(requests.length).toBeLessThan(PERFORMANCE_THRESHOLDS.NETWORK_REQUESTS);
    });

    test('缓存策略测试', async ({ page }) => {
        console.log('💾 开始缓存策略测试...');
        
        // 第一次访问
        await page.goto(TEST_CONFIG.baseURL);
        await page.waitForLoadState('networkidle');
        
        // 记录第一次加载的资源
        const firstLoadRequests = [];
        
        // 第二次访问（测试缓存）
        page.on('request', request => {
            firstLoadRequests.push(request.url());
        });
        
        await page.reload();
        await page.waitForLoadState('networkidle');
        
        // 检查缓存头
        const cachedResources = [];
        const responses = await page.evaluate(() => {
            return performance.getEntriesByType('resource').map(entry => ({
                name: entry.name,
                transferSize: entry.transferSize,
                encodedBodySize: entry.encodedBodySize
            }));
        });
        
        responses.forEach(resource => {
            // 如果transferSize明显小于encodedBodySize，说明使用了缓存
            if (resource.transferSize < resource.encodedBodySize * 0.1) {
                cachedResources.push(resource.name);
            }
        });
        
        const cacheHitRate = cachedResources.length / responses.length;
        console.log(`💾 缓存命中率: ${(cacheHitRate * 100).toFixed(1)}% (阈值: ${(PERFORMANCE_THRESHOLDS.CACHE_HIT_RATE * 100)}%)`);
        console.log(`💾 缓存资源数: ${cachedResources.length}/${responses.length}`);
        
        // 验证缓存命中率
        expect(cacheHitRate).toBeGreaterThan(PERFORMANCE_THRESHOLDS.CACHE_HIT_RATE);
    });

    test('代码分割和懒加载测试', async ({ page }) => {
        console.log('🔄 开始代码分割和懒加载测试...');
        
        // 监听动态导入
        const dynamicImports = [];
        
        await page.addInitScript(() => {
            const originalImport = window.import;
            window.import = function(...args) {
                window.dynamicImports = window.dynamicImports || [];
                window.dynamicImports.push(args[0]);
                return originalImport.apply(this, args);
            };
        });
        
        await page.goto(TEST_CONFIG.baseURL);
        await page.waitForLoadState('networkidle');
        
        // 触发可能的懒加载
        await page.click('button:has-text("登录")');
        await page.waitForTimeout(1000);
        
        // 检查是否有动态导入
        const imports = await page.evaluate(() => window.dynamicImports || []);
        
        console.log(`🔄 动态导入数量: ${imports.length}`);
        if (imports.length > 0) {
            console.log('🔄 动态导入模块:', imports);
        }
        
        // 验证初始包大小（应该相对较小，因为使用了代码分割）
        const initialJSSize = await page.evaluate(() => {
            const scripts = Array.from(document.querySelectorAll('script[src]'));
            return scripts.length;
        });
        
        console.log(`📦 初始JavaScript文件数量: ${initialJSSize}`);
        
        // 验证代码分割效果
        expect(initialJSSize).toBeLessThan(10); // 初始加载的JS文件不应超过10个
    });

    test('图片优化和懒加载测试', async ({ page }) => {
        console.log('🖼️ 开始图片优化测试...');
        
        await page.goto(TEST_CONFIG.baseURL);
        await page.waitForLoadState('networkidle');
        
        // 检查图片懒加载
        const images = await page.$$eval('img', imgs => 
            imgs.map(img => ({
                src: img.src,
                loading: img.loading,
                width: img.naturalWidth,
                height: img.naturalHeight,
                hasLazyLoading: img.hasAttribute('loading') && img.getAttribute('loading') === 'lazy'
            }))
        );
        
        console.log(`🖼️ 图片总数: ${images.length}`);
        
        const lazyImages = images.filter(img => img.hasLazyLoading);
        console.log(`🔄 懒加载图片数: ${lazyImages.length}`);
        
        // 检查现代图片格式
        const modernFormats = images.filter(img => 
            img.src.includes('.webp') || img.src.includes('.avif')
        );
        
        console.log(`🆕 现代格式图片数: ${modernFormats.length}`);
        
        // 验证图片优化
        if (images.length > 0) {
            const lazyLoadingRate = lazyImages.length / images.length;
            console.log(`🔄 懒加载比率: ${(lazyLoadingRate * 100).toFixed(1)}%`);
            
            // 非关键图片应该使用懒加载
            expect(lazyLoadingRate).toBeGreaterThan(0.5);
        }
    });

    test('Service Worker和PWA功能测试', async ({ page }) => {
        console.log('⚙️ 开始Service Worker测试...');
        
        await page.goto(TEST_CONFIG.baseURL);
        await page.waitForLoadState('networkidle');
        
        // 检查Service Worker注册
        const swRegistered = await page.evaluate(async () => {
            if ('serviceWorker' in navigator) {
                try {
                    const registration = await navigator.serviceWorker.getRegistration();
                    return !!registration;
                } catch (error) {
                    return false;
                }
            }
            return false;
        });
        
        console.log(`⚙️ Service Worker状态: ${swRegistered ? '已注册' : '未注册'}`);
        
        // 检查PWA manifest
        const hasManifest = await page.evaluate(() => {
            const manifestLink = document.querySelector('link[rel="manifest"]');
            return !!manifestLink;
        });
        
        console.log(`📱 PWA Manifest: ${hasManifest ? '已配置' : '未配置'}`);
        
        // 如果有Service Worker，测试缓存功能
        if (swRegistered) {
            // 测试离线功能
            await page.setOfflineMode(true);
            
            try {
                await page.reload();
                await page.waitForLoadState('networkidle', { timeout: 5000 });
                
                const isOfflineWorking = await page.evaluate(() => {
                    return document.body.textContent.length > 100;
                });
                
                console.log(`📴 离线功能: ${isOfflineWorking ? '正常' : '异常'}`);
                
                await page.setOfflineMode(false);
            } catch (error) {
                console.log('📴 离线测试失败:', error.message);
                await page.setOfflineMode(false);
            }
        }
    });
});
