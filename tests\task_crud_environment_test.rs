//! # 任务管理CRUD环境测试
//!
//! 本模块测试任务管理CRUD操作的E2E测试环境设置
//! 遵循Context7 MCP最佳实践和rust_axum_Rules.md规范

use anyhow::{Context, Result};
use std::time::Duration;
use tokio::time::sleep;

#[path = "e2e/helpers/mod.rs"]
mod helpers;

use helpers::{AuthHelper, E2EConfig, TaskCrudHelper, TestTaskData};

/// 测试任务管理CRUD环境设置
#[tokio::test]
async fn test_task_crud_environment_setup() -> Result<()> {
    println!("🧪 开始任务管理CRUD环境测试");

    // 1. 加载配置
    let config = E2EConfig::from_env().context("无法加载E2E测试配置")?;

    println!("✅ 配置加载成功: {}", config.base_url);

    // 2. 创建辅助工具
    let auth_helper = AuthHelper::new(config.clone());
    let task_crud_helper = TaskCrudHelper::new(config.clone());

    // 3. 等待服务器启动
    println!("⏳ 等待服务器启动...");
    sleep(Duration::from_secs(2)).await;

    // 4. 测试用户注册（如果用户不存在）
    println!("🔐 测试用户注册...");
    let register_result = auth_helper
        .register_user(
            &config.test_username,
            &config.test_email,
            &config.test_password,
        )
        .await;

    match register_result {
        Ok(response) => {
            let status = response["status"].as_u64().unwrap_or(0);
            if status == 201 {
                println!("✅ 用户注册成功");
            } else if status == 409 {
                println!("ℹ️ 用户已存在，跳过注册");
            } else {
                println!("⚠️ 用户注册返回状态: {}", status);
            }
        }
        Err(e) => {
            println!("⚠️ 用户注册失败（可能用户已存在）: {}", e);
        }
    }

    // 5. 测试用户登录
    println!("🔑 测试用户登录...");
    let login_result = auth_helper
        .login_user(&config.test_username, &config.test_password)
        .await?;

    assert_eq!(
        login_result["status"].as_u64().unwrap_or(0),
        200,
        "登录应该成功"
    );
    println!("✅ 用户登录成功");

    // 6. 获取认证令牌
    println!("🎫 获取认证令牌...");
    let token = auth_helper
        .get_auth_token(&config.test_username, &config.test_password)
        .await?;

    println!("✅ 认证令牌获取成功");

    // 7. 验证令牌
    println!("🔍 验证认证令牌...");
    let verify_result = auth_helper.verify_token(&token).await?;
    assert_eq!(
        verify_result["status"].as_u64().unwrap_or(0),
        200,
        "令牌验证应该成功"
    );
    println!("✅ 令牌验证成功");

    // 8. 测试任务列表获取
    println!("📋 测试任务列表获取...");
    let tasks_list_result = task_crud_helper.fetch_tasks_list(&token).await?;
    assert_eq!(
        tasks_list_result["status"].as_u64().unwrap_or(0),
        200,
        "任务列表获取应该成功"
    );
    println!("✅ 任务列表获取成功");

    // 9. 测试任务创建
    println!("➕ 测试任务创建...");
    let test_task = TestTaskData::new("环境测试任务")
        .with_description("这是一个用于验证环境设置的测试任务")
        .with_priority("high");

    let create_result = task_crud_helper.create_task(&token, &test_task).await?;
    assert_eq!(
        create_result["status"].as_u64().unwrap_or(0),
        201,
        "任务创建应该成功"
    );

    let task_id = create_result["body"]["data"]["id"]
        .as_str()
        .ok_or_else(|| anyhow::anyhow!("无法从创建响应中提取任务ID"))?;

    println!("✅ 任务创建成功，ID: {}", task_id);

    // 10. 测试任务获取
    println!("🔍 测试任务获取...");
    let get_result = task_crud_helper.fetch_task_by_id(&token, task_id).await?;
    assert_eq!(
        get_result["status"].as_u64().unwrap_or(0),
        200,
        "任务获取应该成功"
    );
    println!("✅ 任务获取成功");

    // 11. 测试任务更新
    println!("✏️ 测试任务更新...");
    let updated_task = TestTaskData::new("环境测试任务（已更新）")
        .with_description("这是一个已更新的测试任务")
        .with_completed(true)
        .with_priority("medium");

    let update_result = task_crud_helper
        .update_task(&token, task_id, &updated_task)
        .await?;
    assert_eq!(
        update_result["status"].as_u64().unwrap_or(0),
        200,
        "任务更新应该成功"
    );
    println!("✅ 任务更新成功");

    // 12. 测试任务删除
    println!("🗑️ 测试任务删除...");
    let delete_result = task_crud_helper.delete_task(&token, task_id).await?;
    assert_eq!(
        delete_result["status"].as_u64().unwrap_or(0),
        204,
        "任务删除应该成功"
    );
    println!("✅ 任务删除成功");

    // 13. 验证任务已删除
    println!("🔍 验证任务已删除...");
    let get_deleted_result = task_crud_helper.fetch_task_by_id(&token, task_id).await?;
    assert_eq!(
        get_deleted_result["status"].as_u64().unwrap_or(0),
        404,
        "已删除的任务应该返回404"
    );
    println!("✅ 任务删除验证成功");

    // 14. 清理测试数据
    println!("🧹 清理测试数据...");
    let _ = task_crud_helper.cleanup_test_tasks(&token).await;
    println!("✅ 测试数据清理完成");

    println!("🎉 任务管理CRUD环境测试全部通过！");
    Ok(())
}

/// 测试并发任务操作
#[tokio::test]
async fn test_concurrent_task_operations() -> Result<()> {
    println!("🧪 开始并发任务操作测试");

    // 加载配置
    let config = E2EConfig::from_env()?;
    let auth_helper = AuthHelper::new(config.clone());
    let task_crud_helper = TaskCrudHelper::new(config.clone());

    // 获取认证令牌
    let token = auth_helper
        .get_auth_token(&config.test_username, &config.test_password)
        .await?;

    // 创建多个任务
    println!("➕ 创建多个测试任务...");
    let test_tasks = TaskCrudHelper::generate_test_tasks(3);
    let create_results = task_crud_helper
        .create_multiple_tasks(&token, &test_tasks)
        .await?;

    // 验证所有任务都创建成功
    for (i, result) in create_results.iter().enumerate() {
        assert_eq!(
            result["status"].as_u64().unwrap_or(0),
            201,
            "任务 {} 创建应该成功",
            i + 1
        );
    }

    println!("✅ {} 个任务创建成功", create_results.len());

    // 清理测试数据
    let _ = task_crud_helper.cleanup_test_tasks(&token).await;
    println!("✅ 并发任务操作测试完成");

    Ok(())
}

/// 测试认证流程
#[tokio::test]
async fn test_authentication_flow() -> Result<()> {
    println!("🧪 开始认证流程测试");

    let config = E2EConfig::from_env()?;
    let auth_helper = AuthHelper::new(config.clone());

    // 测试登录
    let login_result = auth_helper
        .login_user(&config.test_username, &config.test_password)
        .await?;

    assert_eq!(login_result["status"].as_u64().unwrap_or(0), 200);

    // 验证登录响应格式
    auth_helper.validate_login_response(&login_result["body"])?;
    println!("✅ 登录响应格式验证通过");

    // 获取令牌并验证
    let token = auth_helper
        .get_auth_token(&config.test_username, &config.test_password)
        .await?;

    let verify_result = auth_helper.verify_token(&token).await?;
    assert_eq!(verify_result["status"].as_u64().unwrap_or(0), 200);

    // 验证令牌响应格式
    auth_helper.validate_token_response(&verify_result["body"])?;
    println!("✅ 令牌验证响应格式验证通过");

    println!("🎉 认证流程测试全部通过！");
    Ok(())
}

#[tokio::main]
async fn main() -> Result<()> {
    println!("🚀 启动任务管理CRUD环境测试");

    // 直接运行测试逻辑
    run_environment_tests().await?;

    println!("🎉 所有测试通过！任务管理CRUD环境设置成功");
    Ok(())
}

/// 运行环境测试
async fn run_environment_tests() -> Result<()> {
    use anyhow::Context;
    use std::time::Duration;
    use tokio::time::sleep;

    println!("🧪 开始任务管理CRUD环境测试");

    // 1. 加载配置
    let config = E2EConfig::from_env().context("无法加载E2E测试配置")?;

    println!("✅ 配置加载成功: {}", config.base_url);

    // 2. 创建辅助工具
    let auth_helper = AuthHelper::new(config.clone());
    let task_crud_helper = TaskCrudHelper::new(config.clone());

    // 3. 等待服务器启动
    println!("⏳ 等待服务器启动...");
    sleep(Duration::from_secs(2)).await;

    // 4. 测试用户登录
    println!("🔑 测试用户登录...");
    let login_result = auth_helper
        .login_user(&config.test_username, &config.test_password)
        .await?;

    assert_eq!(
        login_result["status"].as_u64().unwrap_or(0),
        200,
        "登录应该成功"
    );
    println!("✅ 用户登录成功");

    // 5. 获取认证令牌
    println!("🎫 获取认证令牌...");
    let token = auth_helper
        .get_auth_token(&config.test_username, &config.test_password)
        .await?;

    println!("✅ 认证令牌获取成功");

    // 6. 测试任务列表获取
    println!("📋 测试任务列表获取...");
    let tasks_list_result = task_crud_helper.fetch_tasks_list(&token).await?;
    assert_eq!(
        tasks_list_result["status"].as_u64().unwrap_or(0),
        200,
        "任务列表获取应该成功"
    );
    println!("✅ 任务列表获取成功");

    // 7. 测试任务创建
    println!("➕ 测试任务创建...");
    let test_task = TestTaskData::new("环境测试任务")
        .with_description("这是一个用于验证环境设置的测试任务")
        .with_priority("high");

    let create_result = task_crud_helper.create_task(&token, &test_task).await?;
    assert_eq!(
        create_result["status"].as_u64().unwrap_or(0),
        201,
        "任务创建应该成功"
    );

    let task_id = create_result["body"]["data"]["id"]
        .as_str()
        .ok_or_else(|| anyhow::anyhow!("无法从创建响应中提取任务ID"))?;

    println!("✅ 任务创建成功，ID: {}", task_id);

    // 8. 测试任务删除
    println!("🗑️ 测试任务删除...");
    let delete_result = task_crud_helper.delete_task(&token, task_id).await?;
    assert_eq!(
        delete_result["status"].as_u64().unwrap_or(0),
        204,
        "任务删除应该成功"
    );
    println!("✅ 任务删除成功");

    // 9. 清理测试数据
    println!("🧹 清理测试数据...");
    let _ = task_crud_helper.cleanup_test_tasks(&token).await;
    println!("✅ 测试数据清理完成");

    println!("🎉 任务管理CRUD环境测试全部通过！");
    Ok(())
}
