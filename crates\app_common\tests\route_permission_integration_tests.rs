//! 路由权限控制集成测试
//!
//! 本测试套件验证路由配置中的认证中间件栈工作正常，包括：
//! - 不同权限级别的API访问控制
//! - 认证中间件栈的完整流程
//! - 端到端的权限验证
//! - 路由级别的权限控制
//! - 错误处理和状态码验证

use app_common::{
    middleware::{
        AuthenticatedUser, create_default_permission_checker, create_permission_middleware_state,
        inject_authenticated_user, inject_permission_state, require_admin,
        require_delete_permission, require_manager, require_write_permission,
    },
    utils::JwtUtils,
};
use app_interfaces::auth::UserRole;
use axum::{
    Router,
    body::Body,
    extract::Extension,
    http::{HeaderValue, Request, StatusCode, header::AUTHORIZATION},
    middleware,
    response::{Json, Response},
    routing::{delete, get, post, put},
};
use serde_json::{Value, json};
use tower::ServiceExt;

// ============================================================================
// 测试常量和辅助函数
// ============================================================================

const TEST_JWT_SECRET: &str = "test_route_permission_secret_key";
const TEST_USER_ID: &str = "550e8400-e29b-41d4-a716-************"; // 有效的UUID格式
const TEST_USERNAME: &str = "testuser";

/// 创建测试用的JWT工具实例
fn create_test_jwt_utils() -> JwtUtils {
    JwtUtils::new(TEST_JWT_SECRET.to_string())
}

/// 创建不同角色的测试token
fn create_test_token_for_role(role: UserRole) -> String {
    let jwt_utils = create_test_jwt_utils();
    jwt_utils
        .create_token_with_role(TEST_USER_ID, TEST_USERNAME, role, 24)
        .unwrap()
}

/// 创建带有Authorization头的请求
fn create_auth_request(method: &str, uri: &str, token: &str) -> Request<Body> {
    let mut request = Request::builder()
        .method(method)
        .uri(uri)
        .body(Body::empty())
        .unwrap();

    request.headers_mut().insert(
        AUTHORIZATION,
        HeaderValue::from_str(&format!("Bearer {}", token)).unwrap(),
    );

    request
}

/// JWT密钥注入中间件
async fn jwt_key_injection(mut req: Request<Body>, next: axum::middleware::Next) -> Response {
    req.extensions_mut().insert(TEST_JWT_SECRET.to_string());
    next.run(req).await
}

/// 创建无认证的请求
fn create_unauth_request(method: &str, uri: &str) -> Request<Body> {
    Request::builder()
        .method(method)
        .uri(uri)
        .body(Body::empty())
        .unwrap()
}

/// 测试处理器 - 成功响应
async fn success_handler() -> Json<Value> {
    Json(json!({"status": "success", "message": "操作成功"}))
}

/// 测试处理器 - 需要认证用户信息
async fn user_info_handler(Extension(user): Extension<AuthenticatedUser>) -> Json<Value> {
    Json(json!({
        "status": "success",
        "user_id": user.user_id,
        "username": user.username,
        "role": user.get_role().to_string()
    }))
}

/// 测试处理器 - 管理员专用
async fn admin_handler(Extension(user): Extension<AuthenticatedUser>) -> Json<Value> {
    Json(json!({
        "status": "admin_success",
        "admin_user": user.username,
        "role": user.get_role().to_string()
    }))
}

// ============================================================================
// 基础认证中间件测试
// ============================================================================

#[cfg(test)]
mod basic_auth_middleware_tests {
    use super::*;

    /// 创建基础认证测试应用
    fn create_basic_auth_app() -> Router {
        Router::new()
            .route("/protected", get(user_info_handler))
            .layer(middleware::from_fn(inject_authenticated_user()))
            .layer(middleware::from_fn(
                move |mut req: Request<Body>, next: axum::middleware::Next| {
                    async move {
                        // 注入JWT密钥（最先执行）
                        req.extensions_mut().insert(TEST_JWT_SECRET.to_string());
                        next.run(req).await
                    }
                },
            ))
    }

    /// 测试有效token的认证
    #[tokio::test]
    async fn test_valid_token_authentication() {
        // 首先测试JWT工具是否正常工作
        let jwt_utils = create_test_jwt_utils();
        let token = jwt_utils
            .create_token_with_role(TEST_USER_ID, TEST_USERNAME, UserRole::User, 24)
            .unwrap();

        // 验证token是否有效
        let claims = jwt_utils.validate_token_with_role(&token).unwrap();
        assert_eq!(claims.sub, TEST_USER_ID);
        assert_eq!(claims.username, TEST_USERNAME);
        assert_eq!(claims.role, "User");

        println!("✅ JWT工具测试通过");

        // 测试UserRole::parse_role是否正常工作
        let role_result = UserRole::parse_role(&claims.role);
        match &role_result {
            Ok(role) => println!("✅ 角色解析成功: {:?}", role),
            Err(e) => println!("❌ 角色解析失败: {:?}", e),
        }
        assert!(role_result.is_ok());

        // 测试AuthenticatedUser::from_extended_claims是否正常工作
        let auth_user_result = AuthenticatedUser::from_extended_claims(claims.clone());
        match &auth_user_result {
            Ok(user) => println!(
                "✅ AuthenticatedUser创建成功: user_id={}, username={}, role={:?}",
                user.user_id, user.username, user.role
            ),
            Err(e) => println!("❌ AuthenticatedUser创建失败: {:?}", e),
        }
        assert!(auth_user_result.is_ok());

        println!("✅ 所有组件单独测试通过，现在测试完整的中间件栈");

        // 现在测试完整的中间件栈
        let app = create_basic_auth_app();
        let request = create_auth_request("GET", "/protected", &token);

        let response = app.oneshot(request).await.unwrap();

        // 调试：打印响应状态和内容
        let status = response.status();
        println!("响应状态: {}", status);
        let body = axum::body::to_bytes(response.into_body(), usize::MAX)
            .await
            .unwrap();
        let body_str = String::from_utf8_lossy(&body);
        println!("响应内容: {}", body_str);

        // 如果不是200，先不断言，让我们看看错误信息
        if status != StatusCode::OK {
            panic!(
                "期望状态码200，实际得到: {}，响应内容: {}",
                status, body_str
            );
        }

        let json: Value = serde_json::from_slice(&body).unwrap();

        assert_eq!(json["status"], "success");
        assert_eq!(json["username"], TEST_USERNAME);
        assert_eq!(json["role"], "User");
    }

    /// 测试无效token的认证失败
    #[tokio::test]
    async fn test_invalid_token_authentication() {
        let app = create_basic_auth_app();
        let request = create_auth_request("GET", "/protected", "invalid.token.here");

        let response = app.oneshot(request).await.unwrap();

        assert_eq!(response.status(), StatusCode::UNAUTHORIZED);
    }

    /// 测试缺少token的认证失败
    #[tokio::test]
    async fn test_missing_token_authentication() {
        let app = create_basic_auth_app();
        let request = create_unauth_request("GET", "/protected");

        let response = app.oneshot(request).await.unwrap();

        assert_eq!(response.status(), StatusCode::UNAUTHORIZED);
    }

    /// 测试不同角色的token认证
    #[tokio::test]
    async fn test_different_roles_authentication() {
        let roles = vec![
            UserRole::Admin,
            UserRole::Manager,
            UserRole::User,
            UserRole::Guest,
        ];

        for role in roles {
            let app = create_basic_auth_app(); // 为每个测试创建新的app实例
            let token = create_test_token_for_role(role.clone());
            let request = create_auth_request("GET", "/protected", &token);

            let response = app.oneshot(request).await.unwrap();

            assert_eq!(response.status(), StatusCode::OK);

            let body = axum::body::to_bytes(response.into_body(), usize::MAX)
                .await
                .unwrap();
            let json: Value = serde_json::from_slice(&body).unwrap();

            assert_eq!(json["status"], "success");
            assert_eq!(json["role"], role.to_string());
        }
    }
}

// ============================================================================
// 权限级别控制测试
// ============================================================================

#[cfg(test)]
mod permission_level_tests {
    use super::*;

    /// 创建需要写权限的测试应用
    fn create_write_permission_app() -> Router {
        let permission_state =
            create_permission_middleware_state(create_default_permission_checker());

        Router::new()
            .route("/write-protected", put(success_handler))
            .layer(middleware::from_fn(require_write_permission()))
            .layer(middleware::from_fn(inject_authenticated_user()))
            .layer(middleware::from_fn_with_state(
                permission_state,
                inject_permission_state,
            ))
            .layer(middleware::from_fn(
                move |mut req: Request<Body>, next: axum::middleware::Next| async move {
                    req.extensions_mut().insert(TEST_JWT_SECRET.to_string());
                    next.run(req).await
                },
            ))
    }

    /// 创建需要删除权限的测试应用
    fn create_delete_permission_app() -> Router {
        let permission_state =
            create_permission_middleware_state(create_default_permission_checker());

        Router::new()
            .route("/delete-protected", delete(success_handler))
            .layer(middleware::from_fn(require_delete_permission()))
            .layer(middleware::from_fn(inject_authenticated_user()))
            .layer(middleware::from_fn_with_state(
                permission_state,
                inject_permission_state,
            ))
            .layer(middleware::from_fn(
                move |mut req: Request<Body>, next: axum::middleware::Next| async move {
                    req.extensions_mut().insert(TEST_JWT_SECRET.to_string());
                    next.run(req).await
                },
            ))
    }

    /// 测试写权限控制
    #[tokio::test]
    async fn test_write_permission_control() {
        // 测试Guest用户（权限级别25）- 应该被拒绝
        let app = create_write_permission_app();
        let guest_token = create_test_token_for_role(UserRole::Guest);
        let request = create_auth_request("PUT", "/write-protected", &guest_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::FORBIDDEN);

        // 测试User用户（权限级别50）- 应该通过
        let app = create_write_permission_app();
        let user_token = create_test_token_for_role(UserRole::User);
        let request = create_auth_request("PUT", "/write-protected", &user_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);

        // 测试Manager用户（权限级别75）- 应该通过
        let app = create_write_permission_app();
        let manager_token = create_test_token_for_role(UserRole::Manager);
        let request = create_auth_request("PUT", "/write-protected", &manager_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);

        // 测试Admin用户（权限级别100）- 应该通过
        let app = create_write_permission_app();
        let admin_token = create_test_token_for_role(UserRole::Admin);
        let request = create_auth_request("PUT", "/write-protected", &admin_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);
    }

    /// 测试删除权限控制
    #[tokio::test]
    async fn test_delete_permission_control() {
        // 测试Guest用户（权限级别25）- 应该被拒绝
        let app = create_delete_permission_app();
        let guest_token = create_test_token_for_role(UserRole::Guest);
        let request = create_auth_request("DELETE", "/delete-protected", &guest_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::FORBIDDEN);

        // 测试User用户（权限级别50）- 应该被拒绝
        let app = create_delete_permission_app();
        let user_token = create_test_token_for_role(UserRole::User);
        let request = create_auth_request("DELETE", "/delete-protected", &user_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::FORBIDDEN);

        // 测试Manager用户（权限级别75）- 应该通过
        let app = create_delete_permission_app();
        let manager_token = create_test_token_for_role(UserRole::Manager);
        let request = create_auth_request("DELETE", "/delete-protected", &manager_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);

        // 测试Admin用户（权限级别100）- 应该通过
        let app = create_delete_permission_app();
        let admin_token = create_test_token_for_role(UserRole::Admin);
        let request = create_auth_request("DELETE", "/delete-protected", &admin_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);
    }
}

// ============================================================================
// 角色级别控制测试
// ============================================================================

#[cfg(test)]
mod role_level_tests {
    use super::*;

    /// 创建需要管理员角色的测试应用
    fn create_admin_role_app() -> Router {
        let permission_state =
            create_permission_middleware_state(create_default_permission_checker());

        Router::new()
            .route("/admin-only", get(admin_handler))
            .layer(middleware::from_fn(require_admin()))
            .layer(middleware::from_fn(inject_authenticated_user()))
            .layer(middleware::from_fn_with_state(
                permission_state,
                inject_permission_state,
            ))
            .layer(middleware::from_fn(
                move |mut req: Request<Body>, next: axum::middleware::Next| async move {
                    req.extensions_mut().insert(TEST_JWT_SECRET.to_string());
                    next.run(req).await
                },
            ))
    }

    /// 创建需要管理者角色的测试应用
    fn create_manager_role_app() -> Router {
        let permission_state =
            create_permission_middleware_state(create_default_permission_checker());

        Router::new()
            .route("/manager-only", get(success_handler))
            .layer(middleware::from_fn(require_manager()))
            .layer(middleware::from_fn(inject_authenticated_user()))
            .layer(middleware::from_fn_with_state(
                permission_state,
                inject_permission_state,
            ))
            .layer(middleware::from_fn(
                move |mut req: Request<Body>, next: axum::middleware::Next| async move {
                    req.extensions_mut().insert(TEST_JWT_SECRET.to_string());
                    next.run(req).await
                },
            ))
    }

    /// 测试管理员角色控制
    #[tokio::test]
    async fn test_admin_role_control() {
        // 测试Guest用户 - 应该被拒绝
        let app = create_admin_role_app();
        let guest_token = create_test_token_for_role(UserRole::Guest);
        let request = create_auth_request("GET", "/admin-only", &guest_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::FORBIDDEN);

        // 测试User用户 - 应该被拒绝
        let app = create_admin_role_app();
        let user_token = create_test_token_for_role(UserRole::User);
        let request = create_auth_request("GET", "/admin-only", &user_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::FORBIDDEN);

        // 测试Manager用户 - 应该被拒绝
        let app = create_admin_role_app();
        let manager_token = create_test_token_for_role(UserRole::Manager);
        let request = create_auth_request("GET", "/admin-only", &manager_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::FORBIDDEN);

        // 测试Admin用户 - 应该通过
        let app = create_admin_role_app();
        let admin_token = create_test_token_for_role(UserRole::Admin);
        let request = create_auth_request("GET", "/admin-only", &admin_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);

        let body = axum::body::to_bytes(response.into_body(), usize::MAX)
            .await
            .unwrap();
        let json: Value = serde_json::from_slice(&body).unwrap();
        assert_eq!(json["status"], "admin_success");
        assert_eq!(json["role"], "Admin");
    }

    /// 测试管理者角色控制
    #[tokio::test]
    async fn test_manager_role_control() {
        // 测试Guest用户 - 应该被拒绝
        let app = create_manager_role_app();
        let guest_token = create_test_token_for_role(UserRole::Guest);
        let request = create_auth_request("GET", "/manager-only", &guest_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::FORBIDDEN);

        // 测试User用户 - 应该被拒绝
        let app = create_manager_role_app();
        let user_token = create_test_token_for_role(UserRole::User);
        let request = create_auth_request("GET", "/manager-only", &user_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::FORBIDDEN);

        // 测试Manager用户 - 应该通过
        let app = create_manager_role_app();
        let manager_token = create_test_token_for_role(UserRole::Manager);
        let request = create_auth_request("GET", "/manager-only", &manager_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);

        // 测试Admin用户 - 应该通过（Admin权限高于Manager）
        let app = create_manager_role_app();
        let admin_token = create_test_token_for_role(UserRole::Admin);
        let request = create_auth_request("GET", "/manager-only", &admin_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);
    }
}

// ============================================================================
// 中间件栈集成测试
// ============================================================================

#[cfg(test)]
mod middleware_stack_tests {
    use super::*;

    /// 创建完整中间件栈的测试应用
    /// 模拟真实的业务API路由配置
    fn create_full_middleware_stack_app() -> Router {
        let permission_state =
            create_permission_middleware_state(create_default_permission_checker());

        Router::new()
            // 不同权限级别的路由
            .route("/api/v1/public", get(success_handler))
            .route("/api/v1/user/profile", get(user_info_handler))
            .route("/api/v1/user/profile", put(success_handler))
            .route("/api/v1/admin/users", get(admin_handler))
            .route("/api/v1/admin/system", delete(success_handler))
            // 应用完整的中间件栈（注意顺序：从下到上执行）
            .layer(middleware::from_fn(require_write_permission())) // 最后执行：权限检查
            .layer(middleware::from_fn(inject_authenticated_user())) // 用户认证
            .layer(middleware::from_fn_with_state(permission_state, inject_permission_state)) // 权限状态注入
            .layer(
                middleware::from_fn(move |mut req: Request<Body>, next: axum::middleware::Next| {
                    async move {
                        // 最先执行：JWT密钥注入
                        req.extensions_mut().insert(TEST_JWT_SECRET.to_string());
                        next.run(req).await
                    }
                })
            )
    }

    /// 测试中间件栈的执行顺序
    #[tokio::test]
    async fn test_middleware_stack_execution_order() {
        let app = create_full_middleware_stack_app();

        // 测试有效的User token（权限级别50）
        let user_token = create_test_token_for_role(UserRole::User);
        let request = create_auth_request("PUT", "/api/v1/user/profile", &user_token);

        let response = app.oneshot(request).await.unwrap();

        // User有写权限，应该成功
        assert_eq!(response.status(), StatusCode::OK);
    }

    /// 测试中间件栈的错误传播
    #[tokio::test]
    async fn test_middleware_stack_error_propagation() {
        // 测试无效token - 应该在认证层被拦截
        let app = create_full_middleware_stack_app();
        let request = create_auth_request("PUT", "/api/v1/user/profile", "invalid.token");
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::UNAUTHORIZED);

        // 测试权限不足 - 应该在权限检查层被拦截
        let app = create_full_middleware_stack_app();
        let guest_token = create_test_token_for_role(UserRole::Guest);
        let request = create_auth_request("PUT", "/api/v1/user/profile", &guest_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::FORBIDDEN);
    }

    /// 测试中间件状态传递
    #[tokio::test]
    async fn test_middleware_state_passing() {
        let app = create_full_middleware_stack_app();

        // 测试用户信息是否正确传递到处理器
        let admin_token = create_test_token_for_role(UserRole::Admin);
        let request = create_auth_request("GET", "/api/v1/user/profile", &admin_token);

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);

        let body = axum::body::to_bytes(response.into_body(), usize::MAX)
            .await
            .unwrap();
        let json: Value = serde_json::from_slice(&body).unwrap();

        // 验证用户信息正确传递
        assert_eq!(json["status"], "success");
        assert_eq!(json["username"], TEST_USERNAME);
        assert_eq!(json["user_id"], TEST_USER_ID);
        assert_eq!(json["role"], "Admin");
    }
}

// ============================================================================
// 端到端API访问控制测试
// ============================================================================

#[cfg(test)]
mod end_to_end_api_tests {
    use super::*;

    /// 创建模拟真实业务场景的API路由
    fn create_business_api_routes() -> Router {
        let permission_state =
            create_permission_middleware_state(create_default_permission_checker());

        Router::new()
            // 用户域路由 - 分别配置不同权限要求的路由
            .nest(
                "/api/v1/users",
                Router::new()
                    // 只需认证的路由
                    .route("/me", get(user_info_handler))
                    .layer(middleware::from_fn(inject_authenticated_user()))
                    .layer(
                        middleware::from_fn_with_state(
                            permission_state.clone(),
                            inject_permission_state
                        )
                    )
                    // 需要写权限的路由
                    .merge(
                        Router::new()
                            .route("/me/profile", put(success_handler))
                            .layer(middleware::from_fn(require_write_permission()))
                            .layer(middleware::from_fn(inject_authenticated_user()))
                            .layer(
                                middleware::from_fn_with_state(
                                    permission_state.clone(),
                                    inject_permission_state
                                )
                            )
                    )
            )
            // 任务域路由 - 分别配置不同权限要求的路由
            .nest(
                "/api/v1/tasks",
                Router::new()
                    // 只需认证的路由
                    .route("/", get(success_handler))
                    .layer(middleware::from_fn(inject_authenticated_user()))
                    .layer(
                        middleware::from_fn_with_state(
                            permission_state.clone(),
                            inject_permission_state
                        )
                    )
                    // 需要写权限的路由
                    .merge(
                        Router::new()
                            .route("/", post(success_handler))
                            .layer(middleware::from_fn(require_write_permission()))
                            .layer(middleware::from_fn(inject_authenticated_user()))
                            .layer(
                                middleware::from_fn_with_state(
                                    permission_state.clone(),
                                    inject_permission_state
                                )
                            )
                    )
                    // 需要删除权限的路由
                    .merge(
                        Router::new()
                            .route("/{id}", delete(success_handler))
                            .layer(middleware::from_fn(require_delete_permission()))
                            .layer(middleware::from_fn(inject_authenticated_user()))
                            .layer(
                                middleware::from_fn_with_state(
                                    permission_state.clone(),
                                    inject_permission_state
                                )
                            )
                    )
            )
            // 管理域路由
            .nest(
                "/api/v1/admin",
                Router::new()
                    .route("/users", get(admin_handler)) // 需要管理员角色
                    .route("/system", get(admin_handler)) // 需要管理员角色
                    .layer(middleware::from_fn(require_admin()))
                    .layer(middleware::from_fn(inject_authenticated_user()))
                    .layer(
                        middleware::from_fn_with_state(permission_state, inject_permission_state)
                    )
            )
            // 全局JWT密钥注入（最底层，最先执行）
            .layer(
                middleware::from_fn(move |mut req: Request<Body>, next: axum::middleware::Next| {
                    async move {
                        req.extensions_mut().insert(TEST_JWT_SECRET.to_string());
                        next.run(req).await
                    }
                })
            )
    }

    /// 测试用户域API访问控制
    #[tokio::test]
    async fn test_user_domain_api_access_control() {
        // 测试获取用户信息（只需认证）
        let app = create_business_api_routes();
        let user_token = create_test_token_for_role(UserRole::User);
        let request = create_auth_request("GET", "/api/v1/users/me", &user_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);

        // 测试更新用户资料（需要写权限）
        let app = create_business_api_routes();
        let user_token = create_test_token_for_role(UserRole::User);
        let request = create_auth_request("PUT", "/api/v1/users/me/profile", &user_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);

        // 测试Guest用户更新资料（权限不足）
        let app = create_business_api_routes();
        let guest_token = create_test_token_for_role(UserRole::Guest);
        let request = create_auth_request("PUT", "/api/v1/users/me/profile", &guest_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::FORBIDDEN);
    }

    /// 测试任务域API访问控制
    #[tokio::test]
    async fn test_task_domain_api_access_control() {
        // 首先测试一个简单的路由来验证路由配置
        let simple_app = Router::new()
            .route("/test", get(success_handler))
            .layer(middleware::from_fn(jwt_key_injection));

        let test_request = Request::builder()
            .method("GET")
            .uri("/test")
            .body(Body::empty())
            .unwrap();

        let test_response = simple_app.oneshot(test_request).await.unwrap();
        println!("简单路由测试状态: {}", test_response.status());

        // 测试嵌套路由 - 不带尾部斜杠
        let nested_app = Router::new()
            .nest(
                "/api/v1/tasks",
                Router::new().route("/", get(success_handler)),
            )
            .layer(middleware::from_fn(jwt_key_injection));

        let nested_request = Request::builder()
            .method("GET")
            .uri("/api/v1/tasks/")
            .body(Body::empty())
            .unwrap();

        let nested_response = nested_app.oneshot(nested_request).await.unwrap();
        println!(
            "嵌套路由测试状态（带尾部斜杠）: {}",
            nested_response.status()
        );

        // 测试不带尾部斜杠的路径
        let nested_app2 = Router::new()
            .nest(
                "/api/v1/tasks",
                Router::new().route("/", get(success_handler)),
            )
            .layer(middleware::from_fn(jwt_key_injection));

        let nested_request2 = Request::builder()
            .method("GET")
            .uri("/api/v1/tasks")
            .body(Body::empty())
            .unwrap();

        let nested_response2 = nested_app2.oneshot(nested_request2).await.unwrap();
        println!(
            "嵌套路由测试状态（不带尾部斜杠）: {}",
            nested_response2.status()
        );

        // 测试查看任务（只需认证）
        let app = create_business_api_routes();
        let user_token = create_test_token_for_role(UserRole::User);
        let request = create_auth_request("GET", "/api/v1/tasks", &user_token);
        let response = app.oneshot(request).await.unwrap();

        // 调试信息
        let status = response.status();
        println!("GET /api/v1/tasks 响应状态: {}", status);
        if status != StatusCode::OK {
            let body = axum::body::to_bytes(response.into_body(), usize::MAX)
                .await
                .unwrap();
            let body_str = String::from_utf8_lossy(&body);
            println!("响应内容: {}", body_str);
            panic!("期望状态码200，实际得到: {}", status);
        }

        assert_eq!(response.status(), StatusCode::OK);

        // 测试创建任务（需要写权限）
        let app = create_business_api_routes();
        let user_token = create_test_token_for_role(UserRole::User);
        let request = create_auth_request("POST", "/api/v1/tasks", &user_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);

        // 测试删除任务（需要删除权限）
        let app = create_business_api_routes();
        let manager_token = create_test_token_for_role(UserRole::Manager);
        let request = create_auth_request("DELETE", "/api/v1/tasks/123", &manager_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);

        // 测试User用户删除任务（权限不足）
        let app = create_business_api_routes();
        let user_token = create_test_token_for_role(UserRole::User);
        let request = create_auth_request("DELETE", "/api/v1/tasks/123", &user_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::FORBIDDEN);
    }

    /// 测试管理域API访问控制
    #[tokio::test]
    async fn test_admin_domain_api_access_control() {
        // 测试管理员访问用户管理
        let app = create_business_api_routes();
        let admin_token = create_test_token_for_role(UserRole::Admin);
        let request = create_auth_request("GET", "/api/v1/admin/users", &admin_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);

        // 测试管理员访问系统管理
        let app = create_business_api_routes();
        let admin_token = create_test_token_for_role(UserRole::Admin);
        let request = create_auth_request("GET", "/api/v1/admin/system", &admin_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);

        // 测试Manager用户访问管理API（权限不足）
        let app = create_business_api_routes();
        let manager_token = create_test_token_for_role(UserRole::Manager);
        let request = create_auth_request("GET", "/api/v1/admin/users", &manager_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::FORBIDDEN);

        // 测试User用户访问管理API（权限不足）
        let app = create_business_api_routes();
        let user_token = create_test_token_for_role(UserRole::User);
        let request = create_auth_request("GET", "/api/v1/admin/system", &user_token);
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::FORBIDDEN);
    }
}

// ============================================================================
// 错误处理和边缘情况测试
// ============================================================================

#[cfg(test)]
mod error_handling_tests {
    use super::*;

    /// 创建用于错误测试的应用
    pub fn create_error_test_app() -> Router {
        let permission_state =
            create_permission_middleware_state(create_default_permission_checker());

        Router::new()
            .route("/protected", get(user_info_handler))
            .layer(middleware::from_fn(inject_authenticated_user()))
            .layer(middleware::from_fn_with_state(
                permission_state,
                inject_permission_state,
            ))
            .layer(middleware::from_fn(
                move |mut req: Request<Body>, next: axum::middleware::Next| async move {
                    req.extensions_mut().insert(TEST_JWT_SECRET.to_string());
                    next.run(req).await
                },
            ))
    }

    /// 测试各种无效token格式
    #[tokio::test]
    async fn test_invalid_token_formats() {
        let invalid_tokens = vec![
            "",                                                       // 空token
            "invalid",                                                // 无效格式
            "Bearer",                                                 // 只有Bearer前缀
            "Bearer ",                                                // Bearer后只有空格
            "invalid.token",                                          // 不完整的JWT
            "header.payload",                                         // 缺少签名
            "a.b.c.d",                                                // 过多部分
            "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.invalid.signature", // 无效payload
        ];

        for token in invalid_tokens {
            let app = create_error_test_app(); // 为每个测试创建新的app实例
            let request = if token.is_empty() {
                create_unauth_request("GET", "/protected")
            } else {
                create_auth_request("GET", "/protected", token)
            };

            let response = app.oneshot(request).await.unwrap();
            assert_eq!(
                response.status(),
                StatusCode::UNAUTHORIZED,
                "Token '{}' 应该返回401状态码",
                token
            );
        }
    }

    /// 测试过期token处理
    #[tokio::test]
    async fn test_expired_token_handling() {
        let app = create_error_test_app();

        // 创建一个过期的token（过期时间设为负数）
        let jwt_utils = create_test_jwt_utils();
        let expired_token = jwt_utils.create_token_with_role(
            TEST_USER_ID,
            TEST_USERNAME,
            UserRole::User,
            -1, // 负数小时，表示已过期
        );

        // 由于create_token_with_role可能不允许负数，我们使用一个已知的过期token
        // 或者创建一个极短过期时间的token然后等待
        match expired_token {
            Ok(token) => {
                let request = create_auth_request("GET", "/protected", &token);
                let response = app.oneshot(request).await.unwrap();
                // 过期token应该被拒绝
                assert!(response.status() == StatusCode::UNAUTHORIZED);
            }
            Err(_) => {
                // 如果创建过期token失败，这也是预期的行为
                println!("✅ 创建过期token被正确拒绝");
            }
        }
    }

    /// 测试恶意token攻击
    #[tokio::test]
    async fn test_malicious_token_attacks() {
        let _app = create_error_test_app();

        // 创建超长token字符串
        let long_token = "a".repeat(10000);

        let malicious_tokens = vec![
            // SQL注入尝试
            "'; DROP TABLE users; --",
            // XSS尝试
            "<script>alert('xss')</script>",
            // 超长token
            &long_token,
            // 特殊字符
            "token with spaces and symbols !@#$%^&*()",
            // Unicode字符
            "token_with_中文_and_émojis_🚀",
            // Base64编码的恶意内容
            "eyJhbGciOiJub25lIiwidHlwIjoiSldUIn0.eyJzdWIiOiJhZG1pbiIsInJvbGUiOiJhZG1pbiJ9.",
        ];

        for token in malicious_tokens {
            let app = create_error_test_app(); // 为每个测试创建新的app实例
            let request = create_auth_request("GET", "/protected", token);
            let response = app.oneshot(request).await.unwrap();

            // 所有恶意token都应该被拒绝
            assert_eq!(
                response.status(),
                StatusCode::UNAUTHORIZED,
                "恶意token '{}' 应该被拒绝",
                token
            );
        }
    }

    /// 测试并发请求处理
    #[tokio::test]
    async fn test_concurrent_requests() {
        use std::sync::Arc;
        use tokio::task::JoinSet;

        let app = Arc::new(create_error_test_app());
        let mut join_set = JoinSet::new();

        // 创建10个并发请求
        for i in 0..10 {
            let app_clone = app.clone();
            let token = create_test_token_for_role(UserRole::User);

            join_set.spawn(async move {
                let request = create_auth_request("GET", "/protected", &token);
                let response = (*app_clone).clone().oneshot(request).await.unwrap();
                (i, response.status())
            });
        }

        // 等待所有请求完成
        let mut results = Vec::new();
        while let Some(result) = join_set.join_next().await {
            results.push(result.unwrap());
        }

        // 验证所有请求都成功
        assert_eq!(results.len(), 10);
        for (i, status) in results {
            assert_eq!(status, StatusCode::OK, "并发请求 {} 应该成功", i);
        }
    }

    /// 测试请求头边缘情况
    #[tokio::test]
    async fn test_header_edge_cases() {
        let app = create_error_test_app();

        // 测试多个Authorization头
        let mut request = Request::builder()
            .method("GET")
            .uri("/protected")
            .body(Body::empty())
            .unwrap();

        request
            .headers_mut()
            .insert(AUTHORIZATION, HeaderValue::from_static("Bearer token1"));
        request
            .headers_mut()
            .append(AUTHORIZATION, HeaderValue::from_static("Bearer token2"));

        let response = app.oneshot(request).await.unwrap();
        // 多个Authorization头应该被拒绝或使用第一个
        assert!(response.status() == StatusCode::UNAUTHORIZED);

        // 测试非ASCII字符的Authorization头
        let mut request = Request::builder()
            .method("GET")
            .uri("/protected")
            .body(Body::empty())
            .unwrap();

        // 尝试插入包含非ASCII字符的头（这应该失败）
        let header_result = HeaderValue::from_str("Bearer token_with_中文");

        // 如果创建HeaderValue失败，这是预期的
        if header_result.is_err() {
            println!("✅ 非ASCII字符的Authorization头被正确拒绝");
        } else {
            // 如果创建成功，插入头并测试
            request
                .headers_mut()
                .insert(AUTHORIZATION, header_result.unwrap());
            let app = create_error_test_app();
            let response = app.oneshot(request).await.unwrap();
            assert_eq!(response.status(), StatusCode::UNAUTHORIZED);
        }
    }
}

// ============================================================================
// 性能和压力测试
// ============================================================================

#[cfg(test)]
mod performance_tests {
    use super::*;
    use std::time::Instant;

    /// 测试认证性能
    #[tokio::test]
    async fn test_authentication_performance() {
        let app = error_handling_tests::create_error_test_app();
        let token = create_test_token_for_role(UserRole::User);

        let start = Instant::now();
        let iterations = 100;

        for _ in 0..iterations {
            let app = app.clone(); // 克隆Router用于每次请求
            let request = create_auth_request("GET", "/protected", &token);
            let response = app.oneshot(request).await.unwrap();
            assert_eq!(response.status(), StatusCode::OK);
        }

        let duration = start.elapsed();
        let avg_time = duration / iterations;

        println!("✅ 认证性能测试完成:");
        println!("   - 总时间: {:?}", duration);
        println!("   - 平均时间: {:?}", avg_time);
        println!(
            "   - 每秒请求数: {:.2}",
            1000.0 / (avg_time.as_millis() as f64)
        );

        // 确保平均响应时间在合理范围内（小于10ms）
        assert!(
            avg_time.as_millis() < 10,
            "认证响应时间过长: {:?}",
            avg_time
        );
    }

    /// 测试权限检查性能
    #[tokio::test]
    async fn test_permission_check_performance() {
        let permission_state =
            create_permission_middleware_state(create_default_permission_checker());

        let app = Router::new()
            .route("/write-test", put(success_handler))
            .layer(middleware::from_fn(require_write_permission()))
            .layer(middleware::from_fn(inject_authenticated_user()))
            .layer(middleware::from_fn_with_state(
                permission_state,
                inject_permission_state,
            ))
            .layer(middleware::from_fn(jwt_key_injection));

        let token = create_test_token_for_role(UserRole::User);
        let start = Instant::now();
        let iterations = 50;

        for _ in 0..iterations {
            let app = app.clone(); // 克隆Router用于每次请求
            let request = create_auth_request("PUT", "/write-test", &token);
            let response = app.oneshot(request).await.unwrap();
            assert_eq!(response.status(), StatusCode::OK);
        }

        let duration = start.elapsed();
        let avg_time = duration / iterations;

        println!("✅ 权限检查性能测试完成:");
        println!("   - 总时间: {:?}", duration);
        println!("   - 平均时间: {:?}", avg_time);

        // 确保权限检查不会显著影响性能（小于15ms）
        assert!(
            avg_time.as_millis() < 15,
            "权限检查响应时间过长: {:?}",
            avg_time
        );
    }
}
