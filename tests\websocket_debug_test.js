/**
 * WebSocket调试测试
 * 
 * 专门用于调试WebSocket连接稳定性问题
 */

const { chromium } = require('playwright');

async function runWebSocketDebugTest() {
    console.log('🔍 开始WebSocket调试测试...');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000 // 减慢操作速度以便观察
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // 监听所有控制台消息
    page.on('console', msg => {
        const type = msg.type();
        const text = msg.text();
        console.log(`[浏览器-${type}] ${text}`);
    });
    
    // 监听页面错误
    page.on('pageerror', error => {
        console.log('❌ 页面错误:', error.message);
    });
    
    try {
        // 1. 导航到页面
        console.log('📍 导航到测试页面...');
        await page.goto('http://127.0.0.1:3000');
        await page.waitForLoadState('networkidle');
        
        // 2. 登录测试用户
        console.log('🔐 登录测试用户...');
        await page.click('#loginTab');
        await page.waitForTimeout(500);
        
        await page.fill('#loginUsername', 'testuser456');
        await page.fill('#loginPassword', 'password123');
        await page.click('#loginForm button[type="submit"]');
        
        // 等待登录完成
        await page.waitForSelector('#currentUser:has-text("testuser456")', { timeout: 10000 });
        console.log('✅ 登录成功');
        
        // 3. 获取WebSocket控制元素
        const connectBtn = page.locator('#wsConnectBtn');
        const disconnectBtn = page.locator('#wsDisconnectBtn');
        const connectionStatus = page.locator('#connectionStatus');
        const rawMessages = page.locator('#rawMessages');
        
        // 4. 执行问题重现序列
        console.log('🔄 开始执行问题重现序列...');
        
        // 第一次连接
        console.log('📡 第1次连接...');
        await connectBtn.click();
        await page.waitForTimeout(2000);
        let status = await connectionStatus.textContent();
        console.log(`  连接状态: ${status}`);
        
        // 第一次断开
        console.log('📡 第1次断开...');
        await disconnectBtn.click();
        await page.waitForTimeout(2000);
        status = await connectionStatus.textContent();
        console.log(`  连接状态: ${status}`);
        
        // 第二次连接
        console.log('📡 第2次连接...');
        await connectBtn.click();
        await page.waitForTimeout(2000);
        status = await connectionStatus.textContent();
        console.log(`  连接状态: ${status}`);
        
        // 第二次断开
        console.log('📡 第2次断开...');
        await disconnectBtn.click();
        await page.waitForTimeout(2000);
        status = await connectionStatus.textContent();
        console.log(`  连接状态: ${status}`);
        
        // 第三次连接
        console.log('📡 第3次连接...');
        await connectBtn.click();
        await page.waitForTimeout(2000);
        status = await connectionStatus.textContent();
        console.log(`  连接状态: ${status}`);
        
        // 第三次断开
        console.log('📡 第3次断开...');
        await disconnectBtn.click();
        await page.waitForTimeout(2000);
        status = await connectionStatus.textContent();
        console.log(`  连接状态: ${status}`);
        
        // 最终连接尝试
        console.log('📡 最终连接尝试...');
        await connectBtn.click();
        await page.waitForTimeout(3000); // 等待更长时间
        
        status = await connectionStatus.textContent();
        console.log(`  最终连接状态: ${status}`);
        
        // 检查原始消息日志
        const rawMessagesText = await rawMessages.textContent();
        console.log('\n📝 原始消息日志:');
        console.log('='.repeat(50));
        console.log(rawMessagesText);
        console.log('='.repeat(50));
        
        // 如果连接成功，监控稳定性
        if (status.includes('已连接')) {
            console.log('✅ 最终连接成功，开始监控稳定性...');
            
            for (let i = 1; i <= 10; i++) {
                await page.waitForTimeout(1000);
                const currentStatus = await connectionStatus.textContent();
                console.log(`  第${i}秒: ${currentStatus}`);
                
                if (!currentStatus.includes('已连接')) {
                    console.log(`❌ 连接在第${i}秒时断开！`);
                    break;
                }
            }
        } else {
            console.log('❌ 最终连接失败');
        }
        
        // 获取最终的原始消息日志
        const finalRawMessagesText = await rawMessages.textContent();
        console.log('\n📝 最终原始消息日志:');
        console.log('='.repeat(50));
        console.log(finalRawMessagesText);
        console.log('='.repeat(50));
        
    } catch (error) {
        console.error('❌ 测试执行失败:', error);
    } finally {
        await browser.close();
        console.log('🏁 调试测试完成');
    }
}

// 运行测试
if (require.main === module) {
    runWebSocketDebugTest().catch(console.error);
}

module.exports = { runWebSocketDebugTest };
