use fred::prelude::*;
use std::time::Duration;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🐉 简单DragonflyDB连接测试...");

    // 加载.env文件
    if let Err(e) = dotenvy::dotenv() {
        println!("⚠️ 无法加载.env文件: {}", e);
    }

    // 测试不同的连接URL格式
    let test_urls = vec![
        "redis://:dragonfly_secure_password_2025@************:6379",
        "redis://************:6379",
        "redis://:dragonfly_secure_password_2025@localhost:6379",
        "redis://localhost:6379",
        "redis://127.0.0.1:6379",
        "redis://:dragonfly_secure_password_2025@127.0.0.1:6379",
    ];

    for (i, url) in test_urls.iter().enumerate() {
        println!("\n🔍 测试连接 {}: {}", i + 1, url);

        match test_connection(url).await {
            Ok(_) => {
                println!("✅ 连接成功！");
                break;
            }
            Err(e) => {
                println!("❌ 连接失败: {}", e);
            }
        }
    }

    Ok(())
}

async fn test_connection(url: &str) -> Result<(), Box<dyn std::error::Error>> {
    // 创建Redis配置
    let config = Config::from_url(url)?;

    // 创建客户端
    let client = Builder::from_config(config).build()?;

    // 尝试连接（短超时）
    let connect_result = tokio::time::timeout(Duration::from_secs(3), client.connect()).await;

    match connect_result {
        Ok(Ok(_)) => {
            // 测试PING命令
            let ping_result: String =
                tokio::time::timeout(Duration::from_secs(2), client.ping::<String>(None)).await??;
            println!("🏓 PING响应: {}", ping_result);
            Ok(())
        }
        Ok(Err(e)) => Err(e.into()),
        Err(_) => Err("连接超时".into()),
    }
}
