import { test, expect } from '@playwright/test';

/**
 * 基础Playwright配置验证测试
 * 验证Playwright环境是否正确配置
 */
test.describe('基础配置验证', () => {
  test('应该能够启动浏览器并访问静态页面', async ({ page }) => {
    // 导航到静态HTML页面
    await page.goto('file://' + process.cwd() + '/static/index.html');
    
    // 验证页面标题
    await expect(page).toHaveTitle(/Axum 任务管理系统/);
    
    // 验证主要元素存在
    const pageTitle = page.getByRole('heading', { name: /Axum 任务管理系统/i });
    await expect(pageTitle).toBeVisible();
    
    // 验证主容器存在
    const mainContainer = page.locator('.main-container');
    await expect(mainContainer).toBeVisible();
    
    // 验证三个主要面板存在
    const panels = page.locator('.panel');
    await expect(panels).toHaveCount(3);
    
    // 截图验证
    await page.screenshot({ path: 'tests/ui/test-results/basic-test.png' });
  });

  test('应该支持不同视口大小', async ({ page }) => {
    // 测试桌面视口
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.goto('file://' + process.cwd() + '/static/index.html');
    
    const mainContainer = page.locator('.main-container');
    await expect(mainContainer).toBeVisible();
    
    // 测试平板视口
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(mainContainer).toBeVisible();
    
    // 测试移动视口
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(mainContainer).toBeVisible();
  });

  test('应该能够与表单元素交互', async ({ page }) => {
    await page.goto('file://' + process.cwd() + '/static/index.html');
    
    // 查找用户名输入框
    const usernameInput = page.locator('input[placeholder*="用户名"], input[name*="username"], #username');
    if (await usernameInput.count() > 0) {
      await usernameInput.first().fill('testuser');
      await expect(usernameInput.first()).toHaveValue('testuser');
    }
    
    // 查找密码输入框
    const passwordInput = page.locator('input[type="password"], input[placeholder*="密码"], #password');
    if (await passwordInput.count() > 0) {
      await passwordInput.first().fill('testpass');
      await expect(passwordInput.first()).toHaveValue('testpass');
    }
    
    // 查找按钮
    const buttons = page.locator('button');
    const buttonCount = await buttons.count();
    expect(buttonCount).toBeGreaterThan(0);
  });

  test('应该能够检测页面错误', async ({ page }) => {
    const consoleErrors: string[] = [];
    
    // 监听控制台错误
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    await page.goto('file://' + process.cwd() + '/static/index.html');
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
    
    // 验证没有JavaScript错误（允许一些网络相关的错误，因为是静态文件）
    const criticalErrors = consoleErrors.filter(error => 
      !error.includes('net::') && 
      !error.includes('Failed to load') &&
      !error.includes('WebSocket')
    );
    
    expect(criticalErrors.length).toBe(0);
  });
});
