//! # API调试测试
//!
//! 用于调试API响应问题的简单测试

use anyhow::Result;
use reqwest::Client;
use serde_json::{Value, json};

#[tokio::main]
async fn main() -> Result<()> {
    println!("🔍 开始API调试测试");

    let client = Client::new();
    let base_url = "http://127.0.0.1:3000";

    // 1. 测试健康检查端点
    println!("🏥 测试健康检查端点...");
    let health_response = client.get(&format!("{}/health", base_url)).send().await?;

    println!("健康检查状态: {}", health_response.status());
    let health_text = health_response.text().await?;
    println!("健康检查响应: {}", health_text);

    // 2. 测试登录端点
    println!("🔑 测试登录端点...");
    let login_payload = json!({
        "username": "testuser456",
        "password": "password123"
    });

    let login_response = client
        .post(&format!("{}/api/auth/login", base_url))
        .json(&login_payload)
        .send()
        .await?;

    println!("登录状态: {}", login_response.status());
    let login_text = login_response.text().await?;
    println!("登录响应: {}", login_text);

    // 3. 测试任务列表端点（无认证）
    println!("📋 测试任务列表端点（无认证）...");
    let tasks_response = client
        .get(&format!("{}/api/tasks", base_url))
        .send()
        .await?;

    println!("任务列表状态: {}", tasks_response.status());
    let tasks_text = tasks_response.text().await?;
    println!("任务列表响应: {}", tasks_text);

    println!("✅ API调试测试完成");
    Ok(())
}
