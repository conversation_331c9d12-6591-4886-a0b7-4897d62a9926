//! # API性能测试运行器
//!
//! 本模块提供一个简化的API性能测试运行器，用于验证任务4.3的实现
//! 遵循Context7 MCP最佳实践和rust_axum_Rules.md规范

use anyhow::{Context, Result};
use std::env;

// 导入API响应验证测试套件
mod api_response_validation_tests;
use api_response_validation_tests::ApiResponseValidationTestSuite;

/// 性能测试运行器主函数
#[tokio::main]
async fn main() -> Result<()> {
    println!("🚀 启动API响应时间性能测试运行器");
    println!("📋 任务4.3: 实现API响应时间性能测试");

    // 设置环境变量（如果未设置）
    unsafe {
        if env::var("E2E_SERVER_HOST").is_err() {
            env::set_var("E2E_SERVER_HOST", "127.0.0.1");
        }
        if env::var("E2E_SERVER_PORT").is_err() {
            env::set_var("E2E_SERVER_PORT", "3000");
        }
        if env::var("E2E_BASE_URL").is_err() {
            env::set_var("E2E_BASE_URL", "http://127.0.0.1:3000");
        }
    }

    println!("🔧 环境配置:");
    println!(
        "  - 服务器地址: {}",
        env::var("E2E_SERVER_HOST").unwrap_or_default()
    );
    println!(
        "  - 服务器端口: {}",
        env::var("E2E_SERVER_PORT").unwrap_or_default()
    );
    println!(
        "  - 基础URL: {}",
        env::var("E2E_BASE_URL").unwrap_or_default()
    );

    // 初始化测试套件
    let mut test_suite = ApiResponseValidationTestSuite::new()
        .await
        .context("无法初始化API响应验证测试套件")?;

    println!("🔧 设置测试环境...");

    // 设置测试环境
    match test_suite.setup().await {
        Ok(_) => {
            println!("✅ 测试环境设置成功");

            // 运行性能测试
            println!("🧪 开始执行API响应时间性能测试...");

            match test_suite.run_all_tests().await {
                Ok(_) => {
                    println!("🎉 API响应时间性能测试完成！");
                    println!("📊 请查看生成的性能分析报告:");
                    println!("  - tests/reports/api_performance_analysis_report.md");
                    println!("  - tests/reports/api_response_validation_test_report.md");
                }
                Err(e) => {
                    println!("❌ API响应时间性能测试失败: {}", e);
                    println!("💡 建议检查:");
                    println!("  1. 服务器是否正在运行 (cargo run -p server)");
                    println!("  2. 网络连接是否正常");
                    println!("  3. 数据库是否可访问");
                }
            }
        }
        Err(e) => {
            println!("❌ 测试环境设置失败: {}", e);
            println!("💡 建议:");
            println!("  1. 确保Axum服务器正在运行: cargo run -p server");
            println!("  2. 检查端口3000是否被占用");
            println!("  3. 验证数据库连接");

            // 即使设置失败，也尝试清理
            let _ = test_suite.cleanup().await;
            return Err(e);
        }
    }

    // 清理测试环境
    println!("🧹 清理测试环境...");
    match test_suite.cleanup().await {
        Ok(_) => {
            println!("✅ 测试环境清理完成");
        }
        Err(e) => {
            println!("⚠️ 测试环境清理时出现警告: {}", e);
        }
    }

    println!("🏁 API响应时间性能测试运行器执行完成");
    println!("");
    println!("📈 性能测试功能特性:");
    println!("  ✅ 响应时间基准测试 (< 100ms)");
    println!("  ✅ 性能瓶颈识别 (负载测试)");
    println!("  ✅ 优化建议生成");
    println!("  ✅ 并发性能测试");
    println!("  ✅ 详细性能分析报告");
    println!("  ✅ 统计指标计算 (平均值、中位数、95%、99%)");
    println!("");
    println!("🎯 任务4.3完成状态: ✅ 已实现");

    Ok(())
}
