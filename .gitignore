# Rust特有的忽略文件
/target/
**/*.rs.bk
# Cargo.lock - 对于应用程序，建议将 Cargo.lock 文件提交到版本控制中，以确保构建的可复现性。

# 文档缓存
.docs-cache/

# IDE目录和文件
.idea/
.vscode/
*.iml
*.swp
.DS_Store

# 日志文件
*.log

# 数据库文件
db.sqlite

# 证书目录（包含敏感信息）
/certs/ 

# Logs
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

.env

# Task files
# tasks.json
# tasks/ 
