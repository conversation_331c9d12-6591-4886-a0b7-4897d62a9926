/**
 * 高级搜索界面测试
 * 
 * 测试范围：
 * - 搜索UI组件渲染
 * - 时间范围选择器功能
 * - 多条件过滤功能
 * - 搜索结果展示
 * - 键盘导航支持
 * - 移动端适配
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-23
 */

import { test, expect } from '@playwright/test';

const BASE_URL = 'http://127.0.0.1:3000';
const TEST_USER = {
    username: 'testuser456',
    password: 'password123'
};

test.describe('高级搜索界面测试', () => {
    test.beforeEach(async ({ page }) => {
        // 导航到主页
        await page.goto(BASE_URL);
        
        // 等待页面加载
        await page.waitForLoadState('networkidle');
        
        // 登录测试用户
        await page.fill('#loginUsername', TEST_USER.username);
        await page.fill('#loginPassword', TEST_USER.password);
        await page.click('#loginForm button[type="submit"]');
        
        // 等待登录完成
        await page.waitForTimeout(3000);
        
        // 验证登录成功
        const authStatus = await page.locator('#authStatus').textContent();
        expect(authStatus).toContain('已登录');
    });

    test.describe('搜索UI组件渲染', () => {
        test('应该正确渲染高级搜索界面', async ({ page }) => {
            console.log('🔍 测试高级搜索界面渲染...');
            
            // 打开高级搜索界面
            await page.click('#openAdvancedSearch');
            
            // 验证搜索界面元素存在
            await expect(page.locator('#advancedSearchModal')).toBeVisible();
            await expect(page.locator('#searchKeyword')).toBeVisible();
            await expect(page.locator('#messageTypeFilter')).toBeVisible();
            await expect(page.locator('#senderFilter')).toBeVisible();
            await expect(page.locator('#startDatePicker')).toBeVisible();
            await expect(page.locator('#endDatePicker')).toBeVisible();
            await expect(page.locator('#searchButton')).toBeVisible();
            await expect(page.locator('#clearFiltersButton')).toBeVisible();
            
            console.log('✅ 高级搜索界面渲染正常');
        });

        test('应该显示搜索历史和快捷选项', async ({ page }) => {
            console.log('🔍 测试搜索历史和快捷选项...');
            
            await page.click('#openAdvancedSearch');
            
            // 验证搜索历史区域
            await expect(page.locator('#searchHistory')).toBeVisible();
            await expect(page.locator('#quickSearchOptions')).toBeVisible();
            
            // 验证快捷搜索选项
            const quickOptions = page.locator('.quick-search-option');
            await expect(quickOptions).toHaveCount(4); // 今天、昨天、本周、本月
            
            console.log('✅ 搜索历史和快捷选项显示正常');
        });
    });

    test.describe('时间范围选择器功能', () => {
        test('应该支持日期选择器操作', async ({ page }) => {
            console.log('📅 测试日期选择器功能...');
            
            await page.click('#openAdvancedSearch');
            
            // 设置开始日期
            await page.fill('#startDatePicker', '2025-07-20');
            
            // 设置结束日期
            await page.fill('#endDatePicker', '2025-07-23');
            
            // 验证日期值
            const startDate = await page.inputValue('#startDatePicker');
            const endDate = await page.inputValue('#endDatePicker');
            
            expect(startDate).toBe('2025-07-20');
            expect(endDate).toBe('2025-07-23');
            
            console.log('✅ 日期选择器功能正常');
        });

        test('应该支持快捷时间范围选择', async ({ page }) => {
            console.log('⚡ 测试快捷时间范围选择...');
            
            await page.click('#openAdvancedSearch');
            
            // 点击"今天"快捷选项
            await page.click('[data-quick-range="today"]');
            
            // 验证日期自动填充
            const startDate = await page.inputValue('#startDatePicker');
            const endDate = await page.inputValue('#endDatePicker');
            
            const today = new Date().toISOString().split('T')[0];
            expect(startDate).toBe(today);
            expect(endDate).toBe(today);
            
            console.log('✅ 快捷时间范围选择功能正常');
        });

        test('应该验证日期范围有效性', async ({ page }) => {
            console.log('✅ 测试日期范围验证...');
            
            await page.click('#openAdvancedSearch');
            
            // 设置无效的日期范围（结束日期早于开始日期）
            await page.fill('#startDatePicker', '2025-07-23');
            await page.fill('#endDatePicker', '2025-07-20');
            
            // 点击搜索按钮
            await page.click('#searchButton');
            
            // 验证错误提示
            const errorMessage = page.locator('#dateRangeError');
            await expect(errorMessage).toBeVisible();
            await expect(errorMessage).toContainText('结束日期不能早于开始日期');
            
            console.log('✅ 日期范围验证功能正常');
        });
    });

    test.describe('多条件过滤功能', () => {
        test('应该支持消息类型过滤', async ({ page }) => {
            console.log('🔽 测试消息类型过滤...');
            
            await page.click('#openAdvancedSearch');
            
            // 选择消息类型
            await page.selectOption('#messageTypeFilter', 'text');
            
            // 验证选择值
            const selectedType = await page.inputValue('#messageTypeFilter');
            expect(selectedType).toBe('text');
            
            console.log('✅ 消息类型过滤功能正常');
        });

        test('应该支持发送者过滤', async ({ page }) => {
            console.log('👤 测试发送者过滤...');
            
            await page.click('#openAdvancedSearch');
            
            // 输入发送者用户名
            await page.fill('#senderFilter', 'testuser456');
            
            // 验证输入值
            const senderValue = await page.inputValue('#senderFilter');
            expect(senderValue).toBe('testuser456');
            
            console.log('✅ 发送者过滤功能正常');
        });

        test('应该支持组合条件搜索', async ({ page }) => {
            console.log('🔍 测试组合条件搜索...');
            
            await page.click('#openAdvancedSearch');
            
            // 设置多个搜索条件
            await page.fill('#searchKeyword', '测试消息');
            await page.selectOption('#messageTypeFilter', 'text');
            await page.fill('#senderFilter', 'testuser456');
            await page.fill('#startDatePicker', '2025-07-20');
            await page.fill('#endDatePicker', '2025-07-23');
            
            // 执行搜索
            await page.click('#searchButton');
            
            // 等待搜索结果
            await page.waitForSelector('#searchResults', { timeout: 10000 });
            
            // 验证搜索结果容器存在
            await expect(page.locator('#searchResults')).toBeVisible();
            
            console.log('✅ 组合条件搜索功能正常');
        });
    });

    test.describe('搜索结果展示', () => {
        test('应该正确显示搜索结果', async ({ page }) => {
            console.log('📋 测试搜索结果展示...');
            
            await page.click('#openAdvancedSearch');
            
            // 执行基本搜索
            await page.fill('#searchKeyword', '测试');
            await page.click('#searchButton');
            
            // 等待搜索结果
            await page.waitForSelector('#searchResults', { timeout: 10000 });
            
            // 验证结果结构
            await expect(page.locator('#searchResultsHeader')).toBeVisible();
            await expect(page.locator('#searchResultsList')).toBeVisible();
            await expect(page.locator('#searchPagination')).toBeVisible();
            
            console.log('✅ 搜索结果展示正常');
        });

        test('应该支持结果分页', async ({ page }) => {
            console.log('📄 测试搜索结果分页...');
            
            await page.click('#openAdvancedSearch');
            
            // 执行搜索
            await page.fill('#searchKeyword', 'test');
            await page.click('#searchButton');
            
            // 等待搜索结果
            await page.waitForSelector('#searchResults', { timeout: 10000 });
            
            // 检查分页控件
            const pagination = page.locator('#searchPagination');
            await expect(pagination).toBeVisible();
            
            // 如果有多页，测试翻页功能
            const nextButton = page.locator('#nextPageButton');
            if (await nextButton.isVisible()) {
                await nextButton.click();
                await page.waitForTimeout(2000);
                console.log('✅ 分页翻页功能正常');
            }
            
            console.log('✅ 搜索结果分页功能正常');
        });

        test('应该支持结果排序', async ({ page }) => {
            console.log('🔄 测试搜索结果排序...');
            
            await page.click('#openAdvancedSearch');
            
            // 执行搜索
            await page.fill('#searchKeyword', 'test');
            await page.click('#searchButton');
            
            // 等待搜索结果
            await page.waitForSelector('#searchResults', { timeout: 10000 });
            
            // 测试排序选项
            const sortSelect = page.locator('#sortOptions');
            if (await sortSelect.isVisible()) {
                await sortSelect.selectOption('date_desc');
                await page.waitForTimeout(1000);
                console.log('✅ 结果排序功能正常');
            }
            
            console.log('✅ 搜索结果排序功能正常');
        });
    });

    test.describe('键盘导航支持', () => {
        test('应该支持键盘快捷键', async ({ page }) => {
            console.log('⌨️ 测试键盘导航...');
            
            await page.click('#openAdvancedSearch');
            
            // 测试Tab键导航
            await page.keyboard.press('Tab');
            await page.keyboard.press('Tab');
            
            // 测试Enter键执行搜索
            await page.fill('#searchKeyword', '键盘测试');
            await page.keyboard.press('Enter');
            
            // 验证搜索被触发
            await page.waitForSelector('#searchResults', { timeout: 10000 });
            
            console.log('✅ 键盘导航功能正常');
        });

        test('应该支持Escape键关闭搜索界面', async ({ page }) => {
            console.log('🔚 测试Escape键关闭功能...');
            
            await page.click('#openAdvancedSearch');
            
            // 验证搜索界面打开
            await expect(page.locator('#advancedSearchModal')).toBeVisible();
            
            // 按Escape键
            await page.keyboard.press('Escape');
            
            // 验证搜索界面关闭
            await expect(page.locator('#advancedSearchModal')).not.toBeVisible();
            
            console.log('✅ Escape键关闭功能正常');
        });
    });

    test.describe('移动端适配', () => {
        test('应该在移动设备上正确显示', async ({ page }) => {
            console.log('📱 测试移动端适配...');
            
            // 设置移动设备视口
            await page.setViewportSize({ width: 375, height: 667 });
            
            await page.click('#openAdvancedSearch');
            
            // 验证移动端布局
            const modal = page.locator('#advancedSearchModal');
            await expect(modal).toBeVisible();
            
            // 验证响应式样式
            const modalClass = await modal.getAttribute('class');
            expect(modalClass).toContain('mobile-responsive');
            
            console.log('✅ 移动端适配正常');
        });

        test('应该支持触摸操作', async ({ page }) => {
            console.log('👆 测试触摸操作...');
            
            // 设置移动设备视口
            await page.setViewportSize({ width: 375, height: 667 });
            
            await page.click('#openAdvancedSearch');
            
            // 测试触摸滑动操作（如果有滑动组件）
            const searchResults = page.locator('#searchResults');
            if (await searchResults.isVisible()) {
                // 模拟触摸滑动
                await page.touchscreen.tap(200, 400);
                console.log('✅ 触摸操作支持正常');
            }
            
            console.log('✅ 移动端触摸操作正常');
        });
    });

    test.describe('性能和用户体验', () => {
        test('应该在合理时间内加载搜索界面', async ({ page }) => {
            console.log('⚡ 测试搜索界面加载性能...');
            
            const startTime = Date.now();
            
            await page.click('#openAdvancedSearch');
            await page.waitForSelector('#advancedSearchModal');
            
            const loadTime = Date.now() - startTime;
            
            // 验证加载时间小于2秒
            expect(loadTime).toBeLessThan(2000);
            
            console.log(`✅ 搜索界面加载时间: ${loadTime}ms`);
        });

        test('应该提供搜索进度指示', async ({ page }) => {
            console.log('🔄 测试搜索进度指示...');
            
            await page.click('#openAdvancedSearch');
            
            // 执行搜索
            await page.fill('#searchKeyword', '进度测试');
            await page.click('#searchButton');
            
            // 验证加载指示器
            const loadingIndicator = page.locator('#searchLoadingIndicator');
            await expect(loadingIndicator).toBeVisible();
            
            // 等待搜索完成
            await page.waitForSelector('#searchResults', { timeout: 10000 });
            
            // 验证加载指示器消失
            await expect(loadingIndicator).not.toBeVisible();
            
            console.log('✅ 搜索进度指示功能正常');
        });
    });
});
