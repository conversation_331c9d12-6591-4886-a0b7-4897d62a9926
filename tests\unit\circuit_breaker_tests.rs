//! # 防雪崩机制测试
//!
//! 测试熔断器、限流器、降级策略等防雪崩机制。
//! 严格遵循TDD原则，确保系统在高负载和故障情况下的稳定性。

use std::time::{Duration, Instant};
use std::sync::{Arc, Mutex};
use std::sync::atomic::{AtomicU64, AtomicBool, Ordering};
use tokio::time::sleep;
use tokio_test;
use anyhow::Result;

/// 熔断器状态枚举
#[derive(Debug, Clone, PartialEq)]
pub enum CircuitBreakerState {
    /// 关闭状态（正常工作）
    Closed,
    /// 开启状态（熔断中）
    Open,
    /// 半开状态（尝试恢复）
    HalfOpen,
}

/// 熔断器配置
#[derive(Debug, Clone)]
pub struct CircuitBreakerConfig {
    /// 失败阈值（失败次数）
    pub failure_threshold: u64,
    /// 成功阈值（半开状态下需要的成功次数）
    pub success_threshold: u64,
    /// 超时时间（开启状态持续时间）
    pub timeout: Duration,
    /// 统计窗口大小
    pub window_size: u64,
}

/// 熔断器实现
pub struct CircuitBreaker {
    config: CircuitBreakerConfig,
    state: Arc<Mutex<CircuitBreakerState>>,
    failure_count: Arc<AtomicU64>,
    success_count: Arc<AtomicU64>,
    last_failure_time: Arc<Mutex<Option<Instant>>>,
    total_requests: Arc<AtomicU64>,
}

impl CircuitBreaker {
    /// 创建新的熔断器
    pub fn new(config: CircuitBreakerConfig) -> Self {
        Self {
            config,
            state: Arc::new(Mutex::new(CircuitBreakerState::Closed)),
            failure_count: Arc::new(AtomicU64::new(0)),
            success_count: Arc::new(AtomicU64::new(0)),
            last_failure_time: Arc::new(Mutex::new(None)),
            total_requests: Arc::new(AtomicU64::new(0)),
        }
    }

    /// 执行操作（带熔断保护）
    pub async fn execute<F, T, E>(&self, operation: F) -> Result<T, CircuitBreakerError<E>>
    where
        F: std::future::Future<Output = Result<T, E>>,
    {
        self.total_requests.fetch_add(1, Ordering::Relaxed);

        // 检查熔断器状态
        if !self.can_execute().await {
            return Err(CircuitBreakerError::CircuitOpen);
        }

        // 执行操作
        match operation.await {
            Ok(result) => {
                self.on_success().await;
                Ok(result)
            }
            Err(error) => {
                self.on_failure().await;
                Err(CircuitBreakerError::OperationFailed(error))
            }
        }
    }

    /// 检查是否可以执行操作
    async fn can_execute(&self) -> bool {
        let state = self.state.lock().unwrap().clone();
        
        match state {
            CircuitBreakerState::Closed => true,
            CircuitBreakerState::Open => {
                // 检查是否可以转换到半开状态
                if let Some(last_failure) = *self.last_failure_time.lock().unwrap() {
                    if last_failure.elapsed() >= self.config.timeout {
                        self.transition_to_half_open().await;
                        return true;
                    }
                }
                false
            }
            CircuitBreakerState::HalfOpen => true,
        }
    }

    /// 处理成功结果
    async fn on_success(&self) {
        self.success_count.fetch_add(1, Ordering::Relaxed);
        
        let state = self.state.lock().unwrap().clone();
        if state == CircuitBreakerState::HalfOpen {
            if self.success_count.load(Ordering::Relaxed) >= self.config.success_threshold {
                self.transition_to_closed().await;
            }
        }
    }

    /// 处理失败结果
    async fn on_failure(&self) {
        self.failure_count.fetch_add(1, Ordering::Relaxed);
        *self.last_failure_time.lock().unwrap() = Some(Instant::now());
        
        let state = self.state.lock().unwrap().clone();
        match state {
            CircuitBreakerState::Closed => {
                if self.failure_count.load(Ordering::Relaxed) >= self.config.failure_threshold {
                    self.transition_to_open().await;
                }
            }
            CircuitBreakerState::HalfOpen => {
                self.transition_to_open().await;
            }
            CircuitBreakerState::Open => {
                // 已经是开启状态，无需操作
            }
        }
    }

    /// 转换到关闭状态
    async fn transition_to_closed(&self) {
        *self.state.lock().unwrap() = CircuitBreakerState::Closed;
        self.failure_count.store(0, Ordering::Relaxed);
        self.success_count.store(0, Ordering::Relaxed);
        tracing::info!("熔断器转换到关闭状态");
    }

    /// 转换到开启状态
    async fn transition_to_open(&self) {
        *self.state.lock().unwrap() = CircuitBreakerState::Open;
        self.success_count.store(0, Ordering::Relaxed);
        tracing::warn!("熔断器转换到开启状态");
    }

    /// 转换到半开状态
    async fn transition_to_half_open(&self) {
        *self.state.lock().unwrap() = CircuitBreakerState::HalfOpen;
        self.success_count.store(0, Ordering::Relaxed);
        tracing::info!("熔断器转换到半开状态");
    }

    /// 获取当前状态
    pub fn get_state(&self) -> CircuitBreakerState {
        self.state.lock().unwrap().clone()
    }

    /// 获取统计信息
    pub fn get_stats(&self) -> CircuitBreakerStats {
        CircuitBreakerStats {
            state: self.get_state(),
            failure_count: self.failure_count.load(Ordering::Relaxed),
            success_count: self.success_count.load(Ordering::Relaxed),
            total_requests: self.total_requests.load(Ordering::Relaxed),
        }
    }
}

/// 熔断器错误类型
#[derive(Debug)]
pub enum CircuitBreakerError<E> {
    /// 熔断器开启，拒绝执行
    CircuitOpen,
    /// 操作执行失败
    OperationFailed(E),
}

/// 熔断器统计信息
#[derive(Debug)]
pub struct CircuitBreakerStats {
    pub state: CircuitBreakerState,
    pub failure_count: u64,
    pub success_count: u64,
    pub total_requests: u64,
}

/// 限流器实现
pub struct RateLimiter {
    /// 每秒允许的请求数
    requests_per_second: u64,
    /// 令牌桶容量
    bucket_capacity: u64,
    /// 当前令牌数
    current_tokens: Arc<AtomicU64>,
    /// 上次补充令牌的时间
    last_refill: Arc<Mutex<Instant>>,
}

impl RateLimiter {
    /// 创建新的限流器
    pub fn new(requests_per_second: u64) -> Self {
        Self {
            requests_per_second,
            bucket_capacity: requests_per_second * 2, // 桶容量为2倍QPS
            current_tokens: Arc::new(AtomicU64::new(requests_per_second * 2)),
            last_refill: Arc::new(Mutex::new(Instant::now())),
        }
    }

    /// 尝试获取令牌
    pub async fn try_acquire(&self) -> bool {
        self.refill_tokens().await;
        
        let current = self.current_tokens.load(Ordering::Relaxed);
        if current > 0 {
            self.current_tokens.fetch_sub(1, Ordering::Relaxed);
            true
        } else {
            false
        }
    }

    /// 补充令牌
    async fn refill_tokens(&self) {
        let now = Instant::now();
        let mut last_refill = self.last_refill.lock().unwrap();
        let elapsed = now.duration_since(*last_refill);
        
        if elapsed >= Duration::from_millis(100) { // 每100ms补充一次
            let tokens_to_add = (elapsed.as_millis() as u64 * self.requests_per_second) / 1000;
            if tokens_to_add > 0 {
                let current = self.current_tokens.load(Ordering::Relaxed);
                let new_tokens = (current + tokens_to_add).min(self.bucket_capacity);
                self.current_tokens.store(new_tokens, Ordering::Relaxed);
                *last_refill = now;
            }
        }
    }

    /// 获取当前令牌数
    pub fn get_current_tokens(&self) -> u64 {
        self.current_tokens.load(Ordering::Relaxed)
    }
}

/// 熔断器开启状态测试
#[tokio::test]
async fn test_circuit_breaker_open_state() {
    tracing::info!("开始熔断器开启状态测试");
    
    let config = CircuitBreakerConfig {
        failure_threshold: 3,
        success_threshold: 2,
        timeout: Duration::from_millis(100),
        window_size: 10,
    };
    
    let circuit_breaker = CircuitBreaker::new(config);
    
    // 初始状态应该是关闭
    assert_eq!(circuit_breaker.get_state(), CircuitBreakerState::Closed);
    
    // 模拟连续失败
    for i in 0..3 {
        let result = circuit_breaker.execute(async {
            Err::<(), &str>("模拟失败")
        }).await;
        
        assert!(result.is_err());
        tracing::info!("第{}次失败", i + 1);
    }
    
    // 验证熔断器已开启
    assert_eq!(circuit_breaker.get_state(), CircuitBreakerState::Open);
    
    // 验证后续请求被快速失败
    let start_time = Instant::now();
    let result = circuit_breaker.execute(async {
        Ok::<(), &str>(())
    }).await;
    let elapsed = start_time.elapsed();
    
    assert!(result.is_err());
    assert!(elapsed < Duration::from_millis(10)); // 快速失败
    
    if let Err(CircuitBreakerError::CircuitOpen) = result {
        tracing::info!("熔断器正确拒绝了请求");
    } else {
        panic!("期望熔断器开启错误");
    }
    
    tracing::info!("熔断器开启状态测试完成");
}

/// 熔断器恢复机制测试
#[tokio::test]
async fn test_circuit_breaker_recovery() {
    tracing::info!("开始熔断器恢复机制测试");
    
    let config = CircuitBreakerConfig {
        failure_threshold: 2,
        success_threshold: 2,
        timeout: Duration::from_millis(50),
        window_size: 10,
    };
    
    let circuit_breaker = CircuitBreaker::new(config);
    
    // 1. 触发熔断
    for _ in 0..2 {
        let _ = circuit_breaker.execute(async {
            Err::<(), &str>("失败")
        }).await;
    }
    
    assert_eq!(circuit_breaker.get_state(), CircuitBreakerState::Open);
    
    // 2. 等待超时，进入半开状态
    sleep(Duration::from_millis(60)).await;
    
    // 3. 执行成功操作，应该进入半开状态
    let result = circuit_breaker.execute(async {
        Ok::<(), &str>(())
    }).await;
    
    assert!(result.is_ok());
    assert_eq!(circuit_breaker.get_state(), CircuitBreakerState::HalfOpen);
    
    // 4. 再次成功，应该恢复到关闭状态
    let result = circuit_breaker.execute(async {
        Ok::<(), &str>(())
    }).await;
    
    assert!(result.is_ok());
    assert_eq!(circuit_breaker.get_state(), CircuitBreakerState::Closed);
    
    // 5. 验证统计信息重置
    let stats = circuit_breaker.get_stats();
    assert_eq!(stats.failure_count, 0);
    assert_eq!(stats.success_count, 0);
    
    tracing::info!("熔断器恢复机制测试完成");
}

/// 用户级别限流测试
#[tokio::test]
async fn test_rate_limiter_per_user() {
    tracing::info!("开始用户级别限流测试");
    
    let rate_limiter = RateLimiter::new(5); // 每秒5个请求
    
    // 1. 快速消耗所有令牌
    let mut acquired_count = 0;
    for _ in 0..10 {
        if rate_limiter.try_acquire().await {
            acquired_count += 1;
        }
    }
    
    // 应该只能获取到初始令牌数（10个，因为桶容量是2倍QPS）
    assert!(acquired_count <= 10, "获取的令牌数不应超过桶容量");
    tracing::info!("快速获取了{}个令牌", acquired_count);
    
    // 2. 验证限流生效
    let limited_result = rate_limiter.try_acquire().await;
    assert!(!limited_result, "应该被限流");
    
    // 3. 等待令牌补充
    sleep(Duration::from_millis(200)).await;
    
    // 4. 验证令牌已补充
    let refilled_result = rate_limiter.try_acquire().await;
    assert!(refilled_result, "令牌应该已补充");
    
    tracing::info!("用户级别限流测试完成");
}

/// 全局限流测试
#[tokio::test]
async fn test_global_rate_limiter() {
    tracing::info!("开始全局限流测试");
    
    let global_rate_limiter = Arc::new(RateLimiter::new(100)); // 每秒100个请求
    
    // 模拟多个并发用户
    let concurrent_users = 10;
    let requests_per_user = 20;
    
    let mut tasks = Vec::new();
    
    for user_id in 0..concurrent_users {
        let rate_limiter = global_rate_limiter.clone();
        
        let task = tokio::spawn(async move {
            let mut successful_requests = 0;
            let mut failed_requests = 0;
            
            for _ in 0..requests_per_user {
                if rate_limiter.try_acquire().await {
                    successful_requests += 1;
                } else {
                    failed_requests += 1;
                }
                
                // 模拟请求间隔
                sleep(Duration::from_millis(10)).await;
            }
            
            (user_id, successful_requests, failed_requests)
        });
        
        tasks.push(task);
    }
    
    // 等待所有任务完成
    let results = futures::future::join_all(tasks).await;
    
    let mut total_successful = 0;
    let mut total_failed = 0;
    
    for result in results {
        let (user_id, successful, failed) = result.expect("任务执行失败");
        total_successful += successful;
        total_failed += failed;
        tracing::info!("用户{}: 成功{}次, 失败{}次", user_id, successful, failed);
    }
    
    // 验证全局限流效果
    let total_requests = total_successful + total_failed;
    let success_rate = total_successful as f64 / total_requests as f64;
    
    tracing::info!("全局限流结果: 总请求{}, 成功{}, 失败{}, 成功率{:.2}%", 
        total_requests, total_successful, total_failed, success_rate * 100.0);
    
    // 应该有一定比例的请求被限流
    assert!(total_failed > 0, "应该有请求被限流");
    assert!(success_rate < 1.0, "成功率应该小于100%");
    
    tracing::info!("全局限流测试完成");
}

/// 降级策略测试
#[tokio::test]
async fn test_degradation_strategy() {
    tracing::info!("开始降级策略测试");
    
    let circuit_breaker = Arc::new(CircuitBreaker::new(CircuitBreakerConfig {
        failure_threshold: 2,
        success_threshold: 1,
        timeout: Duration::from_millis(100),
        window_size: 10,
    }));
    
    // 模拟搜索服务
    let search_service = |query: &str, use_fallback: bool| async move {
        if use_fallback {
            // 降级到简单搜索
            Ok(format!("降级搜索结果: {}", query))
        } else {
            // 模拟主搜索服务故障
            Err("主搜索服务不可用")
        }
    };
    
    // 1. 触发熔断
    for _ in 0..2 {
        let _ = circuit_breaker.execute(search_service("测试", false)).await;
    }
    
    assert_eq!(circuit_breaker.get_state(), CircuitBreakerState::Open);
    
    // 2. 使用降级策略
    let degraded_result = search_service("测试查询", true).await;
    assert!(degraded_result.is_ok());
    assert!(degraded_result.unwrap().contains("降级搜索结果"));
    
    // 3. 验证降级服务的性能
    let start_time = Instant::now();
    let _ = search_service("性能测试", true).await;
    let elapsed = start_time.elapsed();
    
    // 降级服务应该响应更快
    assert!(elapsed < Duration::from_millis(50), 
        "降级服务响应时间不达标，耗时: {:?}", elapsed);
    
    tracing::info!("降级策略测试完成");
}

/// 防雪崩综合测试
#[tokio::test]
async fn test_comprehensive_avalanche_protection() {
    tracing::info!("开始防雪崩综合测试");
    
    let circuit_breaker = Arc::new(CircuitBreaker::new(CircuitBreakerConfig {
        failure_threshold: 5,
        success_threshold: 3,
        timeout: Duration::from_millis(200),
        window_size: 20,
    }));
    
    let rate_limiter = Arc::new(RateLimiter::new(50)); // 每秒50个请求
    
    // 模拟高负载场景
    let concurrent_requests = 100;
    let mut tasks = Vec::new();
    
    for request_id in 0..concurrent_requests {
        let cb = circuit_breaker.clone();
        let rl = rate_limiter.clone();
        
        let task = tokio::spawn(async move {
            // 1. 首先检查限流
            if !rl.try_acquire().await {
                return (request_id, "rate_limited", Duration::from_millis(0));
            }
            
            // 2. 通过熔断器执行操作
            let start_time = Instant::now();
            let result = cb.execute(async {
                // 模拟部分请求失败
                if request_id % 10 < 3 {
                    Err("服务故障")
                } else {
                    sleep(Duration::from_millis(10)).await; // 模拟处理时间
                    Ok("成功")
                }
            }).await;
            
            let elapsed = start_time.elapsed();
            
            match result {
                Ok(_) => (request_id, "success", elapsed),
                Err(CircuitBreakerError::CircuitOpen) => (request_id, "circuit_open", elapsed),
                Err(CircuitBreakerError::OperationFailed(_)) => (request_id, "operation_failed", elapsed),
            }
        });
        
        tasks.push(task);
    }
    
    // 等待所有任务完成
    let results = futures::future::join_all(tasks).await;
    
    // 统计结果
    let mut success_count = 0;
    let mut rate_limited_count = 0;
    let mut circuit_open_count = 0;
    let mut operation_failed_count = 0;
    let mut total_elapsed = Duration::from_millis(0);
    
    for result in results {
        let (_, status, elapsed) = result.expect("任务执行失败");
        total_elapsed += elapsed;
        
        match status {
            "success" => success_count += 1,
            "rate_limited" => rate_limited_count += 1,
            "circuit_open" => circuit_open_count += 1,
            "operation_failed" => operation_failed_count += 1,
            _ => {}
        }
    }
    
    let average_elapsed = total_elapsed / concurrent_requests as u32;
    
    tracing::info!("防雪崩综合测试结果:");
    tracing::info!("  成功: {}", success_count);
    tracing::info!("  限流: {}", rate_limited_count);
    tracing::info!("  熔断: {}", circuit_open_count);
    tracing::info!("  失败: {}", operation_failed_count);
    tracing::info!("  平均响应时间: {:?}", average_elapsed);
    
    // 验证防雪崩机制有效
    assert!(rate_limited_count > 0, "应该有请求被限流");
    assert!(success_count > 0, "应该有请求成功");
    assert!(average_elapsed < Duration::from_millis(100), 
        "平均响应时间应该合理");
    
    // 验证系统没有完全崩溃
    let total_processed = success_count + operation_failed_count + circuit_open_count;
    assert!(total_processed > 0, "系统应该处理了一些请求");
    
    tracing::info!("防雪崩综合测试完成");
}
