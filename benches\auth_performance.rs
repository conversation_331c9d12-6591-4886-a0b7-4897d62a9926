use criterion::{BenchmarkId, Criterion, Throughput, criterion_group, criterion_main};
use std::hint::black_box;
use tokio::runtime::Runtime;

use app_common::{
    middleware::{AuthenticatedUser, create_default_permission_checker},
    utils::jwt_utils::{ExtendedClaims, JwtUtils},
};
use app_interfaces::auth::{Claims, Permission, UserRole};

/// 性能基准测试：JWT令牌验证
/// 目标：<1ms响应时间
fn bench_jwt_validation(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let jwt_utils = JwtUtils::new("test_secret_key_for_benchmarking_purposes_only".to_string());

    // 创建测试用的JWT令牌
    let token = jwt_utils.create_token("test_user", "test_user", 1).unwrap();
    let extended_token = jwt_utils
        .create_token_with_role("test_user", "test_user", UserRole::User, 1)
        .unwrap();

    let bearer_token = format!("Bearer {}", token);
    let extended_bearer_token = format!("Bearer {}", extended_token);

    let mut group = c.benchmark_group("JWT验证性能");
    group.throughput(Throughput::Elements(1));

    // 基础JWT令牌验证
    group.bench_function("基础JWT验证", |b| {
        b.iter(|| jwt_utils.validate_token(black_box(&token)));
    });

    // Bearer令牌验证
    group.bench_function("Bearer令牌验证", |b| {
        b.iter(|| jwt_utils.validate_bearer_token(black_box(&bearer_token)));
    });

    // 扩展JWT令牌验证
    group.bench_function("扩展JWT验证", |b| {
        b.iter(|| jwt_utils.validate_token_with_role(black_box(&extended_token)));
    });

    // Bearer扩展令牌验证
    group.bench_function("Bearer扩展令牌验证", |b| {
        b.iter(|| jwt_utils.validate_bearer_token_with_role(black_box(&extended_bearer_token)));
    });

    group.finish();
}

/// 性能基准测试：权限检查
/// 目标：<0.5ms响应时间
fn bench_permission_checking(c: &mut Criterion) {
    let permission_checker = create_default_permission_checker();

    let user_role = UserRole::User;
    let admin_role = UserRole::Admin;
    let manager_role = UserRole::Manager;

    let mut group = c.benchmark_group("权限检查性能");
    group.throughput(Throughput::Elements(1));

    // 基础权限检查
    group.bench_function("读权限检查", |b| {
        b.iter(|| {
            permission_checker.has_permission(black_box(&user_role), black_box(&Permission::Read))
        });
    });

    group.bench_function("写权限检查", |b| {
        b.iter(|| {
            permission_checker.has_permission(black_box(&user_role), black_box(&Permission::Write))
        });
    });

    group.bench_function("删除权限检查", |b| {
        b.iter(|| {
            permission_checker
                .has_permission(black_box(&manager_role), black_box(&Permission::Delete))
        });
    });

    group.bench_function("管理员权限检查", |b| {
        b.iter(|| {
            permission_checker.has_permission(black_box(&admin_role), black_box(&Permission::Admin))
        });
    });

    // 批量权限检查
    let permissions = vec![Permission::Read, Permission::Write, Permission::Delete];
    group.bench_function("批量权限检查", |b| {
        b.iter(|| {
            permission_checker
                .check_multiple_permissions(black_box(&admin_role), black_box(&permissions))
        });
    });

    group.finish();
}

/// 性能基准测试：AuthenticatedUser创建
/// 测试从Claims创建AuthenticatedUser的性能
fn bench_authenticated_user_creation(c: &mut Criterion) {
    let claims = Claims {
        sub: "test_user".to_string(),
        username: "test_user".to_string(),
        exp: (chrono::Utc::now() + chrono::Duration::hours(1)).timestamp(),
        iat: chrono::Utc::now().timestamp(),
    };

    let extended_claims = ExtendedClaims {
        sub: "test_user".to_string(),
        username: "test_user".to_string(),
        role: UserRole::User.as_str().to_string(),
        exp: (chrono::Utc::now() + chrono::Duration::hours(1)).timestamp(),
        iat: chrono::Utc::now().timestamp(),
        nbf: Some(chrono::Utc::now().timestamp()),
        iss: Some("axum-tutorial".to_string()),
        aud: Some("axum-tutorial-users".to_string()),
        jti: Some(uuid::Uuid::new_v4().to_string()),
    };

    let mut group = c.benchmark_group("AuthenticatedUser创建性能");
    group.throughput(Throughput::Elements(1));

    group.bench_function("从基础Claims创建", |b| {
        b.iter(|| AuthenticatedUser::from_claims(black_box(claims.clone())));
    });

    group.bench_function("从扩展Claims创建", |b| {
        b.iter(|| AuthenticatedUser::from_extended_claims(black_box(extended_claims.clone())));
    });

    group.finish();
}

/// 性能基准测试：并发认证处理
/// 目标：>100k QPS
fn bench_concurrent_auth(c: &mut Criterion) {
    let mut group = c.benchmark_group("并发认证性能");

    // 测试不同并发级别
    for concurrency in [10, 50, 100, 500, 1000].iter() {
        group.throughput(Throughput::Elements(*concurrency as u64));
        group.bench_with_input(
            BenchmarkId::new("并发JWT验证", concurrency),
            concurrency,
            |b, &concurrency| {
                b.iter(|| {
                    let jwt_utils =
                        JwtUtils::new("test_secret_key_for_benchmarking_purposes_only".to_string());

                    // 创建测试令牌
                    let tokens: Vec<String> = (0..concurrency)
                        .map(|i| {
                            jwt_utils
                                .create_token(&format!("user_{}", i), &format!("user_{}", i), 1)
                                .unwrap()
                        })
                        .collect();

                    // 并发验证令牌
                    for token in &tokens {
                        let _ = jwt_utils.validate_token(token);
                    }
                });
            },
        );
    }

    group.finish();
}

criterion_group!(
    auth_benches,
    bench_jwt_validation,
    bench_permission_checking,
    bench_authenticated_user_creation,
    bench_concurrent_auth
);
criterion_main!(auth_benches);
