# cargo-llvm-cov 配置文件
# 用于Axum项目的代码覆盖率分析配置

[report]
# 输出格式配置
html = true
lcov = true
json = true
text = true

# HTML报告配置
html-dir = "target/cov/html"

# LCOV报告配置  
lcov-file = "target/cov/lcov.info"

# JSON报告配置
json-file = "target/cov/coverage.json"

# 覆盖率阈值配置
fail-under-lines = 90
fail-under-branches = 85

[filter]
# 排除不需要覆盖率分析的文件和目录
exclude = [
    "tests/*",
    "benches/*", 
    "examples/*",
    "target/*",
    "scripts/*",
    "migrations/*",
    ".cargo/*",
    "*/target/*"
]

# 包含需要分析的文件模式
include = [
    "src/**/*.rs",
    "server/**/*.rs"
]

[instrument]
# 启用分支覆盖率
branch = true

# 启用条件覆盖率
condition = true

# 启用决策覆盖率  
decision = true

# 启用MC/DC覆盖率（修改条件/决策覆盖率）
mcdc = true

[output]
# 输出详细信息
verbose = true

# 显示未覆盖的行
show-missing-lines = true

# 显示函数覆盖率
show-functions = true

# 显示分支覆盖率详情
show-branches = true

# 按文件排序输出
sort-by-file = true

[workspace]
# 工作空间配置
all-features = true
no-default-features = false

# 排除工作空间成员
exclude-members = []

[test]
# 测试配置
all-targets = true
doc = true
examples = false
benches = false

# 测试超时时间（秒）
timeout = 300

# 并行测试数量
jobs = 4

[env]
# 环境变量配置
RUST_BACKTRACE = "1"
RUST_LOG = "info"

# 数据库配置（用于测试）
DATABASE_URL = "sqlite::memory:"
TEST_DATABASE_URL = "sqlite::memory:"

# 服务器配置
SERVER_HOST = "127.0.0.1"
SERVER_PORT = "3000"

[ignore]
# 忽略特定的函数或模块
functions = [
    "main",
    "test_*",
    "*::test_*"
]

# 忽略特定的文件
files = [
    "src/main.rs",
    "*/tests.rs",
    "*/test_*.rs"
]

# 忽略特定的模块
modules = [
    "tests",
    "test_utils",
    "fixtures"
]
