//! # 任务管理CRUD操作E2E测试辅助模块
//!
//! 本模块提供任务管理CRUD操作的E2E测试辅助函数
//! 遵循Context7 MCP最佳实践和rust_axum_Rules.md规范：
//! - 使用清晰的函数命名（create_task、fetch_task、update_task、delete_task等）
//! - 详细的中文注释
//! - 完整的错误处理
//! - 遵循DRY和SOLID原则

use super::E2EConfig;
use anyhow::Result;
use serde_json::{Value, json};
use uuid::Uuid;

/// 任务CRUD操作辅助结构
#[derive(Clone)]
pub struct TaskCrudHelper {
    config: E2EConfig,
    client: reqwest::Client,
}

/// 测试任务数据结构
#[derive(Debug, Clone)]
pub struct TestTaskData {
    pub title: String,
    pub description: Option<String>,
    pub completed: bool,
    pub status: Option<String>, // 新增status字段
    pub priority: Option<String>,
    pub due_date: Option<String>,
}

impl Default for TestTaskData {
    fn default() -> Self {
        Self {
            title: "E2E测试任务".to_string(),
            description: Some("这是一个E2E测试任务描述".to_string()),
            completed: false,
            status: Some("pending".to_string()), // 默认状态为pending
            priority: Some("medium".to_string()),
            due_date: None,
        }
    }
}

impl TestTaskData {
    /// 创建新的测试任务数据
    pub fn new(title: &str) -> Self {
        Self {
            title: title.to_string(),
            ..Default::default()
        }
    }

    /// 设置任务描述
    pub fn with_description(mut self, description: &str) -> Self {
        self.description = Some(description.to_string());
        self
    }

    /// 设置任务完成状态
    pub fn with_completed(mut self, completed: bool) -> Self {
        self.completed = completed;
        // 同时设置对应的status
        self.status = Some(if completed {
            "completed".to_string()
        } else {
            "pending".to_string()
        });
        self
    }

    /// 设置任务状态
    pub fn with_status(mut self, status: &str) -> Self {
        self.status = Some(status.to_string());
        // 同时设置对应的completed字段
        self.completed = status == "completed";
        self
    }

    /// 设置任务优先级
    pub fn with_priority(mut self, priority: &str) -> Self {
        self.priority = Some(priority.to_string());
        self
    }

    /// 设置任务截止日期
    pub fn with_due_date(mut self, due_date: &str) -> Self {
        self.due_date = Some(due_date.to_string());
        self
    }

    /// 转换为JSON值
    pub fn to_json(&self) -> Value {
        let mut json = json!({});

        // 只有非空标题才添加title字段
        if !self.title.is_empty() {
            json["title"] = json!(self.title);
        }

        if let Some(ref description) = self.description {
            json["description"] = json!(description);
        }

        // 使用status字段而不是completed字段
        if let Some(ref status) = self.status {
            json["status"] = json!(status);
        }

        if let Some(ref priority) = self.priority {
            json["priority"] = json!(priority);
        }

        if let Some(ref due_date) = self.due_date {
            json["due_date"] = json!(due_date);
        }

        json
    }
}

impl TaskCrudHelper {
    /// 创建新的任务CRUD辅助实例
    pub fn new(config: E2EConfig) -> Self {
        Self {
            config,
            client: reqwest::Client::new(),
        }
    }

    /// 创建任务
    pub async fn create_task(&self, token: &str, task_data: &TestTaskData) -> Result<Value> {
        let url = format!("{}/api/tasks", self.config.base_url);
        let payload = task_data.to_json();

        let response = self
            .client
            .post(&url)
            .bearer_auth(token)
            .json(&payload)
            .send()
            .await?;

        let status = response.status();
        let body: Value = response.json().await?;

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 获取任务列表
    pub async fn fetch_tasks_list(&self, token: &str) -> Result<Value> {
        let url = format!("{}/api/tasks", self.config.base_url);

        let response = self.client.get(&url).bearer_auth(token).send().await?;

        let status = response.status();
        let response_text = response.text().await?;

        // 尝试解析JSON，如果失败则返回错误信息
        let body: Value = if response_text.trim().is_empty() {
            json!({"error": "Empty response from server"})
        } else {
            match serde_json::from_str(&response_text) {
                Ok(json) => json,
                Err(_) => {
                    json!({"error": "Invalid JSON response", "raw_response": response_text})
                }
            }
        };

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 获取单个任务详情
    pub async fn fetch_task_by_id(&self, token: &str, task_id: &str) -> Result<Value> {
        let url = format!("{}/api/tasks/{}", self.config.base_url, task_id);

        let response = self.client.get(&url).bearer_auth(token).send().await?;

        let status = response.status();
        let response_text = response.text().await?;

        // 尝试解析JSON，如果失败则返回错误信息
        let body: Value = if response_text.trim().is_empty() {
            json!({"error": "Empty response from server"})
        } else {
            match serde_json::from_str(&response_text) {
                Ok(json) => json,
                Err(_) => {
                    json!({"error": "Invalid JSON response", "raw_response": response_text})
                }
            }
        };

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 更新任务
    pub async fn update_task(
        &self,
        token: &str,
        task_id: &str,
        task_data: &TestTaskData,
    ) -> Result<Value> {
        let url = format!("{}/api/tasks/{}", self.config.base_url, task_id);
        let payload = task_data.to_json();

        let response = self
            .client
            .put(&url)
            .bearer_auth(token)
            .json(&payload)
            .send()
            .await?;

        let status = response.status();
        let body: Value = response.json().await?;

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 删除任务
    pub async fn delete_task(&self, token: &str, task_id: &str) -> Result<Value> {
        let url = format!("{}/api/tasks/{}", self.config.base_url, task_id);

        let response = self.client.delete(&url).bearer_auth(token).send().await?;

        let status = response.status();

        // 删除操作可能返回空响应体
        let body = match response.text().await {
            Ok(text) if !text.is_empty() => {
                serde_json::from_str(&text).unwrap_or_else(|_| json!({"message": text}))
            }
            _ => json!({"message": "删除成功"}),
        };

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 批量创建任务
    pub async fn create_multiple_tasks(
        &self,
        token: &str,
        tasks: &[TestTaskData],
    ) -> Result<Vec<Value>> {
        let mut results = Vec::new();

        for task in tasks {
            let result = self.create_task(token, task).await?;
            results.push(result);
        }

        Ok(results)
    }

    /// 验证任务响应格式
    pub fn validate_task_response(&self, response: &Value) -> Result<()> {
        // 验证必需字段存在
        if !response["data"]["id"].is_string() {
            return Err(anyhow::anyhow!("任务响应缺少id字段"));
        }

        if !response["data"]["title"].is_string() {
            return Err(anyhow::anyhow!("任务响应缺少title字段"));
        }

        if !response["data"]["completed"].is_boolean() {
            return Err(anyhow::anyhow!("任务响应缺少completed字段"));
        }

        if !response["data"]["created_at"].is_string() {
            return Err(anyhow::anyhow!("任务响应缺少created_at字段"));
        }

        if !response["data"]["updated_at"].is_string() {
            return Err(anyhow::anyhow!("任务响应缺少updated_at字段"));
        }

        Ok(())
    }

    /// 验证任务列表响应格式
    pub fn validate_tasks_list_response(&self, response: &Value) -> Result<()> {
        // 验证数据是数组
        if !response["data"].is_array() {
            return Err(anyhow::anyhow!("任务列表响应data字段应为数组"));
        }

        // 验证每个任务项的格式
        if let Some(tasks) = response["data"].as_array() {
            for task in tasks {
                if !task["id"].is_string() {
                    return Err(anyhow::anyhow!("任务列表项缺少id字段"));
                }
                if !task["title"].is_string() {
                    return Err(anyhow::anyhow!("任务列表项缺少title字段"));
                }
                if !task["completed"].is_boolean() {
                    return Err(anyhow::anyhow!("任务列表项缺少completed字段"));
                }
            }
        }

        Ok(())
    }

    /// 生成测试任务数据
    pub fn generate_test_tasks(count: usize) -> Vec<TestTaskData> {
        (0..count)
            .map(|i| {
                TestTaskData::new(&format!("E2E测试任务 #{}", i + 1))
                    .with_description(&format!("这是第{}个E2E测试任务的描述", i + 1))
                    .with_priority(if i % 3 == 0 {
                        "high"
                    } else if i % 2 == 0 {
                        "medium"
                    } else {
                        "low"
                    })
                    .with_completed(i % 4 == 0)
            })
            .collect()
    }

    /// 清理测试任务（删除所有测试创建的任务）
    pub async fn cleanup_test_tasks(&self, token: &str) -> Result<()> {
        // 获取所有任务
        let tasks_response = self.fetch_tasks_list(token).await?;

        if let Some(tasks) = tasks_response["body"]["data"].as_array() {
            for task in tasks {
                if let Some(title) = task["title"].as_str() {
                    // 只删除测试任务（标题包含"E2E测试任务"）
                    if title.contains("E2E测试任务") {
                        if let Some(id) = task["id"].as_str() {
                            let _ = self.delete_task(token, id).await;
                        }
                    }
                }
            }
        }

        Ok(())
    }
}
