# Debug JWT Token Generation and Validation

Write-Host "Debugging JWT Token..." -ForegroundColor Yellow

# 1. Register and login to get a token
$timestamp = Get-Date -Format "yyyyMMddHHmmss"
$username = "debuguser$timestamp"

$registerBody = @{
    username = $username
    password = "password123"
    confirm_password = "password123"
    email = "<EMAIL>"
} | ConvertTo-Json

Write-Host "Registering user: $username" -ForegroundColor Cyan

try {
    $registerResponse = Invoke-RestMethod -Uri "http://127.0.0.1:3000/api/auth/register" -Method POST -ContentType "application/json" -Body $registerBody
    Write-Host "Registration successful" -ForegroundColor Green
    $userId = $registerResponse.data.id
    Write-Host "User ID: $userId" -ForegroundColor Cyan
} catch {
    Write-Host "Registration failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. Login to get JWT token
$loginBody = @{
    username = $username
    password = "password123"
} | ConvertTo-Json

Write-Host "Logging in..." -ForegroundColor Cyan

try {
    $loginResponse = Invoke-RestMethod -Uri "http://127.0.0.1:3000/api/auth/login" -Method POST -ContentType "application/json" -Body $loginBody
    Write-Host "Login successful" -ForegroundColor Green
    $token = $loginResponse.data.access_token
    Write-Host "JWT Token: $token" -ForegroundColor Cyan
    
    # Decode JWT token (just the payload part for debugging)
    $parts = $token.Split('.')
    if ($parts.Length -eq 3) {
        $payload = $parts[1]
        # Add padding if needed
        while ($payload.Length % 4 -ne 0) {
            $payload += "="
        }
        try {
            $decodedBytes = [System.Convert]::FromBase64String($payload)
            $decodedText = [System.Text.Encoding]::UTF8.GetString($decodedBytes)
            Write-Host "JWT Payload: $decodedText" -ForegroundColor Yellow
        } catch {
            Write-Host "Failed to decode JWT payload: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "Login failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 3. Test user profile API with detailed error info
Write-Host "Testing user profile API..." -ForegroundColor Cyan
$headers = @{"Authorization" = "Bearer $token"}

try {
    $userResponse = Invoke-RestMethod -Uri "http://127.0.0.1:3000/api/users/$userId" -Method GET -Headers $headers
    Write-Host "User profile retrieval successful!" -ForegroundColor Green
    Write-Host "User information:" -ForegroundColor Cyan
    $userResponse.data | ConvertTo-Json -Depth 3
} catch {
    Write-Host "User profile retrieval failed" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        Write-Host "Status Description: $($_.Exception.Response.StatusDescription)" -ForegroundColor Red
    }
}

Write-Host "Debug completed" -ForegroundColor Green
