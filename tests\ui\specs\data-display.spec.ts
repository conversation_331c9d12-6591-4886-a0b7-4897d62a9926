import { test, expect } from '@playwright/test';
import { MainPage } from '../helpers/main-page';
import { testUsers, testTasks, testConfig } from '../fixtures/test-data';

/**
 * 数据显示功能测试套件
 * 遵循Context7 MCP最佳实践，测试页面数据展示功能
 */
test.describe('数据显示测试', () => {
  let mainPage: MainPage;

  test.beforeEach(async ({ page }) => {
    mainPage = new MainPage(page);
    // 使用静态文件进行测试，避免依赖服务器
    await page.goto('file://' + process.cwd() + '/static/index.html');
    await mainPage.waitForPageLoad();
  });

  test.describe('页面布局和元素显示', () => {
    test('应该正确显示页面标题和主要布局', async ({ page }) => {
      // 验证页面标题
      await expect(page).toHaveTitle(/Axum 任务管理系统/);
      
      // 验证主标题元素
      const pageTitle = page.getByRole('heading', { name: /Axum 任务管理系统/i });
      await expect(pageTitle).toBeVisible();
      
      // 验证主容器
      const mainContainer = page.locator('.main-container');
      await expect(mainContainer).toBeVisible();
      
      // 验证三栏布局
      const panels = page.locator('.panel');
      await expect(panels).toHaveCount(3);
      
      // 验证每个面板都可见
      for (let i = 0; i < 3; i++) {
        await expect(panels.nth(i)).toBeVisible();
      }
    });

    test('应该正确显示认证面板元素', async ({ page }) => {
      const authPanel = page.locator('.panel').first();
      await expect(authPanel).toBeVisible();

      // 验证认证面板标题 - 使用first()避免严格模式错误
      const authTitle = authPanel.getByRole('heading', { name: /用户认证|登录|注册/i }).first();
      await expect(authTitle).toBeVisible();
      
      // 验证表单元素存在
      const forms = authPanel.locator('form');
      expect(await forms.count()).toBeGreaterThan(0);
      
      // 验证输入框存在
      const inputs = authPanel.locator('input');
      expect(await inputs.count()).toBeGreaterThan(0);
      
      // 验证按钮存在
      const buttons = authPanel.locator('button');
      expect(await buttons.count()).toBeGreaterThan(0);
    });

    test('应该正确显示任务管理面板元素', async ({ page }) => {
      const taskPanel = page.locator('.panel').nth(1);
      await expect(taskPanel).toBeVisible();

      // 验证任务面板标题 - 使用first()避免严格模式错误
      const taskTitle = taskPanel.getByRole('heading', { name: /任务管理|任务列表/i }).first();
      await expect(taskTitle).toBeVisible();
      
      // 验证任务列表容器
      const taskList = taskPanel.locator('#taskList, .task-list');
      if (await taskList.count() > 0) {
        await expect(taskList.first()).toBeVisible();
      }
      
      // 验证任务创建表单
      const taskForm = taskPanel.locator('form, .task-form');
      if (await taskForm.count() > 0) {
        await expect(taskForm.first()).toBeVisible();
      }
    });

    test('应该正确显示聊天面板元素', async ({ page }) => {
      const chatPanel = page.locator('.panel').nth(2);
      await expect(chatPanel).toBeVisible();

      // 验证聊天面板标题 - 使用first()避免严格模式错误
      const chatTitle = chatPanel.getByRole('heading', { name: /WebSocket|聊天|消息/i }).first();
      await expect(chatTitle).toBeVisible();
      
      // 验证聊天相关元素
      const chatElements = chatPanel.locator('.chat-area, .messages-container, .input-area');
      expect(await chatElements.count()).toBeGreaterThan(0);
    });
  });

  test.describe('响应式设计测试', () => {
    test('应该在桌面分辨率下正确显示', async ({ page }) => {
      await page.setViewportSize(testConfig.viewports.desktop);
      await page.reload();
      
      // 验证三栏布局在桌面下正常显示
      const mainContainer = page.locator('.main-container');
      await expect(mainContainer).toBeVisible();
      
      const panels = page.locator('.panel');
      await expect(panels).toHaveCount(3);
      
      // 验证面板水平排列
      const firstPanel = panels.first();
      const secondPanel = panels.nth(1);
      
      const firstBox = await firstPanel.boundingBox();
      const secondBox = await secondPanel.boundingBox();
      
      if (firstBox && secondBox) {
        // 在桌面布局下，第二个面板应该在第一个面板右侧
        expect(secondBox.x).toBeGreaterThan(firstBox.x);
      }
    });

    test('应该在平板分辨率下正确显示', async ({ page }) => {
      await page.setViewportSize(testConfig.viewports.tablet);
      await page.reload();
      
      const mainContainer = page.locator('.main-container');
      await expect(mainContainer).toBeVisible();
      
      const panels = page.locator('.panel');
      await expect(panels).toHaveCount(3);
      
      // 验证所有面板仍然可见
      for (let i = 0; i < 3; i++) {
        await expect(panels.nth(i)).toBeVisible();
      }
    });

    test('应该在移动设备分辨率下正确显示', async ({ page }) => {
      await page.setViewportSize(testConfig.viewports.mobile);
      await page.reload();
      
      const mainContainer = page.locator('.main-container');
      await expect(mainContainer).toBeVisible();
      
      const panels = page.locator('.panel');
      await expect(panels).toHaveCount(3);
      
      // 在移动布局下，面板应该垂直堆叠
      const firstPanel = panels.first();
      const secondPanel = panels.nth(1);
      
      const firstBox = await firstPanel.boundingBox();
      const secondBox = await secondPanel.boundingBox();
      
      if (firstBox && secondBox) {
        // 在移动布局下，第二个面板应该在第一个面板下方
        expect(secondBox.y).toBeGreaterThan(firstBox.y);
      }
    });
  });

  test.describe('表单元素交互测试', () => {
    test('应该能够与输入框交互', async ({ page }) => {
      // 查找所有输入框
      const inputs = page.locator('input[type="text"], input[type="email"], input[type="password"], input:not([type]), textarea');
      const inputCount = await inputs.count();
      
      if (inputCount > 0) {
        // 测试第一个文本输入框
        const firstInput = inputs.first();
        await expect(firstInput).toBeVisible();
        await expect(firstInput).toBeEditable();
        
        // 填写测试数据
        await firstInput.fill('测试输入');
        await expect(firstInput).toHaveValue('测试输入');
        
        // 清空输入框
        await firstInput.clear();
        await expect(firstInput).toHaveValue('');
      }
    });

    test('应该能够与按钮交互', async ({ page }) => {
      // 查找可见的按钮
      const visibleButtons = page.locator('button:visible');
      const buttonCount = await visibleButtons.count();

      expect(buttonCount).toBeGreaterThan(0);

      // 测试第一个可见按钮
      const firstButton = visibleButtons.first();
      await expect(firstButton).toBeVisible();

      // 验证按钮可点击（不实际点击，避免触发网络请求）
      await expect(firstButton).toBeEnabled();
    });

    test('应该正确处理表单验证', async ({ page }) => {
      // 查找必填输入框
      const requiredInputs = page.locator('input[required]');
      const requiredCount = await requiredInputs.count();
      
      if (requiredCount > 0) {
        const firstRequired = requiredInputs.first();
        await expect(firstRequired).toBeVisible();
        await expect(firstRequired).toHaveAttribute('required');
      }
    });
  });

  test.describe('视觉元素测试', () => {
    test('应该正确显示样式和动画', async ({ page }) => {
      const mainContainer = page.locator('.main-container');
      
      // 验证CSS样式应用
      const backgroundColor = await mainContainer.evaluate(el => 
        window.getComputedStyle(el).backgroundColor
      );
      expect(backgroundColor).toBeTruthy();
      
      // 验证面板样式
      const panels = page.locator('.panel');
      const firstPanel = panels.first();
      
      const borderRadius = await firstPanel.evaluate(el => 
        window.getComputedStyle(el).borderRadius
      );
      expect(borderRadius).toBeTruthy();
    });

    test('应该支持悬停效果', async ({ page }) => {
      // 查找可见的按钮
      const visibleButtons = page.locator('button:visible');

      if (await visibleButtons.count() > 0) {
        const firstButton = visibleButtons.first();

        // 获取初始样式
        const initialColor = await firstButton.evaluate(el =>
          window.getComputedStyle(el).backgroundColor
        );

        // 悬停
        await firstButton.hover();

        // 验证悬停后样式可能发生变化（不强制要求变化，因为CSS可能不同）
        const hoverColor = await firstButton.evaluate(el =>
          window.getComputedStyle(el).backgroundColor
        );

        expect(hoverColor).toBeTruthy();
      }
    });

    test('应该正确处理焦点状态', async ({ page }) => {
      const inputs = page.locator('input');
      
      if (await inputs.count() > 0) {
        const firstInput = inputs.first();
        
        // 聚焦输入框
        await firstInput.focus();
        
        // 验证焦点状态
        await expect(firstInput).toBeFocused();
      }
    });
  });

  test.describe('可访问性测试', () => {
    test('应该具有正确的语义结构', async ({ page }) => {
      // 验证页面具有主要的语义元素
      const main = page.locator('main, [role="main"]');
      const headers = page.locator('h1, h2, h3, h4, h5, h6');
      
      expect(await headers.count()).toBeGreaterThan(0);
      
      // 验证表单标签
      const labels = page.locator('label');
      const labelCount = await labels.count();
      
      if (labelCount > 0) {
        // 验证标签与输入框的关联
        const firstLabel = labels.first();
        const forAttribute = await firstLabel.getAttribute('for');
        
        if (forAttribute) {
          const associatedInput = page.locator(`#${forAttribute}`);
          await expect(associatedInput).toBeVisible();
        }
      }
    });

    test('应该支持键盘导航', async ({ page }) => {
      // 测试Tab键导航
      await page.keyboard.press('Tab');
      
      // 验证焦点移动到可聚焦元素
      const focusedElement = page.locator(':focus');
      await expect(focusedElement).toBeVisible();
    });
  });
});
