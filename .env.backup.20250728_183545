# Axum Tutorial 环境配置文件
# 用于本地开发和测试

# HTTP 服务器配置
HTTP_ADDR=127.0.0.1:3000

# 数据库配置 - PostgreSQL 17 (与podman-compose.yml保持一致)
DATABASE_URL=postgres://axum_user:axum_secure_password_2025@localhost:5432/axum_tutorial

# PostgreSQL连接池配置
MAX_CONNECTIONS=1000
MIN_CONNECTIONS=10
CONNECTION_TIMEOUT=30
IDLE_TIMEOUT=600
ACQUIRE_TIMEOUT=30

# DragonflyDB缓存配置 (使用WSL2实际IP地址解决网络问题)
CACHE_URL=redis://:dragonfly_secure_password_2025@************:6379
CACHE_DEFAULT_TTL=3600
CACHE_KEY_PREFIX=axum_tutorial:

# JWT 密钥配置（开发环境）
JWT_SECRET=your-secret-key-change-in-production

# 运行环境
ENVIRONMENT=development

# 日志级别
RUST_LOG=info
