//! # WebSocket消息广播修复验证测试
//!
//! 验证WebSocket连接注册与消息通道绑定修复是否有效

use axum::extract::ws::Message;
use std::sync::Arc;
use tokio::sync::mpsc;
use uuid::Uuid;

// 简化的连接管理器用于测试
struct TestConnectionManager {
    connections:
        Arc<tokio::sync::RwLock<std::collections::HashMap<Uuid, mpsc::UnboundedSender<Message>>>>,
    user_connections: Arc<tokio::sync::RwLock<std::collections::HashMap<Uuid, Vec<Uuid>>>>,
}

impl TestConnectionManager {
    fn new() -> Self {
        Self {
            connections: Arc::new(tokio::sync::RwLock::new(std::collections::HashMap::new())),
            user_connections: Arc::new(tokio::sync::RwLock::new(std::collections::HashMap::new())),
        }
    }

    async fn add_connection(
        &self,
        connection_id: Uuid,
        user_id: Uuid,
        sender: mpsc::UnboundedSender<Message>,
    ) -> Result<(), String> {
        // 添加到连接映射
        {
            let mut connections = self.connections.write().await;
            connections.insert(connection_id, sender);
        }

        // 添加到用户映射
        {
            let mut user_connections = self.user_connections.write().await;
            user_connections
                .entry(user_id)
                .or_default()
                .push(connection_id);
        }

        Ok(())
    }

    async fn update_connection_sender(
        &self,
        connection_id: &Uuid,
        sender: mpsc::UnboundedSender<Message>,
    ) -> Result<(), String> {
        let mut connections = self.connections.write().await;
        if connections.contains_key(connection_id) {
            connections.insert(*connection_id, sender);
            Ok(())
        } else {
            Err(format!("连接不存在: {connection_id}"))
        }
    }

    async fn send_to_connection(
        &self,
        connection_id: &Uuid,
        message: Message,
    ) -> Result<(), String> {
        let connections = self.connections.read().await;
        if let Some(sender) = connections.get(connection_id) {
            sender
                .send(message)
                .map_err(|e| format!("发送消息失败: {e}"))
        } else {
            Err(format!("连接不存在: {connection_id}"))
        }
    }

    async fn broadcast_to_all(&self, message: Message) -> Result<usize, String> {
        let connections = self.connections.read().await;
        let mut sent_count = 0;

        for (_connection_id, sender) in connections.iter() {
            if sender.send(message.clone()).is_ok() {
                sent_count += 1;
            }
        }

        Ok(sent_count)
    }

    async fn send_to_user(&self, user_id: &Uuid, message: Message) -> Result<usize, String> {
        let user_connections = self.user_connections.read().await;
        let connections = self.connections.read().await;

        if let Some(connection_ids) = user_connections.get(user_id) {
            let mut sent_count = 0;
            for connection_id in connection_ids {
                if let Some(sender) = connections.get(connection_id) {
                    if sender.send(message.clone()).is_ok() {
                        sent_count += 1;
                    }
                }
            }
            Ok(sent_count)
        } else {
            Ok(0)
        }
    }
}

#[tokio::test]
async fn test_websocket_connection_sender_update() {
    // 创建连接管理器
    let connection_manager = Arc::new(TestConnectionManager::new());

    // 创建测试用户和连接
    let user_id = Uuid::new_v4();
    let connection_id = Uuid::new_v4();

    // 创建初始的占位发送通道
    let (initial_sender, _initial_receiver) = mpsc::unbounded_channel::<Message>();

    // 添加连接
    let result = connection_manager
        .add_connection(connection_id, user_id, initial_sender)
        .await;
    assert!(result.is_ok(), "添加连接应该成功");

    // 创建新的实际发送通道
    let (new_sender, mut new_receiver) = mpsc::unbounded_channel::<Message>();

    // 更新连接的发送通道
    let update_result = connection_manager
        .update_connection_sender(&connection_id, new_sender)
        .await;
    assert!(update_result.is_ok(), "更新发送通道应该成功");

    // 测试消息发送
    let test_message = Message::Text("Hello, WebSocket!".to_string().into());
    let send_result = connection_manager
        .send_to_connection(&connection_id, test_message.clone())
        .await;
    assert!(send_result.is_ok(), "发送消息应该成功");

    // 验证消息是否通过新的发送通道接收
    let received_message = new_receiver.recv().await;
    assert!(received_message.is_some(), "应该接收到消息");

    if let Some(Message::Text(content)) = received_message {
        assert_eq!(content.to_string(), "Hello, WebSocket!", "消息内容应该匹配");
    } else {
        panic!("接收到的消息类型不正确");
    }

    println!("✅ WebSocket连接发送通道更新测试通过");
}

#[tokio::test]
async fn test_websocket_broadcast_functionality() {
    // 创建连接管理器
    let connection_manager = Arc::new(TestConnectionManager::new());

    // 创建多个测试连接
    let mut receivers = Vec::new();

    for i in 0..3 {
        let user_id = Uuid::new_v4();
        let connection_id = Uuid::new_v4();

        // 创建初始占位通道
        let (initial_sender, _) = mpsc::unbounded_channel::<Message>();
        connection_manager
            .add_connection(connection_id, user_id, initial_sender)
            .await
            .unwrap();

        // 创建实际的发送通道并更新
        let (new_sender, receiver) = mpsc::unbounded_channel::<Message>();
        connection_manager
            .update_connection_sender(&connection_id, new_sender)
            .await
            .unwrap();
        receivers.push(receiver);
    }

    // 广播消息
    let broadcast_message = Message::Text("Broadcast test message".to_string().into());
    let broadcast_result = connection_manager.broadcast_to_all(broadcast_message).await;
    assert!(broadcast_result.is_ok(), "广播消息应该成功");

    let sent_count = broadcast_result.unwrap();
    assert_eq!(sent_count, 3, "应该发送给3个连接");

    // 验证所有接收器都收到消息
    for (i, mut receiver) in receivers.into_iter().enumerate() {
        let received = receiver.recv().await;
        assert!(received.is_some(), "连接{}应该接收到消息", i);

        if let Some(Message::Text(content)) = received {
            assert_eq!(
                content.to_string(),
                "Broadcast test message",
                "消息内容应该匹配"
            );
        }
    }

    println!("✅ WebSocket广播消息测试通过");
}

#[tokio::test]
async fn test_websocket_user_specific_messaging() {
    // 创建连接管理器
    let connection_manager = Arc::new(TestConnectionManager::new());

    // 创建目标用户的多个连接
    let target_user_id = Uuid::new_v4();
    let mut target_receivers = Vec::new();

    for i in 0..2 {
        let connection_id = Uuid::new_v4();

        let (initial_sender, _) = mpsc::unbounded_channel::<Message>();
        connection_manager
            .add_connection(connection_id, target_user_id, initial_sender)
            .await
            .unwrap();

        let (new_sender, receiver) = mpsc::unbounded_channel::<Message>();
        connection_manager
            .update_connection_sender(&connection_id, new_sender)
            .await
            .unwrap();
        target_receivers.push(receiver);
    }

    // 创建其他用户连接
    let other_user_id = Uuid::new_v4();
    let other_connection_id = Uuid::new_v4();
    let (initial_sender, _) = mpsc::unbounded_channel::<Message>();
    connection_manager
        .add_connection(other_connection_id, other_user_id, initial_sender)
        .await
        .unwrap();

    // 发送消息给特定用户
    let user_message = Message::Text("User specific message".to_string().into());
    let send_result = connection_manager
        .send_to_user(&target_user_id, user_message)
        .await;
    assert!(send_result.is_ok(), "发送用户消息应该成功");

    let sent_count = send_result.unwrap();
    assert_eq!(sent_count, 2, "应该发送给目标用户的2个连接");

    // 验证目标用户的所有连接都收到消息
    for (i, mut receiver) in target_receivers.into_iter().enumerate() {
        let received = receiver.recv().await;
        assert!(received.is_some(), "目标用户连接{}应该接收到消息", i);

        if let Some(Message::Text(content)) = received {
            assert_eq!(
                content.to_string(),
                "User specific message",
                "消息内容应该匹配"
            );
        }
    }

    println!("✅ WebSocket用户特定消息测试通过");
}
