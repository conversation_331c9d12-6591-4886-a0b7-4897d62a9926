# Apply WSL2 Network Optimization Configuration
Write-Host "Applying WSL2 Network Optimization Configuration..." -ForegroundColor Green

$wslConfigPath = "$env:USERPROFILE\.wslconfig"
$backupPath = "$env:USERPROFILE\.wslconfig.backup"

# Backup existing config if it exists
if (Test-Path $wslConfigPath) {
    Copy-Item $wslConfigPath $backupPath -Force
    Write-Host "Backed up existing config to: $backupPath" -ForegroundColor Yellow
}

# Create optimized WSL2 configuration
$wslConfig = @"
[wsl2]
# Network configuration - Use mirrored mode to solve localhost connection issues
networkingMode=mirrored
dnsTunneling=true
firewall=true
autoProxy=true

# Performance optimization
memory=4GB
processors=2
swap=2GB

# Experimental features
[experimental]
sparseVhd=true
autoMemoryReclaim=gradual
"@

# Write configuration to file
Set-Content -Path $wslConfigPath -Value $wslConfig -Encoding UTF8
Write-Host "WSL2 configuration created: $wslConfigPath" -ForegroundColor Green

Write-Host "`nConfiguration content:" -ForegroundColor Cyan
Write-Host $wslConfig -ForegroundColor Gray

Write-Host "`nIMPORTANT: Please follow these steps to apply the configuration:" -ForegroundColor Yellow
Write-Host "1. Close WSL2: wsl --shutdown" -ForegroundColor White
Write-Host "2. Wait 10 seconds" -ForegroundColor White
Write-Host "3. Restart WSL2: wsl" -ForegroundColor White
Write-Host "4. Restart containers: podman restart axum_dragonflydb" -ForegroundColor White
Write-Host "5. Update .env file to use localhost" -ForegroundColor White
