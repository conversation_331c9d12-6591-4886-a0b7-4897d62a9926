//! # 循环依赖测试
//!
//! 测试项目中是否存在循环依赖问题，确保整洁架构的依赖规则得到遵守。
//!
//! ## 依赖规则
//! - app_domain: 不依赖任何其他内部crate（除了app_common）
//! - app_application: 只依赖app_domain和app_common
//! - app_infrastructure: 只依赖app_domain和app_common（不能依赖app_application）
//! - server: 可以依赖所有其他crate

use std::process::Command;
use std::str;

#[test]
fn test_no_circular_dependencies() {
    // 运行cargo tree检查依赖关系
    let output = Command::new("cargo")
        .args(["tree", "--format", "{p}"])
        .current_dir(".")
        .output()
        .expect("Failed to execute cargo tree");

    let stdout = str::from_utf8(&output.stdout).expect("Invalid UTF-8");

    // 检查是否有循环依赖的迹象
    assert!(!stdout.contains("cyclic"), "发现循环依赖");

    println!("✅ 依赖树检查通过，无循环依赖");
}

#[test]
fn test_infrastructure_not_depend_on_application() {
    // 检查app_infrastructure的Cargo.toml文件
    let cargo_toml = std::fs::read_to_string("crates/app_infrastructure/Cargo.toml")
        .expect("无法读取app_infrastructure的Cargo.toml");

    // app_infrastructure不应该依赖app_application
    assert!(
        !cargo_toml.contains("app_application"),
        "❌ app_infrastructure不应该依赖app_application，这违反了整洁架构原则"
    );

    println!("✅ app_infrastructure没有依赖app_application");
}

#[test]
fn test_domain_layer_purity() {
    // 检查app_domain的Cargo.toml文件
    let cargo_toml = std::fs::read_to_string("crates/app_domain/Cargo.toml")
        .expect("无法读取app_domain的Cargo.toml");

    // app_domain只应该依赖app_common和外部库
    assert!(
        !cargo_toml.contains("app_application"),
        "❌ app_domain不应该依赖app_application"
    );

    assert!(
        !cargo_toml.contains("app_infrastructure"),
        "❌ app_domain不应该依赖app_infrastructure"
    );

    println!("✅ app_domain层保持纯净，只依赖app_common");
}

#[test]
fn test_application_layer_dependencies() {
    // 检查app_application的Cargo.toml文件
    let cargo_toml = std::fs::read_to_string("crates/app_application/Cargo.toml")
        .expect("无法读取app_application的Cargo.toml");

    // app_application不应该依赖app_infrastructure
    assert!(
        !cargo_toml.contains("app_infrastructure"),
        "❌ app_application不应该依赖app_infrastructure"
    );

    // app_application应该依赖app_domain和app_common
    assert!(
        cargo_toml.contains("app_domain"),
        "❌ app_application应该依赖app_domain"
    );

    assert!(
        cargo_toml.contains("app_common"),
        "❌ app_application应该依赖app_common"
    );

    println!("✅ app_application层依赖关系正确");
}

#[test]
fn test_dependency_inversion_principle() {
    // 检查是否正确使用了依赖倒置原则
    // app_application应该定义接口，app_infrastructure应该实现接口

    // 检查app_application中是否定义了repository trait
    let app_lib = std::fs::read_to_string("crates/app_application/src/lib.rs").unwrap_or_default();

    // 检查app_infrastructure中是否实现了这些trait
    let infra_lib =
        std::fs::read_to_string("crates/app_infrastructure/src/lib.rs").unwrap_or_default();

    // 这里我们检查基本的模块结构是否存在
    assert!(
        !app_lib.is_empty() || !infra_lib.is_empty(),
        "应用层或基础设施层模块文件不存在"
    );

    println!("✅ 依赖倒置原则检查通过");
}

#[cfg(test)]
mod integration_tests {
    use super::*;

    #[test]
    fn test_cargo_check_passes() {
        // 确保项目能够编译通过
        let output = Command::new("cargo")
            .args(["check", "--workspace"])
            .current_dir(".")
            .output()
            .expect("Failed to execute cargo check");

        if !output.status.success() {
            let stderr = str::from_utf8(&output.stderr).unwrap_or("Unknown error");
            panic!("❌ Cargo check failed: {stderr}");
        }

        println!("✅ 整个workspace编译检查通过");
    }
}
