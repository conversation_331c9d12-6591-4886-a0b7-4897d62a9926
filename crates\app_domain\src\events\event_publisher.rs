//! # 领域事件发布器
//!
//! 定义事件发布接口和实现，支持同步和异步事件处理

use super::domain_event::{DomainEvent, EventMetadata, EventWrapper};
use app_common::error::AppError;
use async_trait::async_trait;

use app_common::tracing::{debug, info};

/// 领域事件发布器接口
///
/// 负责将领域事件发布到事件总线，支持同步和异步处理
#[async_trait]
pub trait DomainEventPublisher: Send + Sync {
    /// 发布单个事件包装器
    ///
    /// # 参数
    /// - `event`: 要发布的事件包装器
    ///
    /// # 返回
    /// - `Ok(())`: 发布成功
    /// - `Err(AppError)`: 发布失败
    async fn publish_wrapper(&self, event: EventWrapper) -> Result<(), AppError>;

    /// 批量发布事件
    ///
    /// # 参数
    /// - `events`: 要发布的事件列表
    ///
    /// # 返回
    /// - `Ok(())`: 所有事件发布成功
    /// - `Err(AppError)`: 至少一个事件发布失败
    async fn publish_batch(&self, events: Vec<EventWrapper>) -> Result<(), AppError>;

    /// 发布事件并等待处理完成
    ///
    /// # 参数
    /// - `event`: 要发布的事件包装器
    ///
    /// # 返回
    /// - `Ok(())`: 事件处理完成
    /// - `Err(AppError)`: 事件处理失败
    async fn publish_and_wait(&self, event: EventWrapper) -> Result<(), AppError>;
}

/// 扩展trait，提供泛型方法的便利接口
pub trait DomainEventPublisherExt: DomainEventPublisher {
    /// 发布单个事件（泛型便利方法）
    ///
    /// # 参数
    /// - `event`: 要发布的领域事件
    /// - `metadata`: 可选的事件元数据
    ///
    /// # 返回
    /// - `Ok(())`: 发布成功
    /// - `Err(AppError)`: 发布失败
    #[allow(async_fn_in_trait)]
    async fn publish<T>(&self, event: T, metadata: Option<EventMetadata>) -> Result<(), AppError>
    where
        T: DomainEvent + serde::Serialize + Send,
    {
        let wrapper = EventWrapper::from_domain_event(&event, metadata)
            .map_err(|e| AppError::InternalServerError(format!("事件序列化失败: {e}")))?;
        self.publish_wrapper(wrapper).await
    }

    /// 发布事件并等待处理完成（泛型便利方法）
    ///
    /// # 参数
    /// - `event`: 要发布的领域事件
    /// - `metadata`: 可选的事件元数据
    ///
    /// # 返回
    /// - `Ok(())`: 事件处理完成
    /// - `Err(AppError)`: 事件处理失败
    #[allow(async_fn_in_trait)]
    async fn publish_and_wait_typed<T>(
        &self,
        event: T,
        metadata: Option<EventMetadata>,
    ) -> Result<(), AppError>
    where
        T: DomainEvent + serde::Serialize + Send,
    {
        let wrapper = EventWrapper::from_domain_event(&event, metadata)
            .map_err(|e| AppError::InternalServerError(format!("事件序列化失败: {e}")))?;
        self.publish_and_wait(wrapper).await
    }
}

/// 内存事件发布器
///
/// 用于测试和开发环境的简单事件发布器实现
pub struct InMemoryEventPublisher {
    /// 存储的事件列表
    events: std::sync::Arc<std::sync::Mutex<Vec<EventWrapper>>>,

    /// 是否启用日志
    enable_logging: bool,
}

impl InMemoryEventPublisher {
    /// 创建新的内存事件发布器
    pub fn new() -> Self {
        Self {
            events: std::sync::Arc::new(std::sync::Mutex::new(Vec::new())),
            enable_logging: true,
        }
    }

    /// 创建不启用日志的内存事件发布器
    pub fn new_silent() -> Self {
        Self {
            events: std::sync::Arc::new(std::sync::Mutex::new(Vec::new())),
            enable_logging: false,
        }
    }

    /// 获取所有已发布的事件
    pub fn get_events(&self) -> Vec<EventWrapper> {
        let events = self.events.lock().unwrap();
        events.clone()
    }

    /// 获取指定类型的事件
    pub fn get_events_by_type(&self, event_type: &str) -> Vec<EventWrapper> {
        let events = self.events.lock().unwrap();
        events
            .iter()
            .filter(|e| e.event_type == event_type)
            .cloned()
            .collect()
    }

    /// 获取事件数量
    pub fn event_count(&self) -> usize {
        let events = self.events.lock().unwrap();
        events.len()
    }

    /// 清空所有事件
    pub fn clear(&self) {
        let mut events = self.events.lock().unwrap();
        events.clear();

        if self.enable_logging {
            debug!("已清空内存事件发布器中的所有事件");
        }
    }

    /// 检查是否包含指定类型的事件
    pub fn contains_event_type(&self, event_type: &str) -> bool {
        let events = self.events.lock().unwrap();
        events.iter().any(|e| e.event_type == event_type)
    }
}

impl Clone for InMemoryEventPublisher {
    fn clone(&self) -> Self {
        Self {
            events: self.events.clone(),
            enable_logging: self.enable_logging,
        }
    }
}

impl Default for InMemoryEventPublisher {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl DomainEventPublisher for InMemoryEventPublisher {
    async fn publish_wrapper(&self, wrapper: EventWrapper) -> Result<(), AppError> {
        {
            let mut events = self.events.lock().unwrap();
            events.push(wrapper.clone());
        }

        if self.enable_logging {
            info!(
                "📢 已发布领域事件: {} (聚合根ID: {})",
                wrapper.event_type, wrapper.aggregate_id
            );
        }

        Ok(())
    }

    async fn publish_batch(&self, mut new_events: Vec<EventWrapper>) -> Result<(), AppError> {
        {
            let mut events = self.events.lock().unwrap();
            events.append(&mut new_events);
        }

        if self.enable_logging {
            info!("📢 已批量发布 {} 个领域事件", new_events.len());
        }

        Ok(())
    }

    async fn publish_and_wait(&self, wrapper: EventWrapper) -> Result<(), AppError> {
        // 对于内存实现，publish_and_wait 与 publish_wrapper 行为相同
        self.publish_wrapper(wrapper).await
    }
}

// 为所有实现了 DomainEventPublisher 的类型自动实现扩展trait
impl<T: DomainEventPublisher> DomainEventPublisherExt for T {}

/// 空事件发布器
///
/// 不执行任何操作的事件发布器，用于禁用事件发布的场景
#[derive(Clone)]
pub struct NullEventPublisher;

impl NullEventPublisher {
    pub fn new() -> Self {
        Self
    }
}

impl Default for NullEventPublisher {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl DomainEventPublisher for NullEventPublisher {
    async fn publish_wrapper(&self, _event: EventWrapper) -> Result<(), AppError> {
        // 不执行任何操作
        Ok(())
    }

    async fn publish_batch(&self, _events: Vec<EventWrapper>) -> Result<(), AppError> {
        // 不执行任何操作
        Ok(())
    }

    async fn publish_and_wait(&self, _event: EventWrapper) -> Result<(), AppError> {
        // 不执行任何操作
        Ok(())
    }
}

/// 组合事件发布器
///
/// 将事件发布到多个发布器，支持故障转移和冗余
/// 注意：由于 Rust 的 trait object 限制，这里使用具体类型的枚举
#[derive(Clone)]
pub enum EventPublisherType {
    InMemory(InMemoryEventPublisher),
    Null(NullEventPublisher),
}

#[async_trait]
impl DomainEventPublisher for EventPublisherType {
    async fn publish_wrapper(&self, event: EventWrapper) -> Result<(), AppError> {
        match self {
            EventPublisherType::InMemory(publisher) => publisher.publish_wrapper(event).await,
            EventPublisherType::Null(publisher) => publisher.publish_wrapper(event).await,
        }
    }

    async fn publish_batch(&self, events: Vec<EventWrapper>) -> Result<(), AppError> {
        match self {
            EventPublisherType::InMemory(publisher) => publisher.publish_batch(events).await,
            EventPublisherType::Null(publisher) => publisher.publish_batch(events).await,
        }
    }

    async fn publish_and_wait(&self, event: EventWrapper) -> Result<(), AppError> {
        match self {
            EventPublisherType::InMemory(publisher) => publisher.publish_and_wait(event).await,
            EventPublisherType::Null(publisher) => publisher.publish_and_wait(event).await,
        }
    }
}

pub struct CompositeEventPublisher {
    publishers: Vec<EventPublisherType>,
    fail_fast: bool,
}

impl CompositeEventPublisher {
    /// 创建新的组合事件发布器
    ///
    /// # 参数
    /// - `publishers`: 发布器列表
    /// - `fail_fast`: 是否在第一个失败时立即返回错误
    pub fn new(publishers: Vec<EventPublisherType>, fail_fast: bool) -> Self {
        Self {
            publishers,
            fail_fast,
        }
    }
}

#[async_trait]
impl DomainEventPublisher for CompositeEventPublisher {
    async fn publish_wrapper(&self, wrapper: EventWrapper) -> Result<(), AppError> {
        let mut errors = Vec::new();

        for publisher in &self.publishers {
            match publisher.publish_wrapper(wrapper.clone()).await {
                Ok(()) => {
                    if !self.fail_fast {
                        continue;
                    }
                }
                Err(e) => {
                    errors.push(e);
                    if self.fail_fast {
                        return Err(errors.into_iter().next().unwrap());
                    }
                }
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(AppError::InternalServerError(format!(
                "部分事件发布器失败: {} 个错误",
                errors.len()
            )))
        }
    }

    async fn publish_batch(&self, events: Vec<EventWrapper>) -> Result<(), AppError> {
        let mut errors = Vec::new();

        for publisher in &self.publishers {
            match publisher.publish_batch(events.clone()).await {
                Ok(()) => {
                    if !self.fail_fast {
                        continue;
                    }
                }
                Err(e) => {
                    errors.push(e);
                    if self.fail_fast {
                        return Err(errors.into_iter().next().unwrap());
                    }
                }
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(AppError::InternalServerError(format!(
                "部分事件发布器失败: {} 个错误",
                errors.len()
            )))
        }
    }

    async fn publish_and_wait(&self, wrapper: EventWrapper) -> Result<(), AppError> {
        // 对于组合发布器，publish_and_wait 与 publish_wrapper 行为相同
        self.publish_wrapper(wrapper).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::events::user_events::UserCreatedEvent;
    use chrono::Utc;
    use uuid::Uuid;

    #[tokio::test]
    async fn test_in_memory_event_publisher() {
        let publisher = InMemoryEventPublisher::new();

        let event = UserCreatedEvent {
            user_id: Uuid::new_v4(),
            username: "test_user".to_string(),
            email: "<EMAIL>".to_string(),
            occurred_at: Utc::now(),
        };

        publisher.publish(event, None).await.unwrap();

        assert_eq!(publisher.event_count(), 1);
        assert!(publisher.contains_event_type("UserCreated"));

        publisher.clear();
        assert_eq!(publisher.event_count(), 0);
    }

    #[tokio::test]
    async fn test_null_event_publisher() {
        let publisher = NullEventPublisher::new();

        let event = UserCreatedEvent {
            user_id: Uuid::new_v4(),
            username: "test_user".to_string(),
            email: "<EMAIL>".to_string(),
            occurred_at: Utc::now(),
        };

        // 应该成功但不执行任何操作
        publisher.publish(event, None).await.unwrap();
    }
}
