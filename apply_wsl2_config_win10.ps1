# Apply WSL2 Configuration for Windows 10
Write-Host "Applying WSL2 Configuration for Windows 10..." -ForegroundColor Green

$wslConfigPath = "$env:USERPROFILE\.wslconfig"

# Create Windows 10 compatible WSL2 configuration
$wslConfig = @"
[wsl2]
# Network configuration for Windows 10
# Note: mirrored mode is not supported on Windows 10
localhostForwarding=true

# Performance optimization
memory=4GB
processors=2
swap=2GB

# Experimental features (Windows 10 compatible)
[experimental]
sparseVhd=true
autoMemoryReclaim=gradual
"@

# Write configuration to file
Set-Content -Path $wslConfigPath -Value $wslConfig -Encoding UTF8
Write-Host "Windows 10 compatible WSL2 configuration created: $wslConfigPath" -ForegroundColor Green

Write-Host "`nConfiguration content:" -ForegroundColor Cyan
Write-Host $wslConfig -ForegroundColor Gray

Write-Host "`nIMPORTANT: Windows 10 limitations detected:" -ForegroundColor Yellow
Write-Host "• Mirrored networking mode is not supported" -ForegroundColor Red
Write-Host "• DNS tunneling is not supported" -ForegroundColor Red
Write-Host "• Hyper-V firewall is not supported" -ForegroundColor Red

Write-Host "`nRecommended solution for Windows 10:" -ForegroundColor Green
Write-Host "• Use the smart IP detection (already implemented in your code)" -ForegroundColor White
Write-Host "• Or upgrade to Windows 11 for full WSL2 features" -ForegroundColor White

Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Close WSL2: wsl --shutdown" -ForegroundColor White
Write-Host "2. Wait 10 seconds" -ForegroundColor White
Write-Host "3. Restart WSL2: wsl" -ForegroundColor White
Write-Host "4. Use the smart environment setup script" -ForegroundColor White
