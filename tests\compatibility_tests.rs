//! # API兼容性测试套件
//!
//! 基于TDD原则的全面兼容性测试，确保API升级不破坏现有客户端
//!
//! ## 测试覆盖范围
//! - 版本协商测试
//! - 向后兼容性验证
//! - 破坏性变更检测
//! - 数据格式兼容性
//! - 错误响应兼容性

use app_common::compatibility::{CompatibilityChecker, CompatibilityConfig};
use app_interfaces::versioning::{
    ApiChange, ApiVersion, ChangeType, VersioningConfig, VersioningState,
};
use axum::{
    Router,
    body::Body,
    http::{HeaderValue, Request, StatusCode},
};
use chrono::Utc;
use serde_json::{Value, json};
use tower::ServiceExt;

/// 测试API版本协商功能
#[tokio::test]
async fn test_api_version_negotiation() {
    // 创建版本控制状态
    let config = VersioningConfig::default();
    let state = VersioningState::new(config);

    // 测试有效版本协商
    let negotiation = state.negotiate_version(Some("1.0.0")).unwrap();
    assert_eq!(negotiation.requested_version, ApiVersion::new(1, 0, 0));
    assert_eq!(negotiation.resolved_version, ApiVersion::new(1, 0, 0));
    assert!(!negotiation.requires_adaptation);

    // 测试缺少版本信息
    let result = state.negotiate_version(None);
    assert!(result.is_err());

    // 测试无效版本格式
    let result = state.negotiate_version(Some("invalid"));
    assert!(result.is_err());
}

/// 测试向后兼容的API变更
#[tokio::test]
async fn test_backward_compatible_changes() {
    let checker = CompatibilityChecker::default();

    // 测试添加新字段（向后兼容）
    let add_field_change = ApiChange {
        change_type: ChangeType::AddField,
        description: "添加用户头像字段".to_string(),
        affected_endpoints: vec!["/api/users".to_string()],
        version: ApiVersion::new(1, 1, 0),
        timestamp: Utc::now(),
        migration_guide: None,
    };

    // 测试添加新端点（向后兼容）
    let add_endpoint_change = ApiChange {
        change_type: ChangeType::AddEndpoint,
        description: "添加用户统计端点".to_string(),
        affected_endpoints: vec!["/api/users/stats".to_string()],
        version: ApiVersion::new(1, 1, 0),
        timestamp: Utc::now(),
        migration_guide: None,
    };

    let changes = vec![add_field_change, add_endpoint_change];
    let compatibility_check = checker.check_compatibility(&changes);

    assert!(compatibility_check.is_compatible);
    assert!(compatibility_check.breaking_changes.is_empty());
    assert_eq!(compatibility_check.warnings.len(), 0);
}

/// 测试破坏性变更检测
#[tokio::test]
async fn test_breaking_changes_detection() {
    let checker = CompatibilityChecker::default();

    // 测试删除字段（破坏性变更）
    let remove_field_change = ApiChange {
        change_type: ChangeType::RemoveField,
        description: "删除用户邮箱字段".to_string(),
        affected_endpoints: vec!["/api/users".to_string()],
        version: ApiVersion::new(2, 0, 0),
        timestamp: Utc::now(),
        migration_guide: Some("使用新的联系信息端点".to_string()),
    };

    // 测试修改字段类型（破坏性变更）
    let modify_type_change = ApiChange {
        change_type: ChangeType::ModifyFieldType,
        description: "将用户ID从字符串改为数字".to_string(),
        affected_endpoints: vec!["/api/users".to_string()],
        version: ApiVersion::new(2, 0, 0),
        timestamp: Utc::now(),
        migration_guide: Some("更新客户端以处理数字ID".to_string()),
    };

    let changes = vec![remove_field_change, modify_type_change];
    let compatibility_check = checker.check_compatibility(&changes);

    assert!(!compatibility_check.is_compatible);
    assert_eq!(compatibility_check.breaking_changes.len(), 2);
    assert!(!compatibility_check.migration_suggestions.is_empty());
}

/// 测试版本兼容性检查
#[tokio::test]
async fn test_version_compatibility_check() {
    let checker = CompatibilityChecker::default();

    // 测试同主版本号的兼容性
    let v1_0_0 = ApiVersion::new(1, 0, 0);
    let v1_1_0 = ApiVersion::new(1, 1, 0);
    let v1_2_0 = ApiVersion::new(1, 2, 0);

    assert!(checker.check_version_compatibility(&v1_0_0, &v1_1_0));
    assert!(checker.check_version_compatibility(&v1_0_0, &v1_2_0));
    assert!(checker.check_version_compatibility(&v1_1_0, &v1_2_0));

    // 测试向后不兼容的情况
    assert!(!checker.check_version_compatibility(&v1_1_0, &v1_0_0));
    assert!(!checker.check_version_compatibility(&v1_2_0, &v1_1_0));

    // 测试不同主版本号的不兼容性
    let v2_0_0 = ApiVersion::new(2, 0, 0);
    assert!(!checker.check_version_compatibility(&v1_0_0, &v2_0_0));
    assert!(!checker.check_version_compatibility(&v2_0_0, &v1_0_0));
}

/// 测试API响应格式兼容性
#[tokio::test]
async fn test_response_format_compatibility() {
    // 测试标准成功响应格式
    let success_response = json!({
        "success": true,
        "data": {
            "id": 1,
            "username": "testuser",
            "email": "<EMAIL>"
        },
        "message": "操作成功"
    });

    // 验证响应包含必需字段
    assert!(success_response["success"].as_bool().unwrap());
    assert!(success_response["data"].is_object());
    assert!(success_response["message"].is_string());

    // 测试标准错误响应格式
    let error_response = json!({
        "success": false,
        "error": {
            "code": "VALIDATION_ERROR",
            "message": "输入验证失败",
            "details": "用户名长度必须在3-50个字符之间"
        }
    });

    // 验证错误响应包含必需字段
    assert!(!error_response["success"].as_bool().unwrap());
    assert!(error_response["error"].is_object());
    assert!(error_response["error"]["code"].is_string());
    assert!(error_response["error"]["message"].is_string());
}

/// 测试分页响应兼容性
#[tokio::test]
async fn test_pagination_compatibility() {
    let paginated_response = json!({
        "success": true,
        "data": {
            "tasks": [
                {
                    "id": 1,
                    "title": "测试任务",
                    "status": "pending"
                }
            ],
            "pagination": {
                "page": 1,
                "limit": 10,
                "total": 25,
                "total_pages": 3,
                "has_next": true,
                "has_prev": false
            }
        }
    });

    // 验证分页信息完整性
    let pagination = &paginated_response["data"]["pagination"];
    assert!(pagination["page"].is_number());
    assert!(pagination["limit"].is_number());
    assert!(pagination["total"].is_number());
    assert!(pagination["total_pages"].is_number());
    assert!(pagination["has_next"].is_boolean());
    assert!(pagination["has_prev"].is_boolean());
}

/// 测试WebSocket消息格式兼容性
#[tokio::test]
async fn test_websocket_message_compatibility() {
    // 测试聊天消息格式
    let chat_message = json!({
        "type": "chat_message",
        "data": {
            "id": 123,
            "room_id": 1,
            "user_id": 1,
            "username": "testuser",
            "content": "Hello, World!",
            "created_at": "2025-01-11T10:00:00Z"
        }
    });

    // 验证消息格式
    assert_eq!(chat_message["type"].as_str().unwrap(), "chat_message");
    assert!(chat_message["data"].is_object());
    assert!(chat_message["data"]["id"].is_number());
    assert!(chat_message["data"]["content"].is_string());

    // 测试系统消息格式
    let system_message = json!({
        "type": "user_joined",
        "data": {
            "user_id": 1,
            "username": "testuser",
            "room_id": 1,
            "timestamp": "2025-01-11T10:00:00Z"
        }
    });

    assert_eq!(system_message["type"].as_str().unwrap(), "user_joined");
    assert!(system_message["data"]["user_id"].is_number());
}

/// 测试错误代码兼容性
#[tokio::test]
async fn test_error_code_compatibility() {
    // 定义标准错误代码
    let standard_error_codes = vec![
        "AUTHENTICATION_REQUIRED",
        "INVALID_TOKEN",
        "VALIDATION_ERROR",
        "NOT_FOUND",
        "FORBIDDEN",
        "INTERNAL_SERVER_ERROR",
        "DATABASE_ERROR",
        "RATE_LIMIT_EXCEEDED",
    ];

    // 验证错误代码格式一致性
    for error_code in standard_error_codes {
        assert!(
            error_code
                .chars()
                .all(|c| c.is_ascii_uppercase() || c == '_')
        );
        assert!(!error_code.starts_with('_'));
        assert!(!error_code.ends_with('_'));
    }
}

/// 测试HTTP状态码使用一致性
#[tokio::test]
async fn test_http_status_code_consistency() {
    // 定义标准状态码映射
    let status_mappings = vec![
        ("成功操作", StatusCode::OK),
        ("资源创建", StatusCode::CREATED),
        ("无内容响应", StatusCode::NO_CONTENT),
        ("客户端错误", StatusCode::BAD_REQUEST),
        ("认证失败", StatusCode::UNAUTHORIZED),
        ("权限不足", StatusCode::FORBIDDEN),
        ("资源未找到", StatusCode::NOT_FOUND),
        ("方法不允许", StatusCode::METHOD_NOT_ALLOWED),
        ("冲突", StatusCode::CONFLICT),
        ("请求过大", StatusCode::PAYLOAD_TOO_LARGE),
        ("请求频率限制", StatusCode::TOO_MANY_REQUESTS),
        ("服务器错误", StatusCode::INTERNAL_SERVER_ERROR),
        ("服务不可用", StatusCode::SERVICE_UNAVAILABLE),
    ];

    // 验证状态码在合理范围内
    for (_, status_code) in status_mappings {
        let code = status_code.as_u16();
        assert!(code >= 200 && code < 600);
    }
}

/// 集成测试：完整的兼容性验证流程
#[tokio::test]
async fn test_full_compatibility_workflow() {
    // 1. 创建兼容性检查器
    let config = CompatibilityConfig {
        allow_new_fields: true,
        allow_new_endpoints: true,
        allow_optional_field_changes: true,
        strict_mode: false,
        ignored_endpoints: vec!["/_internal".to_string()],
        ignored_fields: vec!["debug_info".to_string()],
    };
    let checker = CompatibilityChecker::new(config);

    // 2. 模拟API变更
    let changes = vec![
        ApiChange {
            change_type: ChangeType::AddField,
            description: "添加用户最后登录时间".to_string(),
            affected_endpoints: vec!["/api/users".to_string()],
            version: ApiVersion::new(1, 1, 0),
            timestamp: Utc::now(),
            migration_guide: None,
        },
        ApiChange {
            change_type: ChangeType::AddEndpoint,
            description: "添加用户活动日志端点".to_string(),
            affected_endpoints: vec!["/api/users/{id}/activity".to_string()],
            version: ApiVersion::new(1, 1, 0),
            timestamp: Utc::now(),
            migration_guide: None,
        },
    ];

    // 3. 执行兼容性检查
    let compatibility_check = checker.check_compatibility(&changes);

    // 4. 验证结果
    assert!(compatibility_check.is_compatible);
    assert!(compatibility_check.breaking_changes.is_empty());

    // 5. 生成兼容性摘要
    let summary = checker.generate_compatibility_summary(&compatibility_check);
    assert!(summary.contains("✅ API变更向后兼容"));
}
