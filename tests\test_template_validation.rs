// 测试模板验证测试

use anyhow::Result;
use std::path::Path;

/// 验证E2E测试模板文件存在
#[tokio::test]
async fn test_e2e_template_files_exist() -> Result<()> {
    println!("验证E2E测试模板文件存在...");

    let template_files = [
        "tests/e2e/templates/basic_e2e_test_template.rs",
        "tests/e2e/config/playwright.config.json",
        "tests/e2e/config/test.env",
        "tests/e2e/fixtures/users.json",
        "tests/e2e/fixtures/tasks.json",
        "tests/e2e/README.md",
    ];

    for file_path in &template_files {
        let path = Path::new(file_path);
        assert!(path.exists(), "模板文件应该存在: {}", file_path);
        println!("✅ 文件存在: {}", file_path);
    }

    println!("✅ 所有E2E测试模板文件验证通过");
    Ok(())
}

/// 验证测试目录结构
#[tokio::test]
async fn test_directory_structure() -> Result<()> {
    println!("验证测试目录结构...");

    let directories = [
        "tests/e2e",
        "tests/e2e/config",
        "tests/e2e/fixtures",
        "tests/e2e/helpers",
        "tests/e2e/templates",
        "tests/e2e/reports",
        "tests/e2e/reports/screenshots",
        "tests/e2e/reports/videos",
    ];

    for dir_path in &directories {
        let path = Path::new(dir_path);
        if !path.exists() {
            std::fs::create_dir_all(path)?;
        }
        assert!(path.exists(), "目录应该存在: {}", dir_path);
        assert!(path.is_dir(), "应该是目录: {}", dir_path);
        println!("✅ 目录存在: {}", dir_path);
    }

    println!("✅ 测试目录结构验证通过");
    Ok(())
}

/// 验证配置文件格式
#[tokio::test]
async fn test_config_file_format() -> Result<()> {
    println!("验证配置文件格式...");

    // 验证Playwright配置文件
    let playwright_config = std::fs::read_to_string("tests/e2e/config/playwright.config.json")?;
    let _: serde_json::Value = serde_json::from_str(&playwright_config)
        .map_err(|e| anyhow::anyhow!("Playwright配置文件JSON格式无效: {}", e))?;
    println!("✅ Playwright配置文件格式正确");

    // 验证用户夹具文件
    let users_fixture = std::fs::read_to_string("tests/e2e/fixtures/users.json")?;
    let users_data: serde_json::Value = serde_json::from_str(&users_fixture)
        .map_err(|e| anyhow::anyhow!("用户夹具文件JSON格式无效: {}", e))?;

    assert!(
        users_data.get("testUsers").is_some(),
        "用户夹具应该包含testUsers"
    );
    assert!(
        users_data.get("invalidUsers").is_some(),
        "用户夹具应该包含invalidUsers"
    );
    println!("✅ 用户夹具文件格式正确");

    // 验证任务夹具文件
    let tasks_fixture = std::fs::read_to_string("tests/e2e/fixtures/tasks.json")?;
    let tasks_data: serde_json::Value = serde_json::from_str(&tasks_fixture)
        .map_err(|e| anyhow::anyhow!("任务夹具文件JSON格式无效: {}", e))?;

    assert!(
        tasks_data.get("testTasks").is_some(),
        "任务夹具应该包含testTasks"
    );
    assert!(
        tasks_data.get("taskTemplates").is_some(),
        "任务夹具应该包含taskTemplates"
    );
    println!("✅ 任务夹具文件格式正确");

    println!("✅ 所有配置文件格式验证通过");
    Ok(())
}

/// 验证环境变量配置
#[tokio::test]
async fn test_environment_configuration() -> Result<()> {
    println!("验证环境变量配置...");

    // 加载测试环境配置
    dotenvy::from_filename("tests/e2e/config/test.env").ok();

    let required_vars = [
        "SERVER_HOST",
        "SERVER_PORT",
        "BASE_URL",
        "DATABASE_URL",
        "TEST_DATABASE_URL",
        "JWT_SECRET",
        "TEST_USERNAME",
        "TEST_PASSWORD",
        "PLAYWRIGHT_HEADLESS",
        "PLAYWRIGHT_VIEWPORT_WIDTH",
        "PLAYWRIGHT_VIEWPORT_HEIGHT",
    ];

    for var in &required_vars {
        let value = std::env::var(var).unwrap_or_default();
        assert!(!value.is_empty(), "环境变量 {} 不能为空", var);
        println!("✅ {}: {}", var, value);
    }

    println!("✅ 环境变量配置验证通过");
    Ok(())
}

/// 验证辅助模块文件
#[tokio::test]
async fn test_helper_modules() -> Result<()> {
    println!("验证辅助模块文件...");

    let helper_files = [
        "tests/e2e/helpers/mod.rs",
        "tests/e2e/helpers/auth.rs",
        "tests/e2e/helpers/api.rs",
        "tests/e2e/helpers/database.rs",
        "tests/e2e/helpers/playwright.rs",
    ];

    for file_path in &helper_files {
        let path = Path::new(file_path);
        assert!(path.exists(), "辅助模块文件应该存在: {}", file_path);

        // 验证文件不为空
        let content = std::fs::read_to_string(path)?;
        assert!(
            !content.trim().is_empty(),
            "辅助模块文件不应为空: {}",
            file_path
        );

        println!("✅ 辅助模块文件存在且有内容: {}", file_path);
    }

    println!("✅ 辅助模块文件验证通过");
    Ok(())
}

/// 验证测试模板可编译性
#[tokio::test]
async fn test_template_compilation() -> Result<()> {
    println!("验证测试模板可编译性...");

    // 检查模板文件语法
    let template_content =
        std::fs::read_to_string("tests/e2e/templates/basic_e2e_test_template.rs")?;

    // 基本语法检查
    assert!(
        template_content.contains("#[tokio::test]"),
        "模板应该包含tokio测试宏"
    );
    assert!(
        template_content.contains("async fn"),
        "模板应该包含异步函数"
    );
    assert!(
        template_content.contains("Result<()>"),
        "模板应该返回Result类型"
    );
    assert!(
        template_content.contains("println!"),
        "模板应该包含输出语句"
    );

    // 检查导入语句
    assert!(
        template_content.contains("use anyhow::Result"),
        "应该导入anyhow::Result"
    );
    assert!(
        template_content.contains("use serde_json"),
        "应该导入serde_json"
    );
    assert!(
        template_content.contains("use tokio::time::sleep"),
        "应该导入tokio::time::sleep"
    );

    println!("✅ 测试模板语法检查通过");

    // 注意：实际编译检查需要在CI/CD中进行，这里只做基本语法验证
    println!("✅ 测试模板可编译性验证通过");
    Ok(())
}

/// 验证README文档
#[tokio::test]
async fn test_readme_documentation() -> Result<()> {
    println!("验证README文档...");

    let readme_content = std::fs::read_to_string("tests/e2e/README.md")?;

    // 检查关键章节
    assert!(
        readme_content.contains("# E2E 测试目录"),
        "README应该包含标题"
    );
    assert!(
        readme_content.contains("## 目录结构"),
        "README应该包含目录结构说明"
    );
    assert!(
        readme_content.contains("## 测试环境配置"),
        "README应该包含环境配置说明"
    );
    assert!(
        readme_content.contains("## 运行测试"),
        "README应该包含运行说明"
    );
    assert!(
        readme_content.contains("cargo test"),
        "README应该包含cargo test命令"
    );

    println!("✅ README文档内容验证通过");
    Ok(())
}

/// 验证测试数据完整性
#[tokio::test]
async fn test_fixture_data_integrity() -> Result<()> {
    println!("验证测试数据完整性...");

    // 验证用户测试数据
    let users_fixture = std::fs::read_to_string("tests/e2e/fixtures/users.json")?;
    let users_data: serde_json::Value = serde_json::from_str(&users_fixture)?;

    let test_users = users_data["testUsers"].as_array().unwrap();
    assert!(!test_users.is_empty(), "应该有测试用户数据");

    for user in test_users {
        assert!(user.get("username").is_some(), "用户应该有用户名");
        assert!(user.get("email").is_some(), "用户应该有邮箱");
        assert!(user.get("password").is_some(), "用户应该有密码");
    }
    println!("✅ 用户测试数据完整性验证通过");

    // 验证任务测试数据
    let tasks_fixture = std::fs::read_to_string("tests/e2e/fixtures/tasks.json")?;
    let tasks_data: serde_json::Value = serde_json::from_str(&tasks_fixture)?;

    let test_tasks = tasks_data["testTasks"].as_array().unwrap();
    assert!(!test_tasks.is_empty(), "应该有测试任务数据");

    for task in test_tasks {
        assert!(task.get("title").is_some(), "任务应该有标题");
        assert!(task.get("description").is_some(), "任务应该有描述");
        assert!(task.get("status").is_some(), "任务应该有状态");
    }
    println!("✅ 任务测试数据完整性验证通过");

    println!("✅ 测试数据完整性验证通过");
    Ok(())
}

/// 集成测试：完整的测试模板验证
#[tokio::test]
async fn test_complete_template_validation() -> Result<()> {
    println!("执行完整的测试模板验证...");

    // 1. 文件存在性检查
    let template_path = Path::new("tests/e2e/templates/basic_e2e_test_template.rs");
    assert!(template_path.exists(), "基础E2E测试模板应该存在");
    println!("✅ 模板文件存在检查通过");

    // 2. 目录结构检查
    let required_dirs = [
        "tests/e2e/config",
        "tests/e2e/fixtures",
        "tests/e2e/helpers",
    ];
    for dir in &required_dirs {
        assert!(Path::new(dir).exists(), "目录应该存在: {}", dir);
    }
    println!("✅ 目录结构检查通过");

    // 3. 配置文件检查
    dotenvy::from_filename("tests/e2e/config/test.env").ok();
    let base_url = std::env::var("BASE_URL").unwrap_or_default();
    assert!(!base_url.is_empty(), "BASE_URL应该被配置");
    println!("✅ 配置文件检查通过");

    // 4. 测试数据检查
    let users_fixture = std::fs::read_to_string("tests/e2e/fixtures/users.json")?;
    let _: serde_json::Value = serde_json::from_str(&users_fixture)?;
    println!("✅ 测试数据检查通过");

    // 5. 模板内容检查
    let template_content = std::fs::read_to_string(template_path)?;
    assert!(
        template_content.contains("template_"),
        "模板应该包含template_前缀的函数"
    );
    assert!(
        template_content.contains("E2EConfig"),
        "模板应该使用E2EConfig"
    );
    println!("✅ 模板内容检查通过");

    println!("🎉 完整的测试模板验证通过！");
    println!("   所有测试模板和配置都已正确设置");
    println!("   开发人员可以基于这些模板快速编写E2E测试");

    Ok(())
}
