//! # 聊天API删除功能测试
//!
//! 根据TDD原则编写的测试用例，用于验证多聊天室API删除操作的正确性
//! 遵循Axum 0.8.4最佳实践和Context7 MCP指导原则
//!
//! 测试目标：
//! - 验证7个多聊天室API端点删除后返回404
//! - 确保保留的3个核心API端点正常工作
//! - 验证WebSocket功能不受影响
//! - 确保无破坏性变更

use anyhow::Result;
use reqwest::Client;
use serde_json::{Value, json};
use std::time::Duration;
use tokio::time::sleep;
use uuid::Uuid;

/// 测试配置常量
const SERVER_URL: &str = "127.0.0.1:3000";
const API_BASE_URL: &str = "http://127.0.0.1:3000/api";
const TEST_TIMEOUT: Duration = Duration::from_secs(30);

/// 测试用户凭据
const TEST_USER_USERNAME: &str = "testuser456";
const TEST_USER_PASSWORD: &str = "password123";

/// 聊天API删除测试辅助结构
#[derive(Debug)]
struct ChatApiDeletionTestHelper {
    client: Client,
    jwt_token: Option<String>,
}

impl ChatApiDeletionTestHelper {
    /// 创建新的测试辅助实例
    fn new() -> Self {
        Self {
            client: Client::new(),
            jwt_token: None,
        }
    }

    /// 用户登录并获取JWT token
    async fn login(&mut self) -> Result<String> {
        let login_url = format!("{}/auth/login", API_BASE_URL);
        let login_data = json!({
            "username": TEST_USER_USERNAME,
            "password": TEST_USER_PASSWORD
        });

        let response = self
            .client
            .post(&login_url)
            .json(&login_data)
            .send()
            .await?;

        if !response.status().is_success() {
            anyhow::bail!("登录失败: {}", response.status());
        }

        let response_json: Value = response.json().await?;
        let token = response_json["data"]["access_token"]
            .as_str()
            .ok_or_else(|| anyhow::anyhow!("响应中缺少access_token字段"))?
            .to_string();

        self.jwt_token = Some(token.clone());
        Ok(token)
    }

    /// 测试API端点是否返回404（已删除）
    async fn test_api_endpoint_deleted(&self, method: &str, endpoint: &str) -> Result<bool> {
        let url = format!("{}{}", API_BASE_URL, endpoint);
        let token = self.jwt_token.as_ref().unwrap();

        let response = match method.to_uppercase().as_str() {
            "GET" => {
                self.client
                    .get(&url)
                    .header("Authorization", format!("Bearer {}", token))
                    .send()
                    .await?
            }
            "POST" => {
                self.client
                    .post(&url)
                    .header("Authorization", format!("Bearer {}", token))
                    .json(&json!({}))
                    .send()
                    .await?
            }
            "PUT" => {
                self.client
                    .put(&url)
                    .header("Authorization", format!("Bearer {}", token))
                    .json(&json!({}))
                    .send()
                    .await?
            }
            "DELETE" => {
                self.client
                    .delete(&url)
                    .header("Authorization", format!("Bearer {}", token))
                    .send()
                    .await?
            }
            _ => anyhow::bail!("不支持的HTTP方法: {}", method),
        };

        // 返回404或405表示API已被删除
        // 404: 路由不存在
        // 405: 方法不被允许（路由存在但不支持该方法，也表示删除成功）
        Ok(response.status() == reqwest::StatusCode::NOT_FOUND
            || response.status() == reqwest::StatusCode::METHOD_NOT_ALLOWED)
    }

    /// 测试API端点是否正常工作（未删除）
    async fn test_api_endpoint_working(
        &self,
        method: &str,
        endpoint: &str,
        payload: Option<Value>,
    ) -> Result<bool> {
        let url = format!("{}{}", API_BASE_URL, endpoint);
        let token = self.jwt_token.as_ref().unwrap();

        let response = match method.to_uppercase().as_str() {
            "GET" => {
                self.client
                    .get(&url)
                    .header("Authorization", format!("Bearer {}", token))
                    .send()
                    .await?
            }
            "POST" => {
                self.client
                    .post(&url)
                    .header("Authorization", format!("Bearer {}", token))
                    .json(&payload.unwrap_or(json!({})))
                    .send()
                    .await?
            }
            _ => anyhow::bail!("不支持的HTTP方法: {}", method),
        };

        // 返回2xx或3xx状态码表示API正常工作
        Ok(response.status().is_success() || response.status().is_redirection())
    }
}

/// 测试1: 验证多聊天室API端点已被删除
///
/// 【功能】: 确认7个多聊天室管理相关端点返回404
/// 【测试内容】:
/// - GET /api/chat/rooms - 获取聊天室列表
/// - POST /api/chat/rooms - 创建聊天室
/// - GET /api/chat/rooms/{id} - 获取聊天室详情
/// - POST /api/chat/rooms/{id}/join - 加入聊天室
/// - POST /api/chat/rooms/{id}/leave - 离开聊天室
/// - GET /api/chat/rooms/{id}/messages - 获取聊天室消息
/// - POST /api/chat/rooms/{id}/messages - 发送消息到聊天室
#[tokio::test]
async fn test_multi_chat_room_apis_deleted() -> Result<()> {
    println!("🗑️ 开始测试: 验证多聊天室API端点已被删除");

    let mut helper = ChatApiDeletionTestHelper::new();

    // 步骤1: 用户登录
    println!("📝 步骤1: 用户登录");
    helper.login().await?;
    println!("✅ 登录成功");

    // 步骤2: 测试需要删除的API端点
    println!("📝 步骤2: 测试需要删除的API端点");

    let test_room_id = Uuid::new_v4();

    // 预先格式化端点路径以避免借用检查器问题
    let room_detail_endpoint = format!("/chat/rooms/{}", test_room_id);
    let join_room_endpoint = format!("/chat/rooms/{}/join", test_room_id);
    let leave_room_endpoint = format!("/chat/rooms/{}/leave", test_room_id);
    let get_messages_endpoint = format!("/chat/rooms/{}/messages", test_room_id);
    let send_message_endpoint = format!("/chat/rooms/{}/messages", test_room_id);

    let deleted_endpoints = vec![
        ("GET", "/chat/rooms", "获取聊天室列表"),
        ("POST", "/chat/rooms", "创建聊天室"),
        ("GET", room_detail_endpoint.as_str(), "获取聊天室详情"),
        ("POST", join_room_endpoint.as_str(), "加入聊天室"),
        ("POST", leave_room_endpoint.as_str(), "离开聊天室"),
        ("GET", get_messages_endpoint.as_str(), "获取聊天室消息"),
        ("POST", send_message_endpoint.as_str(), "发送消息到聊天室"),
    ];

    for (method, endpoint, description) in deleted_endpoints {
        println!("🔍 测试 {} {} - {}", method, endpoint, description);

        let is_deleted = helper.test_api_endpoint_deleted(method, endpoint).await?;
        assert!(
            is_deleted,
            "API端点 {} {} 应该返回404或405（已删除）",
            method, endpoint
        );

        println!("✅ {} {} 已正确删除", method, endpoint);
        sleep(Duration::from_millis(100)).await; // 避免请求过于频繁
    }

    println!("🎉 所有多聊天室API端点删除验证通过");
    Ok(())
}

/// 测试2: 验证保留的核心API端点正常工作
///
/// 【功能】: 确认3个保留的核心API端点仍然正常工作
/// 【测试内容】:
/// - POST /api/chat/send - 简单聊天消息发送
/// - GET /api/messages/search - 消息搜索
/// - GET /api/messages/chat-room/{id} - 获取聊天室消息
#[tokio::test]
async fn test_core_chat_apis_working() -> Result<()> {
    println!("✅ 开始测试: 验证保留的核心API端点正常工作");

    let mut helper = ChatApiDeletionTestHelper::new();

    // 步骤1: 用户登录
    println!("📝 步骤1: 用户登录");
    helper.login().await?;
    println!("✅ 登录成功");

    // 步骤2: 测试保留的核心API端点
    println!("📝 步骤2: 测试保留的核心API端点");

    // 测试简单聊天消息发送
    println!("🔍 测试 POST /api/chat/send - 简单聊天消息发送");
    let chat_message_payload = json!({
        "message_type": "Text",
        "content": "测试消息",
        "sender": {
            "username": TEST_USER_USERNAME,
            "user_id": "test-user-id"
        },
        "timestamp": chrono::Utc::now().to_rfc3339()
    });

    let is_working = helper
        .test_api_endpoint_working("POST", "/chat/send", Some(chat_message_payload))
        .await?;
    assert!(is_working, "POST /api/chat/send 应该正常工作");
    println!("✅ POST /api/chat/send 正常工作");

    // 测试消息搜索
    println!("🔍 测试 GET /api/messages/search - 消息搜索");
    let is_working = helper
        .test_api_endpoint_working("GET", "/messages/search?keyword=test", None)
        .await?;
    assert!(is_working, "GET /api/messages/search 应该正常工作");
    println!("✅ GET /api/messages/search 正常工作");

    // 测试获取聊天室消息
    println!("🔍 测试 GET /api/messages/chat-room/{{id}} - 获取聊天室消息");
    let test_room_id = Uuid::new_v4();
    let endpoint = format!("/messages/chat-room/{}", test_room_id);
    let is_working = helper
        .test_api_endpoint_working("GET", &endpoint, None)
        .await?;
    assert!(
        is_working,
        "GET /api/messages/chat-room/{{id}} 应该正常工作"
    );
    println!("✅ GET /api/messages/chat-room/{{id}} 正常工作");

    println!("🎉 所有保留的核心API端点工作验证通过");
    Ok(())
}

/// 测试3: 验证WebSocket功能不受影响
///
/// 【功能】: 确认WebSocket聊天功能在API删除后仍然正常工作
/// 【测试内容】:
/// - WebSocket连接建立
/// - 消息发送和接收
/// - 连接状态管理
#[tokio::test]
async fn test_websocket_functionality_unaffected() -> Result<()> {
    println!("🔌 开始测试: 验证WebSocket功能不受影响");

    let mut helper = ChatApiDeletionTestHelper::new();

    // 步骤1: 用户登录
    println!("📝 步骤1: 用户登录");
    let token = helper.login().await?;
    println!("✅ 登录成功");

    // 步骤2: 建立WebSocket连接
    println!("📝 步骤2: 建立WebSocket连接");
    let ws_url = format!("ws://{}:3000/ws?token={}", "127.0.0.1", token);

    // 使用tokio-tungstenite建立WebSocket连接
    let (_ws_stream, _) = tokio_tungstenite::connect_async(&ws_url).await?;
    println!("✅ WebSocket连接建立成功");

    // 步骤3: 测试基础通信
    println!("📝 步骤3: 测试WebSocket基础通信");
    // 这里可以添加更详细的WebSocket测试逻辑
    // 由于WebSocket测试较为复杂，这里主要验证连接能够建立

    println!("✅ WebSocket基础通信测试通过");

    println!("🎉 WebSocket功能不受影响验证通过");
    Ok(())
}

/// 测试4: 验证数据库完整性和依赖关系
///
/// 【功能】: 确认API删除后数据库结构和依赖关系保持完整
/// 【测试内容】:
/// - 验证现有数据不受影响
/// - 检查外键约束完整性
/// - 确认相关服务正常工作
#[tokio::test]
async fn test_database_integrity_after_deletion() -> Result<()> {
    println!("🗄️ 开始测试: 验证数据库完整性和依赖关系");

    let mut helper = ChatApiDeletionTestHelper::new();

    // 步骤1: 用户登录
    println!("📝 步骤1: 用户登录");
    helper.login().await?;
    println!("✅ 登录成功");

    // 步骤2: 测试用户相关API仍然正常工作
    println!("📝 步骤2: 测试用户相关API");
    let is_working = helper
        .test_api_endpoint_working("GET", "/online-users", None)
        .await?;
    assert!(is_working, "GET /api/online-users 应该正常工作");
    println!("✅ 用户相关API正常工作");

    // 步骤3: 测试任务管理API不受影响
    println!("📝 步骤3: 测试任务管理API");
    let is_working = helper
        .test_api_endpoint_working("GET", "/tasks", None)
        .await?;
    assert!(is_working, "GET /api/tasks 应该正常工作");
    println!("✅ 任务管理API不受影响");

    // 步骤4: 测试认证API不受影响
    println!("📝 步骤4: 测试认证API");
    let logout_payload = json!({});
    let is_working = helper
        .test_api_endpoint_working("POST", "/auth/logout", Some(logout_payload))
        .await?;
    assert!(is_working, "POST /api/auth/logout 应该正常工作");
    println!("✅ 认证API不受影响");

    println!("🎉 数据库完整性和依赖关系验证通过");
    Ok(())
}

/// 测试5: 验证错误处理和边界条件
///
/// 【功能】: 测试删除操作的错误处理和边界条件
/// 【测试内容】:
/// - 无效的API路径处理
/// - 错误状态码验证
/// - 异常情况处理
#[tokio::test]
async fn test_error_handling_and_edge_cases() -> Result<()> {
    println!("⚠️ 开始测试: 验证错误处理和边界条件");

    let mut helper = ChatApiDeletionTestHelper::new();

    // 步骤1: 用户登录
    println!("📝 步骤1: 用户登录");
    helper.login().await?;
    println!("✅ 登录成功");

    // 步骤2: 测试无效的聊天室ID
    println!("📝 步骤2: 测试无效的聊天室ID");
    let invalid_room_id = "invalid-room-id";
    let endpoint = format!("/chat/rooms/{}", invalid_room_id);
    let is_deleted = helper.test_api_endpoint_deleted("GET", &endpoint).await?;
    assert!(is_deleted, "无效聊天室ID的API应该返回404或405");
    println!("✅ 无效聊天室ID处理正确");

    // 步骤3: 测试不存在的端点
    println!("📝 步骤3: 测试不存在的端点");
    let nonexistent_endpoints = vec![
        "/chat/rooms/nonexistent",
        "/chat/invalid-endpoint",
        "/messages/invalid-endpoint",
    ];

    for endpoint in nonexistent_endpoints {
        let is_deleted = helper.test_api_endpoint_deleted("GET", endpoint).await?;
        assert!(is_deleted, "不存在的端点 {} 应该返回404或405", endpoint);
        println!("✅ 不存在的端点 {} 处理正确", endpoint);
    }

    // 步骤4: 测试HTTP方法不匹配
    println!("📝 步骤4: 测试HTTP方法不匹配");
    // 尝试对已删除的端点使用错误的HTTP方法
    let is_deleted = helper
        .test_api_endpoint_deleted("DELETE", "/chat/rooms")
        .await?;
    assert!(is_deleted, "错误HTTP方法的API应该返回404或405");
    println!("✅ HTTP方法不匹配处理正确");

    println!("🎉 错误处理和边界条件验证通过");
    Ok(())
}

/// 测试6: 性能和稳定性验证
///
/// 【功能】: 验证API删除后系统性能和稳定性
/// 【测试内容】:
/// - 并发请求处理
/// - 响应时间验证
/// - 系统稳定性检查
#[tokio::test]
async fn test_performance_and_stability() -> Result<()> {
    println!("⚡ 开始测试: 性能和稳定性验证");

    let mut helper = ChatApiDeletionTestHelper::new();

    // 步骤1: 用户登录
    println!("📝 步骤1: 用户登录");
    helper.login().await?;
    println!("✅ 登录成功");

    // 步骤2: 并发测试已删除的API端点
    println!("📝 步骤2: 并发测试已删除的API端点");
    let concurrent_requests = 10;
    let mut handles = Vec::new();

    for i in 0..concurrent_requests {
        let helper_clone = ChatApiDeletionTestHelper::new();
        let mut helper_clone = helper_clone;
        helper_clone.jwt_token = helper.jwt_token.clone();

        let handle = tokio::spawn(async move {
            let result = helper_clone
                .test_api_endpoint_deleted("GET", "/chat/rooms")
                .await;
            println!("🔄 并发请求 {} 完成", i + 1);
            result
        });
        handles.push(handle);
    }

    // 等待所有并发请求完成
    for (i, handle) in handles.into_iter().enumerate() {
        let result = handle.await??;
        assert!(result, "并发请求 {} 应该返回404或405", i + 1);
    }

    println!("✅ 并发请求处理正确");

    // 步骤3: 测试保留API的响应时间
    println!("📝 步骤3: 测试保留API的响应时间");
    let start_time = std::time::Instant::now();

    let chat_message_payload = json!({
        "message_type": "Text",
        "content": "性能测试消息",
        "sender": {
            "username": TEST_USER_USERNAME,
            "user_id": "test-user-id"
        },
        "timestamp": chrono::Utc::now().to_rfc3339()
    });

    let is_working = helper
        .test_api_endpoint_working("POST", "/chat/send", Some(chat_message_payload))
        .await?;
    let response_time = start_time.elapsed();

    assert!(is_working, "保留的API应该正常工作");
    assert!(
        response_time < Duration::from_secs(5),
        "API响应时间应该小于5秒"
    );

    println!("✅ API响应时间: {:?}", response_time);

    println!("🎉 性能和稳定性验证通过");
    Ok(())
}
