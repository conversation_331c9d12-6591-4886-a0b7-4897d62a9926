/**
 * Jest测试设置文件
 * 配置全局测试环境和mock
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-23
 */

// 扩展Jest匹配器
import 'jest-extended';

// 全局测试超时设置
jest.setTimeout(10000);

// 模拟浏览器环境
Object.defineProperty(window, 'location', {
  value: {
    href: 'http://localhost:3000',
    origin: 'http://localhost:3000',
    protocol: 'http:',
    host: 'localhost:3000',
    hostname: 'localhost',
    port: '3000',
    pathname: '/',
    search: '',
    hash: ''
  },
  writable: true
});

// 模拟AbortController
if (!global.AbortController) {
  global.AbortController = class AbortController {
    constructor() {
      this.signal = {
        aborted: false,
        addEventListener: jest.fn(),
        removeEventListener: jest.fn()
      };
    }
    
    abort() {
      this.signal.aborted = true;
    }
  };
}

// 模拟setTimeout和clearTimeout
global.setTimeout = jest.fn((fn, delay) => {
  if (delay === 0) {
    fn();
    return 1;
  }
  // 使用原生setTimeout避免递归
  return global.originalSetTimeout(fn, delay);
});

// 保存原生setTimeout
global.originalSetTimeout = global.setTimeout;
global.clearTimeout = jest.fn();

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
});

// 测试前后钩子
beforeEach(() => {
  // 清理所有定时器
  jest.clearAllTimers();
  
  // 重置所有mock
  jest.clearAllMocks();
});

afterEach(() => {
  // 清理DOM
  document.body.innerHTML = '';
  
  // 清理localStorage
  if (global.localStorage) {
    global.localStorage.clear();
  }
});

// 全局测试工具函数
global.testUtils = {
  /**
   * 创建模拟的fetch响应
   * @param {any} data - 响应数据
   * @param {number} status - HTTP状态码
   * @param {boolean} ok - 是否成功
   * @returns {Promise} 模拟的fetch响应
   */
  createMockResponse: (data, status = 200, ok = true) => {
    return Promise.resolve({
      ok,
      status,
      statusText: ok ? 'OK' : 'Error',
      headers: new Map([['content-type', 'application/json']]),
      json: async () => data,
      text: async () => typeof data === 'string' ? data : JSON.stringify(data)
    });
  },

  /**
   * 创建模拟的网络错误
   * @param {string} message - 错误消息
   * @returns {Promise} 拒绝的Promise
   */
  createNetworkError: (message = '网络错误') => {
    return Promise.reject(new TypeError(message));
  },

  /**
   * 等待指定时间
   * @param {number} ms - 等待毫秒数
   * @returns {Promise} 延迟Promise
   */
  delay: (ms) => new Promise(resolve => setTimeout(resolve, ms)),

  /**
   * 模拟用户已登录状态
   * @param {string} token - JWT令牌
   */
  mockLoggedInUser: (token = 'test-jwt-token') => {
    if (global.localStorage) {
      global.localStorage.setItem('authToken', token);
    }
  },

  /**
   * 模拟用户未登录状态
   */
  mockLoggedOutUser: () => {
    if (global.localStorage) {
      global.localStorage.removeItem('authToken');
    }
  }
};

// 控制台输出抑制（可选）
if (process.env.NODE_ENV === 'test') {
  // 在测试环境中抑制某些console输出
  const originalConsoleError = console.error;
  console.error = (...args) => {
    // 过滤掉某些预期的错误消息
    const message = args[0];
    if (typeof message === 'string' && message.includes('Warning:')) {
      return;
    }
    originalConsoleError.apply(console, args);
  };
}
