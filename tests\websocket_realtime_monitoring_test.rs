//! # WebSocket实时监控面板测试
//!
//! 测试WebSocket实时监控面板的功能，包括：
//! - WebSocket连接建立和数据传输
//! - 实时统计数据更新
//! - 图表展示功能
//! - 连接质量监控

use anyhow::Result;
use reqwest::Client;
use serde_json::Value;
use std::time::Duration;

// 导入E2E测试辅助模块
mod e2e {
    pub mod helpers;
}
use e2e::helpers::E2EConfig;

/// 初始化测试环境
async fn setup_test_environment() -> Result<E2EConfig> {
    // 启动测试服务器
    e2e::helpers::test_server::start_global_test_server().await?;

    // 加载测试配置
    let config = E2EConfig::from_env()?;

    println!("✅ WebSocket监控测试环境初始化完成");
    Ok(config)
}

/// 测试WebSocket监控面板页面访问
#[tokio::test]
async fn test_websocket_monitoring_page_access() -> Result<()> {
    let config = setup_test_environment().await?;
    let client = Client::new();
    let base_url = &config.base_url;

    // 访问WebSocket监控面板页面
    let response = client
        .get(&format!("{}/websocket-stats.html", base_url))
        .timeout(Duration::from_secs(10))
        .send()
        .await?;

    assert_eq!(response.status(), 200);

    let html_content = response.text().await?;

    // 验证页面包含必要的元素
    assert!(html_content.contains("WebSocket实时监控面板"));
    assert!(html_content.contains("connectionStatus"));
    assert!(html_content.contains("activeConnections"));
    assert!(html_content.contains("Chart.js"));
    assert!(html_content.contains("websocket-monitoring.js"));

    println!("✅ WebSocket监控面板页面访问测试通过");
    Ok(())
}

/// 测试WebSocket统计API端点数据结构
#[tokio::test]
async fn test_websocket_stats_api_structure() -> Result<()> {
    let config = setup_test_environment().await?;
    let client = Client::new();
    let base_url = &config.base_url;

    let response = client
        .get(&format!("{}/api/websocket/stats", base_url))
        .timeout(Duration::from_secs(10))
        .send()
        .await?;

    assert_eq!(response.status(), 200);

    let stats: Value = response.json().await?;

    // 验证统计数据结构
    assert!(stats.get("connection_stats").is_some());
    assert!(stats.get("message_stats").is_some());
    assert!(stats.get("performance_metrics").is_some());
    assert!(stats.get("timestamp").is_some());

    // 验证连接统计字段
    let connection_stats = &stats["connection_stats"];
    assert!(connection_stats.get("active_connections").is_some());
    assert!(connection_stats.get("total_connections").is_some());
    assert!(connection_stats.get("unique_users").is_some());
    assert!(connection_stats.get("connections_by_type").is_some());

    // 验证消息统计字段
    let message_stats = &stats["message_stats"];
    assert!(message_stats.get("total_sent").is_some());
    assert!(message_stats.get("total_received").is_some());
    assert!(message_stats.get("messages_by_type").is_some());

    println!("✅ WebSocket统计API数据结构测试通过");
    Ok(())
}

/// 测试WebSocket连接信息API
#[tokio::test]
async fn test_websocket_connections_api() -> Result<()> {
    let config = setup_test_environment().await?;
    let client = Client::new();
    let base_url = &config.base_url;

    let response = client
        .get(&format!("{}/api/websocket/connections", base_url))
        .timeout(Duration::from_secs(10))
        .send()
        .await?;

    assert_eq!(response.status(), 200);

    let connections: Value = response.json().await?;

    // 验证连接信息结构
    assert!(connections.get("active_connections").is_some());
    assert!(connections.get("total_connections_today").is_some());
    assert!(connections.get("connections_by_type").is_some());
    assert!(connections.get("connection_quality").is_some());
    assert!(connections.get("timestamp").is_some());

    println!("✅ WebSocket连接信息API测试通过");
    Ok(())
}

/// 测试WebSocket性能指标API
#[tokio::test]
async fn test_websocket_metrics_api() -> Result<()> {
    let config = setup_test_environment().await?;
    let client = Client::new();
    let base_url = &config.base_url;

    let response = client
        .get(&format!("{}/api/websocket/metrics", base_url))
        .timeout(Duration::from_secs(10))
        .send()
        .await?;

    assert_eq!(response.status(), 200);

    let metrics: Value = response.json().await?;

    // 验证性能指标结构
    assert!(metrics.get("performance").is_some());
    assert!(metrics.get("reliability").is_some());
    assert!(metrics.get("resource_usage").is_some());
    assert!(metrics.get("error_statistics").is_some());
    assert!(metrics.get("timestamp").is_some());

    // 验证性能指标详细字段
    let performance = &metrics["performance"];
    assert!(performance.get("message_throughput_per_second").is_some());
    assert!(performance.get("average_latency_ms").is_some());
    assert!(performance.get("p95_latency_ms").is_some());
    assert!(performance.get("p99_latency_ms").is_some());

    println!("✅ WebSocket性能指标API测试通过");
    Ok(())
}

/// 测试WebSocket稳定性API
#[tokio::test]
async fn test_websocket_stability_api() -> Result<()> {
    let config = setup_test_environment().await?;
    let client = Client::new();
    let base_url = &config.base_url;

    let response = client
        .get(&format!("{}/api/websocket/stability", base_url))
        .timeout(Duration::from_secs(10))
        .send()
        .await?;

    assert_eq!(response.status(), 200);

    let stability: Value = response.json().await?;

    // 验证稳定性信息结构
    assert!(stability.get("stability").is_some());
    assert!(stability.get("heartbeat").is_some());
    assert!(stability.get("reconnection").is_some());
    assert!(stability.get("timestamp").is_some());

    // 验证稳定性详细字段
    let stability_info = &stability["stability"];
    assert!(stability_info.get("total_connections").is_some());
    assert!(stability_info.get("active_connections").is_some());
    assert!(stability_info.get("healthy_connections").is_some());
    assert!(stability_info.get("health_rate").is_some());

    println!("✅ WebSocket稳定性API测试通过");
    Ok(())
}

/// 测试监控API的响应时间
#[tokio::test]
async fn test_monitoring_api_response_time() -> Result<()> {
    let config = setup_test_environment().await?;
    let client = Client::new();
    let base_url = &config.base_url;

    let endpoints = vec![
        "/api/websocket/stats",
        "/api/websocket/connections",
        "/api/websocket/metrics",
        "/api/websocket/stability",
    ];

    for endpoint in endpoints {
        let start_time = std::time::Instant::now();

        let response = client
            .get(&format!("{}{}", base_url, endpoint))
            .timeout(Duration::from_secs(10))
            .send()
            .await?;

        let response_time = start_time.elapsed();

        // 验证响应成功
        assert_eq!(response.status(), 200);

        // 验证响应时间在合理范围内（小于1秒）
        assert!(
            response_time < Duration::from_secs(1),
            "API {} 响应时间过长: {:?}",
            endpoint,
            response_time
        );

        println!("✅ {} 响应时间: {:?}", endpoint, response_time);
    }

    println!("✅ 监控API响应时间测试通过");
    Ok(())
}

/// 测试监控面板的错误处理
#[tokio::test]
async fn test_monitoring_error_handling() -> Result<()> {
    let config = setup_test_environment().await?;
    let client = Client::new();
    let base_url = &config.base_url;

    // 测试不存在的监控端点
    let response = client
        .get(&format!("{}/api/websocket/nonexistent", base_url))
        .timeout(Duration::from_secs(10))
        .send()
        .await?;

    assert_eq!(response.status(), 404);

    println!("✅ 监控面板错误处理测试通过");
    Ok(())
}
