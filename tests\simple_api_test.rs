//! # 简单API测试
//!
//! 直接测试API端点，验证服务器是否正常工作

use anyhow::{Context, Result};
use serde_json::json;
use std::time::Duration;

#[tokio::main]
async fn main() -> Result<()> {
    println!("🚀 开始简单API测试...");

    let base_url = "http://127.0.0.1:3000";
    let client = reqwest::Client::new();

    // 测试1: 健康检查
    println!("\n1. 测试健康检查API...");
    let health_response = client
        .get(&format!("{}/api/health", base_url))
        .timeout(Duration::from_secs(10))
        .send()
        .await
        .context("健康检查API请求失败")?;

    println!("   状态码: {}", health_response.status());
    if health_response.status().is_success() {
        let body = health_response.text().await?;
        println!("   响应: {}", body);
        println!("   ✅ 健康检查API正常");
    } else {
        println!("   ❌ 健康检查API异常");
    }

    // 测试2: 注册API
    println!("\n2. 测试用户注册API...");
    let register_response = client
        .post(&format!("{}/api/auth/register", base_url))
        .json(&json!({
            "username": "testuser123",
            "email": "<EMAIL>",
            "password": "testpassword123"
        }))
        .timeout(Duration::from_secs(10))
        .send()
        .await
        .context("注册API请求失败")?;

    let register_status = register_response.status();
    println!("   状态码: {}", register_status);
    let register_body = register_response.text().await?;
    println!("   响应: {}", register_body);

    if register_status.is_success() || register_status == 409 {
        println!("   ✅ 注册API正常 (成功或用户已存在)");
    } else {
        println!("   ❌ 注册API异常");
    }

    // 测试3: 登录API
    println!("\n3. 测试用户登录API...");
    let login_response = client
        .post(&format!("{}/api/auth/login", base_url))
        .json(&json!({
            "username": "testuser123",
            "password": "testpassword123"
        }))
        .timeout(Duration::from_secs(10))
        .send()
        .await
        .context("登录API请求失败")?;

    let login_status = login_response.status();
    println!("   状态码: {}", login_status);
    let login_body = login_response.text().await?;
    println!("   响应: {}", login_body);

    if login_status.is_success() {
        println!("   ✅ 登录API正常");
    } else {
        println!("   ❌ 登录API异常");
    }

    // 测试4: 任务列表API
    println!("\n4. 测试任务列表API...");
    let tasks_response = client
        .get(&format!("{}/api/tasks", base_url))
        .timeout(Duration::from_secs(10))
        .send()
        .await
        .context("任务列表API请求失败")?;

    let tasks_status = tasks_response.status();
    println!("   状态码: {}", tasks_status);
    let tasks_body = tasks_response.text().await?;
    println!("   响应: {}", tasks_body);

    if tasks_status.is_success() || tasks_status == 401 {
        println!("   ✅ 任务列表API正常 (成功或需要认证)");
    } else {
        println!("   ❌ 任务列表API异常");
    }

    // 测试5: WebSocket端点
    println!("\n5. 测试WebSocket端点...");
    let ws_response = client
        .get(&format!("{}/ws", base_url))
        .timeout(Duration::from_secs(10))
        .send()
        .await
        .context("WebSocket端点请求失败")?;

    let ws_status = ws_response.status();
    println!("   状态码: {}", ws_status);
    let ws_body = ws_response.text().await?;
    println!("   响应: {}", ws_body);

    if ws_status == 426 || ws_status.is_success() {
        println!("   ✅ WebSocket端点正常 (426 Upgrade Required是正常的)");
    } else {
        println!("   ❌ WebSocket端点异常");
    }

    // 测试6: 缓存健康检查
    println!("\n6. 测试缓存健康检查API...");
    let cache_response = client
        .get(&format!("{}/api/cache/health", base_url))
        .timeout(Duration::from_secs(10))
        .send()
        .await
        .context("缓存健康检查API请求失败")?;

    let cache_status = cache_response.status();
    println!("   状态码: {}", cache_status);
    let cache_body = cache_response.text().await?;
    println!("   响应: {}", cache_body);

    if cache_status.is_success() {
        println!("   ✅ 缓存健康检查API正常");
    } else {
        println!("   ❌ 缓存健康检查API异常");
    }

    // 测试7: 数据库健康检查
    println!("\n7. 测试数据库健康检查API...");
    let db_response = client
        .get(&format!("{}/api/database/health", base_url))
        .timeout(Duration::from_secs(10))
        .send()
        .await
        .context("数据库健康检查API请求失败")?;

    let db_status = db_response.status();
    println!("   状态码: {}", db_status);
    let db_body = db_response.text().await?;
    println!("   响应: {}", db_body);

    if db_status.is_success() {
        println!("   ✅ 数据库健康检查API正常");
    } else {
        println!("   ❌ 数据库健康检查API异常");
    }

    println!("\n🎊 简单API测试完成！");
    Ok(())
}
