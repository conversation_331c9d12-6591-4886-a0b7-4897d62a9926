/**
 * API客户端集成测试
 * 测试API客户端与实际后端的集成
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-23
 */

const { test, expect } = require('@playwright/test');

// 测试配置
const BASE_URL = 'http://127.0.0.1:3000';
const TEST_USER = {
    username: 'testuser456',
    password: 'password123'
};

test.describe('API客户端集成测试', () => {
    let page;

    test.beforeAll(async ({ browser }) => {
        page = await browser.newPage();
        await page.goto(BASE_URL);
    });

    test.afterAll(async () => {
        await page.close();
    });

    test.beforeEach(async () => {
        // 清理localStorage
        await page.evaluate(() => {
            localStorage.clear();
        });
        
        // 重新加载页面
        await page.reload();
        await page.waitForLoadState('networkidle');
    });

    test('应该成功加载API模块', async () => {
        // 检查API模块是否正确加载
        const apiModuleLoaded = await page.evaluate(() => {
            return typeof window.api !== 'undefined';
        });
        
        expect(apiModuleLoaded).toBe(true);
    });

    test('应该成功进行用户注册', async () => {
        // 注入API客户端测试代码
        const registrationResult = await page.evaluate(async (testUser) => {
            try {
                // 模拟导入API模块
                const { authAPI } = await import('/static/js/modules/api.js');
                
                // 执行注册
                const result = await authAPI.register(testUser.username, testUser.password);
                return { success: true, result };
            } catch (error) {
                return { 
                    success: false, 
                    error: error.message,
                    status: error.status 
                };
            }
        }, TEST_USER);

        // 验证注册结果
        if (registrationResult.success) {
            expect(registrationResult.result).toHaveProperty('message');
        } else {
            // 如果用户已存在，这是预期的
            expect(registrationResult.status).toBe(400);
        }
    });

    test('应该成功进行用户登录', async () => {
        const loginResult = await page.evaluate(async (testUser) => {
            try {
                const { authAPI } = await import('/static/js/modules/api.js');
                const result = await authAPI.login(testUser.username, testUser.password);
                return { success: true, result };
            } catch (error) {
                return { 
                    success: false, 
                    error: error.message,
                    status: error.status 
                };
            }
        }, TEST_USER);

        expect(loginResult.success).toBe(true);
        expect(loginResult.result).toHaveProperty('token');
        expect(loginResult.result).toHaveProperty('user');
    });

    test('应该在登录后能够获取任务列表', async () => {
        // 先登录
        await page.evaluate(async (testUser) => {
            const { authAPI } = await import('/static/js/modules/api.js');
            const loginResult = await authAPI.login(testUser.username, testUser.password);
            
            // 保存token到localStorage
            localStorage.setItem('axum_auth', JSON.stringify({
                token: loginResult.token,
                user: loginResult.user,
                timestamp: Date.now()
            }));
        }, TEST_USER);

        // 获取任务列表
        const tasksResult = await page.evaluate(async () => {
            try {
                const { taskAPI } = await import('/static/js/modules/api.js');
                const tasks = await taskAPI.fetchAll();
                return { success: true, tasks };
            } catch (error) {
                return { 
                    success: false, 
                    error: error.message,
                    status: error.status 
                };
            }
        });

        expect(tasksResult.success).toBe(true);
        expect(Array.isArray(tasksResult.tasks)).toBe(true);
    });

    test('应该能够创建新任务', async () => {
        // 先登录
        await page.evaluate(async (testUser) => {
            const { authAPI } = await import('/static/js/modules/api.js');
            const loginResult = await authAPI.login(testUser.username, testUser.password);
            localStorage.setItem('axum_auth', JSON.stringify({
                token: loginResult.token,
                user: loginResult.user,
                timestamp: Date.now()
            }));
        }, TEST_USER);

        // 创建新任务
        const newTask = {
            title: `测试任务 ${Date.now()}`,
            description: '这是一个API客户端测试任务',
            completed: false
        };

        const createResult = await page.evaluate(async (taskData) => {
            try {
                const { taskAPI } = await import('/static/js/modules/api.js');
                const task = await taskAPI.create(taskData);
                return { success: true, task };
            } catch (error) {
                return { 
                    success: false, 
                    error: error.message,
                    status: error.status 
                };
            }
        }, newTask);

        expect(createResult.success).toBe(true);
        expect(createResult.task).toHaveProperty('id');
        expect(createResult.task.title).toBe(newTask.title);
    });

    test('应该能够更新任务', async () => {
        // 先登录并创建任务
        const taskId = await page.evaluate(async (testUser) => {
            const { authAPI, taskAPI } = await import('/static/js/modules/api.js');
            const loginResult = await authAPI.login(testUser.username, testUser.password);
            localStorage.setItem('axum_auth', JSON.stringify({
                token: loginResult.token,
                user: loginResult.user,
                timestamp: Date.now()
            }));

            const newTask = {
                title: `更新测试任务 ${Date.now()}`,
                description: '待更新的任务',
                completed: false
            };
            const task = await taskAPI.create(newTask);
            return task.id;
        }, TEST_USER);

        // 更新任务
        const updateData = {
            title: '已更新的任务标题',
            completed: true
        };

        const updateResult = await page.evaluate(async ({ taskId, updateData }) => {
            try {
                const { taskAPI } = await import('/static/js/modules/api.js');
                const task = await taskAPI.update(taskId, updateData);
                return { success: true, task };
            } catch (error) {
                return { 
                    success: false, 
                    error: error.message,
                    status: error.status 
                };
            }
        }, { taskId, updateData });

        expect(updateResult.success).toBe(true);
        expect(updateResult.task.title).toBe(updateData.title);
        expect(updateResult.task.completed).toBe(updateData.completed);
    });

    test('应该能够删除任务', async () => {
        // 先登录并创建任务
        const taskId = await page.evaluate(async (testUser) => {
            const { authAPI, taskAPI } = await import('/static/js/modules/api.js');
            const loginResult = await authAPI.login(testUser.username, testUser.password);
            localStorage.setItem('axum_auth', JSON.stringify({
                token: loginResult.token,
                user: loginResult.user,
                timestamp: Date.now()
            }));

            const newTask = {
                title: `删除测试任务 ${Date.now()}`,
                description: '待删除的任务',
                completed: false
            };
            const task = await taskAPI.create(newTask);
            return task.id;
        }, TEST_USER);

        // 删除任务
        const deleteResult = await page.evaluate(async (taskId) => {
            try {
                const { taskAPI } = await import('/static/js/modules/api.js');
                await taskAPI.delete(taskId);
                return { success: true };
            } catch (error) {
                return { 
                    success: false, 
                    error: error.message,
                    status: error.status 
                };
            }
        }, taskId);

        expect(deleteResult.success).toBe(true);

        // 验证任务已被删除
        const fetchResult = await page.evaluate(async (taskId) => {
            try {
                const { taskAPI } = await import('/static/js/modules/api.js');
                await taskAPI.fetchById(taskId);
                return { found: true };
            } catch (error) {
                return { found: false, status: error.status };
            }
        }, taskId);

        expect(fetchResult.found).toBe(false);
        expect(fetchResult.status).toBe(404);
    });

    test('应该正确处理认证错误', async () => {
        // 不登录直接访问受保护的资源
        const unauthorizedResult = await page.evaluate(async () => {
            try {
                const { taskAPI } = await import('/static/js/modules/api.js');
                await taskAPI.create({ title: '未授权任务' });
                return { success: true };
            } catch (error) {
                return { 
                    success: false, 
                    error: error.message,
                    status: error.status 
                };
            }
        });

        expect(unauthorizedResult.success).toBe(false);
        expect(unauthorizedResult.status).toBe(401);
    });

    test('应该正确处理网络错误', async () => {
        // 模拟网络错误
        await page.route('**/api/**', route => {
            route.abort('failed');
        });

        const networkErrorResult = await page.evaluate(async () => {
            try {
                const { authAPI } = await import('/static/js/modules/api.js');
                await authAPI.login('test', 'test');
                return { success: true };
            } catch (error) {
                return { 
                    success: false, 
                    error: error.message,
                    name: error.name
                };
            }
        });

        expect(networkErrorResult.success).toBe(false);
        expect(networkErrorResult.name).toBe('TypeError');
    });

    test('应该正确处理超时', async () => {
        // 模拟慢响应
        await page.route('**/api/auth/login', route => {
            setTimeout(() => {
                route.fulfill({
                    status: 200,
                    contentType: 'application/json',
                    body: JSON.stringify({ token: 'test', user: { id: 1 } })
                });
            }, 15000); // 15秒延迟，超过10秒超时
        });

        const timeoutResult = await page.evaluate(async () => {
            try {
                const { authAPI } = await import('/static/js/modules/api.js');
                await authAPI.login('test', 'test');
                return { success: true };
            } catch (error) {
                return { 
                    success: false, 
                    error: error.message,
                    name: error.name
                };
            }
        });

        expect(timeoutResult.success).toBe(false);
        // 超时错误可能是AbortError或TypeError
        expect(['AbortError', 'TypeError']).toContain(timeoutResult.name);
    });

    test('应该记录请求日志', async () => {
        // 监听console.log
        const logs = [];
        page.on('console', msg => {
            if (msg.type() === 'log' && msg.text().includes('API请求')) {
                logs.push(msg.text());
            }
        });

        await page.evaluate(async (testUser) => {
            try {
                const { authAPI } = await import('/static/js/modules/api.js');
                await authAPI.login(testUser.username, testUser.password);
            } catch (error) {
                // 忽略错误，我们只关心日志
            }
        }, TEST_USER);

        // 等待日志记录
        await page.waitForTimeout(1000);

        expect(logs.length).toBeGreaterThan(0);
        expect(logs.some(log => log.includes('POST /api/auth/login'))).toBe(true);
    });
});
