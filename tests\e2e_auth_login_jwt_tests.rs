//! 用户登录与JWT Token验证的E2E测试
//!
//! 本测试文件专门针对用户登录和JWT Token验证功能，遵循TDD原则：
//! 1. 先编写测试用例，明确期望行为
//! 2. 运行测试确保失败（红色阶段）
//! 3. 实现最小可行代码使测试通过（绿色阶段）
//! 4. 重构优化代码（重构阶段）
//!
//! 测试覆盖范围：
//! - 用户登录成功场景
//! - 用户登录失败场景（错误凭据）
//! - JWT Token生成和验证
//! - JWT Token过期处理
//! - 无效Token处理
//! - 认证中间件测试
//!
//! 遵循rust_axum_Rules.md规范：
//! - 使用清晰的函数命名（test_login_success等）
//! - 详细的中文注释
//! - 完整的错误处理
//! - 遵循DRY和SOLID原则

use anyhow::Result;
use futures_util::future;
use serde_json::{Value, json};

// 导入E2E测试辅助模块
mod e2e {
    pub mod helpers;
}
use e2e::helpers::{ApiHelper, AuthHelper, DatabaseHelper, E2EConfig};

/// 测试配置常量
const TEST_USER_USERNAME: &str = "logintest_user";
const TEST_USER_PASSWORD: &str = "SecurePass123!";
const TEST_USER_EMAIL: &str = "<EMAIL>";
const INVALID_PASSWORD: &str = "wrongpassword";
const INVALID_USERNAME: &str = "nonexistentuser";
const API_TIMEOUT_SECONDS: u64 = 30;

/// 初始化测试环境
async fn setup_test_environment() -> Result<E2EConfig> {
    // 启动测试服务器
    e2e::helpers::test_server::start_global_test_server().await?;

    // 加载测试配置
    let config = E2EConfig::from_env()?;

    // 确保测试目录存在
    e2e::helpers::ensure_dir_exists(&config.report_dir)?;
    e2e::helpers::ensure_dir_exists(&config.screenshot_dir)?;
    e2e::helpers::ensure_dir_exists(&config.video_dir)?;

    // 清理之前的测试数据
    e2e::helpers::cleanup_test_data()?;

    println!("✅ 登录JWT测试环境初始化完成");
    Ok(config)
}

/// 清理测试用户数据
async fn cleanup_test_user(config: &E2EConfig) -> Result<()> {
    let db_helper = DatabaseHelper::new(config.clone());

    // 删除测试用户（如果存在）
    if let Err(e) = db_helper.delete_user_by_username(TEST_USER_USERNAME).await {
        // 忽略用户不存在的错误
        println!("清理测试用户时的警告: {}", e);
    }

    Ok(())
}

/// 创建测试用户（用于登录测试）
async fn create_test_user(auth_helper: &AuthHelper) -> Result<Value> {
    auth_helper
        .register_user(TEST_USER_USERNAME, TEST_USER_EMAIL, TEST_USER_PASSWORD)
        .await
}

/// 测试1: 用户登录成功场景
#[tokio::test]
async fn test_login_success() -> Result<()> {
    println!("\n🧪 开始测试: 用户登录成功场景");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 清理并创建测试用户
    cleanup_test_user(&config).await?;
    let register_result = create_test_user(&auth_helper).await?;

    // 如果注册失败，打印详细错误信息
    if register_result["status"].as_u64().unwrap() != 201 {
        println!(
            "注册响应详情: {}",
            serde_json::to_string_pretty(&register_result)?
        );
    }

    assert_eq!(
        register_result["status"].as_u64().unwrap(),
        201,
        "用户注册应该成功"
    );

    // 执行登录
    let login_result = auth_helper
        .login_user(TEST_USER_USERNAME, TEST_USER_PASSWORD)
        .await?;

    // 验证登录响应
    assert_eq!(
        login_result["status"].as_u64().unwrap(),
        200,
        "登录应该返回200状态码"
    );

    let response_data = &login_result["body"]["data"];
    assert!(
        response_data["access_token"].is_string(),
        "响应应包含access_token"
    );
    assert_eq!(
        response_data["token_type"].as_str().unwrap(),
        "Bearer",
        "token_type应为Bearer"
    );
    assert!(
        response_data["expires_in"].is_number(),
        "响应应包含expires_in"
    );

    // 验证用户信息
    let user_info = &response_data["user"];
    assert_eq!(
        user_info["username"].as_str().unwrap(),
        TEST_USER_USERNAME,
        "用户名应匹配"
    );
    assert_eq!(
        user_info["email"].as_str().unwrap(),
        TEST_USER_EMAIL,
        "邮箱应匹配"
    );
    assert!(user_info["id"].is_string(), "用户ID应存在");
    assert!(user_info["created_at"].is_string(), "创建时间应存在");

    println!("✅ 用户登录成功测试通过");
    Ok(())
}

/// 测试2: 用户登录失败 - 错误密码
#[tokio::test]
async fn test_login_failure_wrong_password() -> Result<()> {
    println!("\n🧪 开始测试: 用户登录失败 - 错误密码");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 清理并创建测试用户
    cleanup_test_user(&config).await?;
    let register_result = create_test_user(&auth_helper).await?;
    assert_eq!(
        register_result["status"].as_u64().unwrap(),
        201,
        "用户注册应该成功"
    );

    // 使用错误密码登录
    let login_result = auth_helper
        .login_user(TEST_USER_USERNAME, INVALID_PASSWORD)
        .await?;

    // 验证登录失败响应
    assert_eq!(
        login_result["status"].as_u64().unwrap(),
        401,
        "错误密码应该返回401状态码"
    );

    let error_response = &login_result["body"];
    assert!(error_response["error"].is_string(), "响应应包含错误信息");
    assert!(error_response["message"].is_string(), "响应应包含错误消息");

    println!("✅ 错误密码登录失败测试通过");
    Ok(())
}

/// 测试3: 用户登录失败 - 不存在的用户
#[tokio::test]
async fn test_login_failure_nonexistent_user() -> Result<()> {
    println!("\n🧪 开始测试: 用户登录失败 - 不存在的用户");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 确保用户不存在
    cleanup_test_user(&config).await?;

    // 使用不存在的用户名登录
    let login_result = auth_helper
        .login_user(INVALID_USERNAME, TEST_USER_PASSWORD)
        .await?;

    // 验证登录失败响应
    assert_eq!(
        login_result["status"].as_u64().unwrap(),
        401,
        "不存在用户应该返回401状态码"
    );

    let error_response = &login_result["body"];
    assert!(error_response["error"].is_string(), "响应应包含错误信息");
    assert!(error_response["message"].is_string(), "响应应包含错误消息");

    println!("✅ 不存在用户登录失败测试通过");
    Ok(())
}

/// 测试4: JWT Token验证成功
#[tokio::test]
async fn test_jwt_token_validation_success() -> Result<()> {
    println!("\n🧪 开始测试: JWT Token验证成功");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 清理并创建测试用户
    cleanup_test_user(&config).await?;
    let register_result = create_test_user(&auth_helper).await?;
    assert_eq!(
        register_result["status"].as_u64().unwrap(),
        201,
        "用户注册应该成功"
    );

    // 登录获取Token
    let token = auth_helper
        .get_auth_token(TEST_USER_USERNAME, TEST_USER_PASSWORD)
        .await?;
    assert!(!token.is_empty(), "应该获取到有效Token");

    // 验证Token
    let verify_result = auth_helper.verify_token(&token).await?;
    assert_eq!(
        verify_result["status"].as_u64().unwrap(),
        200,
        "有效Token验证应该成功"
    );

    let token_data = &verify_result["body"]["data"];
    assert!(token_data["sub"].is_string(), "Token应包含用户ID");
    assert_eq!(
        token_data["username"].as_str().unwrap(),
        TEST_USER_USERNAME,
        "Token应包含正确用户名"
    );
    assert!(token_data["exp"].is_number(), "Token应包含过期时间");
    assert!(token_data["iat"].is_number(), "Token应包含签发时间");

    println!("✅ JWT Token验证成功测试通过");
    Ok(())
}

/// 测试5: JWT Token验证失败 - 无效Token
#[tokio::test]
async fn test_jwt_token_validation_failure_invalid_token() -> Result<()> {
    println!("\n🧪 开始测试: JWT Token验证失败 - 无效Token");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 使用无效Token进行验证
    let invalid_token = "invalid.jwt.token";
    let verify_result = auth_helper.verify_token(invalid_token).await?;

    // 验证失败响应
    assert_eq!(
        verify_result["status"].as_u64().unwrap(),
        401,
        "无效Token应该返回401状态码"
    );

    let error_response = &verify_result["body"];
    assert!(error_response["error"].is_string(), "响应应包含错误信息");
    assert!(error_response["message"].is_string(), "响应应包含错误消息");

    println!("✅ 无效Token验证失败测试通过");
    Ok(())
}

/// 测试6: JWT Token验证失败 - 空Token
#[tokio::test]
async fn test_jwt_token_validation_failure_empty_token() -> Result<()> {
    println!("\n🧪 开始测试: JWT Token验证失败 - 空Token");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 使用空Token进行验证
    let empty_token = "";
    let verify_result = auth_helper.verify_token(empty_token).await?;

    // 验证失败响应
    assert_eq!(
        verify_result["status"].as_u64().unwrap(),
        401,
        "空Token应该返回401状态码"
    );

    let error_response = &verify_result["body"];
    assert!(error_response["error"].is_string(), "响应应包含错误信息");
    assert!(error_response["message"].is_string(), "响应应包含错误消息");

    println!("✅ 空Token验证失败测试通过");
    Ok(())
}

/// 测试7: 认证中间件保护的端点访问测试
#[tokio::test]
async fn test_protected_endpoint_with_valid_token() -> Result<()> {
    println!("\n🧪 开始测试: 认证中间件保护的端点访问 - 有效Token");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());
    let api_helper = ApiHelper::new(config.clone());

    // 清理并创建测试用户
    cleanup_test_user(&config).await?;
    let register_result = create_test_user(&auth_helper).await?;
    assert_eq!(
        register_result["status"].as_u64().unwrap(),
        201,
        "用户注册应该成功"
    );

    // 登录获取Token
    let token = auth_helper
        .get_auth_token(TEST_USER_USERNAME, TEST_USER_PASSWORD)
        .await?;
    assert!(!token.is_empty(), "应该获取到有效Token");

    // 使用有效Token访问受保护的端点（例如获取任务列表）
    let tasks_result = api_helper.get_tasks_with_auth(&token).await?;
    assert_eq!(
        tasks_result["status"].as_u64().unwrap(),
        200,
        "有效Token应该能访问受保护端点"
    );

    println!("✅ 有效Token访问受保护端点测试通过");
    Ok(())
}

/// 测试8: 认证中间件保护的端点访问测试 - 无Token
#[tokio::test]
async fn test_protected_endpoint_without_token() -> Result<()> {
    println!("\n🧪 开始测试: 认证中间件保护的端点访问 - 无Token");

    let config = setup_test_environment().await?;
    let api_helper = ApiHelper::new(config.clone());

    // 不提供Token访问受保护的端点
    let tasks_result = api_helper.get_tasks_without_auth().await?;
    assert_eq!(
        tasks_result["status"].as_u64().unwrap(),
        401,
        "无Token应该返回401状态码"
    );

    let error_response = &tasks_result["body"];
    assert!(error_response["error"].is_string(), "响应应包含错误信息");

    println!("✅ 无Token访问受保护端点测试通过");
    Ok(())
}

/// 测试9: 认证中间件保护的端点访问测试 - 无效Token
#[tokio::test]
async fn test_protected_endpoint_with_invalid_token() -> Result<()> {
    println!("\n🧪 开始测试: 认证中间件保护的端点访问 - 无效Token");

    let config = setup_test_environment().await?;
    let api_helper = ApiHelper::new(config.clone());

    // 使用无效Token访问受保护的端点
    let invalid_token = "invalid.jwt.token.here";
    let tasks_result = api_helper
        .get_tasks_with_invalid_token(invalid_token)
        .await?;
    assert_eq!(
        tasks_result["status"].as_u64().unwrap(),
        401,
        "无效Token应该返回401状态码"
    );

    let error_response = &tasks_result["body"];
    assert!(error_response["error"].is_string(), "响应应包含错误信息");

    println!("✅ 无效Token访问受保护端点测试通过");
    Ok(())
}

/// 测试10: JWT Token格式验证测试
#[tokio::test]
async fn test_jwt_token_format_validation() -> Result<()> {
    println!("\n🧪 开始测试: JWT Token格式验证");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 测试各种无效格式的Token
    let invalid_tokens = vec![
        "",                               // 空Token
        "invalid",                        // 无效格式
        "invalid.token",                  // 不完整的JWT
        "header.payload",                 // 缺少签名
        "header.payload.signature.extra", // 多余部分
        "not-a-jwt-at-all",               // 完全不是JWT格式
    ];

    for (index, invalid_token) in invalid_tokens.iter().enumerate() {
        println!("测试无效Token {}: {}", index + 1, invalid_token);

        let verify_result = auth_helper.test_malformed_token(invalid_token).await?;
        assert_eq!(
            verify_result["status"].as_u64().unwrap(),
            401,
            "无效格式Token应该返回401状态码"
        );

        let error_response = &verify_result["body"];
        assert!(error_response["error"].is_string(), "响应应包含错误信息");
    }

    println!("✅ JWT Token格式验证测试通过");
    Ok(())
}

/// 测试11: 登录响应数据完整性验证
#[tokio::test]
async fn test_login_response_data_integrity() -> Result<()> {
    println!("\n🧪 开始测试: 登录响应数据完整性验证");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 清理并创建测试用户
    cleanup_test_user(&config).await?;
    let register_result = create_test_user(&auth_helper).await?;
    assert_eq!(
        register_result["status"].as_u64().unwrap(),
        201,
        "用户注册应该成功"
    );

    // 执行登录
    let login_result = auth_helper
        .login_user(TEST_USER_USERNAME, TEST_USER_PASSWORD)
        .await?;
    assert_eq!(
        login_result["status"].as_u64().unwrap(),
        200,
        "登录应该成功"
    );

    // 使用辅助方法验证响应格式
    let response_body = &login_result["body"];
    auth_helper.validate_login_response(response_body)?;

    // 验证Token类型
    let token_type = response_body["data"]["token_type"].as_str().unwrap();
    assert_eq!(token_type, "Bearer", "Token类型应该是Bearer");

    // 验证过期时间是合理的（应该大于0且小于24小时）
    let expires_in = response_body["data"]["expires_in"].as_u64().unwrap();
    assert!(expires_in > 0, "过期时间应该大于0");
    assert!(expires_in <= 86400, "过期时间应该不超过24小时（86400秒）");

    println!("✅ 登录响应数据完整性验证测试通过");
    Ok(())
}

/// 测试12: JWT Token声明验证
#[tokio::test]
async fn test_jwt_token_claims_validation() -> Result<()> {
    println!("\n🧪 开始测试: JWT Token声明验证");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 清理并创建测试用户
    cleanup_test_user(&config).await?;
    let register_result = create_test_user(&auth_helper).await?;
    assert_eq!(
        register_result["status"].as_u64().unwrap(),
        201,
        "用户注册应该成功"
    );

    // 登录获取Token
    let token = auth_helper
        .get_auth_token(TEST_USER_USERNAME, TEST_USER_PASSWORD)
        .await?;
    assert!(!token.is_empty(), "应该获取到有效Token");

    // 验证Token
    let verify_result = auth_helper.verify_token(&token).await?;
    assert_eq!(
        verify_result["status"].as_u64().unwrap(),
        200,
        "Token验证应该成功"
    );

    // 使用辅助方法验证Token响应格式
    let response_body = &verify_result["body"];
    auth_helper.validate_token_response(response_body)?;

    // 验证Token声明的具体内容
    let claims = &response_body["data"];

    // 验证用户名匹配
    assert_eq!(
        claims["username"].as_str().unwrap(),
        TEST_USER_USERNAME,
        "Token中的用户名应该匹配"
    );

    // 验证时间戳的合理性
    let current_time = chrono::Utc::now().timestamp();
    let issued_at = claims["iat"].as_i64().unwrap();
    let expires_at = claims["exp"].as_i64().unwrap();

    assert!(issued_at <= current_time, "签发时间应该不晚于当前时间");
    assert!(expires_at > current_time, "过期时间应该晚于当前时间");
    assert!(expires_at > issued_at, "过期时间应该晚于签发时间");

    // 验证用户ID格式（应该是UUID）
    let user_id = claims["sub"].as_str().unwrap();
    assert!(
        uuid::Uuid::parse_str(user_id).is_ok(),
        "用户ID应该是有效的UUID格式"
    );

    println!("✅ JWT Token声明验证测试通过");
    Ok(())
}

/// 测试13: 并发登录测试
#[tokio::test]
async fn test_concurrent_login_requests() -> Result<()> {
    println!("\n🧪 开始测试: 并发登录请求");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 清理并创建测试用户
    cleanup_test_user(&config).await?;
    let register_result = create_test_user(&auth_helper).await?;
    assert_eq!(
        register_result["status"].as_u64().unwrap(),
        201,
        "用户注册应该成功"
    );

    // 创建多个并发登录任务
    let concurrent_requests = 5;
    let mut tasks = Vec::new();

    for i in 0..concurrent_requests {
        let auth_helper_clone = AuthHelper::new(config.clone());
        let username = TEST_USER_USERNAME.to_string();
        let password = TEST_USER_PASSWORD.to_string();

        let task = tokio::spawn(async move {
            println!("执行并发登录请求 {}", i + 1);
            auth_helper_clone.login_user(&username, &password).await
        });

        tasks.push(task);
    }

    // 等待所有任务完成
    let results = future::join_all(tasks).await;

    // 验证所有请求都成功
    for (index, result) in results.into_iter().enumerate() {
        let login_result = result.unwrap()?;
        assert_eq!(
            login_result["status"].as_u64().unwrap(),
            200,
            "并发登录请求 {} 应该成功",
            index + 1
        );

        // 验证每个响应都包含有效的Token
        let token = login_result["body"]["data"]["access_token"]
            .as_str()
            .unwrap();
        assert!(
            !token.is_empty(),
            "并发登录请求 {} 应该返回有效Token",
            index + 1
        );
    }

    println!("✅ 并发登录测试通过");
    Ok(())
}

/// 测试14: 登录性能测试
#[tokio::test]
async fn test_login_performance() -> Result<()> {
    println!("\n🧪 开始测试: 登录性能测试");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 清理并创建测试用户
    cleanup_test_user(&config).await?;
    let register_result = create_test_user(&auth_helper).await?;
    assert_eq!(
        register_result["status"].as_u64().unwrap(),
        201,
        "用户注册应该成功"
    );

    // 测量登录响应时间
    let start_time = std::time::Instant::now();
    let login_result = auth_helper
        .login_user(TEST_USER_USERNAME, TEST_USER_PASSWORD)
        .await?;
    let response_time = start_time.elapsed();

    // 验证登录成功
    assert_eq!(
        login_result["status"].as_u64().unwrap(),
        200,
        "登录应该成功"
    );

    // 验证响应时间在合理范围内（小于2秒）
    assert!(
        response_time.as_millis() < 2000,
        "登录响应时间应该小于2秒，实际: {}ms",
        response_time.as_millis()
    );

    println!("登录响应时间: {}ms", response_time.as_millis());
    println!("✅ 登录性能测试通过");
    Ok(())
}

/// 测试15: Token验证性能测试
#[tokio::test]
async fn test_token_validation_performance() -> Result<()> {
    println!("\n🧪 开始测试: Token验证性能测试");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 清理并创建测试用户
    cleanup_test_user(&config).await?;
    let register_result = create_test_user(&auth_helper).await?;
    assert_eq!(
        register_result["status"].as_u64().unwrap(),
        201,
        "用户注册应该成功"
    );

    // 登录获取Token
    let token = auth_helper
        .get_auth_token(TEST_USER_USERNAME, TEST_USER_PASSWORD)
        .await?;
    assert!(!token.is_empty(), "应该获取到有效Token");

    // 测量Token验证响应时间
    let start_time = std::time::Instant::now();
    let verify_result = auth_helper.verify_token(&token).await?;
    let response_time = start_time.elapsed();

    // 验证Token验证成功
    assert_eq!(
        verify_result["status"].as_u64().unwrap(),
        200,
        "Token验证应该成功"
    );

    // 验证响应时间在合理范围内（小于1秒）
    assert!(
        response_time.as_millis() < 1000,
        "Token验证响应时间应该小于1秒，实际: {}ms",
        response_time.as_millis()
    );

    println!("Token验证响应时间: {}ms", response_time.as_millis());
    println!("✅ Token验证性能测试通过");
    Ok(())
}

/// 测试16: 多次Token验证测试（缓存测试）
#[tokio::test]
async fn test_multiple_token_validations() -> Result<()> {
    println!("\n🧪 开始测试: 多次Token验证测试");

    let config = setup_test_environment().await?;
    let auth_helper = AuthHelper::new(config.clone());

    // 清理并创建测试用户
    cleanup_test_user(&config).await?;
    let register_result = create_test_user(&auth_helper).await?;
    assert_eq!(
        register_result["status"].as_u64().unwrap(),
        201,
        "用户注册应该成功"
    );

    // 登录获取Token
    let token = auth_helper
        .get_auth_token(TEST_USER_USERNAME, TEST_USER_PASSWORD)
        .await?;
    assert!(!token.is_empty(), "应该获取到有效Token");

    // 多次验证同一个Token
    let validation_count = 10;
    let mut total_time = std::time::Duration::new(0, 0);

    for i in 0..validation_count {
        let start_time = std::time::Instant::now();
        let verify_result = auth_helper.verify_token(&token).await?;
        let response_time = start_time.elapsed();

        total_time += response_time;

        // 验证每次都成功
        assert_eq!(
            verify_result["status"].as_u64().unwrap(),
            200,
            "第 {} 次Token验证应该成功",
            i + 1
        );

        println!(
            "第 {} 次验证响应时间: {}ms",
            i + 1,
            response_time.as_millis()
        );
    }

    let average_time = total_time / validation_count;
    println!("平均验证响应时间: {}ms", average_time.as_millis());

    // 验证平均响应时间在合理范围内
    assert!(
        average_time.as_millis() < 500,
        "平均Token验证响应时间应该小于500ms，实际: {}ms",
        average_time.as_millis()
    );

    println!("✅ 多次Token验证测试通过");
    Ok(())
}
