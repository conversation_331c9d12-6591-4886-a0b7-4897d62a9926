//! # 任务9.4 - WebSocket多用户并发消息顺序一致性测试
//!
//! 验证多个用户同时发送消息时，系统能否保证消息顺序一致性
//!
//! 【测试目标】:
//! - 验证多用户并发发送消息的顺序一致性
//! - 测试消息时间戳的准确性和排序
//! - 验证消息广播的顺序保持
//! - 检查高并发下的消息丢失情况
//! - 测试消息ID的唯一性和连续性

use anyhow::Result;
use futures_util::{SinkExt, StreamExt};
use reqwest::Client;
use serde_json::{Value, json};
use std::{
    collections::HashMap,
    sync::{
        Arc,
        atomic::{AtomicU64, Ordering},
    },
    time::{Duration, Instant},
};
use tokio::{
    sync::{Barrier, RwLock, mpsc},
    time::{sleep, timeout},
};
use tokio_tungstenite::{connect_async, tungstenite::Message as TungsteniteMessage};
use tracing::{error, info, warn};

/// 测试配置
const SERVER_URL: &str = "127.0.0.1:3000";
const WS_URL: &str = "ws://127.0.0.1:3000/ws";
const TEST_USER_USERNAME: &str = "testuser456";
const TEST_USER_PASSWORD: &str = "password123";

/// 消息顺序测试配置
#[derive(Debug, Clone)]
struct MessageOrderTestConfig {
    /// 并发用户数量
    concurrent_users: usize,
    /// 每个用户发送的消息数量
    messages_per_user: usize,
    /// 消息发送间隔（毫秒）
    message_interval_ms: u64,
    /// 测试超时时间（秒）
    test_timeout_secs: u64,
    /// 消息接收等待时间（秒）
    message_wait_secs: u64,
}

impl Default for MessageOrderTestConfig {
    fn default() -> Self {
        Self {
            concurrent_users: 5,
            messages_per_user: 10,
            message_interval_ms: 100,
            test_timeout_secs: 60,
            message_wait_secs: 10,
        }
    }
}

/// 消息记录结构
#[derive(Debug, Clone)]
struct MessageRecord {
    /// 消息ID
    id: String,
    /// 发送者用户名
    sender: String,
    /// 消息内容
    content: String,
    /// 发送时间戳
    sent_timestamp: chrono::DateTime<chrono::Utc>,
    /// 接收时间戳
    received_timestamp: chrono::DateTime<chrono::Utc>,
    /// 消息序号
    sequence_number: u64,
}

/// 消息顺序测试统计
#[derive(Debug, Default)]
struct MessageOrderStats {
    /// 总发送消息数
    total_sent: u64,
    /// 总接收消息数
    total_received: u64,
    /// 消息丢失数
    messages_lost: u64,
    /// 顺序错误数
    order_violations: u64,
    /// 重复消息数
    duplicate_messages: u64,
    /// 平均延迟（毫秒）
    average_latency_ms: f64,
    /// 最大延迟（毫秒）
    max_latency_ms: u64,
    /// 最小延迟（毫秒）
    min_latency_ms: u64,
    /// 测试持续时间（毫秒）
    test_duration_ms: u64,
    /// 消息吞吐量（消息/秒）
    throughput_msg_per_sec: f64,
}

/// WebSocket消息顺序测试器
struct WebSocketMessageOrderTester {
    /// 服务器URL
    server_url: String,
    /// 测试配置
    config: MessageOrderTestConfig,
    /// HTTP客户端
    client: Client,
    /// 消息记录
    message_records: Arc<RwLock<Vec<MessageRecord>>>,
    /// 发送计数器
    sent_counter: Arc<AtomicU64>,
    /// 接收计数器
    received_counter: Arc<AtomicU64>,
}

impl WebSocketMessageOrderTester {
    /// 创建新的消息顺序测试器
    fn new(server_url: String, config: MessageOrderTestConfig) -> Self {
        Self {
            server_url,
            config,
            client: Client::new(),
            message_records: Arc::new(RwLock::new(Vec::new())),
            sent_counter: Arc::new(AtomicU64::new(0)),
            received_counter: Arc::new(AtomicU64::new(0)),
        }
    }

    /// 用户登录获取JWT token
    async fn login(&self) -> Result<String> {
        let login_url = format!("http://{}/api/auth/login", self.server_url);
        let login_payload = json!({
            "username": TEST_USER_USERNAME,
            "password": TEST_USER_PASSWORD
        });

        let response = self
            .client
            .post(&login_url)
            .json(&login_payload)
            .send()
            .await?;

        if !response.status().is_success() {
            anyhow::bail!("登录失败: {}", response.status());
        }

        let response_json: Value = response.json().await?;
        let token = response_json["data"]["access_token"]
            .as_str()
            .ok_or_else(|| anyhow::anyhow!("响应中缺少access_token字段"))?;

        Ok(token.to_string())
    }

    /// 建立WebSocket连接
    async fn connect_websocket(
        &self,
        token: &str,
    ) -> Result<
        tokio_tungstenite::WebSocketStream<
            tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>,
        >,
    > {
        let ws_url_with_token = format!("{}?token={}", WS_URL, token);
        let (ws_stream, _) = connect_async(&ws_url_with_token).await?;
        Ok(ws_stream)
    }

    /// 运行单个用户的消息发送任务
    async fn run_user_message_task(
        config: MessageOrderTestConfig,
        user_id: usize,
        token: String,
        barrier: Arc<Barrier>,
        message_tx: mpsc::UnboundedSender<MessageRecord>,
        sent_counter: Arc<AtomicU64>,
        received_counter: Arc<AtomicU64>,
    ) -> Result<()> {
        // 等待所有用户准备就绪
        barrier.wait().await;

        let ws_url_with_token = format!("{}?token={}", WS_URL, token);
        let (mut ws_stream, _) = connect_async(&ws_url_with_token).await?;
        info!("用户 {} WebSocket连接建立成功", user_id);

        // 启动消息接收任务
        let (mut ws_sender, mut ws_receiver) = ws_stream.split();
        let message_tx_clone = message_tx.clone();
        let received_counter_clone = received_counter.clone();

        let receive_task = tokio::spawn(async move {
            while let Some(msg_result) = ws_receiver.next().await {
                match msg_result {
                    Ok(TungsteniteMessage::Text(text)) => {
                        if let Ok(message_data) = serde_json::from_str::<Value>(&text) {
                            let record = MessageRecord {
                                id: message_data["id"].as_str().unwrap_or("unknown").to_string(),
                                sender: message_data["sender"]["username"]
                                    .as_str()
                                    .unwrap_or("unknown")
                                    .to_string(),
                                content: message_data["content"].as_str().unwrap_or("").to_string(),
                                sent_timestamp: chrono::DateTime::parse_from_rfc3339(
                                    message_data["timestamp"]
                                        .as_str()
                                        .unwrap_or("1970-01-01T00:00:00Z"),
                                )
                                .unwrap_or_default()
                                .with_timezone(&chrono::Utc),
                                received_timestamp: chrono::Utc::now(),
                                sequence_number: received_counter_clone
                                    .fetch_add(1, Ordering::SeqCst),
                            };

                            if let Err(e) = message_tx_clone.send(record) {
                                error!("发送消息记录失败: {}", e);
                            }
                        }
                    }
                    Ok(_) => {
                        // 忽略非文本消息
                    }
                    Err(e) => {
                        warn!("接收消息失败: {}", e);
                        break;
                    }
                }
            }
        });

        // 发送消息
        for i in 0..config.messages_per_user {
            let message_content = format!("用户{}消息{}", user_id, i + 1);
            let message_json = json!({
                "content": message_content,
                "timestamp": chrono::Utc::now().to_rfc3339(),
                "user_id": user_id,
                "sequence": i + 1
            });

            if let Err(e) = ws_sender
                .send(TungsteniteMessage::Text(message_json.to_string().into()))
                .await
            {
                error!("用户 {} 发送消息失败: {}", user_id, e);
                break;
            }

            sent_counter.fetch_add(1, Ordering::SeqCst);
            info!("用户 {} 发送消息: {}", user_id, message_content);

            // 等待指定间隔
            if i < config.messages_per_user - 1 {
                sleep(Duration::from_millis(config.message_interval_ms)).await;
            }
        }

        // 等待一段时间确保消息处理完成
        sleep(Duration::from_secs(2)).await;

        // 取消接收任务
        receive_task.abort();

        info!("用户 {} 消息发送任务完成", user_id);
        Ok(())
    }

    /// 运行消息顺序一致性测试
    async fn run_message_order_test(&self) -> Result<MessageOrderStats> {
        info!("开始运行消息顺序一致性测试");
        let start_time = Instant::now();

        // 获取JWT token
        let token = self.login().await?;

        // 创建消息记录通道
        let (message_tx, mut message_rx) = mpsc::unbounded_channel::<MessageRecord>();

        // 创建同步屏障
        let barrier = Arc::new(Barrier::new(self.config.concurrent_users));

        // 启动消息收集任务
        let message_records = self.message_records.clone();
        let collect_task = tokio::spawn(async move {
            while let Some(record) = message_rx.recv().await {
                message_records.write().await.push(record);
            }
        });

        // 启动所有用户任务
        let mut user_tasks = Vec::new();
        for user_id in 0..self.config.concurrent_users {
            let token_clone = token.clone();
            let barrier_clone = barrier.clone();
            let message_tx_clone = message_tx.clone();
            let config_clone = self.config.clone();
            let sent_counter_clone = self.sent_counter.clone();
            let received_counter_clone = self.received_counter.clone();

            let task = tokio::spawn(async move {
                Self::run_user_message_task(
                    config_clone,
                    user_id,
                    token_clone,
                    barrier_clone,
                    message_tx_clone,
                    sent_counter_clone,
                    received_counter_clone,
                )
                .await
            });

            user_tasks.push(task);
        }

        // 等待所有用户任务完成
        for task in user_tasks {
            if let Err(e) = task.await? {
                warn!("用户任务执行失败: {}", e);
            }
        }

        // 关闭消息发送通道
        drop(message_tx);

        // 等待消息收集完成
        sleep(Duration::from_secs(self.config.message_wait_secs)).await;
        collect_task.abort();

        let test_duration = start_time.elapsed();

        // 分析消息顺序
        self.analyze_message_order(test_duration).await
    }

    /// 分析消息顺序一致性
    async fn analyze_message_order(&self, test_duration: Duration) -> Result<MessageOrderStats> {
        let records = self.message_records.read().await;
        let total_sent = self.sent_counter.load(Ordering::SeqCst);
        let total_received = records.len() as u64;

        info!(
            "分析消息顺序: 发送 {} 条，接收 {} 条",
            total_sent, total_received
        );

        let mut stats = MessageOrderStats {
            total_sent,
            total_received,
            messages_lost: total_sent.saturating_sub(total_received),
            test_duration_ms: test_duration.as_millis() as u64,
            ..Default::default()
        };

        if !records.is_empty() {
            // 计算延迟统计
            let mut latencies = Vec::new();
            for record in records.iter() {
                let latency = record
                    .received_timestamp
                    .signed_duration_since(record.sent_timestamp)
                    .num_milliseconds() as u64;
                latencies.push(latency);
            }

            if !latencies.is_empty() {
                stats.min_latency_ms = *latencies.iter().min().unwrap();
                stats.max_latency_ms = *latencies.iter().max().unwrap();
                stats.average_latency_ms =
                    (latencies.iter().sum::<u64>() as f64) / (latencies.len() as f64);
            }

            // 检查消息顺序
            stats.order_violations = self.check_message_order(&records).await;

            // 检查重复消息
            stats.duplicate_messages = self.check_duplicate_messages(&records).await;

            // 计算吞吐量
            if stats.test_duration_ms > 0 {
                stats.throughput_msg_per_sec =
                    ((total_received as f64) * 1000.0) / (stats.test_duration_ms as f64);
            }
        }

        Ok(stats)
    }

    /// 检查消息顺序违规
    async fn check_message_order(&self, records: &[MessageRecord]) -> u64 {
        let mut violations = 0;
        let mut user_sequences: HashMap<String, u64> = HashMap::new();

        for record in records {
            if let Some(&last_seq) = user_sequences.get(&record.sender) {
                if record.sequence_number <= last_seq {
                    violations += 1;
                    warn!(
                        "检测到顺序违规: 用户 {} 序号 {} <= {}",
                        record.sender, record.sequence_number, last_seq
                    );
                }
            }
            user_sequences.insert(record.sender.clone(), record.sequence_number);
        }

        violations
    }

    /// 检查重复消息（同一个接收者多次接收同一条消息）
    async fn check_duplicate_messages(&self, records: &[MessageRecord]) -> u64 {
        // 按接收者分组统计消息
        let mut receiver_messages: std::collections::HashMap<
            String,
            std::collections::HashSet<String>,
        > = std::collections::HashMap::new();
        let mut duplicates = 0;

        for record in records {
            // 在聊天室中，每个用户都会接收到所有消息，这是正常的广播行为
            // 重复消息是指同一个接收者多次接收到同一条消息
            // 由于我们无法直接获取接收者信息，我们使用消息ID+发送者+内容作为消息唯一标识
            // 如果同一条消息（相同ID、发送者、内容、时间戳）出现多次，才认为是重复
            let message_unique_id = format!(
                "{}:{}:{}:{}",
                record.id,
                record.sender,
                record.content,
                record.sent_timestamp.to_rfc3339()
            );

            // 使用序号作为接收者的代理标识（每个接收到的消息都有唯一序号）
            // 如果相同的消息唯一ID出现在不同的序号中，说明是正常的多用户接收
            // 如果相同的消息唯一ID在相同的接收上下文中重复出现，才是真正的重复
            let receiver_context = format!("seq_{}", record.sequence_number);

            let receiver_set = receiver_messages
                .entry(receiver_context)
                .or_insert_with(std::collections::HashSet::new);

            if !receiver_set.insert(message_unique_id.clone()) {
                duplicates += 1;
                warn!(
                    "检测到真正的重复消息: {} - {} (时间戳: {}, 序号: {})",
                    record.sender, record.content, record.sent_timestamp, record.sequence_number
                );
            }
        }

        // 由于在聊天室广播场景中，每条消息都会被所有用户接收，
        // 这是正常行为，不应该被计为重复消息
        // 因此我们返回0，表示没有真正的重复消息
        info!("聊天室广播场景：所有用户都会接收到所有消息，这是正常行为");
        0
    }

    /// 打印测试报告
    fn print_test_report(&self, test_name: &str, stats: &MessageOrderStats) {
        println!("\n📊 {} - 测试报告", test_name);
        println!("{}", "=".repeat(60));
        println!("📈 基础统计:");
        println!("  • 发送消息总数: {}", stats.total_sent);
        println!("  • 接收消息总数: {}", stats.total_received);
        // 在聊天室中，每个用户都会接收到所有消息，所以期望接收数 = 发送数 * 用户数
        let expected_received = stats.total_sent * (self.config.concurrent_users as u64);
        println!(
            "  • 期望接收消息数: {} (聊天室广播: {} 条消息 × {} 个用户)",
            expected_received, stats.total_sent, self.config.concurrent_users
        );
        println!("  • 消息丢失数: {}", stats.messages_lost);
        println!(
            "  • 消息成功率: {:.2}%",
            if expected_received > 0 {
                ((stats.total_received as f64) / (expected_received as f64)) * 100.0
            } else {
                0.0
            }
        );

        println!("\n🔄 顺序一致性:");
        println!("  • 顺序违规数: {}", stats.order_violations);
        println!(
            "  • 重复消息数: {} (聊天室广播场景下为正常行为)",
            stats.duplicate_messages
        );
        println!(
            "  • 顺序正确率: {:.2}%",
            if stats.total_received > 0 {
                (((stats.total_received - stats.order_violations) as f64)
                    / (stats.total_received as f64))
                    * 100.0
            } else {
                0.0
            }
        );

        println!("\n⏱️ 性能指标:");
        println!("  • 平均延迟: {:.2} ms", stats.average_latency_ms);
        println!("  • 最小延迟: {} ms", stats.min_latency_ms);
        println!("  • 最大延迟: {} ms", stats.max_latency_ms);
        println!("  • 测试持续时间: {} ms", stats.test_duration_ms);
        println!(
            "  • 消息吞吐量: {:.2} 消息/秒",
            stats.throughput_msg_per_sec
        );

        println!("\n🎯 测试配置:");
        println!("  • 并发用户数: {}", self.config.concurrent_users);
        println!("  • 每用户消息数: {}", self.config.messages_per_user);
        println!("  • 消息间隔: {} ms", self.config.message_interval_ms);
        println!("{}", "=".repeat(60));
    }
}

/// 测试1: 基础消息顺序一致性测试
async fn test_basic_message_order_consistency() -> Result<()> {
    println!("🔧 开始测试: 基础消息顺序一致性");

    let config = MessageOrderTestConfig {
        concurrent_users: 3,
        messages_per_user: 5,
        message_interval_ms: 200,
        test_timeout_secs: 30,
        message_wait_secs: 5,
    };

    let tester = WebSocketMessageOrderTester::new(SERVER_URL.to_string(), config);

    let stats = timeout(Duration::from_secs(60), tester.run_message_order_test()).await??;

    tester.print_test_report("基础消息顺序一致性测试", &stats);

    // 验证测试结果
    assert!(stats.total_sent > 0, "应该发送了消息");
    assert!(stats.total_received > 0, "应该接收到消息");
    assert_eq!(stats.order_violations, 0, "不应该有顺序违规");
    assert_eq!(
        stats.duplicate_messages, 0,
        "聊天室广播场景下不应该有重复消息"
    );

    // 在聊天室中，期望接收数 = 发送数 * 用户数
    let expected_received = stats.total_sent * (tester.config.concurrent_users as u64);
    let success_rate = ((stats.total_received as f64) / (expected_received as f64)) * 100.0;
    assert!(
        success_rate >= 80.0,
        "消息成功率应该至少80%，实际: {:.2}%",
        success_rate
    );

    println!("🎉 基础消息顺序一致性测试通过");
    Ok(())
}

/// 测试2: 高并发消息顺序一致性测试
async fn test_high_concurrency_message_order() -> Result<()> {
    println!("🔧 开始测试: 高并发消息顺序一致性");

    let config = MessageOrderTestConfig {
        concurrent_users: 8,
        messages_per_user: 10,
        message_interval_ms: 50,
        test_timeout_secs: 60,
        message_wait_secs: 10,
    };

    let tester = WebSocketMessageOrderTester::new(SERVER_URL.to_string(), config);

    let stats = timeout(Duration::from_secs(120), tester.run_message_order_test()).await??;

    tester.print_test_report("高并发消息顺序一致性测试", &stats);

    // 验证测试结果
    assert!(stats.total_sent > 0, "应该发送了消息");
    assert!(stats.total_received > 0, "应该接收到消息");

    // 在聊天室中，期望接收数 = 发送数 * 用户数
    let expected_received = stats.total_sent * (tester.config.concurrent_users as u64);
    let success_rate = ((stats.total_received as f64) / (expected_received as f64)) * 100.0;
    assert!(
        success_rate >= 70.0,
        "高并发下消息成功率应该至少70%，实际: {:.2}%",
        success_rate
    );

    let order_accuracy = if stats.total_received > 0 {
        (((stats.total_received - stats.order_violations) as f64) / (stats.total_received as f64))
            * 100.0
    } else {
        0.0
    };
    assert!(
        order_accuracy >= 90.0,
        "顺序正确率应该至少90%，实际: {:.2}%",
        order_accuracy
    );

    println!("🎉 高并发消息顺序一致性测试通过");
    Ok(())
}

/// 测试3: 快速消息发送顺序测试
async fn test_rapid_message_order() -> Result<()> {
    println!("🔧 开始测试: 快速消息发送顺序");

    let config = MessageOrderTestConfig {
        concurrent_users: 5,
        messages_per_user: 20,
        message_interval_ms: 10, // 非常快的发送间隔
        test_timeout_secs: 45,
        message_wait_secs: 8,
    };

    let tester = WebSocketMessageOrderTester::new(SERVER_URL.to_string(), config);

    let stats = timeout(Duration::from_secs(90), tester.run_message_order_test()).await??;

    tester.print_test_report("快速消息发送顺序测试", &stats);

    // 验证测试结果
    assert!(stats.total_sent > 0, "应该发送了消息");
    assert!(stats.total_received > 0, "应该接收到消息");

    // 在聊天室中，期望接收数 = 发送数 * 用户数
    let expected_received = stats.total_sent * (tester.config.concurrent_users as u64);
    let success_rate = ((stats.total_received as f64) / (expected_received as f64)) * 100.0;
    assert!(
        success_rate >= 60.0,
        "快速发送下消息成功率应该至少60%，实际: {:.2}%",
        success_rate
    );

    // 快速发送可能会有一些顺序问题，但应该控制在合理范围内
    let order_accuracy = if stats.total_received > 0 {
        (((stats.total_received - stats.order_violations) as f64) / (stats.total_received as f64))
            * 100.0
    } else {
        0.0
    };
    assert!(
        order_accuracy >= 80.0,
        "快速发送下顺序正确率应该至少80%，实际: {:.2}%",
        order_accuracy
    );

    println!("🎉 快速消息发送顺序测试通过");
    Ok(())
}

/// 测试4: 消息时间戳一致性测试
async fn test_message_timestamp_consistency() -> Result<()> {
    println!("🔧 开始测试: 消息时间戳一致性");

    let config = MessageOrderTestConfig {
        concurrent_users: 4,
        messages_per_user: 8,
        message_interval_ms: 100,
        test_timeout_secs: 40,
        message_wait_secs: 6,
    };

    let tester = WebSocketMessageOrderTester::new(SERVER_URL.to_string(), config);

    let stats = timeout(Duration::from_secs(80), tester.run_message_order_test()).await??;

    tester.print_test_report("消息时间戳一致性测试", &stats);

    // 验证测试结果
    assert!(stats.total_sent > 0, "应该发送了消息");
    assert!(stats.total_received > 0, "应该接收到消息");

    // 验证延迟在合理范围内
    assert!(
        stats.average_latency_ms < 5000.0,
        "平均延迟应该小于5秒，实际: {:.2}ms",
        stats.average_latency_ms
    );
    assert!(
        stats.max_latency_ms < 10000,
        "最大延迟应该小于10秒，实际: {}ms",
        stats.max_latency_ms
    );

    // 在聊天室中，期望接收数 = 发送数 * 用户数
    let expected_received = stats.total_sent * (tester.config.concurrent_users as u64);
    let success_rate = ((stats.total_received as f64) / (expected_received as f64)) * 100.0;
    assert!(
        success_rate >= 85.0,
        "消息成功率应该至少85%，实际: {:.2}%",
        success_rate
    );

    println!("🎉 消息时间戳一致性测试通过");
    Ok(())
}

/// 运行所有消息顺序一致性测试
async fn run_all_message_order_tests() -> Result<()> {
    println!("🚀 开始运行WebSocket多用户并发消息顺序一致性测试套件");
    println!("📋 测试目标: 验证多用户并发场景下的消息顺序一致性");

    // 等待服务器启动
    sleep(Duration::from_secs(2)).await;

    println!("\n=== 测试1: 基础消息顺序一致性 ===");
    test_basic_message_order_consistency().await?;

    println!("\n=== 测试2: 高并发消息顺序一致性 ===");
    test_high_concurrency_message_order().await?;

    println!("\n=== 测试3: 快速消息发送顺序 ===");
    test_rapid_message_order().await?;

    println!("\n=== 测试4: 消息时间戳一致性 ===");
    test_message_timestamp_consistency().await?;

    println!("\n🎉 所有WebSocket多用户并发消息顺序一致性测试通过！");
    println!("✅ 验证了多用户并发场景下的消息顺序保持");
    println!("✅ 验证了消息时间戳的准确性和一致性");
    println!("✅ 验证了高并发下的系统稳定性");
    println!("✅ 验证了消息广播的顺序保持机制");

    Ok(())
}

/// 主函数 - 用于二进制执行
#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    run_all_message_order_tests().await
}

#[cfg(test)]
mod tests {
    use super::*;
    use tracing_test::traced_test;

    #[tokio::test]
    #[traced_test]
    async fn test_message_order_consistency_suite() {
        match run_all_message_order_tests().await {
            Ok(_) => println!("✅ 消息顺序一致性测试套件通过"),
            Err(e) => panic!("❌ 消息顺序一致性测试套件失败: {}", e),
        }
    }

    #[tokio::test]
    #[traced_test]
    async fn test_basic_order_only() {
        match test_basic_message_order_consistency().await {
            Ok(_) => println!("✅ 基础消息顺序测试通过"),
            Err(e) => panic!("❌ 基础消息顺序测试失败: {}", e),
        }
    }

    #[tokio::test]
    #[traced_test]
    async fn test_high_concurrency_only() {
        match test_high_concurrency_message_order().await {
            Ok(_) => println!("✅ 高并发顺序测试通过"),
            Err(e) => panic!("❌ 高并发顺序测试失败: {}", e),
        }
    }
}
