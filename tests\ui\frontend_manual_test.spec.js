/**
 * 前端功能手动测试
 * 使用Playwright手动点击和测试前端页面的各个功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-23
 */

import { test, expect } from '@playwright/test';

// 测试配置
const BASE_URL = 'http://127.0.0.1:3000';
const TEST_USER = {
    username: 'testuser456',
    password: 'password123'
};

test.describe('前端功能手动测试', () => {
    test.beforeEach(async ({ page }) => {
        // 设置较长的超时时间
        test.setTimeout(60000);
        
        // 访问主页面
        await page.goto(BASE_URL);
        
        // 等待页面完全加载
        await page.waitForLoadState('networkidle');
        
        // 截图记录初始状态
        await page.screenshot({ path: 'tests/ui/screenshots/01-initial-page.png' });
    });

    test('检查页面基本结构和元素', async ({ page }) => {
        console.log('🔍 检查页面基本结构...');
        
        // 检查页面标题
        const title = await page.title();
        console.log(`页面标题: ${title}`);
        
        // 检查是否有基本的HTML结构
        const bodyExists = await page.locator('body').isVisible();
        expect(bodyExists).toBeTruthy();
        console.log('✅ 页面body元素存在');
        
        // 检查是否有登录相关元素
        const loginElements = await page.locator('#loginForm, #username, #password, #loginBtn').count();
        console.log(`找到 ${loginElements} 个登录相关元素`);
        
        // 检查是否有导航或主要内容区域
        const mainElements = await page.locator('main, #app, #dashboard, .container').count();
        console.log(`找到 ${mainElements} 个主要内容元素`);
        
        // 截图记录页面结构
        await page.screenshot({ path: 'tests/ui/screenshots/02-page-structure.png' });
    });

    test('尝试登录流程', async ({ page }) => {
        console.log('🔐 测试登录流程...');
        
        // 查找所有可能的登录表单元素（基于实际HTML结构）
        const usernameSelectors = [
            '#loginUsername',  // 实际的用户名输入框ID
            '#username',
            'input[name="username"]',
            'input[type="text"]',
            'input[placeholder*="用户名"]',
            'input[placeholder*="username"]'
        ];

        const passwordSelectors = [
            '#loginPassword',  // 实际的密码输入框ID
            '#password',
            'input[name="password"]',
            'input[type="password"]',
            'input[placeholder*="密码"]',
            'input[placeholder*="password"]'
        ];

        const loginButtonSelectors = [
            '#loginForm button[type="submit"]',  // 实际的登录按钮选择器
            '#loginBtn',
            'button[type="submit"]',
            'input[type="submit"]',
            'button:has-text("登录")',
            'button:has-text("Login")',
            '.login-btn',
            '.btn-login'
        ];
        
        let usernameField = null;
        let passwordField = null;
        let loginButton = null;
        
        // 尝试找到用户名输入框
        for (const selector of usernameSelectors) {
            try {
                const element = page.locator(selector);
                if (await element.isVisible({ timeout: 1000 })) {
                    usernameField = element;
                    console.log(`✅ 找到用户名输入框: ${selector}`);
                    break;
                }
            } catch (e) {
                // 继续尝试下一个选择器
            }
        }
        
        // 尝试找到密码输入框
        for (const selector of passwordSelectors) {
            try {
                const element = page.locator(selector);
                if (await element.isVisible({ timeout: 1000 })) {
                    passwordField = element;
                    console.log(`✅ 找到密码输入框: ${selector}`);
                    break;
                }
            } catch (e) {
                // 继续尝试下一个选择器
            }
        }
        
        // 尝试找到登录按钮
        for (const selector of loginButtonSelectors) {
            try {
                const element = page.locator(selector);
                if (await element.isVisible({ timeout: 1000 })) {
                    loginButton = element;
                    console.log(`✅ 找到登录按钮: ${selector}`);
                    break;
                }
            } catch (e) {
                // 继续尝试下一个选择器
            }
        }
        
        if (usernameField && passwordField && loginButton) {
            console.log('🎯 执行登录操作...');
            
            // 填写登录信息
            await usernameField.fill(TEST_USER.username);
            await passwordField.fill(TEST_USER.password);
            
            // 截图记录登录前状态
            await page.screenshot({ path: 'tests/ui/screenshots/03-before-login.png' });
            
            // 点击登录按钮
            await loginButton.click();
            
            // 等待登录响应
            await page.waitForTimeout(3000);
            
            // 截图记录登录后状态
            await page.screenshot({ path: 'tests/ui/screenshots/04-after-login.png' });
            
            console.log('✅ 登录操作完成');
        } else {
            console.log('⚠️ 未找到完整的登录表单元素');
            console.log(`用户名输入框: ${usernameField ? '找到' : '未找到'}`);
            console.log(`密码输入框: ${passwordField ? '找到' : '未找到'}`);
            console.log(`登录按钮: ${loginButton ? '找到' : '未找到'}`);
        }
    });

    test('检查JavaScript模块加载', async ({ page }) => {
        console.log('📦 检查JavaScript模块加载...');
        
        // 检查是否有JavaScript错误
        const jsErrors = [];
        page.on('pageerror', error => {
            jsErrors.push(error.message);
            console.log(`❌ JavaScript错误: ${error.message}`);
        });
        
        // 检查控制台消息
        const consoleMessages = [];
        page.on('console', msg => {
            consoleMessages.push(`${msg.type()}: ${msg.text()}`);
            console.log(`📝 控制台: ${msg.type()}: ${msg.text()}`);
        });
        
        // 等待一段时间让JavaScript加载
        await page.waitForTimeout(5000);
        
        // 检查全局对象是否存在
        const globalObjects = await page.evaluate(() => {
            const objects = {};
            
            // 检查常见的全局对象
            objects.chatAPI = typeof window.chatAPI !== 'undefined';
            objects.userAPI = typeof window.userAPI !== 'undefined';
            objects.taskAPI = typeof window.taskAPI !== 'undefined';
            objects.authAPI = typeof window.authAPI !== 'undefined';
            
            // 检查模块化导入
            objects.hasModules = typeof window.modules !== 'undefined';
            
            return objects;
        });
        
        console.log('🔍 全局对象检查结果:');
        Object.entries(globalObjects).forEach(([key, value]) => {
            console.log(`  ${key}: ${value ? '✅ 存在' : '❌ 不存在'}`);
        });
        
        console.log(`📊 JavaScript错误数量: ${jsErrors.length}`);
        console.log(`📊 控制台消息数量: ${consoleMessages.length}`);
        
        // 截图记录当前状态
        await page.screenshot({ path: 'tests/ui/screenshots/05-js-modules-check.png' });
    });

    test('检查搜索功能相关元素', async ({ page }) => {
        console.log('🔍 检查搜索功能相关元素...');
        
        // 查找可能的搜索相关元素
        const searchSelectors = [
            'input[type="search"]',
            'input[placeholder*="搜索"]',
            'input[placeholder*="search"]',
            '#searchInput',
            '#messageSearch',
            '.search-input',
            '.search-box'
        ];
        
        const searchButtonSelectors = [
            'button:has-text("搜索")',
            'button:has-text("Search")',
            '#searchBtn',
            '.search-btn',
            '.btn-search'
        ];
        
        let foundSearchElements = 0;
        
        // 检查搜索输入框
        for (const selector of searchSelectors) {
            try {
                const element = page.locator(selector);
                if (await element.isVisible({ timeout: 1000 })) {
                    foundSearchElements++;
                    console.log(`✅ 找到搜索输入框: ${selector}`);
                    
                    // 尝试在搜索框中输入测试内容
                    await element.fill('测试搜索');
                    await page.waitForTimeout(1000);
                    await element.clear();
                }
            } catch (e) {
                // 继续尝试下一个选择器
            }
        }
        
        // 检查搜索按钮
        for (const selector of searchButtonSelectors) {
            try {
                const element = page.locator(selector);
                if (await element.isVisible({ timeout: 1000 })) {
                    foundSearchElements++;
                    console.log(`✅ 找到搜索按钮: ${selector}`);
                }
            } catch (e) {
                // 继续尝试下一个选择器
            }
        }
        
        console.log(`📊 找到 ${foundSearchElements} 个搜索相关元素`);
        
        // 截图记录搜索元素状态
        await page.screenshot({ path: 'tests/ui/screenshots/06-search-elements.png' });
    });

    test('检查网络请求', async ({ page }) => {
        console.log('🌐 检查网络请求...');
        
        const requests = [];
        const responses = [];
        
        // 监听网络请求
        page.on('request', request => {
            requests.push({
                url: request.url(),
                method: request.method(),
                headers: request.headers()
            });
            console.log(`📤 请求: ${request.method()} ${request.url()}`);
        });
        
        // 监听网络响应
        page.on('response', response => {
            responses.push({
                url: response.url(),
                status: response.status(),
                statusText: response.statusText()
            });
            console.log(`📥 响应: ${response.status()} ${response.url()}`);
        });
        
        // 刷新页面以触发网络请求
        await page.reload();
        await page.waitForLoadState('networkidle');
        
        console.log(`📊 总请求数: ${requests.length}`);
        console.log(`📊 总响应数: ${responses.length}`);
        
        // 检查是否有API相关的请求
        const apiRequests = requests.filter(req => req.url.includes('/api/'));
        console.log(`📊 API请求数: ${apiRequests.length}`);
        
        apiRequests.forEach(req => {
            console.log(`  🔗 ${req.method} ${req.url}`);
        });
        
        // 截图记录最终状态
        await page.screenshot({ path: 'tests/ui/screenshots/07-final-state.png' });
    });
});
