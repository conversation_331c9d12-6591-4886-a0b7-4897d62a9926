//! # WebSocket并发连接端到端测试
//!
//! 使用MCP Playwright进行真实浏览器环境下的多用户并发WebSocket测试

use serde_json::json;
use std::{
    collections::HashMap,
    sync::{
        Arc,
        atomic::{AtomicU64, Ordering},
    },
    time::{Duration, Instant},
};
use tokio::{
    sync::{<PERSON><PERSON>, RwLock, mpsc},
    time::timeout,
};
use tracing::{error, info, warn};

/// 浏览器WebSocket测试配置
#[derive(Debug, Clone)]
pub struct BrowserTestConfig {
    /// 并发浏览器实例数量
    pub concurrent_browsers: usize,
    /// 每个浏览器发送的消息数量
    pub messages_per_browser: usize,
    /// 测试持续时间（秒）
    pub test_duration_secs: u64,
    /// 页面加载超时时间（秒）
    pub page_load_timeout_secs: u64,
    /// WebSocket连接超时时间（秒）
    pub websocket_timeout_secs: u64,
    /// 消息发送间隔（毫秒）
    pub message_interval_ms: u64,
}

impl Default for BrowserTestConfig {
    fn default() -> Self {
        Self {
            concurrent_browsers: 5,
            messages_per_browser: 20,
            test_duration_secs: 60,
            page_load_timeout_secs: 30,
            websocket_timeout_secs: 10,
            message_interval_ms: 500,
        }
    }
}

/// 浏览器测试指标
#[derive(Debug, Default)]
pub struct BrowserTestMetrics {
    /// 成功启动的浏览器数量
    pub browsers_launched: AtomicU64,
    /// 浏览器启动失败数量
    pub browser_launch_failures: AtomicU64,
    /// 成功连接WebSocket的数量
    pub websocket_connections: AtomicU64,
    /// WebSocket连接失败数量
    pub websocket_connection_failures: AtomicU64,
    /// 发送的消息总数
    pub messages_sent: AtomicU64,
    /// 接收的消息总数
    pub messages_received: AtomicU64,
    /// JavaScript错误数量
    pub javascript_errors: AtomicU64,
    /// 网络错误数量
    pub network_errors: AtomicU64,
    /// 页面加载时间统计（毫秒）
    pub page_load_times: Arc<RwLock<Vec<u64>>>,
    /// WebSocket连接时间统计（毫秒）
    pub websocket_connect_times: Arc<RwLock<Vec<u64>>>,
}

impl BrowserTestMetrics {
    /// 记录页面加载时间
    pub async fn record_page_load_time(&self, time_ms: u64) {
        let mut times = self.page_load_times.write().await;
        times.push(time_ms);
    }

    /// 记录WebSocket连接时间
    pub async fn record_websocket_connect_time(&self, time_ms: u64) {
        let mut times = self.websocket_connect_times.write().await;
        times.push(time_ms);
    }

    /// 计算统计信息
    pub async fn calculate_stats(&self) -> BrowserTestStatistics {
        let page_load_times = self.page_load_times.read().await;
        let websocket_connect_times = self.websocket_connect_times.read().await;

        BrowserTestStatistics {
            browsers_launched: self.browsers_launched.load(Ordering::Relaxed),
            browser_launch_failures: self.browser_launch_failures.load(Ordering::Relaxed),
            websocket_connections: self.websocket_connections.load(Ordering::Relaxed),
            websocket_connection_failures: self
                .websocket_connection_failures
                .load(Ordering::Relaxed),
            messages_sent: self.messages_sent.load(Ordering::Relaxed),
            messages_received: self.messages_received.load(Ordering::Relaxed),
            javascript_errors: self.javascript_errors.load(Ordering::Relaxed),
            network_errors: self.network_errors.load(Ordering::Relaxed),
            avg_page_load_time_ms: calculate_average(&page_load_times),
            avg_websocket_connect_time_ms: calculate_average(&websocket_connect_times),
            browser_success_rate: self.calculate_browser_success_rate(),
            websocket_success_rate: self.calculate_websocket_success_rate(),
        }
    }

    /// 计算浏览器启动成功率
    fn calculate_browser_success_rate(&self) -> f64 {
        let successful = self.browsers_launched.load(Ordering::Relaxed) as f64;
        let total = successful + (self.browser_launch_failures.load(Ordering::Relaxed) as f64);
        if total > 0.0 {
            (successful / total) * 100.0
        } else {
            0.0
        }
    }

    /// 计算WebSocket连接成功率
    fn calculate_websocket_success_rate(&self) -> f64 {
        let successful = self.websocket_connections.load(Ordering::Relaxed) as f64;
        let total =
            successful + (self.websocket_connection_failures.load(Ordering::Relaxed) as f64);
        if total > 0.0 {
            (successful / total) * 100.0
        } else {
            0.0
        }
    }
}

/// 浏览器测试统计结果
#[derive(Debug)]
pub struct BrowserTestStatistics {
    pub browsers_launched: u64,
    pub browser_launch_failures: u64,
    pub websocket_connections: u64,
    pub websocket_connection_failures: u64,
    pub messages_sent: u64,
    pub messages_received: u64,
    pub javascript_errors: u64,
    pub network_errors: u64,
    pub avg_page_load_time_ms: f64,
    pub avg_websocket_connect_time_ms: f64,
    pub browser_success_rate: f64,
    pub websocket_success_rate: f64,
}

/// WebSocket浏览器并发测试器
pub struct WebSocketBrowserConcurrentTester {
    config: BrowserTestConfig,
    metrics: Arc<BrowserTestMetrics>,
    base_url: String,
    test_credentials: (String, String), // (username, password)
}

impl WebSocketBrowserConcurrentTester {
    /// 创建新的浏览器并发测试器
    pub fn new(
        base_url: String,
        config: BrowserTestConfig,
        test_credentials: (String, String),
    ) -> Self {
        Self {
            config,
            metrics: Arc::new(BrowserTestMetrics::default()),
            base_url,
            test_credentials,
        }
    }

    /// 执行浏览器并发WebSocket测试
    pub async fn run_browser_concurrent_test(
        &self,
    ) -> Result<BrowserTestStatistics, Box<dyn std::error::Error>> {
        info!("开始浏览器WebSocket并发测试");
        info!("测试配置: {:?}", self.config);

        // 创建同步屏障，确保所有浏览器同时开始
        let barrier = Arc::new(Barrier::new(self.config.concurrent_browsers));

        // 启动并发浏览器任务
        let mut tasks = Vec::new();
        for i in 0..self.config.concurrent_browsers {
            let task = self.spawn_browser_task(i, barrier.clone()).await;
            tasks.push(task);
        }

        // 等待所有任务完成或超时
        let test_timeout = Duration::from_secs(self.config.test_duration_secs + 60);
        match timeout(test_timeout, futures_util::future::join_all(tasks)).await {
            Ok(results) => {
                info!("所有浏览器任务已完成");
                for (i, result) in results.into_iter().enumerate() {
                    if let Err(e) = result {
                        error!("浏览器任务{}失败: {}", i, e);
                    }
                }
            }
            Err(_) => {
                warn!("浏览器并发测试超时，强制结束");
            }
        }

        // 计算并返回统计结果
        let stats = self.metrics.calculate_stats().await;
        info!("浏览器并发测试完成，统计结果: {:?}", stats);
        Ok(stats)
    }

    /// 启动单个浏览器任务
    async fn spawn_browser_task(
        &self,
        browser_index: usize,
        barrier: Arc<Barrier>,
    ) -> tokio::task::JoinHandle<Result<(), Box<dyn std::error::Error + Send + Sync>>> {
        let config = self.config.clone();
        let metrics = self.metrics.clone();
        let base_url = self.base_url.clone();
        let credentials = self.test_credentials.clone();

        tokio::spawn(async move {
            // 等待所有浏览器准备就绪
            barrier.wait().await;

            info!("浏览器{}开始测试", browser_index);

            // 执行浏览器WebSocket测试
            let result =
                Self::run_browser_session(browser_index, base_url, credentials, config, metrics)
                    .await;

            if let Err(e) = &result {
                error!("浏览器{}测试失败: {}", browser_index, e);
            } else {
                info!("浏览器{}测试完成", browser_index);
            }

            result
        })
    }

    /// 执行单个浏览器的WebSocket会话测试
    async fn run_browser_session(
        browser_index: usize,
        base_url: String,
        _credentials: (String, String),
        config: BrowserTestConfig,
        metrics: Arc<BrowserTestMetrics>,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // 这里应该使用MCP Playwright来启动浏览器
        // 由于MCP Playwright需要特定的调用方式，我们先创建一个模拟实现

        info!("浏览器{}开始启动", browser_index);
        let _browser_start = Instant::now();

        // 模拟浏览器启动过程
        tokio::time::sleep(Duration::from_millis(1000)).await;

        // 记录浏览器启动成功
        metrics.browsers_launched.fetch_add(1, Ordering::Relaxed);

        // 模拟页面加载
        let page_load_start = Instant::now();
        let _login_url = format!("{}/login", base_url);

        // 模拟页面加载时间
        tokio::time::sleep(Duration::from_millis(500)).await;
        let page_load_time = page_load_start.elapsed().as_millis() as u64;
        metrics.record_page_load_time(page_load_time).await;

        info!(
            "浏览器{}页面加载完成，耗时: {}ms",
            browser_index, page_load_time
        );

        // 模拟登录过程
        tokio::time::sleep(Duration::from_millis(300)).await;

        // 模拟WebSocket连接
        let ws_connect_start = Instant::now();
        tokio::time::sleep(Duration::from_millis(200)).await;
        let ws_connect_time = ws_connect_start.elapsed().as_millis() as u64;

        metrics
            .websocket_connections
            .fetch_add(1, Ordering::Relaxed);
        metrics.record_websocket_connect_time(ws_connect_time).await;

        info!(
            "浏览器{}WebSocket连接建立，耗时: {}ms",
            browser_index, ws_connect_time
        );

        // 模拟消息发送和接收
        for i in 0..config.messages_per_browser {
            // 模拟发送消息
            tokio::time::sleep(Duration::from_millis(config.message_interval_ms)).await;
            metrics.messages_sent.fetch_add(1, Ordering::Relaxed);

            // 模拟接收消息（假设有其他用户的消息）
            if i % 3 == 0 {
                metrics.messages_received.fetch_add(1, Ordering::Relaxed);
            }

            if i % 10 == 0 {
                info!("浏览器{}已发送{}条消息", browser_index, i + 1);
            }
        }

        info!("浏览器{}消息测试完成", browser_index);

        // 模拟关闭浏览器
        tokio::time::sleep(Duration::from_millis(100)).await;

        Ok(())
    }

    /// 打印详细的浏览器测试报告
    pub fn print_browser_test_report(&self, test_name: &str, stats: &BrowserTestStatistics) {
        println!("\n=== {} 浏览器测试报告 ===", test_name);
        println!("浏览器统计:");
        println!("  成功启动浏览器数: {}", stats.browsers_launched);
        println!("  浏览器启动失败数: {}", stats.browser_launch_failures);
        println!("  浏览器启动成功率: {:.2}%", stats.browser_success_rate);

        println!("\nWebSocket连接统计:");
        println!("  成功连接数: {}", stats.websocket_connections);
        println!("  连接失败数: {}", stats.websocket_connection_failures);
        println!(
            "  WebSocket连接成功率: {:.2}%",
            stats.websocket_success_rate
        );

        println!("\n消息统计:");
        println!("  发送消息数: {}", stats.messages_sent);
        println!("  接收消息数: {}", stats.messages_received);

        println!("\n性能指标:");
        println!("  平均页面加载时间: {:.2}ms", stats.avg_page_load_time_ms);
        println!(
            "  平均WebSocket连接时间: {:.2}ms",
            stats.avg_websocket_connect_time_ms
        );

        println!("\n错误统计:");
        println!("  JavaScript错误数: {}", stats.javascript_errors);
        println!("  网络错误数: {}", stats.network_errors);

        println!("=== 浏览器测试报告结束 ===\n");
    }
}

/// 计算平均值
fn calculate_average(values: &[u64]) -> f64 {
    if values.is_empty() {
        0.0
    } else {
        (values.iter().sum::<u64>() as f64) / (values.len() as f64)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tracing_test::traced_test;

    const TEST_SERVER_URL: &str = "http://127.0.0.1:3000";
    const TEST_CREDENTIALS: (&str, &str) = ("testuser456", "password123");

    #[tokio::test]
    #[traced_test]
    async fn test_browser_concurrent_websockets() {
        let config = BrowserTestConfig {
            concurrent_browsers: 3,
            messages_per_browser: 10,
            test_duration_secs: 30,
            page_load_timeout_secs: 10,
            websocket_timeout_secs: 5,
            message_interval_ms: 300,
        };

        let tester = WebSocketBrowserConcurrentTester::new(
            TEST_SERVER_URL.to_string(),
            config,
            (
                TEST_CREDENTIALS.0.to_string(),
                TEST_CREDENTIALS.1.to_string(),
            ),
        );

        match tester.run_browser_concurrent_test().await {
            Ok(stats) => {
                tester.print_browser_test_report("浏览器并发WebSocket", &stats);
                assert!(
                    stats.browser_success_rate >= 80.0,
                    "浏览器启动成功率应该至少80%"
                );
                assert!(
                    stats.websocket_success_rate >= 80.0,
                    "WebSocket连接成功率应该至少80%"
                );
            }
            Err(e) => {
                panic!("浏览器并发WebSocket测试失败: {}", e);
            }
        }
    }
}
