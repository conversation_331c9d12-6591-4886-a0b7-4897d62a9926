//! # 异步搜索队列模块
//!
//! 基于Tokio实现的高性能异步搜索队列系统，支持：
//! - 优先级队列管理
//! - 任务去重机制
//! - 队列容量控制
//! - 并发处理控制
//! - 性能监控

use app_common::{AppError, Result};
use app_domain::entities::search_task::SearchTask;
use std::collections::{BinaryHeap, HashMap};
use std::sync::Arc;
use tokio::sync::{Mutex, RwLock, Semaphore, mpsc};
use tokio::time::{Duration, Instant};
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// 队列配置结构
///
/// 定义异步搜索队列的配置参数
#[derive(Debug, Clone)]
pub struct QueueConfig {
    /// 队列最大容量
    pub max_capacity: usize,
    /// 最大并发处理数
    pub max_concurrent_tasks: usize,
    /// 任务超时时间（秒）
    pub default_timeout_seconds: u32,
    /// 队列监控间隔（秒）
    pub monitoring_interval_seconds: u64,
    /// 是否启用任务去重
    pub enable_deduplication: bool,
    /// 去重窗口时间（秒）
    pub deduplication_window_seconds: u64,
}

impl Default for QueueConfig {
    fn default() -> Self {
        Self {
            max_capacity: 10000,
            max_concurrent_tasks: 100,
            default_timeout_seconds: 30,
            monitoring_interval_seconds: 60,
            enable_deduplication: true,
            deduplication_window_seconds: 300, // 5分钟去重窗口
        }
    }
}

/// 队列统计信息
///
/// 记录队列的运行时统计数据
#[derive(Debug, Clone)]
pub struct QueueMetrics {
    /// 队列中待处理任务数
    pub pending_tasks: usize,
    /// 正在处理的任务数
    pub processing_tasks: usize,
    /// 已完成任务总数
    pub completed_tasks: u64,
    /// 失败任务总数
    pub failed_tasks: u64,
    /// 取消任务总数
    pub cancelled_tasks: u64,
    /// 超时任务总数
    pub timeout_tasks: u64,
    /// 去重任务总数
    pub deduplicated_tasks: u64,
    /// 平均处理时间（毫秒）
    pub avg_processing_time_ms: f64,
    /// 队列吞吐量（任务/秒）
    pub throughput_per_second: f64,
    /// 最后更新时间
    pub last_updated: Instant,
}

impl Default for QueueMetrics {
    fn default() -> Self {
        Self {
            pending_tasks: 0,
            processing_tasks: 0,
            completed_tasks: 0,
            failed_tasks: 0,
            cancelled_tasks: 0,
            timeout_tasks: 0,
            deduplicated_tasks: 0,
            avg_processing_time_ms: 0.0,
            throughput_per_second: 0.0,
            last_updated: Instant::now(),
        }
    }
}

/// 优先级任务包装器
///
/// 用于在BinaryHeap中实现优先级排序
#[derive(Debug, Clone)]
struct PriorityTask {
    task: SearchTask,
    enqueue_time: Instant,
}

impl PartialEq for PriorityTask {
    fn eq(&self, other: &Self) -> bool {
        self.task.priority == other.task.priority
    }
}

impl Eq for PriorityTask {}

impl PartialOrd for PriorityTask {
    fn partial_cmp(&self, other: &Self) -> Option<std::cmp::Ordering> {
        Some(self.cmp(other))
    }
}

impl Ord for PriorityTask {
    fn cmp(&self, other: &Self) -> std::cmp::Ordering {
        // 优先级高的任务排在前面，如果优先级相同则按入队时间排序
        match self.task.priority.cmp(&other.task.priority) {
            std::cmp::Ordering::Equal => other.enqueue_time.cmp(&self.enqueue_time), // 先入队的优先
            other => other, // 高优先级的优先
        }
    }
}

/// 任务去重键生成器
///
/// 根据任务内容生成去重键
fn generate_dedup_key(task: &SearchTask) -> String {
    format!(
        "{}:{}:{}",
        task.user_id,
        task.query,
        serde_json::to_string(&task.search_params).unwrap_or_default()
    )
}

/// 异步搜索队列
///
/// 基于Tokio实现的高性能异步搜索队列
pub struct AsyncSearchQueue {
    /// 队列配置
    config: QueueConfig,
    /// 优先级队列（使用BinaryHeap实现）
    priority_queue: Arc<Mutex<BinaryHeap<PriorityTask>>>,
    /// 正在处理的任务集合
    processing_tasks: Arc<RwLock<HashMap<Uuid, SearchTask>>>,
    /// 任务去重缓存（任务键 -> 任务ID）
    dedup_cache: Arc<RwLock<HashMap<String, (Uuid, Instant)>>>,
    /// 队列统计信息
    metrics: Arc<RwLock<QueueMetrics>>,
    /// 并发控制信号量
    semaphore: Arc<Semaphore>,
    /// 任务通知通道发送端
    task_sender: mpsc::UnboundedSender<SearchTask>,
    /// 任务通知通道接收端
    task_receiver: Arc<Mutex<mpsc::UnboundedReceiver<SearchTask>>>,
    /// 队列关闭标志
    shutdown: Arc<RwLock<bool>>,
}

impl AsyncSearchQueue {
    /// 创建新的异步搜索队列
    ///
    /// # 参数
    /// - `config`: 队列配置
    ///
    /// # 返回
    /// - 新创建的队列实例
    pub fn new(config: QueueConfig) -> Self {
        let (task_sender, task_receiver) = mpsc::unbounded_channel();
        let semaphore = Arc::new(Semaphore::new(config.max_concurrent_tasks));

        Self {
            config,
            priority_queue: Arc::new(Mutex::new(BinaryHeap::new())),
            processing_tasks: Arc::new(RwLock::new(HashMap::new())),
            dedup_cache: Arc::new(RwLock::new(HashMap::new())),
            metrics: Arc::new(RwLock::new(QueueMetrics::default())),
            semaphore,
            task_sender,
            task_receiver: Arc::new(Mutex::new(task_receiver)),
            shutdown: Arc::new(RwLock::new(false)),
        }
    }

    /// 将搜索任务加入队列
    ///
    /// # 参数
    /// - `task`: 要加入队列的搜索任务
    ///
    /// # 返回
    /// - Ok(task_id): 成功加入队列，返回任务ID
    /// - Err: 加入队列失败
    pub async fn enqueue(&self, mut task: SearchTask) -> Result<Uuid> {
        // 检查队列是否已关闭
        if *self.shutdown.read().await {
            return Err(AppError::ValidationError("队列已关闭".to_string()));
        }

        // 检查队列容量
        let queue_size = self.priority_queue.lock().await.len();
        if queue_size >= self.config.max_capacity {
            warn!("队列已满，当前容量: {}", queue_size);
            return Err(AppError::ValidationError("队列已满".to_string()));
        }

        // 任务去重检查
        if self.config.enable_deduplication {
            let dedup_key = generate_dedup_key(&task);
            let mut dedup_cache = self.dedup_cache.write().await;

            // 清理过期的去重缓存
            let now = Instant::now();
            let window_duration = Duration::from_secs(self.config.deduplication_window_seconds);
            dedup_cache
                .retain(|_, (_, timestamp)| now.duration_since(*timestamp) < window_duration);

            // 检查是否存在重复任务
            if let Some((existing_task_id, _)) = dedup_cache.get(&dedup_key) {
                info!("发现重复任务，返回现有任务ID: {}", existing_task_id);

                // 更新去重统计
                let mut metrics = self.metrics.write().await;
                metrics.deduplicated_tasks += 1;

                return Ok(*existing_task_id);
            }

            // 添加到去重缓存
            dedup_cache.insert(dedup_key, (task.id, now));
        }

        // 设置任务默认超时时间（如果未设置）
        if task.timeout_seconds == 0 {
            task.timeout_seconds = self.config.default_timeout_seconds;
        }

        let task_id = task.id;
        let task_priority = task.priority;
        let priority_task = PriorityTask {
            task: task.clone(),
            enqueue_time: Instant::now(),
        };

        // 加入优先级队列
        self.priority_queue.lock().await.push(priority_task);

        // 通过通道通知有新任务
        if let Err(e) = self.task_sender.send(task) {
            error!("发送任务通知失败: {:?}", e);
            return Err(AppError::InternalServerError("任务通知失败".to_string()));
        }

        // 更新队列统计
        let mut metrics = self.metrics.write().await;
        metrics.pending_tasks = self.priority_queue.lock().await.len();

        info!("任务已加入队列: {} (优先级: {:?})", task_id, task_priority);
        Ok(task_id)
    }

    /// 从队列中取出下一个待处理任务
    ///
    /// # 返回
    /// - Some(task): 下一个待处理任务
    /// - None: 队列为空
    pub async fn dequeue(&self) -> Option<SearchTask> {
        let mut queue = self.priority_queue.lock().await;

        if let Some(priority_task) = queue.pop() {
            let mut task = priority_task.task;
            task.start_processing();

            // 添加到正在处理的任务集合
            self.processing_tasks
                .write()
                .await
                .insert(task.id, task.clone());

            // 更新统计信息
            let mut metrics = self.metrics.write().await;
            metrics.pending_tasks = queue.len();
            metrics.processing_tasks = self.processing_tasks.read().await.len();

            debug!("从队列取出任务: {} (优先级: {:?})", task.id, task.priority);
            Some(task)
        } else {
            None
        }
    }

    /// 标记任务完成
    ///
    /// # 参数
    /// - `task_id`: 任务ID
    /// - `success`: 是否成功完成
    /// - `error_message`: 错误信息（失败时）
    pub async fn complete_task(&self, task_id: Uuid, success: bool, error_message: Option<String>) {
        let mut processing_tasks = self.processing_tasks.write().await;

        if let Some(mut task) = processing_tasks.remove(&task_id) {
            let start_time = task.started_at.unwrap_or_else(chrono::Utc::now);
            let execution_time = chrono::Utc::now() - start_time;
            let execution_time_ms = execution_time.num_milliseconds() as u64;

            if success {
                task.complete();
                info!("任务完成: {} (耗时: {}ms)", task_id, execution_time_ms);
            } else {
                task.fail(error_message.unwrap_or_else(|| "未知错误".to_string()));
                warn!("任务失败: {} (耗时: {}ms)", task_id, execution_time_ms);
            }

            // 更新统计信息
            let mut metrics = self.metrics.write().await;
            metrics.processing_tasks = processing_tasks.len();

            if success {
                metrics.completed_tasks += 1;
            } else {
                metrics.failed_tasks += 1;
            }

            // 更新平均处理时间
            let total_tasks = metrics.completed_tasks + metrics.failed_tasks;
            if total_tasks > 0 {
                metrics.avg_processing_time_ms = (metrics.avg_processing_time_ms
                    * ((total_tasks - 1) as f64)
                    + (execution_time_ms as f64))
                    / (total_tasks as f64);
            }

            metrics.last_updated = Instant::now();
        }
    }

    /// 取消任务
    ///
    /// # 参数
    /// - `task_id`: 要取消的任务ID
    ///
    /// # 返回
    /// - true: 任务成功取消
    /// - false: 任务未找到或无法取消
    pub async fn cancel_task(&self, task_id: Uuid) -> bool {
        // 尝试从正在处理的任务中移除
        let mut processing_tasks = self.processing_tasks.write().await;
        if let Some(mut task) = processing_tasks.remove(&task_id) {
            task.cancel();

            // 更新统计信息
            let mut metrics = self.metrics.write().await;
            metrics.processing_tasks = processing_tasks.len();
            metrics.cancelled_tasks += 1;

            info!("任务已取消: {}", task_id);
            return true;
        }

        // 尝试从队列中移除（需要重建队列）
        let mut queue = self.priority_queue.lock().await;
        let original_tasks: Vec<_> = queue.drain().collect();
        let mut found = false;

        for priority_task in original_tasks {
            if priority_task.task.id == task_id {
                found = true;
                info!("从队列中取消任务: {}", task_id);

                // 更新统计信息
                let mut metrics = self.metrics.write().await;
                metrics.cancelled_tasks += 1;
            } else {
                queue.push(priority_task);
            }
        }

        // 更新队列大小统计
        if found {
            let mut metrics = self.metrics.write().await;
            metrics.pending_tasks = queue.len();
        }

        found
    }

    /// 获取队列统计信息
    ///
    /// # 返回
    /// - 当前队列统计信息
    pub async fn get_metrics(&self) -> QueueMetrics {
        let mut metrics = self.metrics.write().await;

        // 更新实时统计
        metrics.pending_tasks = self.priority_queue.lock().await.len();
        metrics.processing_tasks = self.processing_tasks.read().await.len();

        // 计算吞吐量
        let elapsed = metrics.last_updated.elapsed().as_secs_f64();
        if elapsed > 0.0 {
            let total_processed =
                metrics.completed_tasks + metrics.failed_tasks + metrics.cancelled_tasks;
            metrics.throughput_per_second = (total_processed as f64) / elapsed;
        }

        metrics.last_updated = Instant::now();
        metrics.clone()
    }

    /// 获取指定任务的状态
    ///
    /// # 参数
    /// - `task_id`: 任务ID
    ///
    /// # 返回
    /// - Some(task): 任务信息
    /// - None: 任务未找到
    pub async fn get_task_status(&self, task_id: Uuid) -> Option<SearchTask> {
        // 先检查正在处理的任务
        if let Some(task) = self.processing_tasks.read().await.get(&task_id) {
            return Some(task.clone());
        }

        // 检查队列中的任务
        let queue = self.priority_queue.lock().await;
        for priority_task in queue.iter() {
            if priority_task.task.id == task_id {
                return Some(priority_task.task.clone());
            }
        }

        None
    }

    /// 获取并发控制信号量
    ///
    /// # 返回
    /// - 信号量的Arc引用，用于控制并发处理数量
    pub fn get_semaphore(&self) -> Arc<Semaphore> {
        self.semaphore.clone()
    }

    /// 关闭队列
    ///
    /// 停止接受新任务，等待现有任务完成
    pub async fn shutdown(&self) {
        info!("开始关闭异步搜索队列...");

        // 设置关闭标志
        *self.shutdown.write().await = true;

        // 等待所有正在处理的任务完成
        loop {
            let processing_count = self.processing_tasks.read().await.len();
            if processing_count == 0 {
                break;
            }

            info!("等待 {} 个任务完成...", processing_count);
            tokio::time::sleep(Duration::from_millis(100)).await;
        }

        info!("异步搜索队列已关闭");
    }

    /// 检查队列是否已关闭
    ///
    /// # 返回
    /// - true: 队列已关闭
    /// - false: 队列正常运行
    pub async fn is_shutdown(&self) -> bool {
        *self.shutdown.read().await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use app_domain::entities::search_task::SearchTaskType;
    use app_domain::entities::{SearchTaskPriority, SearchTaskStatus};

    #[tokio::test]
    async fn test_queue_enqueue_dequeue() {
        let config = QueueConfig::default();
        let queue = AsyncSearchQueue::new(config);

        // 创建测试任务
        let task = SearchTask::new(
            Uuid::new_v4(),
            "test query".to_string(),
            SearchTaskType::FullTextSearch,
            SearchTaskPriority::Normal,
        );

        // 测试入队
        let task_id = queue.enqueue(task.clone()).await.unwrap();
        assert_eq!(task_id, task.id);

        // 测试出队
        let dequeued_task = queue.dequeue().await.unwrap();
        assert_eq!(dequeued_task.id, task.id);
        assert_eq!(dequeued_task.status, SearchTaskStatus::Processing);
    }

    #[tokio::test]
    async fn test_priority_ordering() {
        let config = QueueConfig::default();
        let queue = AsyncSearchQueue::new(config);

        // 创建不同优先级的任务
        let low_task = SearchTask::new(
            Uuid::new_v4(),
            "low priority".to_string(),
            SearchTaskType::FullTextSearch,
            SearchTaskPriority::Low,
        );

        let high_task = SearchTask::new(
            Uuid::new_v4(),
            "high priority".to_string(),
            SearchTaskType::FullTextSearch,
            SearchTaskPriority::High,
        );

        // 先入队低优先级任务
        queue.enqueue(low_task.clone()).await.unwrap();
        // 再入队高优先级任务
        queue.enqueue(high_task.clone()).await.unwrap();

        // 出队应该先得到高优先级任务
        let first_task = queue.dequeue().await.unwrap();
        assert_eq!(first_task.id, high_task.id);

        let second_task = queue.dequeue().await.unwrap();
        assert_eq!(second_task.id, low_task.id);
    }

    #[tokio::test]
    async fn test_task_deduplication() {
        let mut config = QueueConfig::default();
        config.enable_deduplication = true;
        let queue = AsyncSearchQueue::new(config);

        // 创建两个相同的任务
        let task1 = SearchTask::new(
            Uuid::new_v4(),
            "duplicate query".to_string(),
            SearchTaskType::FullTextSearch,
            SearchTaskPriority::Normal,
        );

        let mut task2 = task1.clone();
        task2.id = Uuid::new_v4(); // 不同的ID但内容相同

        // 入队第一个任务
        let task_id1 = queue.enqueue(task1).await.unwrap();

        // 入队第二个任务（应该被去重）
        let task_id2 = queue.enqueue(task2).await.unwrap();

        // 应该返回相同的任务ID
        assert_eq!(task_id1, task_id2);

        // 队列中应该只有一个任务
        let metrics = queue.get_metrics().await;
        assert_eq!(metrics.pending_tasks, 1);
        assert_eq!(metrics.deduplicated_tasks, 1);
    }
}
