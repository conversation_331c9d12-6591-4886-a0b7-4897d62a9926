# Simple API test
$baseUrl = "http://127.0.0.1:3000"

Write-Host "Testing login..." -ForegroundColor Yellow
$loginBody = @{
    username = "testuser456"
    password = "password123"
} | ConvertTo-<PERSON>son

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/auth/login" -Method Post -ContentType "application/json" -Body $loginBody
    $token = $loginResponse.data.token
    Write-Host "Login successful" -ForegroundColor Green
    
    Write-Host "Testing search API..." -ForegroundColor Yellow
    $headers = @{ "Authorization" = "Bearer $token" }
    $searchResponse = Invoke-RestMethod -Uri "$baseUrl/api/messages/search?keyword=test&page=1&limit=20" -Method Get -Headers $headers
    Write-Host "Search successful: $($searchResponse.data.total_count) results" -ForegroundColor Green
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "Status: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host "Test completed" -ForegroundColor Green
