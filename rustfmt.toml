# Rustfmt配置文件 - 稳定版本
# 适用于Axum企业级项目的代码格式化标准（仅使用稳定特性）

# Rust版本 - 使用2024 Edition
edition = "2024"

# 最大行宽 - 100字符，适合现代显示器
max_width = 100

# 硬制表符 - 使用空格而不是制表符
hard_tabs = false

# 制表符空格数 - 4个空格
tab_spaces = 4

# 换行样式 - Unix风格（LF）
newline_style = "Unix"

# 使用小的启发式算法
use_small_heuristics = "Default"

# 重新排序导入 - 启用
reorder_imports = true

# 重新排序模块 - 启用
reorder_modules = true

# 删除嵌套的导入 - 启用
remove_nested_parens = true

# 使用字段初始化简写 - 启用
use_field_init_shorthand = true

# 强制显式泛型参数 - 禁用
force_explicit_abi = false

# 函数参数布局（使用新的配置名）
fn_params_layout = "Tall"

# 数组布局
array_width = 60
chain_width = 60

# 单行if语句
single_line_if_else_max_width = 50

# 匹配块布局
match_block_trailing_comma = false

# 使用try操作符
use_try_shorthand = true
