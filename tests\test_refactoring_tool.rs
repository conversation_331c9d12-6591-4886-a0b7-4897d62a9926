//! # 测试重构工具
//!
//! 提供测试代码重构功能，减少重复模式，提高测试质量
//! 遵循Context7 MCP最佳实践和DRY原则

use std::collections::HashMap;
use std::fs;
use std::path::Path;

/// 测试重构工具
pub struct TestRefactoringTool {
    /// 重复模式映射
    duplicate_patterns: HashMap<String, Vec<String>>,
    /// 重构建议
    refactoring_suggestions: Vec<RefactoringSuggestion>,
}

/// 重构建议
#[derive(Debug, Clone)]
pub struct RefactoringSuggestion {
    /// 建议类型
    pub suggestion_type: SuggestionType,
    /// 描述
    pub description: String,
    /// 影响的文件
    pub affected_files: Vec<String>,
    /// 重构前的代码模式
    pub before_pattern: String,
    /// 重构后的代码模式
    pub after_pattern: String,
    /// 优先级
    pub priority: Priority,
}

/// 建议类型
#[derive(Debug, C<PERSON>, PartialEq)]
pub enum SuggestionType {
    /// 提取通用测试工具函数
    ExtractTestUtility,
    /// 创建测试数据构建器
    CreateTestBuilder,
    /// 统一断言模式
    UnifyAssertionPattern,
    /// 重构重复的设置代码
    RefactorSetupCode,
    /// 创建测试宏
    CreateTestMacro,
}

/// 优先级
#[derive(Debug, Clone, PartialEq, PartialOrd)]
pub enum Priority {
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4,
}

impl TestRefactoringTool {
    /// 创建新的测试重构工具
    pub fn new() -> Self {
        Self {
            duplicate_patterns: HashMap::new(),
            refactoring_suggestions: Vec::new(),
        }
    }

    /// 分析项目并生成重构建议
    pub fn analyze_and_suggest(
        &mut self,
        project_root: &str,
    ) -> Result<(), Box<dyn std::error::Error>> {
        self.scan_test_files(project_root)?;
        self.generate_suggestions();
        Ok(())
    }

    /// 扫描测试文件
    fn scan_test_files(&mut self, project_root: &str) -> Result<(), Box<dyn std::error::Error>> {
        self.scan_directory(project_root)?;
        Ok(())
    }

    /// 递归扫描目录
    fn scan_directory(&mut self, dir_path: &str) -> Result<(), Box<dyn std::error::Error>> {
        let path = Path::new(dir_path);
        if !path.exists() {
            return Ok(());
        }

        for entry in fs::read_dir(path)? {
            let entry = entry?;
            let file_path = entry.path();

            if file_path.is_dir() {
                if let Some(dir_name) = file_path.file_name() {
                    if dir_name != "target" && dir_name != ".git" {
                        self.scan_directory(&file_path.to_string_lossy())?;
                    }
                }
            } else if file_path.extension().is_some_and(|ext| ext == "rs") {
                self.analyze_test_file(&file_path)?;
            }
        }
        Ok(())
    }

    /// 分析单个测试文件
    fn analyze_test_file(&mut self, file_path: &Path) -> Result<(), Box<dyn std::error::Error>> {
        let content = fs::read_to_string(file_path)?;
        let file_path_str = file_path.to_string_lossy().to_string();

        if self.is_test_file(&content) {
            self.extract_patterns(&content, &file_path_str);
        }
        Ok(())
    }

    /// 检查是否是测试文件
    fn is_test_file(&self, content: &str) -> bool {
        content.contains("#[test]")
            || content.contains("#[tokio::test]")
            || content.contains("#[cfg(test)]")
    }

    /// 提取重复模式
    fn extract_patterns(&mut self, content: &str, file_path: &str) {
        // 检测常见的重复模式
        self.detect_setup_patterns(content, file_path);
        self.detect_assertion_patterns(content, file_path);
        self.detect_test_data_patterns(content, file_path);
        self.detect_mock_patterns(content, file_path);
    }

    /// 检测设置模式
    fn detect_setup_patterns(&mut self, content: &str, file_path: &str) {
        let setup_patterns = [
            "let db = create_test_db()",
            "let user = create_test_user()",
            "let task = create_test_task()",
            "TestEnvironment::setup()",
            "setup_test_environment()",
        ];

        for pattern in &setup_patterns {
            if content.contains(pattern) {
                self.duplicate_patterns
                    .entry(pattern.to_string())
                    .or_default()
                    .push(file_path.to_string());
            }
        }
    }

    /// 检测断言模式
    fn detect_assertion_patterns(&mut self, content: &str, file_path: &str) {
        let assertion_patterns = ["assert_eq!", "assert!", "assert_ne!", "assert_matches!"];

        for pattern in &assertion_patterns {
            let count = content.matches(pattern).count();
            if count > 3 {
                self.duplicate_patterns
                    .entry(format!("{pattern}(多次使用)"))
                    .or_default()
                    .push(file_path.to_string());
            }
        }
    }

    /// 检测测试数据模式
    fn detect_test_data_patterns(&mut self, content: &str, file_path: &str) {
        let data_patterns = [
            "Uuid::new_v4()",
            "Utc::now()",
            "\"test_user\"",
            "\"<EMAIL>\"",
            "\"Test Task\"",
        ];

        for pattern in &data_patterns {
            let count = content.matches(pattern).count();
            if count > 2 {
                self.duplicate_patterns
                    .entry(format!("测试数据: {pattern}"))
                    .or_default()
                    .push(file_path.to_string());
            }
        }
    }

    /// 检测模拟模式
    fn detect_mock_patterns(&mut self, content: &str, file_path: &str) {
        let mock_patterns = [
            "mock_",
            "MockRepository",
            "MockService",
            ".expect(",
            ".returning(",
        ];

        for pattern in &mock_patterns {
            if content.contains(pattern) {
                self.duplicate_patterns
                    .entry(format!("模拟模式: {pattern}"))
                    .or_default()
                    .push(file_path.to_string());
            }
        }
    }

    /// 生成重构建议
    fn generate_suggestions(&mut self) {
        for (pattern, files) in &self.duplicate_patterns {
            if files.len() >= 2 {
                let suggestion = self.create_suggestion_for_pattern(pattern, files);
                self.refactoring_suggestions.push(suggestion);
            }
        }

        // 按优先级排序
        self.refactoring_suggestions
            .sort_by(|a, b| b.priority.partial_cmp(&a.priority).unwrap());
    }

    /// 为模式创建建议
    fn create_suggestion_for_pattern(
        &self,
        pattern: &str,
        files: &[String],
    ) -> RefactoringSuggestion {
        let (suggestion_type, description, before_pattern, after_pattern, priority) =
            if pattern.contains("create_test_") {
                (
                    SuggestionType::CreateTestBuilder,
                    format!("创建测试数据构建器来替代重复的 {pattern} 调用"),
                    pattern.to_string(),
                    "TestDataBuilder::new().build()".to_string(),
                    Priority::High,
                )
            } else if pattern.contains("assert") {
                (
                    SuggestionType::UnifyAssertionPattern,
                    format!("统一断言模式，减少重复的 {pattern} 使用"),
                    pattern.to_string(),
                    "TestAssertions::assert_*()".to_string(),
                    Priority::Medium,
                )
            } else if pattern.contains("setup") || pattern.contains("TestEnvironment") {
                (
                    SuggestionType::RefactorSetupCode,
                    format!("重构重复的设置代码: {pattern}"),
                    pattern.to_string(),
                    "TestSetup::with_defaults()".to_string(),
                    Priority::High,
                )
            } else if pattern.contains("测试数据") {
                (
                    SuggestionType::ExtractTestUtility,
                    format!("提取测试工具函数: {pattern}"),
                    pattern.to_string(),
                    "test_utils::create_*()".to_string(),
                    Priority::Medium,
                )
            } else {
                (
                    SuggestionType::CreateTestMacro,
                    format!("创建测试宏来简化: {pattern}"),
                    pattern.to_string(),
                    "test_macro!()".to_string(),
                    Priority::Low,
                )
            };

        RefactoringSuggestion {
            suggestion_type,
            description,
            affected_files: files.to_vec(),
            before_pattern,
            after_pattern,
            priority,
        }
    }

    /// 获取重构建议
    pub fn get_suggestions(&self) -> &[RefactoringSuggestion] {
        &self.refactoring_suggestions
    }

    /// 生成重构报告
    pub fn generate_refactoring_report(&self) -> String {
        let mut report = String::new();

        report.push_str("# 测试代码重构建议报告\n\n");

        // 概述
        report.push_str("## 概述\n");
        report.push_str(&format!(
            "- 发现重复模式: {} 个\n",
            self.duplicate_patterns.len()
        ));
        report.push_str(&format!(
            "- 生成建议: {} 条\n",
            self.refactoring_suggestions.len()
        ));

        let high_priority_count = self
            .refactoring_suggestions
            .iter()
            .filter(|s| s.priority == Priority::High)
            .count();
        report.push_str(&format!("- 高优先级建议: {high_priority_count} 条\n\n"));

        // 按优先级分组的建议
        for priority in [
            Priority::Critical,
            Priority::High,
            Priority::Medium,
            Priority::Low,
        ] {
            let suggestions: Vec<_> = self
                .refactoring_suggestions
                .iter()
                .filter(|s| s.priority == priority)
                .collect();

            if !suggestions.is_empty() {
                report.push_str(&format!("## {priority:?} 优先级建议\n\n"));

                for (i, suggestion) in suggestions.iter().enumerate() {
                    report.push_str(&format!("### {}. {}\n", i + 1, suggestion.description));
                    report.push_str(&format!("**类型**: {:?}\n", suggestion.suggestion_type));
                    report.push_str(&format!(
                        "**影响文件数**: {}\n",
                        suggestion.affected_files.len()
                    ));
                    report.push_str("**重构前**:\n");
                    report.push_str(&format!("```rust\n{}\n```\n", suggestion.before_pattern));
                    report.push_str("**重构后**:\n");
                    report.push_str(&format!("```rust\n{}\n```\n", suggestion.after_pattern));
                    report.push_str("**影响的文件**:\n");
                    for file in &suggestion.affected_files {
                        report.push_str(&format!("- {file}\n"));
                    }
                    report.push('\n');
                }
            }
        }

        // 实施建议
        report.push_str("## 实施建议\n\n");
        report.push_str("1. **优先处理高优先级建议**: 这些建议能显著减少代码重复\n");
        report.push_str("2. **创建测试工具模块**: 将通用功能提取到 `tests/test_utils.rs`\n");
        report.push_str("3. **使用构建器模式**: 为测试数据创建提供灵活的构建器\n");
        report.push_str("4. **统一断言风格**: 创建自定义断言函数提高可读性\n");
        report.push_str("5. **逐步重构**: 一次处理一个模式，确保测试仍然通过\n\n");

        report
    }
}

impl Default for TestRefactoringTool {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_refactoring_tool_creation() {
        let tool = TestRefactoringTool::new();
        assert_eq!(tool.duplicate_patterns.len(), 0);
        assert_eq!(tool.refactoring_suggestions.len(), 0);
    }

    #[test]
    fn test_is_test_file_detection() {
        let tool = TestRefactoringTool::new();

        assert!(tool.is_test_file("#[test]\nfn test_something() {}"));
        assert!(tool.is_test_file("#[tokio::test]\nasync fn test_async() {}"));
        assert!(tool.is_test_file("#[cfg(test)]\nmod tests {}"));
        assert!(!tool.is_test_file("fn main() {}"));
    }

    #[test]
    fn test_pattern_detection() {
        let mut tool = TestRefactoringTool::new();

        let test_content = r#"
            #[test]
            fn test_example() {
                let db = create_test_db();
                let user = create_test_user();
                assert_eq!(user.name, "test_user");
                assert_eq!(user.email, "<EMAIL>");
                assert!(user.is_active);
            }
        "#;

        tool.extract_patterns(test_content, "test_file.rs");

        // 验证检测到的模式
        assert!(
            tool.duplicate_patterns
                .contains_key("let db = create_test_db()")
        );
        assert!(
            tool.duplicate_patterns
                .contains_key("let user = create_test_user()")
        );
    }

    #[test]
    fn test_suggestion_generation() {
        let mut tool = TestRefactoringTool::new();

        // 添加重复模式
        tool.duplicate_patterns.insert(
            "create_test_user".to_string(),
            vec![
                "file1.rs".to_string(),
                "file2.rs".to_string(),
                "file3.rs".to_string(),
            ],
        );

        tool.generate_suggestions();

        assert!(!tool.refactoring_suggestions.is_empty());

        let suggestion = &tool.refactoring_suggestions[0];
        assert_eq!(
            suggestion.suggestion_type,
            SuggestionType::CreateTestBuilder
        );
        assert_eq!(suggestion.affected_files.len(), 3);
    }

    #[test]
    fn test_priority_sorting() {
        let mut tool = TestRefactoringTool::new();

        // 添加不同优先级的建议
        tool.refactoring_suggestions.push(RefactoringSuggestion {
            suggestion_type: SuggestionType::CreateTestMacro,
            description: "Low priority".to_string(),
            affected_files: vec!["file1.rs".to_string()],
            before_pattern: "before".to_string(),
            after_pattern: "after".to_string(),
            priority: Priority::Low,
        });

        tool.refactoring_suggestions.push(RefactoringSuggestion {
            suggestion_type: SuggestionType::RefactorSetupCode,
            description: "High priority".to_string(),
            affected_files: vec!["file2.rs".to_string()],
            before_pattern: "before".to_string(),
            after_pattern: "after".to_string(),
            priority: Priority::High,
        });

        // 重新排序
        tool.refactoring_suggestions
            .sort_by(|a, b| b.priority.partial_cmp(&a.priority).unwrap());

        // 验证高优先级在前
        assert_eq!(tool.refactoring_suggestions[0].priority, Priority::High);
        assert_eq!(tool.refactoring_suggestions[1].priority, Priority::Low);
    }

    #[test]
    fn test_report_generation() {
        let mut tool = TestRefactoringTool::new();

        // 添加测试数据
        tool.duplicate_patterns.insert(
            "test_pattern".to_string(),
            vec!["file1.rs".to_string(), "file2.rs".to_string()],
        );

        tool.refactoring_suggestions.push(RefactoringSuggestion {
            suggestion_type: SuggestionType::ExtractTestUtility,
            description: "Extract test utility".to_string(),
            affected_files: vec!["file1.rs".to_string(), "file2.rs".to_string()],
            before_pattern: "old_pattern".to_string(),
            after_pattern: "new_pattern".to_string(),
            priority: Priority::Medium,
        });

        let report = tool.generate_refactoring_report();

        // 验证报告内容
        assert!(report.contains("测试代码重构建议报告"));
        assert!(report.contains("概述"));
        assert!(report.contains("发现重复模式: 1 个"));
        assert!(report.contains("生成建议: 1 条"));
        assert!(report.contains("Extract test utility"));
        assert!(report.contains("实施建议"));
    }
}
