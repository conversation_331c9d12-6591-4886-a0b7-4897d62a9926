// E2E测试辅助模块

pub mod api;
pub mod auth;
pub mod database;
pub mod playwright;
pub mod task_crud;
pub mod test_server;

// 重新导出常用类型和函数
pub use api::ApiHelper;
pub use auth::AuthHelper;
pub use database::DatabaseHelper;
pub use playwright::PlaywrightClient;
pub use task_crud::{TaskCrudHelper, TestTaskData};
pub use test_server::TestServer;

use anyhow::Result;
use serde_json::Value;
use std::env;
use std::path::PathBuf;

/// E2E测试配置结构
#[derive(Debug, Clone)]
pub struct E2EConfig {
    pub server_host: String,
    pub server_port: u16,
    pub base_url: String,
    pub ws_url: String,
    pub test_username: String,
    pub test_password: String,
    pub test_email: String,
    pub database_url: String,
    pub test_database_url: String,
    pub test_timeout: u64,
    pub retry_attempts: u32,
    pub playwright_headless: bool,
    pub playwright_slow_mo: u64,
    pub viewport_width: u32,
    pub viewport_height: u32,
    pub report_dir: PathBuf,
    pub screenshot_dir: PathBuf,
    pub video_dir: PathBuf,
}

impl Default for E2EConfig {
    fn default() -> Self {
        Self {
            server_host: "127.0.0.1".to_string(),
            server_port: 3000,
            base_url: "http://127.0.0.1:3000".to_string(),
            ws_url: "ws://127.0.0.1:3000/ws".to_string(),
            test_username: "testuser456".to_string(),
            test_password: "password123".to_string(),
            test_email: "<EMAIL>".to_string(),
            database_url: "sqlite:./task_manager.db".to_string(),
            test_database_url: "sqlite:./test_task_manager.db".to_string(),
            test_timeout: 30000,
            retry_attempts: 3,
            playwright_headless: false,
            playwright_slow_mo: 100,
            viewport_width: 1280,
            viewport_height: 720,
            report_dir: PathBuf::from("./tests/e2e/reports"),
            screenshot_dir: PathBuf::from("./tests/e2e/reports/screenshots"),
            video_dir: PathBuf::from("./tests/e2e/reports/videos"),
        }
    }
}

impl E2EConfig {
    /// 从环境变量加载配置
    pub fn from_env() -> Result<Self> {
        // 加载.env文件
        if let Ok(_) = dotenvy::from_filename("tests/e2e/config/test.env") {
            println!("已加载E2E测试环境配置");
        }

        let mut config = Self::default();

        // 从环境变量覆盖默认值
        if let Ok(host) = env::var("SERVER_HOST") {
            config.server_host = host;
        }
        if let Ok(port) = env::var("SERVER_PORT") {
            config.server_port = port.parse().unwrap_or(3000);
        }
        if let Ok(base_url) = env::var("BASE_URL") {
            config.base_url = base_url;
        }
        if let Ok(ws_url) = env::var("WS_URL") {
            config.ws_url = ws_url;
        }
        if let Ok(username) = env::var("TEST_USERNAME") {
            config.test_username = username;
        }
        if let Ok(password) = env::var("TEST_PASSWORD") {
            config.test_password = password;
        }
        if let Ok(email) = env::var("TEST_EMAIL") {
            config.test_email = email;
        }
        if let Ok(db_url) = env::var("DATABASE_URL") {
            config.database_url = db_url;
        }
        if let Ok(test_db_url) = env::var("TEST_DATABASE_URL") {
            config.test_database_url = test_db_url;
        }
        if let Ok(timeout) = env::var("TEST_TIMEOUT") {
            config.test_timeout = timeout.parse().unwrap_or(30000);
        }
        if let Ok(retry) = env::var("RETRY_ATTEMPTS") {
            config.retry_attempts = retry.parse().unwrap_or(3);
        }
        if let Ok(headless) = env::var("PLAYWRIGHT_HEADLESS") {
            config.playwright_headless = headless.parse().unwrap_or(false);
        }
        if let Ok(slow_mo) = env::var("PLAYWRIGHT_SLOW_MO") {
            config.playwright_slow_mo = slow_mo.parse().unwrap_or(100);
        }
        if let Ok(width) = env::var("PLAYWRIGHT_VIEWPORT_WIDTH") {
            config.viewport_width = width.parse().unwrap_or(1280);
        }
        if let Ok(height) = env::var("PLAYWRIGHT_VIEWPORT_HEIGHT") {
            config.viewport_height = height.parse().unwrap_or(720);
        }
        if let Ok(report_dir) = env::var("REPORT_DIR") {
            config.report_dir = PathBuf::from(report_dir);
        }
        if let Ok(screenshot_dir) = env::var("SCREENSHOT_DIR") {
            config.screenshot_dir = PathBuf::from(screenshot_dir);
        }
        if let Ok(video_dir) = env::var("VIDEO_DIR") {
            config.video_dir = PathBuf::from(video_dir);
        }

        Ok(config)
    }
}

/// 加载测试夹具数据
pub fn load_fixture(fixture_name: &str) -> Result<Value> {
    let fixture_path = format!("tests/e2e/fixtures/{}.json", fixture_name);
    let content = std::fs::read_to_string(&fixture_path)
        .map_err(|e| anyhow::anyhow!("无法读取夹具文件 {}: {}", fixture_path, e))?;

    let data: Value = serde_json::from_str(&content)
        .map_err(|e| anyhow::anyhow!("无法解析夹具文件 {}: {}", fixture_path, e))?;

    Ok(data)
}

/// 确保目录存在
pub fn ensure_dir_exists(path: &PathBuf) -> Result<()> {
    if !path.exists() {
        std::fs::create_dir_all(path)
            .map_err(|e| anyhow::anyhow!("无法创建目录 {:?}: {}", path, e))?;
    }
    Ok(())
}

/// 清理测试数据
pub fn cleanup_test_data() -> Result<()> {
    // 清理测试数据库
    if let Ok(test_db_path) = env::var("TEST_DATABASE_URL") {
        if test_db_path.starts_with("sqlite:") {
            let db_file = test_db_path
                .strip_prefix("sqlite:")
                .unwrap_or(&test_db_path);
            if std::path::Path::new(db_file).exists() {
                std::fs::remove_file(db_file)
                    .map_err(|e| anyhow::anyhow!("无法删除测试数据库文件 {}: {}", db_file, e))?;
            }
        }
    }

    // 清理临时文件
    let temp_dirs = ["./tests/e2e/reports/temp", "./tests/e2e/browser-data"];
    for dir in &temp_dirs {
        let path = PathBuf::from(dir);
        if path.exists() {
            std::fs::remove_dir_all(&path)
                .map_err(|e| anyhow::anyhow!("无法删除临时目录 {:?}: {}", path, e))?;
        }
    }

    Ok(())
}
