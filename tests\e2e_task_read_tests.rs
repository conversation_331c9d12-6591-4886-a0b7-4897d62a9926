//! # 任务读取功能E2E测试
//!
//! 本模块实现任务读取功能的端到端测试，遵循Context7 MCP最佳实践：
//! - 使用清晰的函数命名（test_fetch_tasks_list、test_fetch_task_by_id等）
//! - 详细的中文注释
//! - 完整的错误处理
//! - 遵循DRY和SOLID原则
//! - 遵循TDD（Test-Driven Development）开发模式
//! - 验证API响应格式、状态码和数据一致性

use anyhow::{Context, Result};
use serde_json::Value;
use std::time::Duration;
use tokio::time::sleep;

// 导入E2E测试辅助模块
#[path = "e2e/helpers/mod.rs"]
mod helpers;

use helpers::{
    AuthHelper, E2EConfig, TaskCrudHelper, TestServer, TestTaskData, ensure_dir_exists,
    test_server::TestServerConfig,
};

/// 任务读取E2E测试套件
pub struct TaskReadTestSuite {
    config: E2EConfig,
    test_server: TestServer,
    auth_helper: <PERSON><PERSON><PERSON><PERSON><PERSON>,
    task_crud_helper: TaskCrudHelper,
    test_user_token: Option<String>,
    test_task_ids: Vec<String>, // 存储测试任务ID用于清理
}

impl TaskReadTestSuite {
    /// 创建新的测试套件实例
    pub async fn new() -> Result<Self> {
        println!("🔧 初始化任务读取E2E测试套件...");

        // 加载配置
        let config = E2EConfig::from_env().context("无法加载E2E测试配置")?;

        // 创建测试服务器
        let server_config = TestServerConfig {
            host: config.server_host.clone(),
            port: config.server_port,
            startup_timeout: 60,
            health_check_interval: 1,
            project_root: std::env::current_dir()
                .context("无法获取当前目录")?
                .to_string_lossy()
                .to_string(),
        };

        let test_server = TestServer::new(server_config);

        // 创建辅助工具
        let auth_helper = AuthHelper::new(config.clone());
        let task_crud_helper = TaskCrudHelper::new(config.clone());

        Ok(Self {
            config,
            test_server,
            auth_helper,
            task_crud_helper,
            test_user_token: None,
            test_task_ids: Vec::new(),
        })
    }

    /// 设置基本测试环境（不包含认证和测试数据）
    pub async fn setup_basic_environment(&mut self) -> Result<()> {
        println!("🚀 设置基本测试环境...");

        // 1. 确保报告目录存在
        self.ensure_report_directories()?;

        // 2. 启动测试服务器
        self.start_test_server().await?;

        println!("✅ 基本测试环境设置完成");
        Ok(())
    }

    /// 设置测试环境
    pub async fn setup_test_environment(&mut self) -> Result<()> {
        println!("🚀 设置任务读取测试环境...");

        // 1. 设置基本环境
        self.setup_basic_environment().await?;

        // 2. 配置认证流程
        self.configure_authentication().await?;

        // 3. 创建测试数据
        self.create_test_data().await?;

        println!("✅ 任务读取测试环境设置完成");
        Ok(())
    }

    /// 确保报告目录存在
    fn ensure_report_directories(&self) -> Result<()> {
        println!("📁 创建测试报告目录...");

        ensure_dir_exists(&self.config.report_dir)?;
        ensure_dir_exists(&self.config.screenshot_dir)?;
        ensure_dir_exists(&self.config.video_dir)?;

        println!("✅ 测试报告目录创建完成");
        Ok(())
    }

    /// 启动测试服务器
    async fn start_test_server(&mut self) -> Result<()> {
        println!("🚀 启动测试服务器...");

        self.test_server
            .start()
            .await
            .context("无法启动测试服务器")?;

        // 等待服务器完全启动
        sleep(Duration::from_secs(3)).await;

        // 验证服务器健康状态
        let health_check = self.test_server.health_check().await?;
        println!("✅ 服务器健康检查通过: {:?}", health_check);

        Ok(())
    }

    /// 配置认证流程
    async fn configure_authentication(&mut self) -> Result<()> {
        println!("🔐 配置认证流程...");

        // 生成唯一的测试用户名，避免冲突
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_millis();
        let unique_username = format!("read_test_user_{}", timestamp);
        let unique_email = format!("read_test_user_{}@example.com", timestamp);

        println!("📝 使用唯一测试用户名: {}", unique_username);

        // 1. 注册测试用户
        let register_result = self
            .auth_helper
            .register_user(&unique_username, &unique_email, &self.config.test_password)
            .await;

        match register_result {
            Ok(response) => {
                if response["status"].as_u64().unwrap_or(0) == 201 {
                    println!("✅ 测试用户注册成功");
                } else {
                    return Err(anyhow::anyhow!("用户注册失败: {:?}", response));
                }
            }
            Err(e) => {
                return Err(anyhow::anyhow!("用户注册请求失败: {}", e));
            }
        }

        // 2. 登录获取认证令牌
        let token = self
            .auth_helper
            .get_auth_token(&unique_username, &self.config.test_password)
            .await?;

        self.test_user_token = Some(token.clone());

        // 3. 验证令牌有效性（通过调用需要认证的API端点）
        let test_result = self.task_crud_helper.fetch_tasks_list(&token).await;
        match test_result {
            Ok(response) => {
                if response["status"].as_u64().unwrap_or(0) == 200 {
                    println!("✅ 令牌验证通过（通过任务列表API测试）");
                } else {
                    return Err(anyhow::anyhow!(
                        "令牌验证失败，任务列表API返回: {:?}",
                        response
                    ));
                }
            }
            Err(e) => {
                return Err(anyhow::anyhow!("令牌验证失败，无法调用任务列表API: {}", e));
            }
        }

        println!("✅ 认证流程配置完成，令牌验证通过");
        Ok(())
    }

    /// 创建测试数据
    async fn create_test_data(&mut self) -> Result<()> {
        println!("📝 创建测试数据...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 创建多个测试任务用于读取测试
        let test_tasks = vec![
            TestTaskData::new("读取测试任务 #1")
                .with_description("第一个用于读取测试的任务")
                .with_priority("high"),
            TestTaskData::new("读取测试任务 #2")
                .with_description("第二个用于读取测试的任务")
                .with_priority("medium")
                .with_completed(true),
            TestTaskData::new("读取测试任务 #3")
                .with_description("第三个用于读取测试的任务")
                .with_priority("low"),
        ];

        for test_task in test_tasks {
            let create_result = self.task_crud_helper.create_task(token, &test_task).await?;
            if create_result["status"].as_u64().unwrap_or(0) == 201 {
                if let Some(task_id) = create_result["body"]["data"]["id"].as_str() {
                    self.test_task_ids.push(task_id.to_string());
                }
            }
        }

        println!(
            "✅ 测试数据创建完成，创建了{}个测试任务",
            self.test_task_ids.len()
        );
        Ok(())
    }

    /// 测试获取任务列表成功场景
    pub async fn test_fetch_tasks_list_success(&self) -> Result<()> {
        println!("🧪 测试获取任务列表成功场景...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 获取任务列表
        let list_result = self.task_crud_helper.fetch_tasks_list(token).await?;

        // 验证响应状态码
        assert_eq!(
            list_result["status"].as_u64().unwrap_or(0),
            200,
            "获取任务列表应返回200状态码"
        );

        // 验证响应格式
        self.task_crud_helper
            .validate_tasks_list_response(&list_result["body"])?;

        // 验证任务列表包含我们创建的测试任务
        let tasks = list_result["body"]["data"].as_array().unwrap();
        assert!(
            tasks.len() >= self.test_task_ids.len(),
            "任务列表应包含至少{}个任务",
            self.test_task_ids.len()
        );

        // 验证每个任务的基本字段
        for task in tasks {
            assert!(task["id"].is_string(), "任务ID应为字符串类型");
            assert!(task["title"].is_string(), "任务标题应为字符串类型");
            assert!(task["completed"].is_boolean(), "任务完成状态应为布尔类型");
            assert!(task["created_at"].is_string(), "创建时间应为字符串类型");
            assert!(task["updated_at"].is_string(), "更新时间应为字符串类型");
        }

        println!("✅ 获取任务列表成功场景测试通过");
        Ok(())
    }

    /// 测试获取单个任务详情成功场景
    pub async fn test_fetch_task_by_id_success(&self) -> Result<()> {
        println!("🧪 测试获取单个任务详情成功场景...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 使用第一个测试任务ID
        let task_id = self
            .test_task_ids
            .first()
            .ok_or_else(|| anyhow::anyhow!("没有可用的测试任务ID"))?;

        // 获取任务详情
        let task_result = self
            .task_crud_helper
            .fetch_task_by_id(token, task_id)
            .await?;

        // 验证响应状态码
        assert_eq!(
            task_result["status"].as_u64().unwrap_or(0),
            200,
            "获取任务详情应返回200状态码"
        );

        // 验证响应格式
        self.task_crud_helper
            .validate_task_response(&task_result["body"])?;

        // 验证任务ID匹配
        let returned_task_id = task_result["body"]["data"]["id"].as_str().unwrap();
        assert_eq!(returned_task_id, task_id, "返回的任务ID应与请求的ID一致");

        // 验证任务数据完整性
        let task_data = &task_result["body"]["data"];
        assert!(
            task_data["title"]
                .as_str()
                .unwrap()
                .contains("读取测试任务"),
            "任务标题应包含测试标识"
        );
        assert!(
            task_data["description"].is_string(),
            "任务描述应为字符串类型"
        );

        println!("✅ 获取单个任务详情成功场景测试通过");
        Ok(())
    }

    /// 测试获取不存在的任务
    pub async fn test_fetch_task_not_found(&self) -> Result<()> {
        println!("🧪 测试获取不存在的任务...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 使用不存在的任务ID（有效的UUID格式）
        let non_existent_id = "00000000-0000-0000-0000-000000000000";

        // 尝试获取不存在的任务
        let task_result = self
            .task_crud_helper
            .fetch_task_by_id(token, non_existent_id)
            .await;

        // 验证响应
        match task_result {
            Ok(response) => {
                let status = response["status"].as_u64().unwrap_or(0);
                assert_eq!(status, 404, "获取不存在的任务应返回404状态码");
            }
            Err(e) => {
                // 如果是网络错误或JSON解析错误，检查是否是404响应
                let error_msg = e.to_string();
                assert!(
                    error_msg.contains("404") || error_msg.contains("Not Found"),
                    "应该是404错误，实际错误: {}",
                    error_msg
                );
            }
        }

        println!("✅ 获取不存在的任务测试通过");
        Ok(())
    }

    /// 测试未认证用户获取任务列表
    pub async fn test_fetch_tasks_list_unauthorized(&self) -> Result<()> {
        println!("🧪 测试未认证用户获取任务列表...");

        // 使用无效令牌
        let invalid_token = "invalid_token";

        // 尝试获取任务列表
        let list_result = self.task_crud_helper.fetch_tasks_list(invalid_token).await;

        // 验证响应 - 应该是认证错误
        match list_result {
            Ok(response) => {
                let status = response["status"].as_u64().unwrap_or(0);
                assert_eq!(status, 401, "未认证用户应返回401状态码");
            }
            Err(e) => {
                // 如果是网络错误或JSON解析错误，检查是否是401响应
                let error_msg = e.to_string();
                assert!(
                    error_msg.contains("401") || error_msg.contains("Unauthorized"),
                    "应该是认证错误，实际错误: {}",
                    error_msg
                );
            }
        }

        println!("✅ 未认证用户获取任务列表测试通过");
        Ok(())
    }

    /// 测试未认证用户获取单个任务
    pub async fn test_fetch_task_by_id_unauthorized(&self) -> Result<()> {
        println!("🧪 测试未认证用户获取单个任务...");

        // 使用无效令牌和任意任务ID（不需要真实存在）
        let invalid_token = "invalid_token";
        let dummy_task_id = "dummy-task-id";

        // 尝试获取任务详情
        let task_result = self
            .task_crud_helper
            .fetch_task_by_id(invalid_token, dummy_task_id)
            .await;

        // 验证响应 - 应该是认证错误，而不是任务不存在错误
        match task_result {
            Ok(response) => {
                let status = response["status"].as_u64().unwrap_or(0);
                assert_eq!(status, 401, "未认证用户应返回401状态码");
            }
            Err(e) => {
                // 如果是网络错误或JSON解析错误，检查是否是401响应
                let error_msg = e.to_string();
                assert!(
                    error_msg.contains("401") || error_msg.contains("Unauthorized"),
                    "应该是认证错误，实际错误: {}",
                    error_msg
                );
            }
        }

        println!("✅ 未认证用户获取单个任务测试通过");
        Ok(())
    }

    /// 测试任务列表分页功能（如果支持）
    pub async fn test_fetch_tasks_list_pagination(&self) -> Result<()> {
        println!("🧪 测试任务列表分页功能...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 获取第一页任务列表
        let list_result = self.task_crud_helper.fetch_tasks_list(token).await?;

        // 验证响应状态码
        assert_eq!(
            list_result["status"].as_u64().unwrap_or(0),
            200,
            "获取任务列表应返回200状态码"
        );

        // 验证响应格式
        self.task_crud_helper
            .validate_tasks_list_response(&list_result["body"])?;

        // 验证任务列表不为空
        let tasks = list_result["body"]["data"].as_array().unwrap();
        assert!(!tasks.is_empty(), "任务列表不应为空");

        println!("✅ 任务列表分页功能测试通过");
        Ok(())
    }

    /// 测试任务读取响应时间性能
    pub async fn test_fetch_tasks_performance(&self) -> Result<()> {
        println!("🧪 测试任务读取响应时间性能...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 测试任务列表获取性能
        let start_time = std::time::Instant::now();
        let list_result = self.task_crud_helper.fetch_tasks_list(token).await?;
        let list_response_time = start_time.elapsed();

        // 验证列表获取成功
        assert_eq!(list_result["status"].as_u64().unwrap_or(0), 200);

        // 验证响应时间（应小于500ms）
        assert!(
            list_response_time.as_millis() < 500,
            "任务列表获取响应时间应小于500ms，实际: {}ms",
            list_response_time.as_millis()
        );

        println!(
            "📊 任务列表获取响应时间: {}ms",
            list_response_time.as_millis()
        );

        // 测试单个任务获取性能
        let task_id = self
            .test_task_ids
            .first()
            .ok_or_else(|| anyhow::anyhow!("没有可用的测试任务ID"))?;

        let start_time = std::time::Instant::now();
        let task_result = self
            .task_crud_helper
            .fetch_task_by_id(token, task_id)
            .await?;
        let task_response_time = start_time.elapsed();

        // 验证任务获取成功
        assert_eq!(task_result["status"].as_u64().unwrap_or(0), 200);

        // 验证响应时间（应小于300ms）
        assert!(
            task_response_time.as_millis() < 300,
            "单个任务获取响应时间应小于300ms，实际: {}ms",
            task_response_time.as_millis()
        );

        println!(
            "📊 单个任务获取响应时间: {}ms",
            task_response_time.as_millis()
        );

        println!("✅ 任务读取响应时间性能测试通过");
        Ok(())
    }

    /// 测试任务数据一致性
    pub async fn test_task_data_consistency(&self) -> Result<()> {
        println!("🧪 测试任务数据一致性...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 获取任务列表
        let list_result = self.task_crud_helper.fetch_tasks_list(token).await?;
        assert_eq!(list_result["status"].as_u64().unwrap_or(0), 200);

        let tasks_list = list_result["body"]["data"].as_array().unwrap();

        // 验证列表中的每个任务都能单独获取
        for task in tasks_list {
            if let Some(task_id) = task["id"].as_str() {
                let individual_result = self
                    .task_crud_helper
                    .fetch_task_by_id(token, task_id)
                    .await?;
                assert_eq!(individual_result["status"].as_u64().unwrap_or(0), 200);

                let individual_task = &individual_result["body"]["data"];

                // 验证数据一致性
                assert_eq!(
                    task["id"].as_str().unwrap(),
                    individual_task["id"].as_str().unwrap(),
                    "任务ID应保持一致"
                );
                assert_eq!(
                    task["title"].as_str().unwrap(),
                    individual_task["title"].as_str().unwrap(),
                    "任务标题应保持一致"
                );
                assert_eq!(
                    task["completed"].as_bool().unwrap(),
                    individual_task["completed"].as_bool().unwrap(),
                    "任务完成状态应保持一致"
                );
            }
        }

        println!("✅ 任务数据一致性测试通过");
        Ok(())
    }

    /// 清理测试环境
    pub async fn cleanup(&mut self) -> Result<()> {
        println!("🧹 清理测试环境...");

        // 清理创建的测试任务
        if let Some(token) = &self.test_user_token {
            for task_id in &self.test_task_ids {
                let _ = self.task_crud_helper.delete_task(token, task_id).await;
            }
        }

        // 停止测试服务器
        self.test_server.stop()?;

        println!("✅ 测试环境清理完成");
        Ok(())
    }
}

impl Drop for TaskReadTestSuite {
    fn drop(&mut self) {
        // 确保服务器被停止
        let _ = self.test_server.stop();
    }
}

/// 获取任务列表成功测试
#[tokio::test]
async fn test_fetch_tasks_list_success() -> Result<()> {
    let mut test_suite = TaskReadTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_fetch_tasks_list_success().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 获取单个任务详情成功测试
#[tokio::test]
async fn test_fetch_task_by_id_success() -> Result<()> {
    let mut test_suite = TaskReadTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_fetch_task_by_id_success().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 获取不存在的任务测试
#[tokio::test]
async fn test_fetch_task_not_found() -> Result<()> {
    let mut test_suite = TaskReadTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_fetch_task_not_found().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 未认证用户获取任务列表测试
#[tokio::test]
async fn test_fetch_tasks_list_unauthorized() -> Result<()> {
    let mut test_suite = TaskReadTestSuite::new().await?;
    // 不需要设置完整的测试环境，只需要基本的服务器连接
    test_suite.setup_basic_environment().await?;

    test_suite.test_fetch_tasks_list_unauthorized().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 未认证用户获取单个任务测试
#[tokio::test]
async fn test_fetch_task_by_id_unauthorized() -> Result<()> {
    let mut test_suite = TaskReadTestSuite::new().await?;
    // 不需要设置完整的测试环境，只需要基本的服务器连接
    test_suite.setup_basic_environment().await?;

    test_suite.test_fetch_task_by_id_unauthorized().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 任务列表分页功能测试
#[tokio::test]
async fn test_fetch_tasks_list_pagination() -> Result<()> {
    let mut test_suite = TaskReadTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_fetch_tasks_list_pagination().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 任务读取响应时间性能测试
#[tokio::test]
async fn test_fetch_tasks_performance() -> Result<()> {
    let mut test_suite = TaskReadTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_fetch_tasks_performance().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 任务数据一致性测试
#[tokio::test]
async fn test_task_data_consistency() -> Result<()> {
    let mut test_suite = TaskReadTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_task_data_consistency().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 主测试函数 - 运行所有任务读取测试
#[tokio::main]
async fn main() -> Result<()> {
    // 设置日志
    tracing_subscriber::fmt::init();

    println!("🚀 启动任务读取功能E2E测试套件");

    let mut test_suite = TaskReadTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    println!("📋 开始执行任务读取测试用例...");

    // 1. 基础功能测试
    println!("1️⃣ 执行任务列表获取成功测试...");
    test_suite.test_fetch_tasks_list_success().await?;

    // 2. 单个任务获取测试
    println!("2️⃣ 执行单个任务详情获取成功测试...");
    test_suite.test_fetch_task_by_id_success().await?;

    // 3. 错误场景测试
    println!("3️⃣ 执行获取不存在任务测试...");
    test_suite.test_fetch_task_not_found().await?;

    // 4. 未认证用户测试
    println!("4️⃣ 执行未认证用户获取任务列表测试...");
    test_suite.test_fetch_tasks_list_unauthorized().await?;

    // 5. 未认证用户获取单个任务测试
    println!("5️⃣ 执行未认证用户获取单个任务测试...");
    test_suite.test_fetch_task_by_id_unauthorized().await?;

    // 6. 分页功能测试
    println!("6️⃣ 执行任务列表分页功能测试...");
    test_suite.test_fetch_tasks_list_pagination().await?;

    // 7. 性能测试
    println!("7️⃣ 执行任务读取响应时间性能测试...");
    test_suite.test_fetch_tasks_performance().await?;

    // 8. 数据一致性测试
    println!("8️⃣ 执行任务数据一致性测试...");
    test_suite.test_task_data_consistency().await?;

    test_suite.cleanup().await?;

    println!("✅ 所有任务读取E2E测试完成");
    Ok(())
}
