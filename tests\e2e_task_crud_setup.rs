//! # 任务管理CRUD操作E2E测试环境设置
//!
//! 本模块负责搭建任务管理CRUD操作的E2E测试环境并配置认证流程
//! 遵循Context7 MCP最佳实践和rust_axum_Rules.md规范：
//! - 使用清晰的函数命名（setup_test_environment、configure_authentication等）
//! - 详细的中文注释
//! - 完整的错误处理
//! - 遵循DRY和SOLID原则
//! - 遵循TDD（Test-Driven Development）开发模式

use anyhow::{Context, Result};
use serde_json::{Value, json};
use std::time::Duration;
use tokio::time::sleep;

// 导入E2E测试辅助模块
#[path = "e2e/helpers/mod.rs"]
mod helpers;

use helpers::{AuthHelper, E2EConfig, TaskCrudHelper, TestServer, TestTaskData, ensure_dir_exists};

use helpers::test_server::TestServerConfig;

/// 任务管理CRUD测试环境管理器
pub struct TaskCrudTestEnvironment {
    config: E2EConfig,
    test_server: TestServer,
    auth_helper: AuthHelper,
    task_crud_helper: TaskCrudHelper,
    test_user_token: Option<String>,
}

impl TaskCrudTestEnvironment {
    /// 创建新的测试环境实例
    pub async fn new() -> Result<Self> {
        println!("🔧 初始化任务管理CRUD测试环境...");

        // 加载配置
        let config = E2EConfig::from_env().context("无法加载E2E测试配置")?;

        // 创建测试服务器
        let server_config = TestServerConfig {
            host: config.server_host.clone(),
            port: config.server_port,
            startup_timeout: 60,
            health_check_interval: 1,
            project_root: std::env::current_dir()
                .context("无法获取当前目录")?
                .to_string_lossy()
                .to_string(),
        };

        let test_server = TestServer::new(server_config);

        // 创建辅助工具
        let auth_helper = AuthHelper::new(config.clone());
        let task_crud_helper = TaskCrudHelper::new(config.clone());

        Ok(Self {
            config,
            test_server,
            auth_helper,
            task_crud_helper,
            test_user_token: None,
        })
    }

    /// 设置测试环境
    pub async fn setup_test_environment(&mut self) -> Result<()> {
        println!("🚀 设置任务管理CRUD测试环境...");

        // 1. 确保报告目录存在
        self.ensure_report_directories()?;

        // 2. 启动测试服务器
        self.start_test_server().await?;

        // 3. 配置认证流程
        self.configure_authentication().await?;

        // 4. 验证API端点可用性
        self.verify_api_endpoints().await?;

        println!("✅ 任务管理CRUD测试环境设置完成");
        Ok(())
    }

    /// 确保报告目录存在
    fn ensure_report_directories(&self) -> Result<()> {
        println!("📁 创建测试报告目录...");

        ensure_dir_exists(&self.config.report_dir)?;
        ensure_dir_exists(&self.config.screenshot_dir)?;
        ensure_dir_exists(&self.config.video_dir)?;

        println!("✅ 测试报告目录创建完成");
        Ok(())
    }

    /// 启动测试服务器
    async fn start_test_server(&mut self) -> Result<()> {
        println!("🚀 启动测试服务器...");

        self.test_server
            .start()
            .await
            .context("无法启动测试服务器")?;

        // 等待服务器完全启动
        sleep(Duration::from_secs(3)).await;

        // 验证服务器健康状态
        let health_check = self.test_server.health_check().await?;
        println!("✅ 服务器健康检查通过: {:?}", health_check);

        Ok(())
    }

    /// 配置认证流程
    async fn configure_authentication(&mut self) -> Result<()> {
        println!("🔐 配置认证流程...");

        // 1. 注册测试用户（如果不存在）
        let register_result = self
            .auth_helper
            .register_user(
                &self.config.test_username,
                &self.config.test_email,
                &self.config.test_password,
            )
            .await;

        match register_result {
            Ok(response) => {
                if response["status"].as_u64().unwrap_or(0) == 201 {
                    println!("✅ 测试用户注册成功");
                } else if response["status"].as_u64().unwrap_or(0) == 409 {
                    println!("ℹ️ 测试用户已存在，跳过注册");
                } else {
                    return Err(anyhow::anyhow!("用户注册失败: {:?}", response));
                }
            }
            Err(e) => {
                println!("⚠️ 用户注册失败，可能用户已存在: {}", e);
            }
        }

        // 2. 登录获取认证令牌
        let login_result = self
            .auth_helper
            .login_user(&self.config.test_username, &self.config.test_password)
            .await?;

        if login_result["status"].as_u64().unwrap_or(0) != 200 {
            return Err(anyhow::anyhow!("用户登录失败: {:?}", login_result));
        }

        // 3. 提取并保存认证令牌
        let token = self
            .auth_helper
            .get_auth_token(&self.config.test_username, &self.config.test_password)
            .await?;

        self.test_user_token = Some(token.clone());

        // 4. 验证令牌有效性
        let verify_result = self.auth_helper.verify_token(&token).await?;
        if verify_result["status"].as_u64().unwrap_or(0) != 200 {
            return Err(anyhow::anyhow!("令牌验证失败: {:?}", verify_result));
        }

        println!("✅ 认证流程配置完成，令牌验证通过");
        Ok(())
    }

    /// 验证API端点可用性
    async fn verify_api_endpoints(&self) -> Result<()> {
        println!("🔍 验证API端点可用性...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 1. 验证任务列表端点
        let tasks_list_result = self.task_crud_helper.fetch_tasks_list(token).await?;
        if tasks_list_result["status"].as_u64().unwrap_or(0) != 200 {
            return Err(anyhow::anyhow!(
                "任务列表端点不可用: {:?}",
                tasks_list_result
            ));
        }

        // 2. 验证任务创建端点（创建一个测试任务）
        let test_task = TestTaskData::new("API端点验证测试任务")
            .with_description("用于验证API端点可用性的测试任务");

        let create_result = self.task_crud_helper.create_task(token, &test_task).await?;
        if create_result["status"].as_u64().unwrap_or(0) != 201 {
            return Err(anyhow::anyhow!("任务创建端点不可用: {:?}", create_result));
        }

        // 3. 获取创建的任务ID并验证获取端点
        if let Some(task_id) = create_result["body"]["data"]["id"].as_str() {
            let get_result = self
                .task_crud_helper
                .fetch_task_by_id(token, task_id)
                .await?;
            if get_result["status"].as_u64().unwrap_or(0) != 200 {
                return Err(anyhow::anyhow!("任务获取端点不可用: {:?}", get_result));
            }

            // 4. 验证任务更新端点
            let updated_task =
                TestTaskData::new("API端点验证测试任务（已更新）").with_completed(true);

            let update_result = self
                .task_crud_helper
                .update_task(token, task_id, &updated_task)
                .await?;
            if update_result["status"].as_u64().unwrap_or(0) != 200 {
                return Err(anyhow::anyhow!("任务更新端点不可用: {:?}", update_result));
            }

            // 5. 验证任务删除端点
            let delete_result = self.task_crud_helper.delete_task(token, task_id).await?;
            if delete_result["status"].as_u64().unwrap_or(0) != 204 {
                return Err(anyhow::anyhow!("任务删除端点不可用: {:?}", delete_result));
            }
        } else {
            return Err(anyhow::anyhow!("无法从创建响应中提取任务ID"));
        }

        println!("✅ 所有API端点验证通过");
        Ok(())
    }

    /// 获取认证令牌
    pub fn get_auth_token(&self) -> Option<&String> {
        self.test_user_token.as_ref()
    }

    /// 获取任务CRUD辅助工具
    pub fn get_task_crud_helper(&self) -> &TaskCrudHelper {
        &self.task_crud_helper
    }

    /// 获取认证辅助工具
    pub fn get_auth_helper(&self) -> &AuthHelper {
        &self.auth_helper
    }

    /// 获取配置
    pub fn get_config(&self) -> &E2EConfig {
        &self.config
    }

    /// 清理测试环境
    pub async fn cleanup(&mut self) -> Result<()> {
        println!("🧹 清理测试环境...");

        // 清理测试任务
        if let Some(token) = &self.test_user_token {
            let _ = self.task_crud_helper.cleanup_test_tasks(token).await;
        }

        // 停止测试服务器
        self.test_server.stop()?;

        println!("✅ 测试环境清理完成");
        Ok(())
    }
}

impl Drop for TaskCrudTestEnvironment {
    fn drop(&mut self) {
        // 确保服务器被停止
        let _ = self.test_server.stop();
    }
}

/// 主测试函数
#[tokio::test]
async fn test_task_crud_environment_setup() -> Result<()> {
    println!("🧪 开始任务管理CRUD测试环境设置测试");

    let mut test_env = TaskCrudTestEnvironment::new().await?;

    // 设置测试环境
    test_env.setup_test_environment().await?;

    // 验证环境设置成功
    assert!(test_env.get_auth_token().is_some(), "认证令牌应该已设置");

    // 清理测试环境
    test_env.cleanup().await?;

    println!("✅ 任务管理CRUD测试环境设置测试通过");
    Ok(())
}

#[tokio::main]
async fn main() -> Result<()> {
    // 设置日志
    tracing_subscriber::fmt::init();

    println!("🚀 启动任务管理CRUD测试环境设置");

    let mut test_env = TaskCrudTestEnvironment::new().await?;
    test_env.setup_test_environment().await?;

    println!("✅ 任务管理CRUD测试环境设置完成，可以开始运行测试");

    // 保持环境运行，等待手动停止
    println!("按 Ctrl+C 停止测试环境");
    tokio::signal::ctrl_c().await?;

    test_env.cleanup().await?;
    Ok(())
}
