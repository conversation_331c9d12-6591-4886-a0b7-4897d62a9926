/**
 * WebSocket连接稳定性测试
 * 
 * 测试场景：
 * 1. 连续点击"连接"按钮3次
 * 2. 连续点击"断开"按钮3次  
 * 3. 最后点击"连接"按钮，使WebSocket处于连接状态
 * 4. 等待约2秒后，检查连接是否自动断开
 */

const { test, expect } = require('@playwright/test');

test.describe('WebSocket连接稳定性测试', () => {
    let page;
    let serverLogs = [];
    
    test.beforeEach(async ({ browser }) => {
        page = await browser.newPage();
        
        // 监听控制台日志
        page.on('console', msg => {
            console.log(`[浏览器控制台] ${msg.type()}: ${msg.text()}`);
        });
        
        // 导航到测试页面
        await page.goto('http://127.0.0.1:3000');
        
        // 等待页面加载完成
        await page.waitForLoadState('networkidle');
        
        // 登录测试用户
        await loginTestUser(page);
    });
    
    test.afterEach(async () => {
        if (page) {
            await page.close();
        }
    });
    
    test('频繁连接断开操作后的连接稳定性', async () => {
        console.log('开始WebSocket连接稳定性测试...');
        
        // 获取WebSocket控制按钮
        const connectBtn = page.locator('#wsConnectBtn');
        const disconnectBtn = page.locator('#wsDisconnectBtn');
        const connectionStatus = page.locator('#connectionStatus');
        
        // 验证初始状态
        await expect(connectBtn).toBeEnabled();
        await expect(disconnectBtn).toBeDisabled();
        await expect(connectionStatus).toContainText('未连接');
        
        console.log('步骤1: 连续点击"连接"按钮3次');
        for (let i = 1; i <= 3; i++) {
            console.log(`  第${i}次点击连接按钮`);
            await connectBtn.click();
            await page.waitForTimeout(500); // 等待500ms观察状态变化
            
            // 记录连接状态
            const statusText = await connectionStatus.textContent();
            console.log(`  连接状态: ${statusText}`);
            
            // 检查是否有重复连接的警告消息
            const rawMessages = page.locator('#rawMessages');
            const messagesText = await rawMessages.textContent();
            if (messagesText.includes('已经连接到WebSocket服务器')) {
                console.log(`  检测到重复连接警告`);
            }
        }
        
        // 验证最终应该是连接状态
        await expect(connectionStatus).toContainText('已连接', { timeout: 2000 });
        await expect(connectBtn).toBeDisabled();
        await expect(disconnectBtn).toBeEnabled();
        
        console.log('步骤2: 连续点击"断开"按钮3次');
        for (let i = 1; i <= 3; i++) {
            console.log(`  第${i}次点击断开按钮`);
            await disconnectBtn.click();
            await page.waitForTimeout(500); // 等待500ms观察状态变化
            
            // 记录连接状态
            const statusText = await connectionStatus.textContent();
            console.log(`  连接状态: ${statusText}`);
        }
        
        // 验证最终应该是断开状态
        await expect(connectionStatus).toContainText('未连接', { timeout: 2000 });
        await expect(connectBtn).toBeEnabled();
        await expect(disconnectBtn).toBeDisabled();
        
        console.log('步骤3: 最后点击"连接"按钮');
        await connectBtn.click();
        
        // 验证连接成功
        await expect(connectionStatus).toContainText('已连接', { timeout: 5000 });
        await expect(connectBtn).toBeDisabled();
        await expect(disconnectBtn).toBeEnabled();
        
        console.log('步骤4: 等待30秒检查连接稳定性');
        
        // 记录开始时间
        const startTime = Date.now();
        let connectionLost = false;
        let connectionLostTime = null;
        
        // 每秒检查一次连接状态，持续30秒
        for (let second = 1; second <= 30; second++) {
            await page.waitForTimeout(1000);
            
            const statusText = await connectionStatus.textContent();
            const currentTime = Date.now();
            const elapsedSeconds = Math.floor((currentTime - startTime) / 1000);
            
            console.log(`  第${elapsedSeconds}秒: 连接状态 = ${statusText}`);
            
            // 检查是否连接丢失
            if (statusText.includes('未连接') && !connectionLost) {
                connectionLost = true;
                connectionLostTime = elapsedSeconds;
                console.log(`❌ 连接在第${elapsedSeconds}秒时丢失！`);
                break;
            }
            
            // 检查按钮状态是否正确
            const connectBtnEnabled = await connectBtn.isEnabled();
            const disconnectBtnEnabled = await disconnectBtn.isEnabled();
            
            if (statusText.includes('已连接')) {
                if (connectBtnEnabled || !disconnectBtnEnabled) {
                    console.log(`⚠️  第${elapsedSeconds}秒: 按钮状态异常 - 连接按钮启用:${connectBtnEnabled}, 断开按钮启用:${disconnectBtnEnabled}`);
                }
            }
        }
        
        // 测试结果评估
        if (connectionLost) {
            console.log(`❌ 测试失败: 连接在${connectionLostTime}秒后自动断开`);
            
            // 获取原始消息日志以分析断开原因
            const rawMessages = await page.locator('#rawMessages').textContent();
            console.log('原始消息日志:');
            console.log(rawMessages);
            
            // 测试失败
            throw new Error(`连接稳定性测试失败: 连接在${connectionLostTime}秒后自动断开`);
        } else {
            console.log('✅ 测试成功: 连接在30秒内保持稳定');
        }
        
        // 最终验证连接仍然活跃
        await expect(connectionStatus).toContainText('已连接');
        await expect(connectBtn).toBeDisabled();
        await expect(disconnectBtn).toBeEnabled();
        
        // 发送一条测试消息验证连接功能正常
        console.log('步骤5: 发送测试消息验证连接功能');
        const messageInput = page.locator('#messageInput');
        await messageInput.fill('连接稳定性测试消息');
        await page.locator('#sendBtn').click();
        
        // 验证消息发送成功
        const chatMessages = page.locator('#chatMessages');
        await expect(chatMessages).toContainText('连接稳定性测试消息', { timeout: 3000 });
        
        console.log('✅ WebSocket连接稳定性测试完成');
    });
    
    test('检查服务器端连接管理', async () => {
        console.log('开始服务器端连接管理测试...');
        
        const connectBtn = page.locator('#wsConnectBtn');
        const disconnectBtn = page.locator('#wsDisconnectBtn');
        
        // 执行相同的点击序列
        console.log('执行连接/断开点击序列...');
        
        // 3次连接
        for (let i = 0; i < 3; i++) {
            await connectBtn.click();
            await page.waitForTimeout(300);
        }
        
        // 3次断开
        for (let i = 0; i < 3; i++) {
            await disconnectBtn.click();
            await page.waitForTimeout(300);
        }
        
        // 最后连接
        await connectBtn.click();
        await page.waitForTimeout(1000);
        
        // 检查WebSocket统计信息
        console.log('检查WebSocket统计信息...');
        const response = await page.request.get('http://127.0.0.1:3000/api/websocket/stats', {
            headers: {
                'Authorization': `Bearer ${await getAuthToken(page)}`
            }
        });
        
        expect(response.ok()).toBeTruthy();
        const stats = await response.json();
        console.log('WebSocket统计信息:', JSON.stringify(stats, null, 2));
        
        // 验证统计信息合理性
        expect(stats.active_connections).toBeGreaterThanOrEqual(0);
        expect(stats.total_connections).toBeGreaterThanOrEqual(1);
    });
});

/**
 * 登录测试用户
 */
async function loginTestUser(page) {
    console.log('登录测试用户...');
    
    // 切换到登录标签
    await page.click('#loginTab');
    
    // 填写登录信息
    await page.fill('#loginUsername', 'testuser456');
    await page.fill('#loginPassword', 'password123');
    
    // 点击登录按钮
    await page.click('#loginBtn');
    
    // 等待登录成功
    await page.waitForSelector('#userInfo:has-text("testuser456")', { timeout: 5000 });
    
    console.log('登录成功');
}

/**
 * 获取认证令牌
 */
async function getAuthToken(page) {
    return await page.evaluate(() => {
        return localStorage.getItem('authToken');
    });
}
