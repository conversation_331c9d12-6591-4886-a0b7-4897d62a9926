/**
 * Lighthouse配置文件 - 前端性能优化评估
 * 基于2025年最新Lighthouse最佳实践
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-28
 */

module.exports = {
  // 扩展默认配置
  extends: 'lighthouse:default',
  
  // 设置运行环境
  settings: {
    // 模拟移动设备
    formFactor: 'mobile',
    
    // 网络节流设置
    throttling: {
      rttMs: 150,
      throughputKbps: 1638.4,
      cpuSlowdownMultiplier: 4,
      requestLatencyMs: 150,
      downloadThroughputKbps: 1638.4,
      uploadThroughputKbps: 675
    },
    
    // 屏幕模拟设置
    screenEmulation: {
      mobile: true,
      width: 375,
      height: 667,
      deviceScaleFactor: 2,
      disabled: false
    },
    
    // 用户代理
    emulatedUserAgent: 'Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
    
    // 其他设置
    locale: 'zh-CN',
    onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo', 'pwa'],
    skipAudits: [],
    
    // 预算配置
    budgets: [{
      resourceSizes: [
        { resourceType: 'total', budget: 2048 },      // 总大小 2MB
        { resourceType: 'script', budget: 512 },     // JavaScript 512KB
        { resourceType: 'stylesheet', budget: 128 }, // CSS 128KB
        { resourceType: 'image', budget: 1024 },     // 图片 1MB
        { resourceType: 'font', budget: 256 },       // 字体 256KB
        { resourceType: 'document', budget: 64 },    // HTML 64KB
        { resourceType: 'other', budget: 128 }       // 其他 128KB
      ],
      resourceCounts: [
        { resourceType: 'total', budget: 50 },       // 总请求数 50
        { resourceType: 'script', budget: 10 },      // JavaScript文件 10个
        { resourceType: 'stylesheet', budget: 5 },   // CSS文件 5个
        { resourceType: 'image', budget: 20 },       // 图片 20个
        { resourceType: 'font', budget: 5 },         // 字体 5个
        { resourceType: 'document', budget: 3 },     // HTML文档 3个
        { resourceType: 'third-party', budget: 10 }  // 第三方资源 10个
      ],
      timings: [
        { metric: 'first-contentful-paint', budget: 1800 },      // FCP 1.8s
        { metric: 'largest-contentful-paint', budget: 2500 },    // LCP 2.5s
        { metric: 'first-meaningful-paint', budget: 2000 },      // FMP 2s
        { metric: 'speed-index', budget: 3000 },                 // SI 3s
        { metric: 'interactive', budget: 3800 },                 // TTI 3.8s
        { metric: 'first-cpu-idle', budget: 3500 },              // FCI 3.5s
        { metric: 'max-potential-fid', budget: 100 }             // Max FID 100ms
      ]
    }]
  },
  
  // 自定义审计
  audits: [
    // 添加自定义性能审计
    'custom-audits/resource-compression',
    'custom-audits/cache-headers',
    'custom-audits/critical-resource-chains'
  ],
  
  // 类别配置
  categories: {
    performance: {
      title: '性能',
      description: '这些检查确保您的页面针对速度进行了优化。',
      auditRefs: [
        // Core Web Vitals
        { id: 'first-contentful-paint', weight: 10, group: 'metrics' },
        { id: 'largest-contentful-paint', weight: 25, group: 'metrics' },
        { id: 'first-meaningful-paint', weight: 10, group: 'metrics' },
        { id: 'speed-index', weight: 10, group: 'metrics' },
        { id: 'interactive', weight: 10, group: 'metrics' },
        { id: 'first-cpu-idle', weight: 10, group: 'metrics' },
        { id: 'max-potential-fid', weight: 10, group: 'metrics' },
        { id: 'cumulative-layout-shift', weight: 15, group: 'metrics' },
        
        // 加载性能
        { id: 'render-blocking-resources', weight: 0, group: 'load-opportunities' },
        { id: 'unused-css-rules', weight: 0, group: 'load-opportunities' },
        { id: 'unused-javascript', weight: 0, group: 'load-opportunities' },
        { id: 'modern-image-formats', weight: 0, group: 'load-opportunities' },
        { id: 'offscreen-images', weight: 0, group: 'load-opportunities' },
        { id: 'unminified-css', weight: 0, group: 'load-opportunities' },
        { id: 'unminified-javascript', weight: 0, group: 'load-opportunities' },
        { id: 'efficient-animated-content', weight: 0, group: 'load-opportunities' },
        { id: 'duplicated-javascript', weight: 0, group: 'load-opportunities' },
        { id: 'legacy-javascript', weight: 0, group: 'load-opportunities' },
        
        // 诊断
        { id: 'total-byte-weight', weight: 0, group: 'diagnostics' },
        { id: 'uses-long-cache-ttl', weight: 0, group: 'diagnostics' },
        { id: 'dom-size', weight: 0, group: 'diagnostics' },
        { id: 'critical-request-chains', weight: 0, group: 'diagnostics' },
        { id: 'user-timings', weight: 0, group: 'diagnostics' },
        { id: 'bootup-time', weight: 0, group: 'diagnostics' },
        { id: 'mainthread-work-breakdown', weight: 0, group: 'diagnostics' },
        { id: 'font-display', weight: 0, group: 'diagnostics' },
        { id: 'performance-budget', weight: 0, group: 'budgets' },
        { id: 'timing-budget', weight: 0, group: 'budgets' },
        { id: 'resource-summary', weight: 0, group: 'diagnostics' },
        { id: 'third-party-summary', weight: 0, group: 'diagnostics' },
        { id: 'third-party-facades', weight: 0, group: 'diagnostics' },
        { id: 'lcp-lazy-loaded', weight: 0, group: 'diagnostics' },
        { id: 'layout-shift-elements', weight: 0, group: 'diagnostics' },
        { id: 'uses-passive-event-listeners', weight: 0, group: 'diagnostics' },
        { id: 'no-document-write', weight: 0, group: 'diagnostics' },
        { id: 'long-tasks', weight: 0, group: 'diagnostics' },
        { id: 'non-composited-animations', weight: 0, group: 'diagnostics' },
        { id: 'unsized-images', weight: 0, group: 'diagnostics' },
        { id: 'preload-lcp-image', weight: 0, group: 'diagnostics' },
        { id: 'full-page-screenshot', weight: 0, group: 'diagnostics' }
      ]
    },
    
    accessibility: {
      title: '无障碍访问',
      description: '这些检查突出显示了改善网页可访问性的机会。',
      manualDescription: '这些项目解决了自动化测试无法涵盖的区域。了解更多信息请访问我们的无障碍访问指南。'
    },
    
    'best-practices': {
      title: '最佳实践',
      description: '我们已经汇编了一些建议，以便您的页面遵循现代Web开发最佳实践。'
    },
    
    seo: {
      title: 'SEO',
      description: '这些检查确保您的页面针对搜索引擎结果排名进行了优化。'
    },
    
    pwa: {
      title: 'PWA',
      description: '这些检查验证了Progressive Web App的各个方面。'
    }
  },
  
  // 组配置
  groups: {
    'metrics': {
      title: '指标'
    },
    'load-opportunities': {
      title: '优化机会',
      description: '这些建议可以加快您的页面加载速度。它们不会直接影响性能分数。'
    },
    'budgets': {
      title: '预算',
      description: '性能预算设置了资源大小和数量的标准，以帮助保持性能。'
    },
    'diagnostics': {
      title: '诊断',
      description: '有关应用程序性能的更多信息。这些不会直接影响性能分数。'
    }
  }
};
