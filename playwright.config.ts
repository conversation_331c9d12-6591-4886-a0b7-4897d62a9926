import { defineConfig, devices } from '@playwright/test';

/**
 * Playwright配置文件 - 针对Axum项目前端UI交互测试优化
 * 遵循Context7 MCP最佳实践，支持多分辨率响应式设计测试
 */
export default defineConfig({
  // 测试目录配置
  testDir: './tests',
  
  // 全局测试超时设置（30秒）
  timeout: 30 * 1000,
  
  // 断言超时设置（5秒）
  expect: {
    timeout: 5000,
    // 截图比较容差配置
    toMatchSnapshot: {
      maxDiffPixels: 100,
      threshold: 0.2,
    },
  },
  
  // 测试执行配置
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  
  // 报告配置
  reporter: [
    ['html', { outputFolder: 'tests/ui/reports/html' }],
    ['json', { outputFile: 'tests/ui/reports/results.json' }],
    ['junit', { outputFile: 'tests/ui/reports/results.xml' }],
    ['line'],
  ],
  
  // 全局配置选项
  use: {
    // 基础URL配置
    baseURL: 'http://127.0.0.1:3000',
    
    // 浏览器配置
    headless: process.env.CI ? true : false,
    
    // 视口配置 - 默认桌面分辨率
    viewport: { width: 1280, height: 720 },
    
    // 忽略HTTPS错误
    ignoreHTTPSErrors: true,
    
    // 截图配置
    screenshot: 'only-on-failure',
    
    // 视频录制配置
    video: 'retain-on-failure',
    
    // 追踪配置
    trace: 'retain-on-failure',
    
    // 自动下载配置
    acceptDownloads: true,
    
    // 额外HTTP头配置
    extraHTTPHeaders: {
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    },
  },

  // 项目配置 - 支持多浏览器和多分辨率测试
  projects: [
    // 桌面浏览器测试
    {
      name: 'chromium-desktop',
      use: { 
        ...devices['Desktop Chrome'],
        viewport: { width: 1920, height: 1080 },
      },
    },
    {
      name: 'firefox-desktop',
      use: { 
        ...devices['Desktop Firefox'],
        viewport: { width: 1920, height: 1080 },
      },
    },
    {
      name: 'webkit-desktop',
      use: { 
        ...devices['Desktop Safari'],
        viewport: { width: 1920, height: 1080 },
      },
    },
    
    // 平板设备测试
    {
      name: 'tablet-chrome',
      use: { 
        ...devices['Desktop Chrome'],
        viewport: { width: 1024, height: 768 },
      },
    },
    
    // 移动设备测试
    {
      name: 'mobile-chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'mobile-safari',
      use: { ...devices['iPhone 12'] },
    },
  ],

  // 输出目录配置
  outputDir: 'tests/ui/test-results/',
  
  // Web服务器配置 - 自动启动Axum服务器 (已禁用，手动启动)
  // webServer: {
  //   command: 'cd server && cargo run',
  //   url: 'http://127.0.0.1:3000',
  //   reuseExistingServer: !process.env.CI,
  //   timeout: 120 * 1000, // 2分钟启动超时
  //   stdout: 'pipe',
  //   stderr: 'pipe',
  // },
});
