# 任务管理CRUD测试配置报告

生成时间: 2025-07-13 05:50:31 UTC

## 环境变量配置

- SERVER_HOST: 127.0.0.1
- SERVER_PORT: 3000
- BASE_URL: http://127.0.0.1:3000
- TEST_USERNAME: testuser456
- DATABASE_URL: sqlite:./task_manager.db
- JWT_SECRET: ***

## API端点配置

- API_AUTH_LOGIN: /api/auth/login
- API_AUTH_REGISTER: /api/auth/register
- API_TASKS_BASE: /api/tasks
- API_TASKS_CREATE: /api/tasks
- API_TASKS_LIST: /api/tasks
- API_TASKS_GET: /api/tasks/{id}

## 测试配置

- CRUD_TEST_TIMEOUT: 45
- CRUD_TEST_RETRY_COUNT: 3
- CONCURRENT_USERS: 3
- TEST_TASK_COUNT: 5
- PLAYWRIGHT_HEADLESS: false
