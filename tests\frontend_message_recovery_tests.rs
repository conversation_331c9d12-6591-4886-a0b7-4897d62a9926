//! # 前端消息恢复集成测试
//!
//! 测试前端页面刷新后自动加载历史消息的功能
//! 使用MCP Playwright进行端到端测试
//! 验证WebSocket连接后的消息恢复机制

use anyhow::Result;
use futures_util::{SinkExt, StreamExt};
use reqwest::Client;
use serde_json::{Value, json};
use std::time::Duration;
use tokio::time::{sleep, timeout};
use tokio_tungstenite::{connect_async, tungstenite::Message as TungsteniteMessage};
use uuid::Uuid;

/// 前端消息恢复测试配置
const SERVER_URL: &str = "127.0.0.1:3000";
const WS_URL: &str = "ws://127.0.0.1:3000/ws";
const API_BASE_URL: &str = "http://127.0.0.1:3000/api";
const TEST_TIMEOUT: Duration = Duration::from_secs(30);

/// 测试用户凭据
const TEST_USER_USERNAME: &str = "testuser456";
const TEST_USER_EMAIL: &str = "<EMAIL>";
const TEST_USER_PASSWORD: &str = "password123";

/// 前端消息恢复测试辅助结构
struct FrontendRecoveryTestHelper {
    /// HTTP客户端
    client: Client,
    /// JWT认证令牌
    jwt_token: Option<String>,
    /// 测试用户ID
    user_id: Option<Uuid>,
}

impl FrontendRecoveryTestHelper {
    /// 创建新的测试辅助实例
    fn new() -> Self {
        Self {
            client: Client::new(),
            jwt_token: None,
            user_id: None,
        }
    }

    /// 用户登录获取JWT令牌
    async fn login(&mut self) -> Result<String> {
        println!("🔐 开始用户登录流程");

        let login_url = format!("http://{}/api/auth/login", SERVER_URL);
        let login_payload = json!({
            "username": TEST_USER_USERNAME,
            "password": TEST_USER_PASSWORD
        });

        let response = timeout(
            TEST_TIMEOUT,
            self.client.post(&login_url).json(&login_payload).send(),
        )
        .await??;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("登录失败: {}", error_text));
        }

        let login_result: Value = response.json().await?;

        let token = login_result["data"]["token"]
            .as_str()
            .ok_or_else(|| anyhow::anyhow!("登录响应中未找到token"))?
            .to_string();

        let user_id_str = login_result["data"]["user"]["id"]
            .as_str()
            .ok_or_else(|| anyhow::anyhow!("登录响应中未找到用户ID"))?;

        let user_id = Uuid::parse_str(user_id_str)?;

        self.jwt_token = Some(token.clone());
        self.user_id = Some(user_id);

        println!("✅ 登录成功，用户ID: {}", user_id);
        Ok(token)
    }

    /// 建立WebSocket连接
    async fn connect_websocket(
        &self,
    ) -> Result<
        tokio_tungstenite::WebSocketStream<
            tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>,
        >,
    > {
        let token = self
            .jwt_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("未找到JWT令牌，请先登录"))?;

        let ws_url_with_token = format!("{}?token={}", WS_URL, token);
        println!("🔌 连接WebSocket: {}", ws_url_with_token);

        let (ws_stream, _) = timeout(TEST_TIMEOUT, connect_async(&ws_url_with_token)).await??;
        println!("✅ WebSocket连接建立成功");

        Ok(ws_stream)
    }

    /// 通过WebSocket发送消息
    async fn send_websocket_message(
        &self,
        ws_stream: &mut tokio_tungstenite::WebSocketStream<
            tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>,
        >,
        content: &str,
    ) -> Result<()> {
        let message = json!({
            "message_type": "Text",
            "content": content,
            "sender": {
                "username": TEST_USER_USERNAME,
                "user_id": self.user_id.unwrap().to_string()
            },
            "timestamp": chrono::Utc::now().to_rfc3339()
        });

        let message_text = serde_json::to_string(&message)?;
        ws_stream
            .send(TungsteniteMessage::Text(message_text.into()))
            .await?;

        println!("📤 WebSocket消息发送成功: {}", content);
        Ok(())
    }

    /// 获取聊天历史消息
    async fn get_chat_history(&self, limit: Option<i64>) -> Result<Vec<Value>> {
        let token = self
            .jwt_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("未找到JWT令牌"))?;

        // 使用固定的全局聊天室UUID
        let global_room_id = "00000000-0000-0000-0000-000000000000";
        let mut url = format!("{}/messages/chat-room/{}", API_BASE_URL, global_room_id);

        if let Some(limit_val) = limit {
            url.push_str(&format!("?limit={}", limit_val));
        }

        println!("🔍 获取聊天历史: {}", url);

        let response = timeout(
            TEST_TIMEOUT,
            self.client
                .get(&url)
                .header("Authorization", format!("Bearer {}", token))
                .send(),
        )
        .await??;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow::anyhow!("获取聊天历史失败: {}", error_text));
        }

        let result: Value = response.json().await?;

        let messages = result["data"]["messages"]
            .as_array()
            .ok_or_else(|| anyhow::anyhow!("响应中未找到messages数组"))?
            .clone();

        println!("✅ 获取到 {} 条历史消息", messages.len());
        Ok(messages)
    }

    /// 验证消息是否存在于历史记录中
    async fn verify_message_in_history(&self, expected_content: &str) -> Result<bool> {
        let history = self.get_chat_history(Some(50)).await?;

        for message in &history {
            if let Some(content) = message["content"].as_str() {
                if content == expected_content {
                    println!("✅ 消息在历史记录中找到: {}", content);
                    return Ok(true);
                }
            }
        }

        println!("❌ 消息在历史记录中未找到: {}", expected_content);
        Ok(false)
    }

    /// 模拟页面刷新后的消息恢复流程
    async fn simulate_page_refresh_recovery(&mut self) -> Result<Vec<Value>> {
        println!("🔄 模拟页面刷新后的消息恢复流程");

        // 步骤1: 重新登录（模拟页面刷新后从localStorage恢复认证状态）
        self.login().await?;

        // 步骤2: 建立WebSocket连接
        let mut ws_stream = self.connect_websocket().await?;

        // 步骤3: WebSocket连接成功后自动加载历史消息
        println!("📥 WebSocket连接成功，开始自动加载历史消息");
        let history = self.get_chat_history(Some(50)).await?;

        // 步骤4: 关闭WebSocket连接
        ws_stream.close(None).await?;

        println!("✅ 页面刷新恢复流程完成，恢复了 {} 条消息", history.len());
        Ok(history)
    }
}

/// 测试1: 基本消息恢复功能
///
/// 【功能】: 验证页面刷新后能够自动加载历史消息
/// 【测试内容】:
/// - 发送消息到WebSocket
/// - 模拟页面刷新
/// - 验证历史消息能够恢复
#[tokio::test]
async fn test_basic_message_recovery() -> Result<()> {
    println!("🔧 开始测试: 基本消息恢复功能");

    let mut helper = FrontendRecoveryTestHelper::new();

    // 步骤1: 登录并建立WebSocket连接
    helper.login().await?;
    let mut ws_stream = helper.connect_websocket().await?;

    // 步骤2: 发送测试消息
    let test_message = "这是一条用于测试消息恢复的消息";
    helper
        .send_websocket_message(&mut ws_stream, test_message)
        .await?;

    // 等待消息处理和持久化
    sleep(Duration::from_millis(500)).await;

    // 步骤3: 关闭WebSocket连接（模拟页面关闭）
    ws_stream.close(None).await?;
    println!("🔌 WebSocket连接已关闭，模拟页面关闭");

    // 步骤4: 模拟页面刷新后的恢复流程
    let recovered_messages = helper.simulate_page_refresh_recovery().await?;

    // 步骤5: 验证消息是否恢复
    let found = helper.verify_message_in_history(test_message).await?;
    assert!(found, "测试消息应该在历史记录中找到");
    assert!(!recovered_messages.is_empty(), "应该恢复到历史消息");

    println!("🎉 基本消息恢复功能测试通过");
    Ok(())
}

/// 测试2: 多条消息恢复测试
///
/// 【功能】: 验证多条消息的恢复功能
/// 【测试内容】:
/// - 发送多条消息
/// - 模拟页面刷新
/// - 验证所有消息都能恢复
#[tokio::test]
async fn test_multiple_messages_recovery() -> Result<()> {
    println!("🔧 开始测试: 多条消息恢复");

    let mut helper = FrontendRecoveryTestHelper::new();

    // 步骤1: 登录并建立WebSocket连接
    helper.login().await?;
    let mut ws_stream = helper.connect_websocket().await?;

    // 步骤2: 发送多条测试消息
    let test_messages = vec![
        "恢复测试消息 #1",
        "恢复测试消息 #2",
        "恢复测试消息 #3",
        "恢复测试消息 #4",
        "恢复测试消息 #5",
    ];

    for message in &test_messages {
        helper
            .send_websocket_message(&mut ws_stream, message)
            .await?;
        sleep(Duration::from_millis(100)).await; // 确保消息顺序
    }

    // 等待所有消息处理完成
    sleep(Duration::from_millis(1000)).await;

    // 步骤3: 关闭连接并模拟页面刷新
    ws_stream.close(None).await?;
    let recovered_messages = helper.simulate_page_refresh_recovery().await?;

    // 步骤4: 验证所有消息都能恢复
    for expected_message in &test_messages {
        let found = helper.verify_message_in_history(expected_message).await?;
        assert!(found, "消息 '{}' 应该在历史记录中找到", expected_message);
    }

    assert!(
        recovered_messages.len() >= test_messages.len(),
        "恢复的消息数量应该至少包含发送的消息"
    );

    println!("🎉 多条消息恢复测试通过");
    Ok(())
}
