//! # WebSocket功能修复测试
//!
//! 测试WebSocket消息重复发送、重连机制异常和心跳响应问题的修复

use futures_util::{SinkExt, StreamExt};
use serde_json::json;
use std::time::Duration;
use tokio::time::timeout;
use tokio_tungstenite::{connect_async, tungstenite::Message};

/// 测试WebSocket消息不重复发送
#[tokio::test]
async fn test_websocket_message_no_duplication() {
    // 启动测试服务器（假设已经在运行）
    let ws_url = "ws://127.0.0.1:3000/websocket?token=test_token";

    // 连接WebSocket
    let (ws_stream, _) = connect_async(ws_url).await.expect("连接WebSocket失败");
    let (mut write, mut read) = ws_stream.split();

    // 发送测试消息
    let test_message = json!({
        "message_type": "Text",
        "content": "测试消息不重复",
        "sender": {
            "username": "test_user",
            "user_id": "test_id"
        },
        "timestamp": "2025-07-17T10:00:00Z"
    });

    write
        .send(Message::Text(test_message.to_string().into()))
        .await
        .expect("发送消息失败");

    // 等待接收消息，确保只收到一条
    let mut message_count = 0;
    let timeout_duration = Duration::from_secs(3);

    while let Ok(Some(msg)) = timeout(timeout_duration, read.next()).await {
        if let Ok(Message::Text(text)) = msg {
            if text.contains("测试消息不重复") {
                message_count += 1;
            }
        }

        // 如果收到超过1条相同消息，说明有重复发送问题
        if message_count > 1 {
            panic!("检测到消息重复发送，收到{}条相同消息", message_count);
        }
    }

    // 验证至少收到一条消息
    assert_eq!(message_count, 1, "应该收到恰好一条消息");
}

/// 测试WebSocket重连机制稳定性
#[tokio::test]
async fn test_websocket_reconnection_stability() {
    let ws_url = "ws://127.0.0.1:3000/websocket?token=test_token";

    // 第一次连接
    let (ws_stream, _) = connect_async(ws_url).await.expect("首次连接WebSocket失败");
    let (mut write, mut read) = ws_stream.split();

    // 发送一条消息确认连接正常
    let ping_message = json!({
        "message_type": "Ping",
        "content": "ping",
        "timestamp": "2025-07-17T10:00:00Z"
    });

    write
        .send(Message::Text(ping_message.to_string().into()))
        .await
        .expect("发送ping失败");

    // 主动关闭连接
    write.close().await.expect("关闭连接失败");

    // 等待连接完全关闭
    tokio::time::sleep(Duration::from_secs(1)).await;

    // 重新连接
    let (ws_stream2, _) = connect_async(ws_url).await.expect("重连WebSocket失败");
    let (mut write2, mut read2) = ws_stream2.split();

    // 发送测试消息
    let test_message = json!({
        "message_type": "Text",
        "content": "重连测试消息",
        "sender": {
            "username": "test_user",
            "user_id": "test_id"
        },
        "timestamp": "2025-07-17T10:00:00Z"
    });

    write2
        .send(Message::Text(test_message.to_string().into()))
        .await
        .expect("重连后发送消息失败");

    // 验证重连后连接保持稳定至少5秒
    let stability_duration = Duration::from_secs(5);
    let start_time = std::time::Instant::now();
    let mut connection_stable = true;

    while start_time.elapsed() < stability_duration {
        match timeout(Duration::from_millis(100), read2.next()).await {
            Ok(Some(Ok(_))) => {
                // 收到消息，连接正常
            }
            Ok(Some(Err(_))) => {
                // 连接错误
                connection_stable = false;
                break;
            }
            Ok(None) => {
                // 连接关闭
                connection_stable = false;
                break;
            }
            Err(_) => {
                // 超时，继续检查
            }
        }

        tokio::time::sleep(Duration::from_millis(100)).await;
    }

    assert!(connection_stable, "重连后连接应该保持稳定至少5秒");
}

/// 测试心跳机制优化
#[tokio::test]
async fn test_websocket_heartbeat_optimization() {
    let ws_url = "ws://127.0.0.1:3000/websocket?token=test_token";

    // 连接WebSocket
    let (ws_stream, _) = connect_async(ws_url).await.expect("连接WebSocket失败");
    let (mut write, mut read) = ws_stream.split();

    // 记录开始时间
    let start_time = std::time::Instant::now();
    let mut ping_count = 0;
    let mut pong_count = 0;

    // 监听30秒内的心跳消息
    let monitor_duration = Duration::from_secs(30);

    while start_time.elapsed() < monitor_duration {
        match timeout(Duration::from_secs(1), read.next()).await {
            Ok(Some(Ok(Message::Ping(_)))) => {
                ping_count += 1;
                // 自动回复Pong
                write.send(Message::Pong(vec![].into())).await.ok();
            }
            Ok(Some(Ok(Message::Pong(_)))) => {
                pong_count += 1;
            }
            Ok(Some(Ok(Message::Text(text)))) => {
                // 检查是否是心跳相关的文本消息
                if text.contains("ping") || text.contains("pong") {
                    // 这里可以添加更详细的心跳消息分析
                }
            }
            _ => {
                // 其他消息或超时，继续监听
            }
        }
    }

    // 验证心跳频率合理（30秒内不应该有太多心跳）
    // 修复后心跳间隔为60秒，所以30秒内最多应该有1次心跳
    assert!(ping_count <= 1, "30秒内心跳次数过多: {}", ping_count);

    println!(
        "30秒内收到Ping消息: {}次, Pong消息: {}次",
        ping_count, pong_count
    );
}
