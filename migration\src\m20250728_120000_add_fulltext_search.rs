//! 添加PostgreSQL全文搜索支持的迁移
//!
//! 这个迁移为消息表添加全文搜索功能：
//! 1. 添加search_vector字段用于存储tsvector
//! 2. 创建GIN索引优化全文搜索性能
//! 3. 创建触发器自动更新search_vector
//! 4. 支持中文分词和权重排序

use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        // 1. 添加search_vector字段到messages表
        manager
            .alter_table(
                Table::alter()
                    .table(Messages::Table)
                    .add_column(
                        ColumnDef::new(Messages::SearchVector)
                            .custom(Alias::new("TSVECTOR"))
                            .null(),
                    )
                    .to_owned(),
            )
            .await?;

        // 2. 创建GIN索引用于全文搜索
        let create_gin_index_sql = r#"
            CREATE INDEX IF NOT EXISTS idx_messages_search_vector 
            ON messages USING GIN(search_vector);
        "#;

        manager
            .get_connection()
            .execute_unprepared(create_gin_index_sql)
            .await?;

        // 3. 创建触发器函数自动更新search_vector
        let create_trigger_function_sql = r#"
            CREATE OR REPLACE FUNCTION update_message_search_vector()
            RETURNS TRIGGER AS $$
            BEGIN
                -- 使用默认英文分词配置，为content设置A权重，为metadata设置B权重
                NEW.search_vector :=
                    setweight(to_tsvector('english', COALESCE(NEW.content, '')), 'A') ||
                    setweight(to_tsvector('english', COALESCE(NEW.metadata, '')), 'B');
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
        "#;

        manager
            .get_connection()
            .execute_unprepared(create_trigger_function_sql)
            .await?;

        // 4. 创建触发器
        let create_trigger_sql = r#"
            DROP TRIGGER IF EXISTS trigger_update_message_search_vector ON messages;
            CREATE TRIGGER trigger_update_message_search_vector
                BEFORE INSERT OR UPDATE ON messages
                FOR EACH ROW EXECUTE FUNCTION update_message_search_vector();
        "#;

        manager
            .get_connection()
            .execute_unprepared(create_trigger_sql)
            .await?;

        // 5. 为现有消息更新search_vector
        let update_existing_sql = r#"
            UPDATE messages
            SET search_vector =
                setweight(to_tsvector('english', COALESCE(content, '')), 'A') ||
                setweight(to_tsvector('english', COALESCE(metadata, '')), 'B')
            WHERE search_vector IS NULL;
        "#;

        manager
            .get_connection()
            .execute_unprepared(update_existing_sql)
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        // 删除触发器
        let drop_trigger_sql = r#"
            DROP TRIGGER IF EXISTS trigger_update_message_search_vector ON messages;
        "#;

        manager
            .get_connection()
            .execute_unprepared(drop_trigger_sql)
            .await?;

        // 删除触发器函数
        let drop_function_sql = r#"
            DROP FUNCTION IF EXISTS update_message_search_vector();
        "#;

        manager
            .get_connection()
            .execute_unprepared(drop_function_sql)
            .await?;

        // 删除GIN索引
        let drop_index_sql = r#"
            DROP INDEX IF EXISTS idx_messages_search_vector;
        "#;

        manager
            .get_connection()
            .execute_unprepared(drop_index_sql)
            .await?;

        // 删除search_vector字段
        manager
            .alter_table(
                Table::alter()
                    .table(Messages::Table)
                    .drop_column(Messages::SearchVector)
                    .to_owned(),
            )
            .await?;

        Ok(())
    }
}

/// 消息表的列定义枚举
#[derive(DeriveIden)]
#[allow(dead_code)]
enum Messages {
    Table,
    Id,
    Content,
    MessageType,
    Status,
    SenderId,
    ChatRoomId,
    ReplyToId,
    Metadata,
    Priority,
    IsPinned,
    ExpiresAt,
    CreatedAt,
    UpdatedAt,
    SearchVector, // 新增的全文搜索向量字段
}
