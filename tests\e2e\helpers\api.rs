// API相关的E2E测试辅助函数

use super::E2EConfig;
use anyhow::Result;
use serde_json::{Value, json};

/// API辅助结构
pub struct ApiHelper {
    config: E2EConfig,
    client: reqwest::Client,
}

impl ApiHelper {
    /// 创建新的API辅助实例
    pub fn new(config: E2EConfig) -> Self {
        Self {
            config,
            client: reqwest::Client::new(),
        }
    }

    /// 创建带认证的API辅助实例
    pub fn with_auth(config: E2EConfig, token: &str) -> Self {
        let mut headers = reqwest::header::HeaderMap::new();
        headers.insert(
            reqwest::header::AUTHORIZATION,
            reqwest::header::HeaderValue::from_str(&format!("Bearer {}", token)).unwrap(),
        );

        let client = reqwest::Client::builder()
            .default_headers(headers)
            .build()
            .unwrap();

        Self { config, client }
    }

    /// 创建任务
    pub async fn create_task(
        &self,
        title: &str,
        description: &str,
        priority: &str,
    ) -> Result<Value> {
        let url = format!("{}/api/tasks", self.config.base_url);
        let payload = json!({
            "title": title,
            "description": description,
            "priority": priority
        });

        let response = self.client.post(&url).json(&payload).send().await?;

        let status = response.status();
        let body: Value = response.json().await?;

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 获取任务列表
    pub async fn get_tasks(&self) -> Result<Value> {
        let url = format!("{}/api/tasks", self.config.base_url);

        let response = self.client.get(&url).send().await?;

        let status = response.status();
        let body: Value = response.json().await?;

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 获取单个任务
    pub async fn get_task(&self, task_id: &str) -> Result<Value> {
        let url = format!("{}/api/tasks/{}", self.config.base_url, task_id);

        let response = self.client.get(&url).send().await?;

        let status = response.status();
        let body: Value = response.json().await?;

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 更新任务
    pub async fn update_task(&self, task_id: &str, updates: Value) -> Result<Value> {
        let url = format!("{}/api/tasks/{}", self.config.base_url, task_id);

        let response = self.client.put(&url).json(&updates).send().await?;

        let status = response.status();
        let body: Value = response.json().await?;

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 删除任务
    pub async fn delete_task(&self, task_id: &str) -> Result<Value> {
        let url = format!("{}/api/tasks/{}", self.config.base_url, task_id);

        let response = self.client.delete(&url).send().await?;

        let status = response.status();

        // DELETE请求可能返回空响应
        let body = if response.content_length().unwrap_or(0) > 0 {
            response.json().await?
        } else {
            json!({})
        };

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 健康检查
    pub async fn health_check(&self) -> Result<Value> {
        let url = format!("{}/health", self.config.base_url);

        let response = self.client.get(&url).send().await?;

        let status = response.status();
        let body: Value = response.json().await?;

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 获取API版本信息
    pub async fn get_version(&self) -> Result<Value> {
        let url = format!("{}/api/version", self.config.base_url);

        let response = self.client.get(&url).send().await?;

        let status = response.status();
        let body: Value = response.json().await?;

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 使用认证Token获取任务列表
    pub async fn get_tasks_with_auth(&self, token: &str) -> Result<Value> {
        let url = format!("{}/api/tasks", self.config.base_url);

        let response = self.client.get(&url).bearer_auth(token).send().await?;

        let status = response.status();
        let body: Value = response.json().await?;

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 不使用认证Token获取任务列表（测试认证中间件）
    pub async fn get_tasks_without_auth(&self) -> Result<Value> {
        let url = format!("{}/api/tasks", self.config.base_url);

        let response = self.client.get(&url).send().await?;

        let status = response.status();

        // 尝试解析JSON，如果失败则使用文本内容
        let body = match response.json::<Value>().await {
            Ok(json_body) => json_body,
            Err(_) => {
                // 如果JSON解析失败，创建一个包含错误信息的JSON对象
                json!({
                    "error": "response_parse_error",
                    "message": "无法解析响应为JSON"
                })
            }
        };

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 使用认证Token创建任务
    pub async fn create_task_with_auth(
        &self,
        token: &str,
        title: &str,
        description: &str,
        priority: &str,
    ) -> Result<Value> {
        let url = format!("{}/api/tasks", self.config.base_url);
        let payload = json!({
            "title": title,
            "description": description,
            "priority": priority
        });

        let response = self
            .client
            .post(&url)
            .bearer_auth(token)
            .json(&payload)
            .send()
            .await?;

        let status = response.status();
        let body: Value = response.json().await?;

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 不使用认证Token创建任务（测试认证中间件）
    pub async fn create_task_without_auth(
        &self,
        title: &str,
        description: &str,
        priority: &str,
    ) -> Result<Value> {
        let url = format!("{}/api/tasks", self.config.base_url);
        let payload = json!({
            "title": title,
            "description": description,
            "priority": priority
        });

        let response = self.client.post(&url).json(&payload).send().await?;

        let status = response.status();

        // 尝试解析JSON，如果失败则使用错误信息
        let body = match response.json::<Value>().await {
            Ok(json_body) => json_body,
            Err(_) => {
                json!({
                    "error": "response_parse_error",
                    "message": "无法解析响应为JSON"
                })
            }
        };

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 使用无效Token访问受保护端点
    pub async fn get_tasks_with_invalid_token(&self, invalid_token: &str) -> Result<Value> {
        let url = format!("{}/api/tasks", self.config.base_url);

        let response = self
            .client
            .get(&url)
            .bearer_auth(invalid_token)
            .send()
            .await?;

        let status = response.status();

        // 尝试解析JSON，如果失败则使用错误信息
        let body = match response.json::<Value>().await {
            Ok(json_body) => json_body,
            Err(_) => {
                json!({
                    "error": "response_parse_error",
                    "message": "无法解析响应为JSON"
                })
            }
        };

        Ok(json!({
            "status": status.as_u16(),
            "body": body
        }))
    }

    /// 测试API响应时间
    pub async fn measure_response_time<F, Fut>(&self, api_call: F) -> Result<(Value, u128)>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = Result<Value>>,
    {
        let start = std::time::Instant::now();
        let result = api_call().await?;
        let duration = start.elapsed().as_millis();

        Ok((result, duration))
    }
}
