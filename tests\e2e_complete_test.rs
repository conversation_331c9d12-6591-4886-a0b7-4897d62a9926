//! # 完整的E2E测试
//!
//! 测试Axum服务器的所有主要功能，包括：
//! - 健康检查端点
//! - 用户认证（注册和登录）
//! - 任务管理CRUD操作
//! - API响应验证
//! - 错误处理

use serde_json::{Value, json};
use std::collections::HashMap;
use tokio;

/// 测试配置
struct TestConfig {
    base_url: String,
    test_user: TestUser,
}

/// 测试用户信息
#[derive(Clone)]
struct TestUser {
    username: String,
    email: String,
    password: String,
}

impl Default for TestConfig {
    fn default() -> Self {
        // 使用时间戳创建唯一用户名
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();
        let unique_username = format!("testuser{}", timestamp);

        Self {
            base_url: "http://127.0.0.1:3000".to_string(),
            test_user: TestUser {
                username: unique_username.clone(),
                email: format!("{}@example.com", unique_username),
                password: "password123".to_string(),
            },
        }
    }
}

/// HTTP客户端封装
struct TestClient {
    client: reqwest::Client,
    config: TestConfig,
    auth_token: Option<String>,
}

impl TestClient {
    fn new() -> Self {
        Self {
            client: reqwest::Client::new(),
            config: TestConfig::default(),
            auth_token: None,
        }
    }

    /// 发送GET请求
    async fn get(&self, path: &str) -> Result<reqwest::Response, reqwest::Error> {
        let url = format!("{}{}", self.config.base_url, path);
        let mut request = self.client.get(&url);

        if let Some(token) = &self.auth_token {
            request = request.header("Authorization", format!("Bearer {}", token));
        }

        request.send().await
    }

    /// 发送POST请求
    async fn post(&self, path: &str, body: Value) -> Result<reqwest::Response, reqwest::Error> {
        let url = format!("{}{}", self.config.base_url, path);
        let mut request = self.client.post(&url).json(&body);

        if let Some(token) = &self.auth_token {
            request = request.header("Authorization", format!("Bearer {}", token));
        }

        request.send().await
    }

    /// 设置认证令牌
    fn set_auth_token(&mut self, token: String) {
        self.auth_token = Some(token);
    }
}

/// 测试健康检查端点
async fn test_health_endpoints(client: &TestClient) -> Result<(), Box<dyn std::error::Error>> {
    println!("🔍 测试健康检查端点...");

    // 测试基础健康检查
    let response = client.get("/api/health").await?;
    assert_eq!(response.status(), 200, "健康检查端点应该返回200");

    let health_data: Value = response.json().await?;
    assert_eq!(health_data["status"], "healthy", "健康状态应该是healthy");
    assert!(health_data["timestamp"].is_string(), "应该包含时间戳");
    assert_eq!(health_data["version"], "1.0.0", "版本应该是1.0.0");

    println!("✅ 基础健康检查通过");

    // 测试性能健康检查
    let response = client.get("/api/performance/health").await?;
    assert_eq!(response.status(), 200, "性能健康检查端点应该返回200");

    println!("✅ 性能健康检查通过");

    // 测试深度健康检查
    let response = client.get("/api/health/deep").await?;
    assert_eq!(response.status(), 200, "深度健康检查端点应该返回200");

    println!("✅ 深度健康检查通过");

    Ok(())
}

/// 测试用户认证功能
async fn test_authentication(client: &mut TestClient) -> Result<(), Box<dyn std::error::Error>> {
    println!("🔍 测试用户认证功能...");

    // 测试用户注册
    let register_data = json!({
        "username": client.config.test_user.username,
        "password": client.config.test_user.password,
        "confirm_password": client.config.test_user.password
    });

    let response = client.post("/api/auth/register", register_data).await?;

    // 检查注册结果
    let status = response.status();
    if status == 200 || status == 201 || status == 409 {
        // 注册成功或用户已存在，都是可接受的结果
        println!("✅ 注册状态: {}", status);
    } else {
        let error_text = response.text().await?;
        panic!("注册失败，状态码: {}, 错误信息: {}", status, error_text);
    }

    println!("✅ 用户注册测试通过");

    // 测试用户登录
    let login_data = json!({
        "username": client.config.test_user.username,
        "password": client.config.test_user.password
    });

    let response = client.post("/api/auth/login", login_data).await?;
    assert_eq!(response.status(), 200, "登录应该返回200");

    let login_response: Value = response.json().await?;
    println!(
        "登录响应: {}",
        serde_json::to_string_pretty(&login_response)?
    );

    // 检查响应结构，token在data.access_token字段中
    let token = if let Some(token_str) = login_response["token"].as_str() {
        token_str.to_string()
    } else if let Some(data) = login_response["data"].as_object() {
        if let Some(token_str) = data["access_token"].as_str() {
            token_str.to_string()
        } else {
            panic!(
                "登录响应中找不到access_token字段，响应内容: {}",
                login_response
            );
        }
    } else {
        panic!("登录响应格式不正确，响应内容: {}", login_response);
    };

    // 保存认证令牌
    client.set_auth_token(token);

    println!("✅ 用户登录测试通过");

    Ok(())
}

/// 测试任务管理功能
async fn test_task_management(client: &TestClient) -> Result<(), Box<dyn std::error::Error>> {
    println!("🔍 测试任务管理功能...");

    // 测试获取任务列表
    let response = client.get("/api/tasks").await?;
    assert_eq!(response.status(), 200, "获取任务列表应该返回200");

    let tasks_response: Value = response.json().await?;
    println!(
        "任务列表响应: {}",
        serde_json::to_string_pretty(&tasks_response)?
    );

    // 检查响应格式，任务列表可能在data字段中
    let tasks = if tasks_response.is_array() {
        &tasks_response
    } else if let Some(data) = tasks_response["data"].as_array() {
        tasks_response.get("data").unwrap()
    } else {
        panic!("任务列表响应格式不正确，响应内容: {}", tasks_response);
    };

    assert!(tasks.is_array(), "任务列表应该是数组");

    println!("✅ 获取任务列表测试通过");

    // 测试创建任务
    let new_task = json!({
        "title": "E2E测试任务",
        "description": "这是一个E2E测试创建的任务",
        "priority": "medium"
    });

    let response = client.post("/api/tasks", new_task).await?;
    let status = response.status();

    let task_response: Value = response.json().await?;
    println!(
        "创建任务响应: {}",
        serde_json::to_string_pretty(&task_response)?
    );

    // 检查状态码，可能是200而不是201
    if status != 200 && status != 201 {
        panic!("创建任务失败，状态码: {}, 响应: {}", status, task_response);
    }

    // 检查响应格式，任务数据可能在data字段中
    let created_task = if let Some(data) = task_response["data"].as_object() {
        task_response.get("data").unwrap()
    } else if task_response["id"].is_string() {
        &task_response
    } else {
        panic!("创建任务响应格式不正确，响应内容: {}", task_response);
    };

    assert!(created_task["id"].is_string(), "创建的任务应该有ID");
    assert_eq!(created_task["title"], "E2E测试任务", "任务标题应该匹配");

    let task_id = created_task["id"].as_str().unwrap();
    println!("✅ 创建任务测试通过，任务ID: {}", task_id);

    // 测试获取单个任务
    let response = client.get(&format!("/api/tasks/{}", task_id)).await?;
    assert_eq!(response.status(), 200, "获取单个任务应该返回200");

    let task_response: Value = response.json().await?;
    println!(
        "获取单个任务响应: {}",
        serde_json::to_string_pretty(&task_response)?
    );

    // 检查响应格式，任务数据可能在data字段中
    let task = if let Some(data) = task_response["data"].as_object() {
        task_response.get("data").unwrap()
    } else if task_response["id"].is_string() {
        &task_response
    } else {
        panic!("获取任务响应格式不正确，响应内容: {}", task_response);
    };

    assert_eq!(task["id"], task_id, "任务ID应该匹配");

    println!("✅ 获取单个任务测试通过");

    Ok(())
}

/// 测试错误处理
async fn test_error_handling(client: &TestClient) -> Result<(), Box<dyn std::error::Error>> {
    println!("🔍 测试错误处理...");

    // 测试访问不存在的端点
    let response = client.get("/api/nonexistent").await?;
    assert_eq!(response.status(), 404, "不存在的端点应该返回404");

    println!("✅ 404错误处理测试通过");

    // 测试无效的JSON请求
    let invalid_json = json!({
        "invalid": "data"
    });

    let response = client.post("/api/auth/register", invalid_json).await?;
    assert_eq!(response.status(), 400, "无效请求应该返回400");

    println!("✅ 400错误处理测试通过");

    Ok(())
}

/// 主测试函数
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 开始Axum服务器E2E测试");
    println!("📡 服务器地址: http://127.0.0.1:3000");

    let mut client = TestClient::new();

    // 等待服务器启动
    println!("⏳ 等待服务器启动...");
    tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;

    // 运行所有测试
    test_health_endpoints(&client).await?;
    test_authentication(&mut client).await?;
    test_task_management(&client).await?;
    test_error_handling(&client).await?;

    println!("🎉 所有E2E测试通过！");
    println!("✅ 服务器功能正常，可以进行前端集成");

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_server_health() {
        let client = TestClient::new();
        let result = test_health_endpoints(&client).await;
        assert!(result.is_ok(), "健康检查测试应该通过");
    }

    #[tokio::test]
    async fn test_auth_flow() {
        let mut client = TestClient::new();
        let result = test_authentication(&mut client).await;
        assert!(result.is_ok(), "认证流程测试应该通过");
    }
}
