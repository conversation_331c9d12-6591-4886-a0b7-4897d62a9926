//! # 任务管理CRUD测试配置验证
//!
//! 本模块验证任务管理CRUD测试的配置是否正确
//! 遵循Context7 MCP最佳实践和rust_axum_Rules.md规范：
//! - 使用清晰的函数命名（validate_config、check_dependencies等）
//! - 详细的中文注释
//! - 完整的错误处理
//! - 遵循DRY和SOLID原则

use anyhow::{Context, Result};
use serde_json::Value;
use std::env;
use std::path::Path;

/// 配置验证器
pub struct ConfigValidator;

impl ConfigValidator {
    /// 验证所有配置
    pub fn validate_all_configs() -> Result<()> {
        println!("🔍 开始验证任务管理CRUD测试配置...");

        Self::validate_environment_variables()?;
        Self::validate_file_structure()?;
        Self::validate_dependencies()?;
        Self::validate_test_fixtures()?;
        Self::validate_api_endpoints_config()?;

        println!("✅ 所有配置验证通过");
        Ok(())
    }

    /// 验证环境变量
    fn validate_environment_variables() -> Result<()> {
        println!("🔧 验证环境变量配置...");

        // 加载测试环境配置
        if dotenvy::from_filename("tests/e2e/config/test.env").is_err() {
            return Err(anyhow::anyhow!("无法加载测试环境配置文件"));
        }

        // 必需的环境变量
        let required_vars = [
            "SERVER_HOST",
            "SERVER_PORT",
            "BASE_URL",
            "TEST_USERNAME",
            "TEST_PASSWORD",
            "TEST_EMAIL",
            "DATABASE_URL",
            "JWT_SECRET",
            "API_TASKS_BASE",
            "API_AUTH_LOGIN",
            "API_AUTH_REGISTER",
        ];

        for var in &required_vars {
            env::var(var).with_context(|| format!("环境变量 {} 未设置", var))?;
        }

        // 验证数值类型的环境变量
        let numeric_vars = [
            ("SERVER_PORT", "服务器端口"),
            ("TEST_TIMEOUT", "测试超时时间"),
            ("CRUD_TEST_TIMEOUT", "CRUD测试超时时间"),
            ("CRUD_TEST_RETRY_COUNT", "CRUD测试重试次数"),
            ("CONCURRENT_USERS", "并发用户数"),
        ];

        for (var, desc) in &numeric_vars {
            if let Ok(value) = env::var(var) {
                value
                    .parse::<u32>()
                    .with_context(|| format!("{} ({}) 必须是有效的数字", desc, var))?;
            }
        }

        // 验证布尔类型的环境变量
        let boolean_vars = [
            ("AUTH_TOKEN_EXPIRY_TEST", "Token过期测试"),
            ("DB_TRANSACTION_TEST", "数据库事务测试"),
            ("PLAYWRIGHT_HEADLESS", "Playwright无头模式"),
        ];

        for (var, desc) in &boolean_vars {
            if let Ok(value) = env::var(var) {
                value
                    .parse::<bool>()
                    .with_context(|| format!("{} ({}) 必须是有效的布尔值", desc, var))?;
            }
        }

        println!("✅ 环境变量验证通过");
        Ok(())
    }

    /// 验证文件结构
    fn validate_file_structure() -> Result<()> {
        println!("📁 验证文件结构...");

        // 必需的目录
        let required_dirs = [
            "tests/e2e",
            "tests/e2e/config",
            "tests/e2e/fixtures",
            "tests/e2e/helpers",
            "tests/e2e/reports",
            "tests/e2e/reports/screenshots",
            "tests/e2e/reports/videos",
        ];

        for dir in &required_dirs {
            if !Path::new(dir).exists() {
                return Err(anyhow::anyhow!("必需的目录不存在: {}", dir));
            }
        }

        // 必需的文件
        let required_files = [
            "tests/e2e/config/test.env",
            "tests/e2e/helpers/mod.rs",
            "tests/e2e/helpers/auth.rs",
            "tests/e2e/helpers/task_crud.rs",
            "tests/e2e/helpers/test_server.rs",
            "tests/test_utils.rs",
        ];

        for file in &required_files {
            if !Path::new(file).exists() {
                return Err(anyhow::anyhow!("必需的文件不存在: {}", file));
            }
        }

        println!("✅ 文件结构验证通过");
        Ok(())
    }

    /// 验证依赖项
    fn validate_dependencies() -> Result<()> {
        println!("📦 验证依赖项配置...");

        // 检查Cargo.toml是否存在
        if !Path::new("Cargo.toml").exists() {
            return Err(anyhow::anyhow!("Cargo.toml文件不存在"));
        }

        // 读取Cargo.toml内容
        let cargo_content =
            std::fs::read_to_string("Cargo.toml").context("无法读取Cargo.toml文件")?;

        // 验证必需的依赖项
        let required_deps = [
            "axum",
            "tokio",
            "serde",
            "serde_json",
            "uuid",
            "chrono",
            "anyhow",
            "reqwest",
            "sea-orm",
            "jsonwebtoken",
            "validator",
        ];

        for dep in &required_deps {
            if !cargo_content.contains(dep) {
                return Err(anyhow::anyhow!("缺少必需的依赖项: {}", dep));
            }
        }

        // 验证开发依赖项
        let dev_deps = ["tokio-test", "assert-json-diff", "wiremock", "fake"];

        for dep in &dev_deps {
            if !cargo_content.contains(dep) {
                println!("⚠️ 建议添加开发依赖项: {}", dep);
            }
        }

        println!("✅ 依赖项验证通过");
        Ok(())
    }

    /// 验证测试夹具
    fn validate_test_fixtures() -> Result<()> {
        println!("🗂️ 验证测试夹具...");

        // 验证用户夹具
        if Path::new("tests/e2e/fixtures/users.json").exists() {
            let users_content = std::fs::read_to_string("tests/e2e/fixtures/users.json")
                .context("无法读取用户夹具文件")?;

            let _users: Value =
                serde_json::from_str(&users_content).context("用户夹具文件格式无效")?;

            println!("✅ 用户夹具验证通过");
        }

        // 验证任务夹具
        if Path::new("tests/e2e/fixtures/tasks.json").exists() {
            let tasks_content = std::fs::read_to_string("tests/e2e/fixtures/tasks.json")
                .context("无法读取任务夹具文件")?;

            let _tasks: Value =
                serde_json::from_str(&tasks_content).context("任务夹具文件格式无效")?;

            println!("✅ 任务夹具验证通过");
        }

        println!("✅ 测试夹具验证通过");
        Ok(())
    }

    /// 验证API端点配置
    fn validate_api_endpoints_config() -> Result<()> {
        println!("🌐 验证API端点配置...");

        // 验证认证端点
        let auth_endpoints = [
            "API_AUTH_REGISTER",
            "API_AUTH_LOGIN",
            "API_AUTH_LOGOUT",
            "API_AUTH_VERIFY",
        ];

        for endpoint in &auth_endpoints {
            let path =
                env::var(endpoint).with_context(|| format!("认证端点 {} 未配置", endpoint))?;

            if !path.starts_with("/api/") {
                return Err(anyhow::anyhow!(
                    "认证端点 {} 路径格式无效: {}",
                    endpoint,
                    path
                ));
            }
        }

        // 验证任务管理端点
        let task_endpoints = [
            "API_TASKS_BASE",
            "API_TASKS_CREATE",
            "API_TASKS_LIST",
            "API_TASKS_GET",
            "API_TASKS_UPDATE",
            "API_TASKS_DELETE",
        ];

        for endpoint in &task_endpoints {
            let path =
                env::var(endpoint).with_context(|| format!("任务端点 {} 未配置", endpoint))?;

            if !path.starts_with("/api/") {
                return Err(anyhow::anyhow!(
                    "任务端点 {} 路径格式无效: {}",
                    endpoint,
                    path
                ));
            }
        }

        // 验证基础URL格式
        let base_url = env::var("BASE_URL").context("BASE_URL未配置")?;

        if !base_url.starts_with("http://") && !base_url.starts_with("https://") {
            return Err(anyhow::anyhow!("BASE_URL格式无效: {}", base_url));
        }

        println!("✅ API端点配置验证通过");
        Ok(())
    }

    /// 生成配置报告
    pub fn generate_config_report() -> Result<String> {
        let mut report = String::new();

        report.push_str("# 任务管理CRUD测试配置报告\n\n");
        report.push_str(&format!(
            "生成时间: {}\n\n",
            chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC")
        ));

        // 环境变量报告
        report.push_str("## 环境变量配置\n\n");
        let env_vars = [
            "SERVER_HOST",
            "SERVER_PORT",
            "BASE_URL",
            "TEST_USERNAME",
            "DATABASE_URL",
            "JWT_SECRET",
        ];

        for var in &env_vars {
            if let Ok(value) = env::var(var) {
                // 敏感信息脱敏
                let display_value = if var.contains("SECRET") || var.contains("PASSWORD") {
                    "***".to_string()
                } else {
                    value
                };
                report.push_str(&format!("- {}: {}\n", var, display_value));
            } else {
                report.push_str(&format!("- {}: 未设置\n", var));
            }
        }

        // API端点报告
        report.push_str("\n## API端点配置\n\n");
        let api_endpoints = [
            "API_AUTH_LOGIN",
            "API_AUTH_REGISTER",
            "API_TASKS_BASE",
            "API_TASKS_CREATE",
            "API_TASKS_LIST",
            "API_TASKS_GET",
        ];

        for endpoint in &api_endpoints {
            if let Ok(value) = env::var(endpoint) {
                report.push_str(&format!("- {}: {}\n", endpoint, value));
            }
        }

        // 测试配置报告
        report.push_str("\n## 测试配置\n\n");
        let test_configs = [
            "CRUD_TEST_TIMEOUT",
            "CRUD_TEST_RETRY_COUNT",
            "CONCURRENT_USERS",
            "TEST_TASK_COUNT",
            "PLAYWRIGHT_HEADLESS",
        ];

        for config in &test_configs {
            if let Ok(value) = env::var(config) {
                report.push_str(&format!("- {}: {}\n", config, value));
            }
        }

        Ok(report)
    }
}

#[tokio::test]
async fn test_config_validation() -> Result<()> {
    println!("🧪 开始配置验证测试");

    ConfigValidator::validate_all_configs()?;

    println!("✅ 配置验证测试通过");
    Ok(())
}

#[tokio::main]
async fn main() -> Result<()> {
    println!("🚀 启动任务管理CRUD测试配置验证");

    match ConfigValidator::validate_all_configs() {
        Ok(()) => {
            println!("✅ 所有配置验证通过");

            // 生成配置报告
            let report = ConfigValidator::generate_config_report()?;

            // 保存报告到文件
            let report_path = "tests/e2e/reports/config_validation_report.md";
            std::fs::write(report_path, report).context("无法保存配置报告")?;

            println!("📄 配置报告已保存到: {}", report_path);
        }
        Err(e) => {
            eprintln!("❌ 配置验证失败: {}", e);
            std::process::exit(1);
        }
    }

    Ok(())
}
