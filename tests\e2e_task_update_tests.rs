//! # 任务更新功能E2E测试
//!
//! 本模块实现任务更新功能的端到端测试，遵循Context7 MCP最佳实践：
//! - 使用清晰的函数命名（test_update_task_success、test_update_task_unauthorized等）
//! - 详细的中文注释
//! - 完整的错误处理
//! - 遵循DRY和SOLID原则
//! - 遵循TDD（Test-Driven Development）开发模式
//! - 验证API响应格式、状态码、权限控制和并发修改冲突处理

use anyhow::{Context, Result};
use serde_json::Value;
use std::time::Duration;
use tokio::time::sleep;

// 导入E2E测试辅助模块
#[path = "e2e/helpers/mod.rs"]
mod helpers;

use helpers::{
    AuthHelper, E2EConfig, TaskCrudHelper, TestServer, TestTaskData, ensure_dir_exists,
    test_server::TestServerConfig,
};

/// 任务更新E2E测试套件
pub struct TaskUpdateTestSuite {
    config: E2EConfig,
    test_server: TestServer,
    auth_helper: Auth<PERSON><PERSON><PERSON>,
    task_crud_helper: TaskCrudHelper,
    test_user_token: Option<String>,
    test_task_ids: Vec<String>,           // 存储测试任务ID用于清理
    secondary_user_token: Option<String>, // 用于权限测试的第二个用户
}

impl TaskUpdateTestSuite {
    /// 创建新的测试套件实例
    pub async fn new() -> Result<Self> {
        println!("🔧 初始化任务更新E2E测试套件...");

        // 加载配置
        let config = E2EConfig::from_env().context("无法加载E2E测试配置")?;

        // 创建测试服务器
        let server_config = TestServerConfig {
            host: config.server_host.clone(),
            port: config.server_port,
            startup_timeout: 60,
            health_check_interval: 1,
            project_root: std::env::current_dir()
                .context("无法获取当前目录")?
                .to_string_lossy()
                .to_string(),
        };

        let test_server = TestServer::new(server_config);

        // 创建辅助工具
        let auth_helper = AuthHelper::new(config.clone());
        let task_crud_helper = TaskCrudHelper::new(config.clone());

        Ok(Self {
            config,
            test_server,
            auth_helper,
            task_crud_helper,
            test_user_token: None,
            test_task_ids: Vec::new(),
            secondary_user_token: None,
        })
    }

    /// 设置基本测试环境（不包含认证和测试数据）
    pub async fn setup_basic_environment(&mut self) -> Result<()> {
        println!("🚀 设置基本测试环境...");

        // 1. 确保报告目录存在
        self.ensure_report_directories()?;

        // 2. 启动测试服务器
        self.start_test_server().await?;

        println!("✅ 基本测试环境设置完成");
        Ok(())
    }

    /// 设置测试环境
    pub async fn setup_test_environment(&mut self) -> Result<()> {
        println!("🚀 设置任务更新测试环境...");

        // 1. 设置基本环境
        self.setup_basic_environment().await?;

        // 2. 配置认证流程
        self.configure_authentication().await?;

        // 3. 创建测试数据
        self.create_test_data().await?;

        println!("✅ 任务更新测试环境设置完成");
        Ok(())
    }

    /// 确保报告目录存在
    fn ensure_report_directories(&self) -> Result<()> {
        println!("📁 创建测试报告目录...");

        ensure_dir_exists(&self.config.report_dir)?;
        ensure_dir_exists(&self.config.screenshot_dir)?;
        ensure_dir_exists(&self.config.video_dir)?;

        println!("✅ 测试报告目录创建完成");
        Ok(())
    }

    /// 启动测试服务器
    async fn start_test_server(&mut self) -> Result<()> {
        println!("🚀 启动测试服务器...");

        self.test_server
            .start()
            .await
            .context("无法启动测试服务器")?;

        // 等待服务器完全启动
        sleep(Duration::from_secs(3)).await;

        // 验证服务器健康状态
        let health_check = self.test_server.health_check().await?;
        println!("✅ 服务器健康检查通过: {:?}", health_check);

        Ok(())
    }

    /// 配置认证流程
    async fn configure_authentication(&mut self) -> Result<()> {
        println!("🔐 配置认证流程...");

        // 生成唯一的测试用户名，避免冲突
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_millis();
        let unique_username = format!("update_test_user_{}", timestamp);
        let unique_email = format!("update_test_user_{}@example.com", timestamp);
        let secondary_username = format!("update_test_user2_{}", timestamp);
        let secondary_email = format!("update_test_user2_{}@example.com", timestamp);

        println!("📝 使用唯一测试用户名: {}", unique_username);

        // 1. 注册主测试用户
        let register_result = self
            .auth_helper
            .register_user(&unique_username, &unique_email, &self.config.test_password)
            .await;

        match register_result {
            Ok(response) => {
                if response["status"].as_u64().unwrap_or(0) == 201 {
                    println!("✅ 主测试用户注册成功");
                } else {
                    return Err(anyhow::anyhow!("主用户注册失败: {:?}", response));
                }
            }
            Err(e) => {
                return Err(anyhow::anyhow!("主用户注册请求失败: {}", e));
            }
        }

        // 2. 注册第二个测试用户（用于权限测试）
        let secondary_register_result = self
            .auth_helper
            .register_user(
                &secondary_username,
                &secondary_email,
                &self.config.test_password,
            )
            .await;

        match secondary_register_result {
            Ok(response) => {
                if response["status"].as_u64().unwrap_or(0) == 201 {
                    println!("✅ 第二个测试用户注册成功");
                } else {
                    return Err(anyhow::anyhow!("第二个用户注册失败: {:?}", response));
                }
            }
            Err(e) => {
                return Err(anyhow::anyhow!("第二个用户注册请求失败: {}", e));
            }
        }

        // 3. 登录获取主用户认证令牌
        let token = self
            .auth_helper
            .get_auth_token(&unique_username, &self.config.test_password)
            .await?;

        self.test_user_token = Some(token.clone());

        // 4. 登录获取第二个用户认证令牌
        let secondary_token = self
            .auth_helper
            .get_auth_token(&secondary_username, &self.config.test_password)
            .await?;

        self.secondary_user_token = Some(secondary_token);

        // 5. 验证令牌有效性（通过调用需要认证的API端点）
        let test_result = self.task_crud_helper.fetch_tasks_list(&token).await;
        match test_result {
            Ok(response) => {
                if response["status"].as_u64().unwrap_or(0) == 200 {
                    println!("✅ 令牌验证通过（通过任务列表API测试）");
                } else {
                    return Err(anyhow::anyhow!(
                        "令牌验证失败，任务列表API返回: {:?}",
                        response
                    ));
                }
            }
            Err(e) => {
                return Err(anyhow::anyhow!("令牌验证失败，无法调用任务列表API: {}", e));
            }
        }

        println!("✅ 认证流程配置完成，令牌验证通过");
        Ok(())
    }

    /// 创建测试数据
    async fn create_test_data(&mut self) -> Result<()> {
        println!("📝 创建测试数据...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 创建多个测试任务用于更新测试
        let test_tasks = vec![
            TestTaskData::new("更新测试任务 #1")
                .with_description("第一个用于更新测试的任务")
                .with_priority("high"),
            TestTaskData::new("更新测试任务 #2")
                .with_description("第二个用于更新测试的任务")
                .with_priority("medium"),
            TestTaskData::new("更新测试任务 #3")
                .with_description("第三个用于更新测试的任务")
                .with_priority("low")
                .with_completed(true),
        ];

        for test_task in test_tasks {
            let create_result = self.task_crud_helper.create_task(token, &test_task).await?;
            if create_result["status"].as_u64().unwrap_or(0) == 201 {
                if let Some(task_id) = create_result["body"]["data"]["id"].as_str() {
                    self.test_task_ids.push(task_id.to_string());
                }
            }
        }

        println!(
            "✅ 测试数据创建完成，创建了{}个测试任务",
            self.test_task_ids.len()
        );
        Ok(())
    }

    /// 测试任务更新成功场景
    pub async fn test_update_task_success(&self) -> Result<()> {
        println!("🧪 测试任务更新成功场景...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 使用第一个测试任务ID
        let task_id = self
            .test_task_ids
            .first()
            .ok_or_else(|| anyhow::anyhow!("没有可用的测试任务ID"))?;

        // 准备更新数据
        let updated_task_data = TestTaskData::new("更新后的任务标题")
            .with_description("更新后的任务描述")
            .with_priority("high")
            .with_status("completed");

        // 执行任务更新
        let update_result = self
            .task_crud_helper
            .update_task(token, task_id, &updated_task_data)
            .await?;

        // 验证响应状态码
        assert_eq!(
            update_result["status"].as_u64().unwrap_or(0),
            200,
            "任务更新应返回200状态码"
        );

        // 验证响应格式
        self.task_crud_helper
            .validate_task_response(&update_result["body"])?;

        // 验证更新后的数据
        let updated_task = &update_result["body"]["data"];
        assert_eq!(
            updated_task["title"].as_str().unwrap(),
            "更新后的任务标题",
            "任务标题应已更新"
        );
        assert_eq!(
            updated_task["description"].as_str().unwrap(),
            "更新后的任务描述",
            "任务描述应已更新"
        );
        assert_eq!(
            updated_task["completed"].as_bool().unwrap(),
            true,
            "任务完成状态应已更新"
        );

        // 验证任务ID保持不变
        assert_eq!(
            updated_task["id"].as_str().unwrap(),
            task_id,
            "任务ID应保持不变"
        );

        // 验证更新时间已改变
        assert!(
            updated_task["updated_at"].is_string(),
            "更新时间应为字符串类型"
        );

        println!("✅ 任务更新成功场景测试通过");
        Ok(())
    }

    /// 测试部分字段更新
    pub async fn test_update_task_partial_fields(&self) -> Result<()> {
        println!("🧪 测试部分字段更新...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 使用第二个测试任务ID
        let task_id = self
            .test_task_ids
            .get(1)
            .ok_or_else(|| anyhow::anyhow!("没有足够的测试任务ID"))?;

        // 获取原始任务数据
        let original_result = self
            .task_crud_helper
            .fetch_task_by_id(token, task_id)
            .await?;
        let original_task = &original_result["body"]["data"];
        let original_title = original_task["title"].as_str().unwrap();
        let original_description = original_task["description"].as_str();

        // 只更新完成状态
        let partial_update_data = TestTaskData {
            title: "".to_string(),                 // 空标题，不应更新
            description: None,                     // 不更新描述
            completed: true,                       // 只更新完成状态
            status: Some("completed".to_string()), // 使用status字段
            priority: None,
            due_date: None,
        };

        // 执行部分更新
        let update_result = self
            .task_crud_helper
            .update_task(token, task_id, &partial_update_data)
            .await?;

        // 验证响应状态码
        assert_eq!(
            update_result["status"].as_u64().unwrap_or(0),
            200,
            "部分更新应返回200状态码"
        );

        // 验证更新后的数据
        let updated_task = &update_result["body"]["data"];

        // 标题和描述应保持不变
        assert_eq!(
            updated_task["title"].as_str().unwrap(),
            original_title,
            "标题应保持不变"
        );
        assert_eq!(
            updated_task["description"].as_str(),
            original_description,
            "描述应保持不变"
        );

        // 完成状态应已更新
        assert_eq!(
            updated_task["completed"].as_bool().unwrap(),
            true,
            "完成状态应已更新"
        );

        println!("✅ 部分字段更新测试通过");
        Ok(())
    }

    /// 测试更新不存在的任务
    pub async fn test_update_task_not_found(&self) -> Result<()> {
        println!("🧪 测试更新不存在的任务...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;

        // 使用不存在的任务ID（有效的UUID格式）
        let non_existent_id = "00000000-0000-0000-0000-000000000000";

        // 准备更新数据
        let update_data = TestTaskData::new("尝试更新不存在的任务");

        // 尝试更新不存在的任务
        let update_result = self
            .task_crud_helper
            .update_task(token, non_existent_id, &update_data)
            .await;

        // 验证响应
        match update_result {
            Ok(response) => {
                let status = response["status"].as_u64().unwrap_or(0);
                assert_eq!(status, 404, "更新不存在的任务应返回404状态码");
            }
            Err(e) => {
                // 如果是网络错误或JSON解析错误，检查是否是404响应
                let error_msg = e.to_string();
                assert!(
                    error_msg.contains("404") || error_msg.contains("Not Found"),
                    "应该是404错误，实际错误: {}",
                    error_msg
                );
            }
        }

        println!("✅ 更新不存在的任务测试通过");
        Ok(())
    }

    /// 测试未认证用户更新任务
    pub async fn test_update_task_unauthorized(&self) -> Result<()> {
        println!("🧪 测试未认证用户更新任务...");

        // 使用无效令牌
        let invalid_token = "invalid_token";
        let task_id = self
            .test_task_ids
            .first()
            .ok_or_else(|| anyhow::anyhow!("没有可用的测试任务ID"))?;

        // 准备更新数据
        let update_data = TestTaskData::new("未认证用户尝试更新");

        // 尝试更新任务
        let update_result = self
            .task_crud_helper
            .update_task(invalid_token, task_id, &update_data)
            .await;

        // 验证响应 - 应该是认证错误
        match update_result {
            Ok(response) => {
                let status = response["status"].as_u64().unwrap_or(0);
                assert_eq!(status, 401, "未认证用户应返回401状态码");
            }
            Err(e) => {
                // 如果是网络错误或JSON解析错误，检查是否是401响应
                let error_msg = e.to_string();
                assert!(
                    error_msg.contains("401") || error_msg.contains("Unauthorized"),
                    "应该是认证错误，实际错误: {}",
                    error_msg
                );
            }
        }

        println!("✅ 未认证用户更新任务测试通过");
        Ok(())
    }

    /// 测试权限控制 - 用户只能更新自己的任务
    pub async fn test_update_task_permission_denied(&self) -> Result<()> {
        println!("🧪 测试权限控制 - 用户只能更新自己的任务...");

        let secondary_token = self
            .secondary_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("第二个用户令牌未设置"))?;

        // 使用第二个用户的令牌尝试更新第一个用户的任务
        let task_id = self
            .test_task_ids
            .first()
            .ok_or_else(|| anyhow::anyhow!("没有可用的测试任务ID"))?;

        // 准备更新数据
        let update_data = TestTaskData::new("尝试更新他人的任务");

        // 尝试更新他人的任务
        let update_result = self
            .task_crud_helper
            .update_task(secondary_token, task_id, &update_data)
            .await;

        // 验证响应 - 应该是权限错误或任务不存在错误
        match update_result {
            Ok(response) => {
                let status = response["status"].as_u64().unwrap_or(0);
                // 可能返回404（任务不存在）或403（权限不足）
                assert!(
                    status == 404 || status == 403,
                    "尝试更新他人任务应返回404或403状态码，实际: {}",
                    status
                );
            }
            Err(e) => {
                // 如果是网络错误或JSON解析错误，检查是否是权限相关错误
                let error_msg = e.to_string();
                assert!(
                    error_msg.contains("404")
                        || error_msg.contains("403")
                        || error_msg.contains("Not Found")
                        || error_msg.contains("Forbidden"),
                    "应该是权限相关错误，实际错误: {}",
                    error_msg
                );
            }
        }

        println!("✅ 权限控制测试通过");
        Ok(())
    }

    /// 测试无效数据更新
    pub async fn test_update_task_invalid_data(&self) -> Result<()> {
        println!("🧪 测试无效数据更新...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;
        let task_id = self
            .test_task_ids
            .first()
            .ok_or_else(|| anyhow::anyhow!("没有可用的测试任务ID"))?;

        // 测试空标题
        let invalid_data = TestTaskData {
            title: "".to_string(), // 空标题应该被拒绝
            description: Some("有效描述".to_string()),
            completed: false,
            status: Some("pending".to_string()),
            priority: Some("medium".to_string()),
            due_date: None,
        };

        let update_result = self
            .task_crud_helper
            .update_task(token, task_id, &invalid_data)
            .await;

        // 验证响应 - 应该是验证错误
        match update_result {
            Ok(response) => {
                let status = response["status"].as_u64().unwrap_or(0);
                assert_eq!(status, 400, "无效数据应返回400状态码");
            }
            Err(e) => {
                // 如果是网络错误，检查是否是400响应
                let error_msg = e.to_string();
                assert!(
                    error_msg.contains("400") || error_msg.contains("Bad Request"),
                    "应该是验证错误，实际错误: {}",
                    error_msg
                );
            }
        }

        println!("✅ 无效数据更新测试通过");
        Ok(())
    }

    /// 测试任务更新响应时间性能
    pub async fn test_update_task_performance(&self) -> Result<()> {
        println!("🧪 测试任务更新响应时间性能...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;
        let task_id = self
            .test_task_ids
            .first()
            .ok_or_else(|| anyhow::anyhow!("没有可用的测试任务ID"))?;

        // 准备更新数据
        let update_data =
            TestTaskData::new("性能测试更新").with_description("测试更新操作的响应时间");

        // 测试更新性能
        let start_time = std::time::Instant::now();
        let update_result = self
            .task_crud_helper
            .update_task(token, task_id, &update_data)
            .await?;
        let response_time = start_time.elapsed();

        // 验证更新成功
        assert_eq!(update_result["status"].as_u64().unwrap_or(0), 200);

        // 验证响应时间（应小于500ms）
        assert!(
            response_time.as_millis() < 500,
            "任务更新响应时间应小于500ms，实际: {}ms",
            response_time.as_millis()
        );

        println!("📊 任务更新响应时间: {}ms", response_time.as_millis());
        println!("✅ 任务更新响应时间性能测试通过");
        Ok(())
    }

    /// 测试并发更新冲突处理
    pub async fn test_update_task_concurrent_modification(&self) -> Result<()> {
        println!("🧪 测试并发更新冲突处理...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;
        let task_id = self
            .test_task_ids
            .first()
            .ok_or_else(|| anyhow::anyhow!("没有可用的测试任务ID"))?;

        // 准备两个不同的更新数据
        let update_data1 = TestTaskData::new("并发更新测试 #1").with_description("第一个并发更新");
        let update_data2 = TestTaskData::new("并发更新测试 #2").with_description("第二个并发更新");

        // 同时发起两个更新请求
        let (result1, result2) = tokio::join!(
            self.task_crud_helper
                .update_task(token, task_id, &update_data1),
            self.task_crud_helper
                .update_task(token, task_id, &update_data2)
        );

        // 验证至少有一个更新成功
        let success_count = [&result1, &result2]
            .iter()
            .filter(|result| match result {
                Ok(response) => response["status"].as_u64().unwrap_or(0) == 200,
                Err(_) => false,
            })
            .count();

        assert!(success_count >= 1, "至少应有一个并发更新成功");

        // 验证最终状态一致性 - 获取任务当前状态
        let final_result = self
            .task_crud_helper
            .fetch_task_by_id(token, task_id)
            .await?;
        assert_eq!(final_result["status"].as_u64().unwrap_or(0), 200);

        let final_task = &final_result["body"]["data"];
        let final_title = final_task["title"].as_str().unwrap();

        // 最终标题应该是其中一个更新的结果
        assert!(
            final_title == "并发更新测试 #1" || final_title == "并发更新测试 #2",
            "最终任务标题应该是其中一个更新的结果，实际: {}",
            final_title
        );

        println!("✅ 并发更新冲突处理测试通过");
        Ok(())
    }

    /// 测试任务状态切换
    pub async fn test_update_task_status_toggle(&self) -> Result<()> {
        println!("🧪 测试任务状态切换...");

        let token = self
            .test_user_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("认证令牌未设置"))?;
        let task_id = self.test_task_ids
            .get(2) // 使用第三个任务（初始状态为已完成）
            .ok_or_else(|| anyhow::anyhow!("没有足够的测试任务ID"))?;

        // 获取原始任务状态
        let original_result = self
            .task_crud_helper
            .fetch_task_by_id(token, task_id)
            .await?;
        let original_completed = original_result["body"]["data"]["completed"]
            .as_bool()
            .unwrap();

        // 切换任务状态
        let toggle_data = TestTaskData {
            title: "".to_string(),          // 不更新标题
            description: None,              // 不更新描述
            completed: !original_completed, // 切换完成状态
            status: Some(if !original_completed {
                "completed".to_string()
            } else {
                "pending".to_string()
            }),
            priority: None,
            due_date: None,
        };

        // 执行状态切换
        let update_result = self
            .task_crud_helper
            .update_task(token, task_id, &toggle_data)
            .await?;

        // 验证响应状态码
        assert_eq!(
            update_result["status"].as_u64().unwrap_or(0),
            200,
            "状态切换应返回200状态码"
        );

        // 验证状态已切换
        let updated_task = &update_result["body"]["data"];
        assert_eq!(
            updated_task["completed"].as_bool().unwrap(),
            !original_completed,
            "任务完成状态应已切换"
        );

        println!("✅ 任务状态切换测试通过");
        Ok(())
    }

    /// 清理测试环境
    pub async fn cleanup(&mut self) -> Result<()> {
        println!("🧹 清理测试环境...");

        // 清理创建的测试任务
        if let Some(token) = &self.test_user_token {
            for task_id in &self.test_task_ids {
                let _ = self.task_crud_helper.delete_task(token, task_id).await;
            }
        }

        // 停止测试服务器
        self.test_server.stop()?;

        println!("✅ 测试环境清理完成");
        Ok(())
    }
}

impl Drop for TaskUpdateTestSuite {
    fn drop(&mut self) {
        // 确保服务器被停止
        let _ = self.test_server.stop();
    }
}

/// 任务更新成功测试
#[tokio::test]
async fn test_update_task_success() -> Result<()> {
    let mut test_suite = TaskUpdateTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_update_task_success().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 部分字段更新测试
#[tokio::test]
async fn test_update_task_partial_fields() -> Result<()> {
    let mut test_suite = TaskUpdateTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_update_task_partial_fields().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 更新不存在的任务测试
#[tokio::test]
async fn test_update_task_not_found() -> Result<()> {
    let mut test_suite = TaskUpdateTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_update_task_not_found().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 未认证用户更新任务测试
#[tokio::test]
async fn test_update_task_unauthorized() -> Result<()> {
    let mut test_suite = TaskUpdateTestSuite::new().await?;
    // 不需要设置完整的测试环境，只需要基本的服务器连接
    test_suite.setup_basic_environment().await?;

    test_suite.test_update_task_unauthorized().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 权限控制测试
#[tokio::test]
async fn test_update_task_permission_denied() -> Result<()> {
    let mut test_suite = TaskUpdateTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_update_task_permission_denied().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 无效数据更新测试
#[tokio::test]
async fn test_update_task_invalid_data() -> Result<()> {
    let mut test_suite = TaskUpdateTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_update_task_invalid_data().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 任务更新性能测试
#[tokio::test]
async fn test_update_task_performance() -> Result<()> {
    let mut test_suite = TaskUpdateTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_update_task_performance().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 并发更新冲突处理测试
#[tokio::test]
async fn test_update_task_concurrent_modification() -> Result<()> {
    let mut test_suite = TaskUpdateTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite
        .test_update_task_concurrent_modification()
        .await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 任务状态切换测试
#[tokio::test]
async fn test_update_task_status_toggle() -> Result<()> {
    let mut test_suite = TaskUpdateTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    test_suite.test_update_task_status_toggle().await?;

    test_suite.cleanup().await?;
    Ok(())
}

/// 主测试函数 - 运行所有任务更新测试
#[tokio::main]
async fn main() -> Result<()> {
    // 设置日志
    tracing_subscriber::fmt::init();

    println!("🚀 启动任务更新功能E2E测试套件");

    let mut test_suite = TaskUpdateTestSuite::new().await?;
    test_suite.setup_test_environment().await?;

    println!("📋 开始执行任务更新测试用例...");

    // 1. 基础功能测试
    println!("1️⃣ 执行任务更新成功测试...");
    test_suite.test_update_task_success().await?;

    // 2. 部分字段更新测试
    println!("2️⃣ 执行部分字段更新测试...");
    test_suite.test_update_task_partial_fields().await?;

    // 3. 错误场景测试
    println!("3️⃣ 执行更新不存在任务测试...");
    test_suite.test_update_task_not_found().await?;

    // 4. 未认证用户测试
    println!("4️⃣ 执行未认证用户更新任务测试...");
    test_suite.test_update_task_unauthorized().await?;

    // 5. 权限控制测试
    println!("5️⃣ 执行权限控制测试...");
    test_suite.test_update_task_permission_denied().await?;

    // 6. 无效数据测试
    println!("6️⃣ 执行无效数据更新测试...");
    test_suite.test_update_task_invalid_data().await?;

    // 7. 性能测试
    println!("7️⃣ 执行任务更新响应时间性能测试...");
    test_suite.test_update_task_performance().await?;

    // 8. 并发更新冲突处理测试
    println!("8️⃣ 执行并发更新冲突处理测试...");
    test_suite
        .test_update_task_concurrent_modification()
        .await?;

    // 9. 任务状态切换测试
    println!("9️⃣ 执行任务状态切换测试...");
    test_suite.test_update_task_status_toggle().await?;

    test_suite.cleanup().await?;

    println!("✅ 所有任务更新E2E测试完成");
    Ok(())
}
