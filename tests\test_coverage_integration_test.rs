//! # 测试覆盖率集成测试
//!
//! 验证测试覆盖率分析器在真实项目中的功能
//! 遵循TDD方法和Context7 MCP最佳实践

mod test_coverage_analysis;
mod test_utils;

use std::fs;
use test_coverage_analysis::TestCoverageAnalyzer;
use test_utils::TestEnvironment;

/// 测试覆盖率分析集成测试
#[cfg(test)]
mod integration_tests {
    use super::*;

    #[test]
    fn test_analyze_real_project_coverage() {
        println!("🔍 开始分析真实项目的测试覆盖率...");

        // 设置测试环境
        TestEnvironment::setup_test_env();

        let mut analyzer = TestCoverageAnalyzer::new();

        // 扫描当前项目的测试文件
        let project_root = ".";
        let result = analyzer.scan_test_files(project_root);

        assert!(result.is_ok(), "扫描测试文件应该成功");

        let stats = analyzer.get_coverage_stats();

        // 验证基本统计信息
        assert!(stats.total_tests > 0, "应该发现一些测试");
        println!("📊 发现测试总数: {}", stats.total_tests);
        println!("📊 单元测试数: {}", stats.unit_tests);
        println!("📊 集成测试数: {}", stats.integration_tests);
        println!("📊 覆盖率: {:.1}%", stats.coverage_percentage);

        // 验证重复模式检测
        let duplicate_patterns = analyzer.get_duplicate_patterns();
        println!("🔄 发现重复模式数: {}", duplicate_patterns.len());

        for (pattern, count) in duplicate_patterns {
            if *count > 5 {
                println!("⚠️  高频重复模式: {pattern} (出现 {count} 次)");
            }
        }

        // 生成并验证报告
        let report = analyzer.generate_report();
        assert!(report.contains("测试覆盖率分析报告"), "报告应包含标题");
        assert!(report.contains("基本统计"), "报告应包含统计信息");

        // 将报告写入文件以供查看
        let report_path = "target/test_coverage_report.md";
        fs::write(report_path, &report).expect("写入报告文件失败");
        println!("📄 测试覆盖率报告已生成: {report_path}");

        // 清理测试环境
        TestEnvironment::cleanup_test_env();

        println!("✅ 测试覆盖率分析完成");
    }

    #[test]
    fn test_detect_specific_test_patterns() {
        println!("🔍 测试特定测试模式检测...");

        let mut analyzer = TestCoverageAnalyzer::new();

        // 创建临时测试文件内容
        let test_content = r#"
            #[cfg(test)]
            mod tests {
                use super::*;
                
                #[test]
                fn test_create_user_success() {
                    let user = create_test_user();
                    assert_eq!(user.username, "test_user");
                }
                
                #[test]
                fn test_create_user_invalid_username() {
                    let result = create_user_with_invalid_username();
                    assert!(result.is_err());
                }
                
                #[test]
                fn test_validate_email_format() {
                    assert!(validate_email("<EMAIL>"));
                    assert!(!validate_email("invalid-email"));
                }
                
                #[tokio::test]
                async fn test_async_operation() {
                    let result = async_operation().await;
                    assert!(result.is_ok());
                }
            }
        "#;

        // 分析测试内容
        let test_functions = analyzer.extract_test_functions(test_content);

        // 验证提取的测试函数
        assert_eq!(test_functions.len(), 4);
        assert!(test_functions.contains(&"test_create_user_success".to_string()));
        assert!(test_functions.contains(&"test_create_user_invalid_username".to_string()));
        assert!(test_functions.contains(&"test_validate_email_format".to_string()));
        assert!(test_functions.contains(&"test_async_operation".to_string()));

        // 分析重复模式
        analyzer.analyze_duplicate_patterns(test_content, &test_functions);
        let patterns = analyzer.get_duplicate_patterns();

        // 验证检测到的模式
        assert!(
            patterns.contains_key("test_create_*"),
            "应该检测到 test_create_* 模式"
        );
        assert!(patterns.contains_key("assert!"), "应该检测到 assert! 模式");

        println!("✅ 特定测试模式检测完成");
    }

    #[test]
    fn test_coverage_stats_calculation() {
        println!("📊 测试覆盖率统计计算...");

        let mut analyzer = TestCoverageAnalyzer::new();

        // 模拟不同类型的测试文件
        let unit_test_functions = vec![
            "test_unit_1".to_string(),
            "test_unit_2".to_string(),
            "test_unit_3".to_string(),
        ];

        let integration_test_functions = vec![
            "test_integration_1".to_string(),
            "test_integration_2".to_string(),
        ];

        // 更新统计信息
        analyzer.update_coverage_stats("src/lib.rs", &unit_test_functions);
        analyzer.update_coverage_stats("tests/integration_test.rs", &integration_test_functions);
        analyzer.analyze_coverage();

        let stats = analyzer.get_coverage_stats();

        // 验证统计结果
        assert_eq!(stats.total_tests, 5);
        assert_eq!(stats.unit_tests, 3);
        assert_eq!(stats.integration_tests, 2);
        assert!(stats.coverage_percentage >= 0.0 && stats.coverage_percentage <= 100.0);

        println!("📊 统计结果验证通过:");
        println!("   - 总测试数: {}", stats.total_tests);
        println!("   - 单元测试: {}", stats.unit_tests);
        println!("   - 集成测试: {}", stats.integration_tests);
        println!("   - 覆盖率: {:.1}%", stats.coverage_percentage);

        println!("✅ 覆盖率统计计算测试完成");
    }

    #[test]
    fn test_report_generation_quality() {
        println!("📄 测试报告生成质量...");

        let mut analyzer = TestCoverageAnalyzer::new();

        // 添加测试数据
        analyzer.test_files.insert(
            "tests/user_test.rs".to_string(),
            vec![
                "test_create_user".to_string(),
                "test_update_user".to_string(),
                "test_delete_user".to_string(),
            ],
        );

        analyzer.test_files.insert(
            "tests/task_test.rs".to_string(),
            vec![
                "test_create_task".to_string(),
                "test_complete_task".to_string(),
            ],
        );

        analyzer
            .duplicate_patterns
            .insert("test_create_*".to_string(), 3);
        analyzer
            .duplicate_patterns
            .insert("assert_eq!".to_string(), 15);
        analyzer.duplicate_patterns.insert("expect(".to_string(), 8);

        analyzer.coverage_stats.total_tests = 5;
        analyzer.coverage_stats.unit_tests = 3;
        analyzer.coverage_stats.integration_tests = 2;
        analyzer.coverage_stats.coverage_percentage = 75.0;
        analyzer.coverage_stats.duplicate_patterns = 3;

        let report = analyzer.generate_report();

        // 验证报告结构和内容
        assert!(report.contains("# 测试覆盖率分析报告"), "报告应包含主标题");
        assert!(report.contains("## 基本统计"), "报告应包含基本统计部分");
        assert!(report.contains("## 重复模式分析"), "报告应包含重复模式分析");
        assert!(report.contains("## 测试文件列表"), "报告应包含测试文件列表");

        // 验证统计数据
        assert!(report.contains("总测试数量: 5"), "报告应包含正确的总测试数");
        assert!(report.contains("单元测试: 3"), "报告应包含正确的单元测试数");
        assert!(report.contains("集成测试: 2"), "报告应包含正确的集成测试数");
        assert!(report.contains("覆盖率: 75.0%"), "报告应包含正确的覆盖率");

        // 验证重复模式信息
        assert!(report.contains("test_create_*"), "报告应包含重复模式信息");
        assert!(report.contains("assert_eq!"), "报告应包含断言模式信息");

        // 验证测试文件信息
        assert!(
            report.contains("tests/user_test.rs"),
            "报告应包含用户测试文件"
        );
        assert!(
            report.contains("tests/task_test.rs"),
            "报告应包含任务测试文件"
        );
        assert!(
            report.contains("test_create_user"),
            "报告应包含具体测试函数"
        );

        println!("📄 报告内容验证通过");
        println!("📄 报告长度: {} 字符", report.len());

        // 验证报告格式
        let lines: Vec<&str> = report.lines().collect();
        assert!(lines.len() > 10, "报告应该有足够的行数");

        println!("✅ 报告生成质量测试完成");
    }

    #[test]
    fn test_file_scanning_robustness() {
        println!("🔍 测试文件扫描健壮性...");

        let mut analyzer = TestCoverageAnalyzer::new();

        // 测试扫描不存在的目录
        let result = analyzer.scan_test_files("non_existent_directory");
        assert!(result.is_ok(), "扫描不存在的目录应该优雅处理");

        // 测试扫描当前目录
        let result = analyzer.scan_test_files(".");
        assert!(result.is_ok(), "扫描当前目录应该成功");

        let stats = analyzer.get_coverage_stats();
        println!("🔍 扫描结果: 发现 {} 个测试", stats.total_tests);

        // 验证至少发现了一些测试（因为这个测试文件本身就包含测试）
        assert!(stats.total_tests > 0, "应该至少发现一些测试");

        println!("✅ 文件扫描健壮性测试完成");
    }

    #[test]
    fn test_edge_cases_handling() {
        println!("🧪 测试边界情况处理...");

        let analyzer = TestCoverageAnalyzer::new();

        // 测试空内容
        assert!(!analyzer.is_test_file("", "empty.rs"));

        // 测试只有注释的文件
        let comment_only = "// This is just a comment file\n/* More comments */";
        assert!(!analyzer.is_test_file(comment_only, "comments.rs"));

        // 测试包含test关键字但不是测试的文件
        let false_positive = "fn test_helper() { /* not a test */ }";
        assert!(!analyzer.is_test_file(false_positive, "helper.rs"));

        // 测试函数名提取的边界情况
        assert_eq!(analyzer.extract_function_name(""), None);
        assert_eq!(analyzer.extract_function_name("fn"), None);
        assert_eq!(
            analyzer.extract_function_name("fn ()"),
            Some("".to_string())
        );

        // 测试模式提取的边界情况
        assert_eq!(analyzer.extract_test_pattern(""), "");
        assert_eq!(analyzer.extract_test_pattern("test"), "test");
        assert_eq!(analyzer.extract_test_pattern("test_"), "test_");

        println!("✅ 边界情况处理测试完成");
    }
}

/// 性能测试
#[cfg(test)]
mod performance_tests {
    use super::*;
    use std::time::Instant;

    #[test]
    fn test_large_project_scanning_performance() {
        println!("⚡ 测试大型项目扫描性能...");

        let mut analyzer = TestCoverageAnalyzer::new();

        let start = Instant::now();
        let result = analyzer.scan_test_files(".");
        let duration = start.elapsed();

        assert!(result.is_ok(), "大型项目扫描应该成功");

        println!("⚡ 扫描耗时: {duration:?}");
        println!(
            "⚡ 发现测试数: {}",
            analyzer.get_coverage_stats().total_tests
        );

        // 性能断言：扫描应该在合理时间内完成（10秒内）
        assert!(duration.as_secs() < 10, "扫描时间应该在10秒内完成");

        println!("✅ 大型项目扫描性能测试完成");
    }
}
