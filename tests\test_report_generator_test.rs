//! # 测试报告生成器测试
//!
//! 验证测试报告生成器的各项功能
//!
//! ## 测试覆盖范围
//! - HTML报告生成
//! - JSON数据导出
//! - 图表可视化
//! - 趋势分析
//! - 自动化流程

use axum_tutorial::test_report::*;
use chrono::Utc;
use std::fs;
use std::path::Path;
use tempfile::TempDir;

/// 创建测试用的报告配置
fn create_test_config(output_dir: &str) -> ReportConfig {
    ReportConfig {
        output_dir: output_dir.to_string(),
        project_name: "测试项目".to_string(),
        project_version: "1.0.0-test".to_string(),
        generate_html: true,
        generate_json: true,
        include_charts: true,
        include_trends: true,
        history_retention_days: 7,
    }
}

/// 创建测试用的测试报告数据
fn create_test_report() -> TestReport {
    let test_cases = vec![
        TestCase {
            name: "test_authentication".to_string(),
            status: TestStatus::Passed,
            duration_ms: 150,
            error_message: None,
            output: Some("测试通过".to_string()),
            file_path: "tests/auth_test.rs".to_string(),
            line_number: Some(42),
        },
        TestCase {
            name: "test_api_endpoint".to_string(),
            status: TestStatus::Failed,
            duration_ms: 300,
            error_message: Some("断言失败: expected 200, got 404".to_string()),
            output: Some("测试失败".to_string()),
            file_path: "tests/api_test.rs".to_string(),
            line_number: Some(78),
        },
        TestCase {
            name: "test_database_connection".to_string(),
            status: TestStatus::Skipped,
            duration_ms: 0,
            error_message: None,
            output: Some("跳过测试".to_string()),
            file_path: "tests/db_test.rs".to_string(),
            line_number: Some(123),
        },
    ];

    let test_suite = TestSuite {
        name: "集成测试".to_string(),
        test_cases,
        total_duration_ms: 450,
        passed_count: 1,
        failed_count: 1,
        skipped_count: 1,
        error_count: 0,
    };

    let summary = TestSummary {
        total_tests: 3,
        passed_tests: 1,
        failed_tests: 1,
        skipped_tests: 1,
        error_tests: 0,
        total_duration_ms: 450,
        success_rate: 33.33,
    };

    let coverage = Some(CoverageInfo {
        line_coverage: 75.5,
        branch_coverage: 68.2,
        function_coverage: 82.1,
        lines_covered: 755,
        lines_total: 1000,
        branches_covered: 68,
        branches_total: 100,
        functions_covered: 82,
        functions_total: 100,
    });

    let environment = EnvironmentInfo {
        rust_version: "rustc 1.70.0".to_string(),
        os: "windows".to_string(),
        arch: "x86_64".to_string(),
        hostname: "test-machine".to_string(),
        env_vars: std::collections::HashMap::new(),
    };

    TestReport {
        timestamp: Utc::now(),
        project_name: "测试项目".to_string(),
        project_version: "1.0.0-test".to_string(),
        test_suites: vec![test_suite],
        summary,
        coverage,
        environment,
    }
}

#[tokio::test]
async fn test_html_template_generation() {
    let temp_dir = TempDir::new().expect("创建临时目录失败");
    let output_dir = temp_dir.path().to_str().unwrap();
    let config = create_test_config(output_dir);
    let test_report = create_test_report();

    let html_generator = HtmlTemplateGenerator::new(config);
    let result = html_generator.generate_html_report(&test_report);

    assert!(result.is_ok(), "HTML报告生成失败: {:?}", result.err());

    // 验证HTML文件是否生成
    let html_path = Path::new(output_dir).join("test_report.html");
    assert!(html_path.exists(), "HTML报告文件不存在");

    // 验证HTML内容
    let html_content = fs::read_to_string(&html_path).expect("读取HTML文件失败");
    assert!(html_content.contains("测试项目"), "HTML内容不包含项目名称");
    assert!(
        html_content.contains("test_authentication"),
        "HTML内容不包含测试用例"
    );
    assert!(html_content.contains("Chart.js"), "HTML内容不包含图表库");

    println!("✅ HTML模板生成测试通过");
}

#[tokio::test]
async fn test_json_export() {
    let temp_dir = TempDir::new().expect("创建临时目录失败");
    let output_dir = temp_dir.path().to_str().unwrap();
    let config = create_test_config(output_dir);
    let test_report = create_test_report();

    let json_exporter = JsonExporter::new(config);

    // 测试完整格式导出
    let result = json_exporter.export_json(&test_report, None);
    assert!(result.is_ok(), "JSON导出失败: {:?}", result.err());

    // 验证JSON文件是否生成
    let json_path = Path::new(output_dir).join("test_report_full.json");
    assert!(json_path.exists(), "JSON报告文件不存在");

    // 验证JSON内容
    let json_content = fs::read_to_string(&json_path).expect("读取JSON文件失败");
    let parsed: serde_json::Value = serde_json::from_str(&json_content).expect("JSON解析失败");

    assert_eq!(parsed["project_name"], "测试项目");
    assert_eq!(parsed["summary"]["total_tests"], 3);
    assert_eq!(parsed["summary"]["passed_tests"], 1);

    // 测试多格式导出
    let multi_result = json_exporter.export_multiple_formats(&test_report);
    assert!(
        multi_result.is_ok(),
        "多格式JSON导出失败: {:?}",
        multi_result.err()
    );

    println!("✅ JSON导出测试通过");
}

#[tokio::test]
async fn test_chart_visualization() {
    let config = create_test_config("target/test-charts");
    let test_report = create_test_report();

    let chart_visualizer = ChartVisualizer::new(config);

    // 测试状态图表生成
    let status_chart = chart_visualizer.generate_status_chart(&test_report);
    assert!(
        status_chart.is_ok(),
        "状态图表生成失败: {:?}",
        status_chart.err()
    );

    let chart_js = status_chart.unwrap();
    assert!(chart_js.contains("Chart"), "图表代码不包含Chart.js");
    assert!(chart_js.contains("doughnut"), "图表代码不包含环形图类型");

    // 测试执行时间图表生成
    let duration_chart = chart_visualizer.generate_duration_chart(&test_report);
    assert!(
        duration_chart.is_ok(),
        "执行时间图表生成失败: {:?}",
        duration_chart.err()
    );

    // 测试覆盖率图表生成
    let coverage_chart = chart_visualizer.generate_coverage_chart(&test_report);
    assert!(
        coverage_chart.is_ok(),
        "覆盖率图表生成失败: {:?}",
        coverage_chart.err()
    );

    println!("✅ 图表可视化测试通过");
}

#[tokio::test]
async fn test_trend_analysis() {
    let temp_dir = TempDir::new().expect("创建临时目录失败");
    let output_dir = temp_dir.path().to_str().unwrap();
    let config = create_test_config(output_dir);
    let test_report = create_test_report();

    let trend_analyzer = TrendAnalyzer::new(config);

    // 保存历史数据
    let save_result = trend_analyzer.save_to_history(&test_report);
    assert!(
        save_result.is_ok(),
        "保存历史数据失败: {:?}",
        save_result.err()
    );

    // 分析趋势
    let trend_analysis = trend_analyzer.analyze_trends(None);
    assert!(
        trend_analysis.is_ok(),
        "趋势分析失败: {:?}",
        trend_analysis.err()
    );

    let analysis = trend_analysis.unwrap();
    assert_eq!(analysis.data_points.len(), 1, "历史数据点数量不正确");

    // 生成趋势报告
    let trend_report = trend_analyzer.generate_trend_report(&analysis);
    assert!(
        trend_report.is_ok(),
        "趋势报告生成失败: {:?}",
        trend_report.err()
    );

    let report_content = trend_report.unwrap();
    assert!(
        report_content.contains("测试趋势分析报告"),
        "趋势报告内容不正确"
    );

    println!("✅ 趋势分析测试通过");
}

#[tokio::test]
async fn test_complete_report_generation() {
    let temp_dir = TempDir::new().expect("创建临时目录失败");
    let output_dir = temp_dir.path().to_str().unwrap();
    let config = create_test_config(output_dir);

    let generator = TestReportGenerator::new(config.clone());

    // 测试执行选项
    let test_options = TestExecutionOptions {
        test_command: "echo 'mock test execution'".to_string(),
        working_dir: ".".to_string(),
        timeout_seconds: 30,
        retry_count: 1,
        parallel: false,
        env_vars: std::collections::HashMap::new(),
    };

    // 生成完整报告
    let result = generator.generate_complete_report(Some(test_options)).await;
    assert!(result.is_ok(), "完整报告生成失败: {:?}", result.err());

    let generation_result = result.unwrap();
    assert!(generation_result.success, "报告生成未成功");
    assert!(
        !generation_result.generated_files.is_empty(),
        "未生成任何文件"
    );

    // 验证生成的文件
    if config.generate_html {
        let html_path = Path::new(output_dir).join("test_report.html");
        assert!(html_path.exists(), "HTML报告文件不存在");
    }

    if config.generate_json {
        let json_path = Path::new(output_dir).join("test_report_full.json");
        assert!(json_path.exists(), "JSON报告文件不存在");
    }

    println!("✅ 完整报告生成测试通过");
}

#[tokio::test]
async fn test_report_validation() {
    let temp_dir = TempDir::new().expect("创建临时目录失败");
    let output_dir = temp_dir.path().to_str().unwrap();
    let config = create_test_config(output_dir);
    let test_report = create_test_report();

    // 先生成一些报告文件
    let html_generator = HtmlTemplateGenerator::new(config.clone());
    let json_exporter = JsonExporter::new(config.clone());

    let _ = html_generator.generate_html_report(&test_report);
    let _ = json_exporter.export_json(&test_report, None);

    // 验证报告
    let generator = TestReportGenerator::new(config);
    let validation_result = generator.validate_reports();

    assert!(
        validation_result.is_ok(),
        "报告验证失败: {:?}",
        validation_result.err()
    );
    assert!(validation_result.unwrap(), "报告验证未通过");

    println!("✅ 报告验证测试通过");
}

#[tokio::test]
async fn test_ci_integration_generation() {
    let temp_dir = TempDir::new().expect("创建临时目录失败");
    let output_dir = temp_dir.path().to_str().unwrap();
    let config = create_test_config(output_dir);

    let generator = TestReportGenerator::new(config);
    let result = generator.generate_ci_integration();

    assert!(result.is_ok(), "CI/CD集成脚本生成失败: {:?}", result.err());

    // 验证生成的文件
    let workflow_path = Path::new(output_dir).join(".github/workflows/test-report.yml");
    let jenkins_path = Path::new(output_dir).join("Jenkinsfile");
    let docker_path = Path::new(output_dir).join("Dockerfile.test-report");

    assert!(workflow_path.exists(), "GitHub Actions工作流文件不存在");
    assert!(jenkins_path.exists(), "Jenkins管道文件不存在");
    assert!(docker_path.exists(), "Dockerfile不存在");

    println!("✅ CI/CD集成生成测试通过");
}
