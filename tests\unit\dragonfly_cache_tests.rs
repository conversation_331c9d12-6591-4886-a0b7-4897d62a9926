//! # DragonflyDB缓存功能测试
//!
//! 测试DragonflyDB多级缓存、TTL管理、缓存预热等功能。
//! 严格遵循TDD原则，确保缓存系统的高性能和可靠性。

use std::time::{Duration, Instant};
use std::collections::HashMap;
use tokio_test;
use uuid::Uuid;
use serde_json;
use fred::prelude::*;
use anyhow::Result;

use crate::message_search_test_framework::{
    MessageSearchTestFramework, MessageSearchTestConfig, TestMessage
};

/// DragonflyDB缓存测试套件
pub struct DragonflyCacheTests {
    framework: MessageSearchTestFramework,
    redis_client: Option<RedisClient>,
    l1_cache: HashMap<String, (String, Instant)>, // 模拟L1内存缓存
}

impl DragonflyCacheTests {
    /// 创建新的缓存测试套件实例
    pub fn new() -> Self {
        let config = MessageSearchTestConfig::default();
        Self {
            framework: MessageSearchTestFramework::new(config),
            redis_client: None,
            l1_cache: HashMap::new(),
        }
    }

    /// 设置测试环境
    pub async fn setup(&mut self) -> Result<()> {
        tracing::info!("设置DragonflyDB缓存测试环境");
        
        // 初始化测试框架
        self.framework.initialize().await?;
        
        // 建立DragonflyDB连接
        let config = RedisConfig::from_url("redis://localhost:6379")?;
        let client = RedisClient::new(config, None, None, None);
        client.connect();
        client.wait_for_connect().await?;
        
        self.redis_client = Some(client);
        
        // 清理测试缓存
        self.cleanup_cache().await?;
        
        tracing::info!("DragonflyDB缓存测试环境设置完成");
        Ok(())
    }

    /// 清理测试缓存
    async fn cleanup_cache(&mut self) -> Result<()> {
        if let Some(client) = &self.redis_client {
            // 清理所有测试相关的键
            let _: () = client.flushdb(false).await?;
        }
        self.l1_cache.clear();
        Ok(())
    }

    /// 模拟L1缓存获取
    fn get_from_l1_cache(&self, key: &str) -> Option<String> {
        if let Some((value, timestamp)) = self.l1_cache.get(key) {
            // 检查是否过期（L1缓存TTL为1分钟）
            if timestamp.elapsed() < Duration::from_secs(60) {
                return Some(value.clone());
            }
        }
        None
    }

    /// 模拟L1缓存设置
    fn set_to_l1_cache(&mut self, key: String, value: String) {
        self.l1_cache.insert(key, (value, Instant::now()));
    }

    /// 从L2缓存（DragonflyDB）获取
    async fn get_from_l2_cache(&self, key: &str) -> Result<Option<String>> {
        if let Some(client) = &self.redis_client {
            let value: Option<String> = client.get(key).await?;
            Ok(value)
        } else {
            Ok(None)
        }
    }

    /// 设置到L2缓存（DragonflyDB）
    async fn set_to_l2_cache(&self, key: &str, value: &str, ttl_seconds: u64) -> Result<()> {
        if let Some(client) = &self.redis_client {
            let _: () = client.setex(key, ttl_seconds, value).await?;
        }
        Ok(())
    }

    /// 多级缓存获取逻辑
    async fn get_from_multilevel_cache(&mut self, key: &str) -> Result<Option<String>> {
        // 1. 尝试从L1缓存获取
        if let Some(value) = self.get_from_l1_cache(key) {
            tracing::debug!("L1缓存命中: {}", key);
            return Ok(Some(value));
        }

        // 2. 尝试从L2缓存获取
        if let Some(value) = self.get_from_l2_cache(key).await? {
            tracing::debug!("L2缓存命中: {}", key);
            // 回填到L1缓存
            self.set_to_l1_cache(key.to_string(), value.clone());
            return Ok(Some(value));
        }

        // 3. 缓存未命中
        tracing::debug!("缓存未命中: {}", key);
        Ok(None)
    }

    /// 多级缓存设置逻辑
    async fn set_to_multilevel_cache(&mut self, key: &str, value: &str, ttl_seconds: u64) -> Result<()> {
        // 设置到L1缓存
        self.set_to_l1_cache(key.to_string(), value.to_string());
        
        // 设置到L2缓存
        self.set_to_l2_cache(key, value, ttl_seconds).await?;
        
        Ok(())
    }
}

/// L1/L2缓存层级测试
#[tokio::test]
async fn test_l1_l2_cache_hierarchy() {
    tracing::info!("开始L1/L2缓存层级测试");
    
    let mut test_suite = DragonflyCacheTests::new();
    test_suite.setup().await.expect("测试环境设置失败");
    
    let test_key = "test:hierarchy:message:123";
    let test_value = r#"{"id":"123","content":"测试消息","sender":"user1"}"#;
    
    // 1. 设置缓存
    test_suite.set_to_multilevel_cache(test_key, test_value, 300).await
        .expect("设置多级缓存失败");
    
    // 2. 验证L1缓存命中
    let l1_result = test_suite.get_from_l1_cache(test_key);
    assert!(l1_result.is_some(), "L1缓存应该命中");
    assert_eq!(l1_result.unwrap(), test_value);
    
    // 3. 验证L2缓存命中
    let l2_result = test_suite.get_from_l2_cache(test_key).await
        .expect("L2缓存查询失败");
    assert!(l2_result.is_some(), "L2缓存应该命中");
    assert_eq!(l2_result.unwrap(), test_value);
    
    // 4. 清空L1缓存，测试L2回填
    test_suite.l1_cache.clear();
    let multilevel_result = test_suite.get_from_multilevel_cache(test_key).await
        .expect("多级缓存查询失败");
    assert!(multilevel_result.is_some(), "多级缓存应该命中");
    
    // 5. 验证L1缓存已回填
    let l1_refill_result = test_suite.get_from_l1_cache(test_key);
    assert!(l1_refill_result.is_some(), "L1缓存应该已回填");
    
    tracing::info!("L1/L2缓存层级测试完成");
}

/// 缓存TTL管理测试
#[tokio::test]
async fn test_cache_ttl_management() {
    tracing::info!("开始缓存TTL管理测试");
    
    let mut test_suite = DragonflyCacheTests::new();
    test_suite.setup().await.expect("测试环境设置失败");
    
    let client = test_suite.redis_client.as_ref().unwrap();
    
    // 1. 测试短TTL缓存
    let short_ttl_key = "test:ttl:short";
    let short_ttl_value = "短期缓存数据";
    let _: () = client.setex(short_ttl_key, 2, short_ttl_value).await
        .expect("设置短TTL缓存失败");
    
    // 验证缓存存在
    let result1: Option<String> = client.get(short_ttl_key).await
        .expect("获取短TTL缓存失败");
    assert!(result1.is_some(), "短TTL缓存应该存在");
    
    // 等待过期
    tokio::time::sleep(Duration::from_secs(3)).await;
    
    // 验证缓存已过期
    let result2: Option<String> = client.get(short_ttl_key).await
        .expect("获取过期缓存失败");
    assert!(result2.is_none(), "短TTL缓存应该已过期");
    
    // 2. 测试长TTL缓存
    let long_ttl_key = "test:ttl:long";
    let long_ttl_value = "长期缓存数据";
    let _: () = client.setex(long_ttl_key, 3600, long_ttl_value).await
        .expect("设置长TTL缓存失败");
    
    // 验证TTL设置正确
    let ttl: i64 = client.ttl(long_ttl_key).await
        .expect("获取TTL失败");
    assert!(ttl > 3500 && ttl <= 3600, "长TTL应该在合理范围内");
    
    // 3. 测试热点数据TTL延长
    let hotspot_key = "test:ttl:hotspot";
    let hotspot_value = "热点数据";
    let _: () = client.setex(hotspot_key, 600, hotspot_value).await
        .expect("设置热点缓存失败");
    
    // 模拟访问热点数据，延长TTL
    let _: Option<String> = client.get(hotspot_key).await
        .expect("访问热点数据失败");
    let _: () = client.expire(hotspot_key, 3600).await
        .expect("延长热点数据TTL失败");
    
    let extended_ttl: i64 = client.ttl(hotspot_key).await
        .expect("获取延长后TTL失败");
    assert!(extended_ttl > 3500, "热点数据TTL应该已延长");
    
    tracing::info!("缓存TTL管理测试完成");
}

/// 缓存预热策略测试
#[tokio::test]
async fn test_cache_warmup_strategy() {
    tracing::info!("开始缓存预热策略测试");
    
    let mut test_suite = DragonflyCacheTests::new();
    test_suite.setup().await.expect("测试环境设置失败");
    
    let client = test_suite.redis_client.as_ref().unwrap();
    
    // 1. 预热热门搜索词
    let popular_keywords = vec![
        ("Rust", "rust_search_results"),
        ("PostgreSQL", "postgres_search_results"),
        ("DragonflyDB", "dragonfly_search_results"),
        ("Axum", "axum_search_results"),
        ("TDD", "tdd_search_results"),
    ];
    
    let warmup_start = Instant::now();
    
    for (keyword, cache_key) in &popular_keywords {
        let search_results = format!(
            r#"{{"keyword":"{}","results":[{{"id":"{}","content":"关于{}的搜索结果"}}]}}"#,
            keyword, Uuid::new_v4(), keyword
        );
        
        let _: () = client.setex(
            &format!("search:warmup:{}", cache_key),
            3600, // 1小时TTL
            &search_results
        ).await.expect("预热缓存设置失败");
    }
    
    let warmup_elapsed = warmup_start.elapsed();
    
    // 验证预热性能：应在100ms内完成
    assert!(warmup_elapsed < Duration::from_millis(100), 
        "缓存预热性能不达标，耗时: {:?}", warmup_elapsed);
    
    // 2. 验证预热数据可访问
    for (keyword, cache_key) in &popular_keywords {
        let cached_result: Option<String> = client.get(
            &format!("search:warmup:{}", cache_key)
        ).await.expect("获取预热缓存失败");
        
        assert!(cached_result.is_some(), "预热缓存应该存在: {}", keyword);
        
        let result_json: serde_json::Value = serde_json::from_str(&cached_result.unwrap())
            .expect("预热缓存JSON解析失败");
        assert_eq!(result_json["keyword"], keyword);
    }
    
    // 3. 测试预热缓存的访问性能
    let access_start = Instant::now();
    
    for (_, cache_key) in &popular_keywords {
        let _: Option<String> = client.get(
            &format!("search:warmup:{}", cache_key)
        ).await.expect("访问预热缓存失败");
    }
    
    let access_elapsed = access_start.elapsed();
    let average_access_time = access_elapsed / popular_keywords.len() as u32;
    
    // 验证预热缓存访问性能：平均应在10ms内
    assert!(average_access_time < Duration::from_millis(10), 
        "预热缓存访问性能不达标，平均耗时: {:?}", average_access_time);
    
    tracing::info!("缓存预热策略测试完成，预热耗时: {:?}, 平均访问耗时: {:?}", 
        warmup_elapsed, average_access_time);
}

/// 缓存失效机制测试
#[tokio::test]
async fn test_cache_invalidation() {
    tracing::info!("开始缓存失效机制测试");
    
    let mut test_suite = DragonflyCacheTests::new();
    test_suite.setup().await.expect("测试环境设置失败");
    
    let client = test_suite.redis_client.as_ref().unwrap();
    
    // 1. 设置消息相关缓存
    let message_id = Uuid::new_v4();
    let chat_room_id = Uuid::new_v4();
    
    let message_cache_key = format!("message:{}", message_id);
    let room_cache_key = format!("room:{}:messages", chat_room_id);
    let search_cache_key = "search:latest:messages";
    
    // 设置相关缓存
    let _: () = client.setex(&message_cache_key, 3600, "消息内容").await
        .expect("设置消息缓存失败");
    let _: () = client.setex(&room_cache_key, 3600, "聊天室消息列表").await
        .expect("设置聊天室缓存失败");
    let _: () = client.setex(search_cache_key, 3600, "搜索结果缓存").await
        .expect("设置搜索缓存失败");
    
    // 验证缓存存在
    let message_exists: bool = client.exists(&message_cache_key).await
        .expect("检查消息缓存存在性失败");
    assert!(message_exists, "消息缓存应该存在");
    
    // 2. 模拟消息更新，触发缓存失效
    let invalidation_start = Instant::now();
    
    // 删除相关缓存
    let invalidation_keys = vec![
        message_cache_key.clone(),
        room_cache_key.clone(),
        search_cache_key.to_string(),
    ];
    
    let _: u64 = client.del(&invalidation_keys).await
        .expect("缓存失效操作失败");
    
    let invalidation_elapsed = invalidation_start.elapsed();
    
    // 验证失效性能：应在50ms内完成
    assert!(invalidation_elapsed < Duration::from_millis(50), 
        "缓存失效性能不达标，耗时: {:?}", invalidation_elapsed);
    
    // 3. 验证缓存已失效
    let message_exists_after: bool = client.exists(&message_cache_key).await
        .expect("检查消息缓存失效失败");
    assert!(!message_exists_after, "消息缓存应该已失效");
    
    let room_exists_after: bool = client.exists(&room_cache_key).await
        .expect("检查聊天室缓存失效失败");
    assert!(!room_exists_after, "聊天室缓存应该已失效");
    
    let search_exists_after: bool = client.exists(search_cache_key).await
        .expect("检查搜索缓存失效失败");
    assert!(!search_exists_after, "搜索缓存应该已失效");
    
    tracing::info!("缓存失效机制测试完成，失效耗时: {:?}", invalidation_elapsed);
}

/// 缓存命中率统计测试
#[tokio::test]
async fn test_cache_hit_ratio_statistics() {
    tracing::info!("开始缓存命中率统计测试");
    
    let mut test_suite = DragonflyCacheTests::new();
    test_suite.setup().await.expect("测试环境设置失败");
    
    let client = test_suite.redis_client.as_ref().unwrap();
    
    // 1. 预设一些缓存数据
    let cache_keys = vec![
        ("cache:hit:1", "数据1"),
        ("cache:hit:2", "数据2"),
        ("cache:hit:3", "数据3"),
    ];
    
    for (key, value) in &cache_keys {
        let _: () = client.setex(key, 3600, value).await
            .expect("设置测试缓存失败");
    }
    
    // 2. 模拟缓存访问，统计命中率
    let mut total_requests = 0;
    let mut cache_hits = 0;
    
    // 访问存在的缓存（命中）
    for (key, _) in &cache_keys {
        total_requests += 1;
        let result: Option<String> = client.get(key).await
            .expect("访问缓存失败");
        if result.is_some() {
            cache_hits += 1;
        }
    }
    
    // 访问不存在的缓存（未命中）
    let miss_keys = vec!["cache:miss:1", "cache:miss:2"];
    for key in &miss_keys {
        total_requests += 1;
        let result: Option<String> = client.get(key).await
            .expect("访问不存在缓存失败");
        if result.is_some() {
            cache_hits += 1;
        }
    }
    
    // 3. 计算命中率
    let hit_ratio = cache_hits as f64 / total_requests as f64;
    
    // 验证命中率计算正确
    let expected_hit_ratio = cache_keys.len() as f64 / total_requests as f64;
    assert!((hit_ratio - expected_hit_ratio).abs() < 0.01, 
        "缓存命中率计算错误，期望: {}, 实际: {}", expected_hit_ratio, hit_ratio);
    
    // 验证命中率达到预期（60%，因为有3个命中，2个未命中）
    assert!(hit_ratio >= 0.6, "缓存命中率应该达到60%，实际: {:.2}%", hit_ratio * 100.0);
    
    tracing::info!("缓存命中率统计测试完成，命中率: {:.2}%", hit_ratio * 100.0);
}

/// 高并发缓存访问测试
#[tokio::test]
async fn test_concurrent_cache_access() {
    tracing::info!("开始高并发缓存访问测试");
    
    let mut test_suite = DragonflyCacheTests::new();
    test_suite.setup().await.expect("测试环境设置失败");
    
    let client = test_suite.redis_client.as_ref().unwrap();
    
    // 1. 预设缓存数据
    let cache_key = "test:concurrent:access";
    let cache_value = "并发访问测试数据";
    let _: () = client.setex(cache_key, 3600, cache_value).await
        .expect("设置并发测试缓存失败");
    
    // 2. 创建并发访问任务
    let concurrent_count = 100;
    let mut tasks = Vec::new();
    
    for i in 0..concurrent_count {
        let client_clone = client.clone();
        let key = cache_key.to_string();
        
        let task = tokio::spawn(async move {
            let start_time = Instant::now();
            
            let result: Result<Option<String>, _> = client_clone.get(&key).await;
            
            let elapsed = start_time.elapsed();
            (i, result.is_ok() && result.unwrap().is_some(), elapsed)
        });
        
        tasks.push(task);
    }
    
    // 3. 等待所有任务完成
    let results = futures::future::join_all(tasks).await;
    
    // 4. 统计结果
    let mut success_count = 0;
    let mut total_elapsed = Duration::from_millis(0);
    
    for result in results {
        let (_, success, elapsed) = result.expect("并发任务执行失败");
        if success {
            success_count += 1;
        }
        total_elapsed += elapsed;
    }
    
    let average_elapsed = total_elapsed / concurrent_count as u32;
    let success_ratio = success_count as f64 / concurrent_count as f64;
    
    // 5. 验证并发访问结果
    assert_eq!(success_count, concurrent_count, "所有并发访问都应该成功");
    assert!(success_ratio >= 0.99, "并发访问成功率应该达到99%");
    assert!(average_elapsed < Duration::from_millis(50), 
        "并发访问平均响应时间不达标，平均耗时: {:?}", average_elapsed);
    
    tracing::info!("高并发缓存访问测试完成，成功率: {:.2}%, 平均耗时: {:?}", 
        success_ratio * 100.0, average_elapsed);
}
