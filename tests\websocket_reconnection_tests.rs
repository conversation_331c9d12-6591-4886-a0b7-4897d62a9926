//! # WebSocket重连机制测试
//!
//! 专门测试WebSocket连接中断后的重连机制和错误处理
//! 验证"Connection reset without closing handshake"错误的处理

use anyhow::Result;
use futures_util::{SinkExt, StreamExt};
use reqwest::Client;
use serde_json::{Value, json};
use std::time::Duration;
use tokio::time::{sleep, timeout};
use tokio_tungstenite::{connect_async, tungstenite::Message as TungsteniteMessage};

/// 测试配置
const SERVER_URL: &str = "127.0.0.1:3000";
const WS_URL: &str = "ws://127.0.0.1:3000/ws";
const TEST_TIMEOUT: Duration = Duration::from_secs(30);

/// 测试用户凭据
const TEST_USER_USERNAME: &str = "testuser456";
const TEST_USER_PASSWORD: &str = "password123";

/// WebSocket重连测试辅助结构
struct WebSocketReconnectionHelper {
    client: Client,
    jwt_token: Option<String>,
}

impl WebSocketReconnectionHelper {
    /// 创建新的测试辅助实例
    fn new() -> Self {
        Self {
            client: Client::new(),
            jwt_token: None,
        }
    }

    /// 用户登录并获取JWT token
    async fn login(&mut self) -> Result<String> {
        let login_url = format!("http://{}/api/auth/login", SERVER_URL);
        let login_data = json!({
            "username": TEST_USER_USERNAME,
            "password": TEST_USER_PASSWORD
        });

        let response = self
            .client
            .post(&login_url)
            .json(&login_data)
            .send()
            .await?;

        if !response.status().is_success() {
            anyhow::bail!("登录失败: {}", response.status());
        }

        let response_json: Value = response.json().await?;
        println!(
            "🔍 登录响应: {}",
            serde_json::to_string_pretty(&response_json)?
        );

        // 尝试不同的响应格式
        let token = if let Some(token) = response_json["data"]["access_token"].as_str() {
            token.to_string()
        } else if let Some(token) = response_json["token"].as_str() {
            token.to_string()
        } else {
            anyhow::bail!(
                "响应中缺少token字段，响应格式: {}",
                serde_json::to_string_pretty(&response_json)?
            );
        };

        self.jwt_token = Some(token.clone());
        Ok(token)
    }

    /// 建立WebSocket连接
    async fn connect_websocket(
        &self,
    ) -> Result<
        tokio_tungstenite::WebSocketStream<
            tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>,
        >,
    > {
        let token = self
            .jwt_token
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("需要先登录获取JWT token"))?;

        let ws_url_with_token = format!("{}?token={}", WS_URL, token);

        let (ws_stream, _) = connect_async(&ws_url_with_token).await?;
        Ok(ws_stream)
    }

    /// 发送文本消息
    async fn send_text_message(
        &self,
        ws_stream: &mut tokio_tungstenite::WebSocketStream<
            tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>,
        >,
        message: &str,
    ) -> Result<()> {
        ws_stream
            .send(TungsteniteMessage::Text(message.to_string().into()))
            .await?;
        Ok(())
    }

    /// 接收消息（带超时）
    async fn receive_message_with_timeout(
        &self,
        ws_stream: &mut tokio_tungstenite::WebSocketStream<
            tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>,
        >,
        timeout_duration: Duration,
    ) -> Result<Option<TungsteniteMessage>> {
        match timeout(timeout_duration, ws_stream.next()).await {
            Ok(Some(Ok(message))) => Ok(Some(message)),
            Ok(Some(Err(e))) => Err(e.into()),
            Ok(None) => Ok(None),
            Err(_) => Ok(None), // 超时
        }
    }
}

/// 测试1: WebSocket连接中断恢复测试
async fn test_websocket_connection_interruption_recovery() -> Result<()> {
    println!("🔧 开始测试: WebSocket连接中断恢复");

    let mut helper = WebSocketReconnectionHelper::new();

    // 步骤1: 建立初始连接
    println!("📝 步骤1: 建立初始WebSocket连接");
    helper.login().await?;
    let mut ws_stream = helper.connect_websocket().await?;
    println!("✅ 初始连接建立成功");

    // 步骤2: 发送消息验证连接正常
    println!("📝 步骤2: 验证初始连接正常工作");
    helper
        .send_text_message(&mut ws_stream, "连接测试消息")
        .await?;
    println!("✅ 初始连接消息发送成功");

    // 步骤3: 模拟连接中断（强制关闭）
    println!("📝 步骤3: 模拟连接中断");
    drop(ws_stream); // 强制关闭连接，模拟网络中断
    println!("✅ 连接已中断");

    // 步骤4: 等待一段时间模拟网络恢复
    println!("📝 步骤4: 等待网络恢复");
    sleep(Duration::from_secs(2)).await;

    // 步骤5: 重新建立连接
    println!("📝 步骤5: 重新建立WebSocket连接");
    let mut new_ws_stream = helper.connect_websocket().await?;
    println!("✅ 重连成功");

    // 步骤6: 验证重连后的连接正常工作
    println!("📝 步骤6: 验证重连后连接正常工作");
    helper
        .send_text_message(&mut new_ws_stream, "重连后测试消息")
        .await?;
    println!("✅ 重连后消息发送成功");

    println!("🎉 WebSocket连接中断恢复测试通过");
    Ok(())
}

/// 测试2: WebSocket心跳机制测试
async fn test_websocket_heartbeat_mechanism() -> Result<()> {
    println!("🔧 开始测试: WebSocket心跳机制");

    let mut helper = WebSocketReconnectionHelper::new();

    // 步骤1: 建立连接
    println!("📝 步骤1: 建立WebSocket连接");
    helper.login().await?;
    let mut ws_stream = helper.connect_websocket().await?;
    println!("✅ 连接建立成功");

    // 步骤2: 等待并监听心跳消息
    println!("📝 步骤2: 监听心跳Ping消息");
    let mut ping_received = false;

    // 等待最多35秒来接收心跳ping（服务器每30秒发送一次）
    for _ in 0..7 {
        if let Some(message) = helper
            .receive_message_with_timeout(&mut ws_stream, Duration::from_secs(5))
            .await?
        {
            match message {
                TungsteniteMessage::Ping(_) => {
                    println!("✅ 收到心跳Ping消息");
                    ping_received = true;
                    break;
                }
                TungsteniteMessage::Pong(_) => {
                    println!("📡 收到Pong响应");
                }
                TungsteniteMessage::Text(text) => {
                    println!("📨 收到文本消息: {}", text);
                }
                _ => {
                    println!("📦 收到其他类型消息");
                }
            }
        }
    }

    if ping_received {
        println!("✅ 心跳机制正常工作");
    } else {
        println!("⚠️ 未在预期时间内收到心跳Ping消息");
    }

    // 步骤3: 发送Ping测试Pong响应
    println!("📝 步骤3: 发送Ping测试Pong响应");
    ws_stream
        .send(TungsteniteMessage::Ping(vec![1, 2, 3, 4].into()))
        .await?;

    if let Some(message) = helper
        .receive_message_with_timeout(&mut ws_stream, Duration::from_secs(5))
        .await?
    {
        match message {
            TungsteniteMessage::Pong(data) => {
                assert_eq!(data, vec![1, 2, 3, 4], "Pong数据应该与Ping数据匹配");
                println!("✅ Ping/Pong机制正常工作");
            }
            _ => {
                println!("⚠️ 收到非Pong消息，可能是其他类型的响应");
            }
        }
    }

    println!("🎉 WebSocket心跳机制测试通过");
    Ok(())
}

/// 测试3: 错误处理机制测试
async fn test_websocket_error_handling() -> Result<()> {
    println!("🔧 开始测试: WebSocket错误处理机制");

    let mut helper = WebSocketReconnectionHelper::new();

    // 步骤1: 测试无效token连接
    println!("📝 步骤1: 测试无效token连接处理");
    let invalid_token_url = format!("{}?token=invalid_token_12345", WS_URL);

    match connect_async(&invalid_token_url).await {
        Err(e) => {
            println!("✅ 无效token连接被正确拒绝: {}", e);
        }
        Ok((mut ws_stream, _)) => {
            // 如果连接成功，应该立即收到关闭消息
            if let Some(Ok(message)) = ws_stream.next().await {
                match message {
                    TungsteniteMessage::Close(close_frame) => {
                        println!("✅ 无效token连接被正确关闭: {:?}", close_frame);
                    }
                    _ => {
                        println!("⚠️ 期望收到关闭消息，但收到其他消息");
                    }
                }
            }
        }
    }

    // 步骤2: 测试正常连接的错误恢复
    println!("📝 步骤2: 测试正常连接的错误恢复");
    helper.login().await?;
    let mut ws_stream = helper.connect_websocket().await?;

    // 发送正常消息
    helper
        .send_text_message(&mut ws_stream, "正常消息测试")
        .await?;
    println!("✅ 正常消息发送成功");

    // 等待一段时间确保消息被处理
    sleep(Duration::from_millis(100)).await;

    println!("🎉 WebSocket错误处理机制测试通过");
    Ok(())
}

/// 运行所有WebSocket重连测试
async fn run_all_websocket_reconnection_tests() -> Result<()> {
    println!("🚀 开始运行WebSocket重连机制测试套件");

    // 等待服务器启动
    sleep(Duration::from_secs(2)).await;

    // 运行所有测试
    println!("\n=== 测试1: 连接中断恢复 ===");
    test_websocket_connection_interruption_recovery().await?;

    println!("\n=== 测试2: 心跳机制 ===");
    test_websocket_heartbeat_mechanism().await?;

    println!("\n=== 测试3: 错误处理机制 ===");
    test_websocket_error_handling().await?;

    println!("\n🎉 所有WebSocket重连机制测试通过！");
    Ok(())
}

/// 主函数 - 用于二进制执行
#[tokio::main]
async fn main() -> Result<()> {
    run_all_websocket_reconnection_tests().await
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_websocket_reconnection_suite() -> Result<()> {
        run_all_websocket_reconnection_tests().await
    }
}
