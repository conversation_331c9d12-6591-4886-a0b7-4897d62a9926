/**
 * 简单性能测试 - 验证前端性能优化效果
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-28
 */

// 简单的性能测试，不依赖Playwright
console.log('🚀 开始前端性能优化验证...');

// 1. 检查Service Worker文件
const fs = require('fs');
const path = require('path');

function checkFileExists(filePath) {
    try {
        const fullPath = path.join(__dirname, '..', filePath);
        const exists = fs.existsSync(fullPath);
        console.log(`${exists ? '✅' : '❌'} ${filePath}: ${exists ? '存在' : '不存在'}`);
        return exists;
    } catch (error) {
        console.log(`❌ ${filePath}: 检查失败 - ${error.message}`);
        return false;
    }
}

function checkFileSize(filePath, maxSizeKB) {
    try {
        const fullPath = path.join(__dirname, '..', filePath);
        if (!fs.existsSync(fullPath)) {
            console.log(`❌ ${filePath}: 文件不存在`);
            return false;
        }
        
        const stats = fs.statSync(fullPath);
        const sizeKB = Math.round(stats.size / 1024);
        const passed = sizeKB <= maxSizeKB;
        
        console.log(`${passed ? '✅' : '❌'} ${filePath}: ${sizeKB}KB ${passed ? '≤' : '>'} ${maxSizeKB}KB`);
        return passed;
    } catch (error) {
        console.log(`❌ ${filePath}: 大小检查失败 - ${error.message}`);
        return false;
    }
}

function checkFileContent(filePath, requiredContent) {
    try {
        const fullPath = path.join(__dirname, '..', filePath);
        if (!fs.existsSync(fullPath)) {
            console.log(`❌ ${filePath}: 文件不存在`);
            return false;
        }
        
        const content = fs.readFileSync(fullPath, 'utf8');
        const hasContent = requiredContent.every(item => content.includes(item));
        
        console.log(`${hasContent ? '✅' : '❌'} ${filePath}: ${hasContent ? '包含必要内容' : '缺少必要内容'}`);
        if (!hasContent) {
            const missing = requiredContent.filter(item => !content.includes(item));
            console.log(`   缺少: ${missing.join(', ')}`);
        }
        return hasContent;
    } catch (error) {
        console.log(`❌ ${filePath}: 内容检查失败 - ${error.message}`);
        return false;
    }
}

// 测试结果统计
let totalTests = 0;
let passedTests = 0;

function runTest(testName, testFunction) {
    totalTests++;
    console.log(`\n📋 测试: ${testName}`);
    try {
        const result = testFunction();
        if (result) {
            passedTests++;
            console.log(`✅ ${testName}: 通过`);
        } else {
            console.log(`❌ ${testName}: 失败`);
        }
        return result;
    } catch (error) {
        console.log(`❌ ${testName}: 异常 - ${error.message}`);
        return false;
    }
}

// 开始测试
console.log('\n🔍 检查性能优化文件...');

// 测试1: 检查关键文件是否存在
runTest('关键文件存在性检查', () => {
    const files = [
        'static/sw.js',
        'static/manifest.json',
        'static/js/modules/performance-optimizer.js',
        'lighthouse.config.js',
        'performance-budget.json'
    ];
    
    return files.every(file => checkFileExists(file));
});

// 测试2: 检查文件大小
runTest('文件大小检查', () => {
    const checks = [
        ['static/sw.js', 50],  // Service Worker应该小于50KB
        ['static/manifest.json', 10],  // Manifest应该小于10KB
        ['static/js/modules/performance-optimizer.js', 100],  // 性能优化器应该小于100KB
        ['lighthouse.config.js', 20],  // Lighthouse配置应该小于20KB
        ['performance-budget.json', 5]  // 性能预算应该小于5KB
    ];
    
    return checks.every(([file, maxSize]) => checkFileSize(file, maxSize));
});

// 测试3: 检查Service Worker内容
runTest('Service Worker功能检查', () => {
    return checkFileContent('static/sw.js', [
        'Cache First',
        'Network First',
        'Stale While Revalidate',
        'addEventListener',
        'caches.open',
        'fetch'
    ]);
});

// 测试4: 检查性能优化器内容
runTest('性能优化器功能检查', () => {
    return checkFileContent('static/js/modules/performance-optimizer.js', [
        'PerformanceMonitor',
        'ResourcePreloader',
        'LazyLoader',
        'CacheManager',
        'PerformanceObserver',
        'IntersectionObserver'
    ]);
});

// 测试5: 检查HTML优化
runTest('HTML性能优化检查', () => {
    return checkFileContent('static/index.html', [
        'rel="preload"',
        'rel="preconnect"',
        'rel="manifest"',
        'serviceWorker',
        'performance'
    ]);
});

// 测试6: 检查PWA Manifest
runTest('PWA Manifest配置检查', () => {
    return checkFileContent('static/manifest.json', [
        '"name"',
        '"short_name"',
        '"start_url"',
        '"display"',
        '"theme_color"',
        '"icons"',
        '"shortcuts"'
    ]);
});

// 测试7: 检查Lighthouse配置
runTest('Lighthouse配置检查', () => {
    return checkFileContent('lighthouse.config.js', [
        'performance',
        'accessibility',
        'best-practices',
        'seo',
        'pwa',
        'budgets',
        'first-contentful-paint',
        'largest-contentful-paint'
    ]);
});

// 测试8: 检查性能预算
runTest('性能预算配置检查', () => {
    return checkFileContent('performance-budget.json', [
        'resourceSizes',
        'resourceCounts',
        'timings',
        'first-contentful-paint',
        'largest-contentful-paint',
        'script',
        'stylesheet'
    ]);
});

// 输出测试结果
console.log('\n📊 测试结果汇总:');
console.log(`总测试数: ${totalTests}`);
console.log(`通过测试: ${passedTests}`);
console.log(`失败测试: ${totalTests - passedTests}`);
console.log(`通过率: ${Math.round((passedTests / totalTests) * 100)}%`);

if (passedTests === totalTests) {
    console.log('\n🎉 所有性能优化测试通过！');
    console.log('✅ 前端性能优化实施成功');
    process.exit(0);
} else {
    console.log('\n⚠️ 部分测试失败，请检查上述错误');
    process.exit(1);
}
