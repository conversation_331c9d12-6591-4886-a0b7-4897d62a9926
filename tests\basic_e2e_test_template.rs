// E2E测试基础模板
// 这个模板提供了编写E2E测试的标准结构和最佳实践

use anyhow::Result;
use serde_json::{Value, json};
use std::time::Duration;
use tokio::time::sleep;

// 引入E2E测试辅助模块
// 注意：在实际使用中，这些模块应该从 tests/e2e/helpers/ 目录引入
mod helpers {
    // pub mod auth;
    // pub mod api;
    // pub mod database;
    // pub mod playwright;

    use anyhow::Result;
    use serde_json::Value;
    use std::env;
    use std::path::PathBuf;

    /// E2E测试配置结构
    #[derive(Debug, Clone)]
    pub struct E2EConfig {
        pub server_host: String,
        pub server_port: u16,
        pub base_url: String,
        pub ws_url: String,
        pub test_username: String,
        pub test_password: String,
        pub test_email: String,
        pub database_url: String,
        pub test_database_url: String,
        pub test_timeout: u64,
        pub retry_attempts: u32,
        pub playwright_headless: bool,
        pub playwright_slow_mo: u64,
        pub viewport_width: u32,
        pub viewport_height: u32,
        pub report_dir: PathBuf,
        pub screenshot_dir: PathBuf,
        pub video_dir: PathBuf,
    }

    impl Default for E2EConfig {
        fn default() -> Self {
            Self {
                server_host: "127.0.0.1".to_string(),
                server_port: 3000,
                base_url: "http://127.0.0.1:3000".to_string(),
                ws_url: "ws://127.0.0.1:3000/ws".to_string(),
                test_username: "testuser456".to_string(),
                test_password: "password123".to_string(),
                test_email: "<EMAIL>".to_string(),
                database_url: "sqlite:./task_manager.db".to_string(),
                test_database_url: "sqlite:./test_task_manager.db".to_string(),
                test_timeout: 30000,
                retry_attempts: 3,
                playwright_headless: false,
                playwright_slow_mo: 100,
                viewport_width: 1280,
                viewport_height: 720,
                report_dir: PathBuf::from("./tests/e2e/reports"),
                screenshot_dir: PathBuf::from("./tests/e2e/reports/screenshots"),
                video_dir: PathBuf::from("./tests/e2e/reports/videos"),
            }
        }
    }

    impl E2EConfig {
        /// 从环境变量加载配置
        pub fn from_env() -> Result<Self> {
            // 加载.env文件
            if let Ok(_) = dotenvy::from_filename("tests/e2e/config/test.env") {
                println!("已加载E2E测试环境配置");
            }

            let mut config = Self::default();

            // 从环境变量覆盖默认值
            if let Ok(host) = env::var("SERVER_HOST") {
                config.server_host = host;
            }
            if let Ok(port) = env::var("SERVER_PORT") {
                config.server_port = port.parse().unwrap_or(3000);
            }
            if let Ok(base_url) = env::var("BASE_URL") {
                config.base_url = base_url;
            }
            if let Ok(ws_url) = env::var("WS_URL") {
                config.ws_url = ws_url;
            }
            if let Ok(username) = env::var("TEST_USERNAME") {
                config.test_username = username;
            }
            if let Ok(password) = env::var("TEST_PASSWORD") {
                config.test_password = password;
            }
            if let Ok(email) = env::var("TEST_EMAIL") {
                config.test_email = email;
            }

            Ok(config)
        }
    }

    /// 加载测试夹具数据
    pub fn load_fixture(fixture_name: &str) -> Result<Value> {
        let fixture_path = format!("tests/e2e/fixtures/{}.json", fixture_name);
        let content = std::fs::read_to_string(&fixture_path)
            .map_err(|e| anyhow::anyhow!("无法读取夹具文件 {}: {}", fixture_path, e))?;

        let data: Value = serde_json::from_str(&content)
            .map_err(|e| anyhow::anyhow!("无法解析夹具文件 {}: {}", fixture_path, e))?;

        Ok(data)
    }

    /// 确保目录存在
    pub fn ensure_dir_exists(path: &PathBuf) -> Result<()> {
        if !path.exists() {
            std::fs::create_dir_all(path)
                .map_err(|e| anyhow::anyhow!("无法创建目录 {:?}: {}", path, e))?;
        }
        Ok(())
    }
}

use helpers::{E2EConfig, ensure_dir_exists, load_fixture};

/// 基础E2E测试模板 - 测试环境准备
#[tokio::test]
async fn template_test_environment_setup() -> Result<()> {
    println!("🚀 E2E测试模板 - 环境准备");

    // 1. 加载配置
    let config = E2EConfig::from_env()?;
    println!("✅ 配置加载完成: {}", config.base_url);

    // 2. 确保目录存在
    ensure_dir_exists(&config.report_dir)?;
    ensure_dir_exists(&config.screenshot_dir)?;
    ensure_dir_exists(&config.video_dir)?;
    println!("✅ 目录结构验证完成");

    // 3. 加载测试数据
    let users_fixture = load_fixture("users")?;
    let tasks_fixture = load_fixture("tasks")?;
    println!("✅ 测试数据加载完成");

    // 4. 验证测试数据
    assert!(
        users_fixture.get("testUsers").is_some(),
        "用户测试数据应该存在"
    );
    assert!(
        tasks_fixture.get("testTasks").is_some(),
        "任务测试数据应该存在"
    );
    println!("✅ 测试数据验证完成");

    println!("🎉 环境准备测试模板执行成功");
    Ok(())
}

/// 基础E2E测试模板 - API测试
#[tokio::test]
async fn template_api_test() -> Result<()> {
    println!("🚀 E2E测试模板 - API测试");

    let config = E2EConfig::from_env()?;
    let client = reqwest::Client::new();

    // 1. 健康检查
    let health_url = format!("{}/health", config.base_url);
    let response = client.get(&health_url).send().await;

    match response {
        Ok(resp) => {
            println!("✅ 健康检查成功: {}", resp.status());
            if resp.status().is_success() {
                let body: Value = resp.json().await?;
                println!("响应内容: {}", serde_json::to_string_pretty(&body)?);
            }
        }
        Err(e) => {
            println!("⚠️  健康检查失败: {}", e);
            println!("请确保服务器正在运行: cargo run -p server");
            // 在模板中，我们不让测试失败，而是给出提示
            return Ok(());
        }
    }

    // 2. API版本检查（如果有的话）
    let version_url = format!("{}/api/version", config.base_url);
    if let Ok(resp) = client.get(&version_url).send().await {
        if resp.status().is_success() {
            let body: Value = resp.json().await?;
            println!("✅ API版本信息: {}", serde_json::to_string_pretty(&body)?);
        }
    }

    println!("🎉 API测试模板执行成功");
    Ok(())
}

/// 基础E2E测试模板 - 认证流程测试
#[tokio::test]
async fn template_authentication_test() -> Result<()> {
    println!("🚀 E2E测试模板 - 认证流程测试");

    let config = E2EConfig::from_env()?;
    let client = reqwest::Client::new();

    // 加载用户测试数据
    let users_fixture = load_fixture("users")?;
    let test_users = users_fixture["testUsers"].as_array().unwrap();
    let test_user = &test_users[0];

    let username = test_user["username"].as_str().unwrap();
    let password = test_user["password"].as_str().unwrap();
    let email = test_user["email"].as_str().unwrap();

    // 1. 用户注册测试
    let register_url = format!("{}/api/auth/register", config.base_url);
    let register_payload = json!({
        "username": username,
        "email": email,
        "password": password
    });

    let register_response = client
        .post(&register_url)
        .json(&register_payload)
        .send()
        .await;

    match register_response {
        Ok(resp) => {
            println!("✅ 注册请求发送成功: {}", resp.status());
            // 注意：在实际测试中，可能需要处理用户已存在的情况
        }
        Err(e) => {
            println!("⚠️  注册请求失败: {}", e);
            return Ok(());
        }
    }

    // 2. 用户登录测试
    let login_url = format!("{}/api/auth/login", config.base_url);
    let login_payload = json!({
        "username": username,
        "password": password
    });

    let login_response = client.post(&login_url).json(&login_payload).send().await;

    match login_response {
        Ok(resp) => {
            println!("✅ 登录请求发送成功: {}", resp.status());
            if resp.status().is_success() {
                let body: Value = resp.json().await?;
                if let Some(token) = body.get("token") {
                    println!("✅ 获取到JWT令牌: {}", token);
                }
            }
        }
        Err(e) => {
            println!("⚠️  登录请求失败: {}", e);
        }
    }

    println!("🎉 认证流程测试模板执行成功");
    Ok(())
}

/// 基础E2E测试模板 - 性能测试
#[tokio::test]
async fn template_performance_test() -> Result<()> {
    println!("🚀 E2E测试模板 - 性能测试");

    let config = E2EConfig::from_env()?;
    let client = reqwest::Client::new();

    // 测试API响应时间
    let health_url = format!("{}/health", config.base_url);
    let iterations = 5;
    let mut response_times = Vec::new();

    for i in 0..iterations {
        let start = std::time::Instant::now();

        match client.get(&health_url).send().await {
            Ok(resp) => {
                let duration = start.elapsed();
                response_times.push(duration);
                println!(
                    "第{}次请求: {}ms, 状态: {}",
                    i + 1,
                    duration.as_millis(),
                    resp.status()
                );
            }
            Err(e) => {
                println!("⚠️  第{}次请求失败: {}", i + 1, e);
                return Ok(());
            }
        }

        // 请求间隔
        sleep(Duration::from_millis(100)).await;
    }

    // 计算性能指标
    if !response_times.is_empty() {
        let total_time: Duration = response_times.iter().sum();
        let avg_time = total_time / (response_times.len() as u32);
        let min_time = response_times.iter().min().unwrap();
        let max_time = response_times.iter().max().unwrap();

        println!("📊 性能统计:");
        println!("   平均响应时间: {}ms", avg_time.as_millis());
        println!("   最快响应时间: {}ms", min_time.as_millis());
        println!("   最慢响应时间: {}ms", max_time.as_millis());

        // 性能断言（可根据需要调整）
        // 如果服务器没有运行，响应时间会很长，这是正常的
        if avg_time > Duration::from_millis(2000) {
            println!("⚠️  响应时间过长，可能服务器未运行");
        } else {
            assert!(
                avg_time < Duration::from_millis(1000),
                "平均响应时间应该小于1秒"
            );
        }
    }

    println!("🎉 性能测试模板执行成功");
    Ok(())
}

/// 基础E2E测试模板 - 错误处理测试
#[tokio::test]
async fn template_error_handling_test() -> Result<()> {
    println!("🚀 E2E测试模板 - 错误处理测试");

    let config = E2EConfig::from_env()?;
    let client = reqwest::Client::new();

    // 1. 测试不存在的端点
    let invalid_url = format!("{}/api/nonexistent", config.base_url);
    match client.get(&invalid_url).send().await {
        Ok(resp) => {
            println!("✅ 无效端点响应: {}", resp.status());
            // 如果服务器没有运行，会返回502，这是正常的
            if resp.status().as_u16() == 502 {
                println!("⚠️  服务器未运行，跳过错误处理测试");
                return Ok(());
            }
            assert!(resp.status().is_client_error(), "应该返回4xx错误");
        }
        Err(e) => {
            println!("⚠️  请求失败: {}", e);
            return Ok(());
        }
    }

    // 2. 测试无效的JSON数据
    let login_url = format!("{}/api/auth/login", config.base_url);
    let invalid_payload = "invalid json";

    match client
        .post(&login_url)
        .header("Content-Type", "application/json")
        .body(invalid_payload)
        .send()
        .await
    {
        Ok(resp) => {
            println!("✅ 无效JSON响应: {}", resp.status());
            assert!(resp.status().is_client_error(), "应该返回4xx错误");
        }
        Err(e) => {
            println!("⚠️  请求失败: {}", e);
        }
    }

    println!("🎉 错误处理测试模板执行成功");
    Ok(())
}

/// 集成测试模板 - 完整的E2E测试流程
#[tokio::test]
async fn template_complete_e2e_flow() -> Result<()> {
    println!("🚀 完整E2E测试流程模板");

    // 1. 环境准备
    let config = E2EConfig::from_env()?;
    println!("✅ 步骤1: 环境配置加载完成");

    // 2. 服务健康检查
    let client = reqwest::Client::new();
    let health_url = format!("{}/health", config.base_url);

    match client.get(&health_url).send().await {
        Ok(resp) if resp.status().is_success() => {
            println!("✅ 步骤2: 服务健康检查通过");
        }
        Ok(resp) => {
            println!("⚠️  步骤2: 服务响应异常: {}", resp.status());
            return Ok(());
        }
        Err(e) => {
            println!("⚠️  步骤2: 服务连接失败: {}", e);
            println!("请确保服务器正在运行: cargo run -p server");
            return Ok(());
        }
    }

    // 3. 数据准备
    let users_fixture = load_fixture("users")?;
    let tasks_fixture = load_fixture("tasks")?;
    println!("✅ 步骤3: 测试数据准备完成");

    // 4. 认证流程
    // （这里可以添加实际的认证逻辑）
    println!("✅ 步骤4: 认证流程模拟完成");

    // 5. 业务操作
    // （这里可以添加实际的业务操作测试）
    println!("✅ 步骤5: 业务操作模拟完成");

    // 6. 清理工作
    println!("✅ 步骤6: 清理工作完成");

    println!("🎉 完整E2E测试流程模板执行成功！");
    println!("   这个模板展示了如何组织和执行完整的E2E测试");

    Ok(())
}
