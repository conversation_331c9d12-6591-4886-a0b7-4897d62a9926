//! # 历史趋势分析模块
//!
//! 添加对多次测试运行的历史趋势分析支持，例如成功率随时间的变化
//!
//! ## 功能特性
//! - 历史数据存储和管理
//! - 趋势计算和分析
//! - 异常检测
//! - 性能回归分析

use super::*;
use anyhow::{ Result, Context };
use std::fs;
use std::path::Path;
// use app_common::chrono::{ DateTime, Utc, Duration };
use serde_json;

/// 趋势分析器
pub struct TrendAnalyzer {
    /// 配置选项
    config: ReportConfig,
    /// 历史数据存储路径
    history_path: String,
}

/// 趋势计算选项
#[derive(Debug, Clone)]
pub struct TrendCalculationOptions {
    /// 分析窗口大小（天数）
    pub window_days: u32,
    /// 最小数据点数量
    pub min_data_points: usize,
    /// 趋势敏感度（0.0-1.0）
    pub sensitivity: f64,
    /// 是否启用异常检测
    pub enable_anomaly_detection: bool,
}

impl Default for TrendCalculationOptions {
    fn default() -> Self {
        Self {
            window_days: 30,
            min_data_points: 5,
            sensitivity: 0.1,
            enable_anomaly_detection: true,
        }
    }
}

/// 异常检测结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnomalyDetection {
    /// 是否检测到异常
    pub has_anomaly: bool,
    /// 异常类型
    pub anomaly_type: Option<AnomalyType>,
    /// 异常描述
    pub description: Option<String>,
    /// 异常严重程度（1-10）
    pub severity: u8,
}

/// 异常类型枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AnomalyType {
    /// 成功率突然下降
    SuccessRateDrop,
    /// 执行时间异常增长
    DurationSpike,
    /// 测试数量异常变化
    TestCountAnomaly,
    /// 覆盖率下降
    CoverageDecline,
}

impl std::fmt::Display for AnomalyType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            AnomalyType::SuccessRateDrop => write!(f, "成功率下降"),
            AnomalyType::DurationSpike => write!(f, "执行时间异常"),
            AnomalyType::TestCountAnomaly => write!(f, "测试数量异常"),
            AnomalyType::CoverageDecline => write!(f, "覆盖率下降"),
        }
    }
}

impl TrendAnalyzer {
    /// 创建新的趋势分析器
    pub fn new(config: ReportConfig) -> Self {
        let history_path = format!("{}/history", config.output_dir);
        Self {
            config,
            history_path,
        }
    }

    /// 保存当前测试报告到历史记录
    pub fn save_to_history(&self, report: &TestReport) -> Result<()> {
        // 确保历史目录存在
        let history_dir = Path::new(&self.history_path);
        if !history_dir.exists() {
            fs
                ::create_dir_all(history_dir)
                .with_context(|| format!("创建历史目录失败: {}", self.history_path))?;
        }

        // 创建历史数据点
        let data_point = TrendDataPoint {
            timestamp: report.timestamp,
            success_rate: report.summary.success_rate,
            total_tests: report.summary.total_tests,
            duration_ms: report.summary.total_duration_ms,
            coverage_rate: report.coverage.as_ref().map(|c| c.line_coverage),
        };

        // 保存到文件
        let filename = format!("history_{}.json", report.timestamp.format("%Y%m%d_%H%M%S"));
        let file_path = history_dir.join(filename);

        let json_content = serde_json
            ::to_string_pretty(&data_point)
            .with_context(|| "序列化历史数据点失败")?;

        fs
            ::write(&file_path, json_content)
            .with_context(|| format!("写入历史文件失败: {:?}", file_path))?;

        println!("📊 历史数据已保存: {:?}", file_path);
        Ok(())
    }

    /// 加载历史数据
    pub fn load_history(&self, options: &TrendCalculationOptions) -> Result<Vec<TrendDataPoint>> {
        let history_dir = Path::new(&self.history_path);
        if !history_dir.exists() {
            return Ok(Vec::new());
        }

        let mut data_points = Vec::new();
        let cutoff_time = Utc::now() - Duration::days(options.window_days as i64);

        // 读取历史文件
        for entry in fs::read_dir(history_dir)? {
            let entry = entry?;
            let path = entry.path();

            if path.extension().and_then(|s| s.to_str()) == Some("json") {
                if let Ok(content) = fs::read_to_string(&path) {
                    if let Ok(data_point) = serde_json::from_str::<TrendDataPoint>(&content) {
                        // 只保留指定时间窗口内的数据
                        if data_point.timestamp >= cutoff_time {
                            data_points.push(data_point);
                        }
                    }
                }
            }
        }

        // 按时间排序
        data_points.sort_by(|a, b| a.timestamp.cmp(&b.timestamp));

        println!("📈 加载了 {} 个历史数据点", data_points.len());
        Ok(data_points)
    }

    /// 分析趋势
    pub fn analyze_trends(
        &self,
        options: Option<TrendCalculationOptions>
    ) -> Result<TrendAnalysis> {
        let options = options.unwrap_or_default();
        let data_points = self.load_history(&options)?;

        if data_points.len() < options.min_data_points {
            return Ok(TrendAnalysis {
                data_points,
                success_rate_trend: TrendDirection::Insufficient,
                test_count_trend: TrendDirection::Insufficient,
                duration_trend: TrendDirection::Insufficient,
                coverage_trend: Some(TrendDirection::Insufficient),
            });
        }

        // 计算各项趋势
        let success_rate_trend = self.calculate_trend(
            &data_points
                .iter()
                .map(|p| p.success_rate)
                .collect::<Vec<_>>(),
            options.sensitivity
        );

        let test_count_trend = self.calculate_trend(
            &data_points
                .iter()
                .map(|p| p.total_tests as f64)
                .collect::<Vec<_>>(),
            options.sensitivity
        );

        let duration_trend = self.calculate_trend(
            &data_points
                .iter()
                .map(|p| p.duration_ms as f64)
                .collect::<Vec<_>>(),
            options.sensitivity
        );

        let coverage_trend = if data_points.iter().any(|p| p.coverage_rate.is_some()) {
            let coverage_data: Vec<f64> = data_points
                .iter()
                .filter_map(|p| p.coverage_rate)
                .collect();

            if coverage_data.len() >= options.min_data_points {
                Some(self.calculate_trend(&coverage_data, options.sensitivity))
            } else {
                Some(TrendDirection::Insufficient)
            }
        } else {
            None
        };

        Ok(TrendAnalysis {
            data_points,
            success_rate_trend,
            test_count_trend,
            duration_trend,
            coverage_trend,
        })
    }

    /// 计算单个指标的趋势方向
    fn calculate_trend(&self, data: &[f64], sensitivity: f64) -> TrendDirection {
        if data.len() < 2 {
            return TrendDirection::Insufficient;
        }

        // 使用线性回归计算趋势
        let n = data.len() as f64;
        let x_sum: f64 = (0..data.len()).map(|i| i as f64).sum();
        let y_sum: f64 = data.iter().sum();
        let xy_sum: f64 = data
            .iter()
            .enumerate()
            .map(|(i, &y)| (i as f64) * y)
            .sum();
        let x_squared_sum: f64 = (0..data.len()).map(|i| (i as f64).powi(2)).sum();

        let slope = (n * xy_sum - x_sum * y_sum) / (n * x_squared_sum - x_sum.powi(2));

        // 根据斜率和敏感度判断趋势
        if slope.abs() < sensitivity {
            TrendDirection::Stable
        } else if slope > 0.0 {
            TrendDirection::Increasing
        } else {
            TrendDirection::Decreasing
        }
    }

    /// 检测异常
    pub fn detect_anomalies(
        &self,
        trend_analysis: &TrendAnalysis
    ) -> Result<Vec<AnomalyDetection>> {
        let mut anomalies = Vec::new();

        // 检测成功率异常下降
        if let Some(latest) = trend_analysis.data_points.last() {
            if trend_analysis.data_points.len() >= 3 {
                let recent_avg =
                    trend_analysis.data_points
                        .iter()
                        .rev()
                        .take(3)
                        .map(|p| p.success_rate)
                        .sum::<f64>() / 3.0;

                let historical_avg =
                    trend_analysis.data_points
                        .iter()
                        .take(trend_analysis.data_points.len() - 3)
                        .map(|p| p.success_rate)
                        .sum::<f64>() / ((trend_analysis.data_points.len() - 3) as f64);

                if recent_avg < historical_avg - 10.0 {
                    anomalies.push(AnomalyDetection {
                        has_anomaly: true,
                        anomaly_type: Some(AnomalyType::SuccessRateDrop),
                        description: Some(
                            format!("成功率从 {:.1}% 下降到 {:.1}%", historical_avg, recent_avg)
                        ),
                        severity: if recent_avg < historical_avg - 20.0 {
                            8
                        } else {
                            5
                        },
                    });
                }
            }

            // 检测执行时间异常
            if trend_analysis.data_points.len() >= 5 {
                let recent_durations: Vec<f64> = trend_analysis.data_points
                    .iter()
                    .rev()
                    .take(3)
                    .map(|p| p.duration_ms as f64)
                    .collect();

                let historical_durations: Vec<f64> = trend_analysis.data_points
                    .iter()
                    .take(trend_analysis.data_points.len() - 3)
                    .map(|p| p.duration_ms as f64)
                    .collect();

                let recent_avg =
                    recent_durations.iter().sum::<f64>() / (recent_durations.len() as f64);
                let historical_avg =
                    historical_durations.iter().sum::<f64>() / (historical_durations.len() as f64);

                if recent_avg > historical_avg * 1.5 {
                    anomalies.push(AnomalyDetection {
                        has_anomaly: true,
                        anomaly_type: Some(AnomalyType::DurationSpike),
                        description: Some(
                            format!(
                                "执行时间从平均 {:.0}ms 增长到 {:.0}ms",
                                historical_avg,
                                recent_avg
                            )
                        ),
                        severity: if recent_avg > historical_avg * 2.0 {
                            7
                        } else {
                            4
                        },
                    });
                }
            }
        }

        Ok(anomalies)
    }

    /// 生成趋势报告
    pub fn generate_trend_report(&self, trend_analysis: &TrendAnalysis) -> Result<String> {
        let mut report = String::new();

        report.push_str("# 📈 测试趋势分析报告\n\n");

        // 数据概览
        report.push_str(
            &format!(
                "## 📊 数据概览\n\n- 分析时间范围: {} 天\n- 数据点数量: {}\n- 最新测试时间: {}\n\n",
                self.config.history_retention_days,
                trend_analysis.data_points.len(),
                trend_analysis.data_points
                    .last()
                    .map(|p| p.timestamp.format("%Y-%m-%d %H:%M:%S").to_string())
                    .unwrap_or_else(|| "无数据".to_string())
            )
        );

        // 趋势分析
        report.push_str("## 📈 趋势分析\n\n");
        report.push_str(&format!("- **成功率趋势**: {}\n", trend_analysis.success_rate_trend));
        report.push_str(&format!("- **测试数量趋势**: {}\n", trend_analysis.test_count_trend));
        report.push_str(&format!("- **执行时间趋势**: {}\n", trend_analysis.duration_trend));

        if let Some(coverage_trend) = &trend_analysis.coverage_trend {
            report.push_str(&format!("- **覆盖率趋势**: {}\n", coverage_trend));
        }

        report.push_str("\n");

        // 异常检测
        let anomalies = self.detect_anomalies(trend_analysis)?;
        if !anomalies.is_empty() {
            report.push_str("## ⚠️ 异常检测\n\n");
            for anomaly in &anomalies {
                if let Some(anomaly_type) = &anomaly.anomaly_type {
                    report.push_str(
                        &format!(
                            "- **{}** (严重程度: {}): {}\n",
                            anomaly_type,
                            anomaly.severity,
                            anomaly.description.as_deref().unwrap_or("无详细信息")
                        )
                    );
                }
            }
            report.push_str("\n");
        } else {
            report.push_str("## ✅ 异常检测\n\n未检测到异常情况。\n\n");
        }

        // 建议
        report.push_str("## 💡 改进建议\n\n");
        report.push_str(&self.generate_recommendations(trend_analysis, &anomalies));

        Ok(report)
    }

    /// 生成改进建议
    fn generate_recommendations(
        &self,
        trend_analysis: &TrendAnalysis,
        anomalies: &[AnomalyDetection]
    ) -> String {
        let mut recommendations = String::new();

        // 基于趋势的建议
        match trend_analysis.success_rate_trend {
            TrendDirection::Decreasing => {
                recommendations.push_str("- 成功率呈下降趋势，建议检查最近的代码变更和测试用例\n");
            }
            TrendDirection::Stable => {
                recommendations.push_str("- 成功率保持稳定，继续保持当前的测试质量\n");
            }
            TrendDirection::Increasing => {
                recommendations.push_str("- 成功率呈上升趋势，测试质量在改善\n");
            }
            TrendDirection::Insufficient => {
                recommendations.push_str("- 数据不足，建议积累更多历史数据进行分析\n");
            }
        }

        // 基于异常的建议
        for anomaly in anomalies {
            match &anomaly.anomaly_type {
                Some(AnomalyType::SuccessRateDrop) => {
                    recommendations.push_str("- 检查最近失败的测试用例，分析失败原因\n");
                }
                Some(AnomalyType::DurationSpike) => {
                    recommendations.push_str("- 分析执行时间增长的原因，可能需要性能优化\n");
                }
                Some(AnomalyType::TestCountAnomaly) => {
                    recommendations.push_str("- 检查测试用例数量变化的原因\n");
                }
                Some(AnomalyType::CoverageDecline) => {
                    recommendations.push_str("- 增加测试覆盖率，补充缺失的测试用例\n");
                }
                None => {}
            }
        }

        if recommendations.is_empty() {
            recommendations.push_str("- 当前测试状态良好，继续保持\n");
        }

        recommendations
    }

    /// 清理过期的历史数据
    pub fn cleanup_old_history(&self) -> Result<()> {
        let history_dir = Path::new(&self.history_path);
        if !history_dir.exists() {
            return Ok(());
        }

        let cutoff_time = Utc::now() - Duration::days(self.config.history_retention_days as i64);
        let mut cleaned_count = 0;

        for entry in fs::read_dir(history_dir)? {
            let entry = entry?;
            let path = entry.path();

            if path.extension().and_then(|s| s.to_str()) == Some("json") {
                if let Ok(content) = fs::read_to_string(&path) {
                    if let Ok(data_point) = serde_json::from_str::<TrendDataPoint>(&content) {
                        if data_point.timestamp < cutoff_time {
                            fs::remove_file(&path)?;
                            cleaned_count += 1;
                        }
                    }
                }
            }
        }

        if cleaned_count > 0 {
            println!("🧹 清理了 {} 个过期的历史数据文件", cleaned_count);
        }

        Ok(())
    }
}
