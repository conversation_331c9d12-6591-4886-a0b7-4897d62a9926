// 测试环境变量配置验证测试

use anyhow::Result;
use dotenvy;
use std::env;

/// 测试环境变量加载
#[tokio::test]
async fn test_environment_variables_loading() -> Result<()> {
    println!("测试环境变量加载...");

    // 加载测试环境配置
    dotenvy::from_filename("tests/e2e/config/test.env").ok();

    // 验证服务器配置
    let server_host = env::var("SERVER_HOST").unwrap_or_default();
    let server_port = env::var("SERVER_PORT").unwrap_or_default();
    let base_url = env::var("BASE_URL").unwrap_or_default();

    assert!(!server_host.is_empty(), "SERVER_HOST不能为空");
    assert!(!server_port.is_empty(), "SERVER_PORT不能为空");
    assert!(!base_url.is_empty(), "BASE_URL不能为空");

    println!("✅ 服务器配置: {}:{}", server_host, server_port);
    println!("✅ 基础URL: {}", base_url);

    Ok(())
}

/// 测试数据库配置
#[tokio::test]
async fn test_database_configuration() -> Result<()> {
    println!("测试数据库配置...");

    dotenvy::from_filename("tests/e2e/config/test.env").ok();

    let database_url = env::var("DATABASE_URL").unwrap_or_default();
    let test_database_url = env::var("TEST_DATABASE_URL").unwrap_or_default();

    assert!(!database_url.is_empty(), "DATABASE_URL不能为空");
    assert!(!test_database_url.is_empty(), "TEST_DATABASE_URL不能为空");
    assert!(database_url.starts_with("sqlite:"), "应该使用SQLite数据库");
    assert!(
        test_database_url.starts_with("sqlite:"),
        "测试数据库应该使用SQLite"
    );

    println!("✅ 数据库URL: {}", database_url);
    println!("✅ 测试数据库URL: {}", test_database_url);

    Ok(())
}

/// 测试认证配置
#[tokio::test]
async fn test_authentication_configuration() -> Result<()> {
    println!("测试认证配置...");

    dotenvy::from_filename("tests/e2e/config/test.env").ok();

    let jwt_secret = env::var("JWT_SECRET").unwrap_or_default();
    let test_username = env::var("TEST_USERNAME").unwrap_or_default();
    let test_password = env::var("TEST_PASSWORD").unwrap_or_default();

    assert!(!jwt_secret.is_empty(), "JWT_SECRET不能为空");
    assert!(!test_username.is_empty(), "TEST_USERNAME不能为空");
    assert!(!test_password.is_empty(), "TEST_PASSWORD不能为空");
    assert!(jwt_secret.len() >= 16, "JWT密钥长度应该至少16个字符");

    println!("✅ JWT密钥长度: {}", jwt_secret.len());
    println!("✅ 测试用户: {}", test_username);

    Ok(())
}

/// 测试Playwright配置
#[tokio::test]
async fn test_playwright_configuration() -> Result<()> {
    println!("测试Playwright配置...");

    dotenvy::from_filename("tests/e2e/config/test.env").ok();

    let headless = env::var("PLAYWRIGHT_HEADLESS").unwrap_or_default();
    let viewport_width = env::var("PLAYWRIGHT_VIEWPORT_WIDTH").unwrap_or_default();
    let viewport_height = env::var("PLAYWRIGHT_VIEWPORT_HEIGHT").unwrap_or_default();
    let timeout = env::var("PLAYWRIGHT_TIMEOUT").unwrap_or_default();

    assert!(!headless.is_empty(), "PLAYWRIGHT_HEADLESS不能为空");
    assert!(
        !viewport_width.is_empty(),
        "PLAYWRIGHT_VIEWPORT_WIDTH不能为空"
    );
    assert!(
        !viewport_height.is_empty(),
        "PLAYWRIGHT_VIEWPORT_HEIGHT不能为空"
    );
    assert!(!timeout.is_empty(), "PLAYWRIGHT_TIMEOUT不能为空");

    // 验证数值格式
    let width: u32 = viewport_width.parse().expect("视口宽度应该是有效数字");
    let height: u32 = viewport_height.parse().expect("视口高度应该是有效数字");
    let timeout_ms: u32 = timeout.parse().expect("超时时间应该是有效数字");

    assert!(width > 0, "视口宽度应该大于0");
    assert!(height > 0, "视口高度应该大于0");
    assert!(timeout_ms > 0, "超时时间应该大于0");

    println!(
        "✅ Playwright配置: {}x{}, 超时: {}ms",
        width, height, timeout_ms
    );

    Ok(())
}

/// 测试性能配置
#[tokio::test]
async fn test_performance_configuration() -> Result<()> {
    println!("测试性能配置...");

    dotenvy::from_filename("tests/e2e/config/test.env").ok();

    let max_response_time = env::var("MAX_RESPONSE_TIME").unwrap_or_default();
    let max_connections = env::var("MAX_CONCURRENT_CONNECTIONS").unwrap_or_default();
    let retry_attempts = env::var("RETRY_ATTEMPTS").unwrap_or_default();

    assert!(!max_response_time.is_empty(), "MAX_RESPONSE_TIME不能为空");
    assert!(
        !max_connections.is_empty(),
        "MAX_CONCURRENT_CONNECTIONS不能为空"
    );
    assert!(!retry_attempts.is_empty(), "RETRY_ATTEMPTS不能为空");

    let response_time: u32 = max_response_time.parse().expect("响应时间应该是有效数字");
    let connections: u32 = max_connections.parse().expect("连接数应该是有效数字");
    let retries: u32 = retry_attempts.parse().expect("重试次数应该是有效数字");

    assert!(response_time > 0, "最大响应时间应该大于0");
    assert!(connections > 0, "最大连接数应该大于0");
    assert!(retries > 0, "重试次数应该大于0");

    println!(
        "✅ 性能配置: 响应时间{}ms, 连接数{}, 重试{}次",
        response_time, connections, retries
    );

    Ok(())
}

/// 测试日志配置
#[tokio::test]
async fn test_logging_configuration() -> Result<()> {
    println!("测试日志配置...");

    dotenvy::from_filename("tests/e2e/config/test.env").ok();

    let log_level = env::var("LOG_LEVEL").unwrap_or_default();
    let rust_log = env::var("RUST_LOG").unwrap_or_default();
    let test_log_file = env::var("TEST_LOG_FILE").unwrap_or_default();

    assert!(!log_level.is_empty(), "LOG_LEVEL不能为空");
    assert!(!rust_log.is_empty(), "RUST_LOG不能为空");
    assert!(!test_log_file.is_empty(), "TEST_LOG_FILE不能为空");

    // 验证日志级别
    let valid_levels = ["trace", "debug", "info", "warn", "error"];
    assert!(
        valid_levels.contains(&log_level.as_str()),
        "日志级别应该是有效值"
    );

    println!("✅ 日志配置: 级别={}, 文件={}", log_level, test_log_file);

    Ok(())
}

/// 测试安全配置
#[tokio::test]
async fn test_security_configuration() -> Result<()> {
    println!("测试安全配置...");

    dotenvy::from_filename("tests/e2e/config/test.env").ok();

    let enable_cors = env::var("ENABLE_CORS").unwrap_or_default();
    let cors_origins = env::var("CORS_ORIGINS").unwrap_or_default();

    assert!(!enable_cors.is_empty(), "ENABLE_CORS不能为空");
    assert!(!cors_origins.is_empty(), "CORS_ORIGINS不能为空");

    let cors_enabled: bool = enable_cors.parse().expect("CORS配置应该是布尔值");
    assert!(cors_enabled, "测试环境应该启用CORS");

    // 验证CORS源
    let origins: Vec<&str> = cors_origins.split(',').collect();
    assert!(!origins.is_empty(), "应该配置CORS源");
    assert!(
        origins.iter().any(|&origin| origin.contains("127.0.0.1")),
        "应该包含本地地址"
    );

    println!("✅ 安全配置: CORS={}, 源={}", cors_enabled, cors_origins);

    Ok(())
}

/// 测试环境变量覆盖
#[tokio::test]
async fn test_environment_variable_override() -> Result<()> {
    println!("测试环境变量覆盖...");

    // 先加载配置文件
    dotenvy::from_filename("tests/e2e/config/test.env").ok();

    // 获取原始值
    let original_port = env::var("SERVER_PORT").unwrap_or_default();

    // 设置覆盖值
    unsafe {
        env::set_var("SERVER_PORT", "3001");
    }

    // 验证覆盖生效
    let overridden_port = env::var("SERVER_PORT").unwrap_or_default();
    assert_eq!(overridden_port, "3001", "环境变量应该被覆盖");

    // 恢复原始值
    unsafe {
        env::set_var("SERVER_PORT", &original_port);
    }

    println!("✅ 环境变量覆盖功能正常");

    Ok(())
}

/// 集成测试：完整的环境配置验证
#[tokio::test]
async fn test_complete_environment_configuration() -> Result<()> {
    println!("执行完整的环境配置验证...");

    // 加载配置
    dotenvy::from_filename("tests/e2e/config/test.env").ok();

    // 验证所有关键配置项
    let required_vars = [
        "SERVER_HOST",
        "SERVER_PORT",
        "BASE_URL",
        "DATABASE_URL",
        "TEST_DATABASE_URL",
        "JWT_SECRET",
        "TEST_USERNAME",
        "TEST_PASSWORD",
        "PLAYWRIGHT_HEADLESS",
        "PLAYWRIGHT_VIEWPORT_WIDTH",
        "PLAYWRIGHT_VIEWPORT_HEIGHT",
        "LOG_LEVEL",
        "RUST_LOG",
    ];

    for var in &required_vars {
        let value = env::var(var).unwrap_or_default();
        assert!(!value.is_empty(), "环境变量 {} 不能为空", var);
        println!("✅ {}: {}", var, value);
    }

    // 验证配置一致性
    let base_url = env::var("BASE_URL").unwrap();
    let server_host = env::var("SERVER_HOST").unwrap();
    let server_port = env::var("SERVER_PORT").unwrap();
    let expected_url = format!("http://{}:{}", server_host, server_port);
    assert_eq!(base_url, expected_url, "BASE_URL应该与服务器配置一致");

    println!("🎉 完整的环境配置验证通过！");
    println!("   所有环境变量都已正确配置");

    Ok(())
}
