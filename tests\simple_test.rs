//! # 简单测试
//!
//! 验证基本的编译和测试基础设施

use anyhow::Result;
use std::sync::Arc;
use tracing::info;

/// 简单的编译测试
#[tokio::test]
async fn test_compilation() -> Result<()> {
    info!("测试编译成功");
    Ok(())
}

/// 测试基本的异步功能
#[tokio::test]
async fn test_async_basic() -> Result<()> {
    let result = async_function().await;
    assert_eq!(result, "success");
    Ok(())
}

async fn async_function() -> &'static str {
    "success"
}

/// 测试Arc和基本数据结构
#[tokio::test]
async fn test_arc_basic() -> Result<()> {
    let data = Arc::new(String::from("test data"));
    let cloned = Arc::clone(&data);

    assert_eq!(*data, *cloned);
    assert_eq!(*data, "test data");

    Ok(())
}
