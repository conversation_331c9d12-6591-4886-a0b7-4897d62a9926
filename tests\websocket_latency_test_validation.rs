// WebSocket延迟测试脚本验证测试
// 验证WebSocket延迟测试工具的基本功能

use std::time::Duration;
use tokio::time::sleep;

// 导入延迟测试相关的结构体和函数
// 注意：由于脚本在scripts目录中，我们需要通过模块路径访问

#[tokio::test]
async fn test_latency_stats_calculation() {
    // 测试延迟统计计算功能

    // 模拟延迟数据
    let latencies = vec![10, 20, 30, 40, 50, 60, 70, 80, 90, 100];

    // 测试平均值计算
    let average = calculate_average(&latencies);
    assert_eq!(average, 55.0);

    // 测试百分位数计算
    let p50 = calculate_percentile(&latencies, 50.0);
    assert_eq!(p50, 60); // 中位数 (修正期望值)

    let p95 = calculate_percentile(&latencies, 95.0);
    assert_eq!(p95, 100); // 95百分位 (修正期望值)

    let p99 = calculate_percentile(&latencies, 99.0);
    assert_eq!(p99, 100); // 99百分位 (修正期望值)

    println!("✅ 延迟统计计算测试通过");
}

#[tokio::test]
async fn test_latency_config_validation() {
    // 测试延迟测试配置的有效性

    // 创建默认配置
    let config = create_default_config();

    // 验证配置参数
    assert!(!config.server_url.is_empty(), "服务器URL不能为空");
    assert!(!config.auth_token.is_empty(), "认证token不能为空");
    assert!(config.connection_timeout > 0, "连接超时时间必须大于0");
    assert!(config.message_timeout > 0, "消息超时时间必须大于0");
    assert!(config.test_rounds > 0, "测试轮次必须大于0");
    assert!(config.concurrent_connections > 0, "并发连接数必须大于0");
    assert!(config.heartbeat_interval > 0, "心跳间隔必须大于0");

    println!("✅ 延迟测试配置验证通过");
}

#[tokio::test]
async fn test_websocket_url_construction() {
    // 测试WebSocket URL构建

    let base_url = "ws://127.0.0.1:3000/ws";
    let token = "test_token_123";

    // 构建WebSocket URL
    let ws_url = format!("{}?token={}", base_url, token);

    // 验证URL格式
    assert!(ws_url.starts_with("ws://"), "URL必须以ws://开头");
    assert!(ws_url.contains("token="), "URL必须包含token参数");
    assert!(ws_url.contains(token), "URL必须包含指定的token值");

    println!("✅ WebSocket URL构建测试通过");
}

#[tokio::test]
async fn test_message_serialization() {
    // 测试消息序列化性能

    use serde_json::json;
    use std::time::Instant;

    let test_message = json!({
        "type": "ping",
        "timestamp": "2024-01-01T00:00:00Z",
        "test_id": "test_123"
    });

    let start_time = Instant::now();

    // 序列化消息
    let serialized = test_message.to_string();

    let serialization_time = start_time.elapsed();

    // 验证序列化结果
    assert!(!serialized.is_empty(), "序列化结果不能为空");
    assert!(serialized.contains("ping"), "序列化结果必须包含消息类型");
    assert!(serialized.contains("test_123"), "序列化结果必须包含测试ID");

    // 验证序列化性能（应该在1ms内完成）
    assert!(serialization_time.as_millis() < 1, "序列化时间应该小于1ms");

    println!("✅ 消息序列化测试通过，耗时: {:?}", serialization_time);
}

#[tokio::test]
async fn test_message_deserialization() {
    // 测试消息反序列化性能

    use serde_json::Value;
    use std::time::Instant;

    let test_json = r#"{"type":"pong","timestamp":"2024-01-01T00:00:00Z","sequence":1}"#;

    let start_time = Instant::now();

    // 反序列化消息
    let parsed: Value = serde_json::from_str(test_json).expect("反序列化失败");

    let deserialization_time = start_time.elapsed();

    // 验证反序列化结果
    assert_eq!(parsed["type"], "pong", "消息类型必须为pong");
    assert_eq!(parsed["sequence"], 1, "序列号必须为1");

    // 验证反序列化性能（应该在1ms内完成）
    assert!(
        deserialization_time.as_millis() < 1,
        "反序列化时间应该小于1ms"
    );

    println!("✅ 消息反序列化测试通过，耗时: {:?}", deserialization_time);
}

#[tokio::test]
async fn test_concurrent_operations_simulation() {
    // 测试并发操作模拟

    use std::sync::{
        Arc,
        atomic::{AtomicU64, Ordering},
    };
    use tokio::task;

    let counter = Arc::new(AtomicU64::new(0));
    let mut handles = Vec::new();

    let concurrent_count = 5;

    // 创建多个并发任务
    for i in 0..concurrent_count {
        let counter_clone = Arc::clone(&counter);

        let handle = task::spawn(async move {
            // 模拟延迟操作
            sleep(Duration::from_millis(10)).await;

            // 原子操作计数
            counter_clone.fetch_add(1, Ordering::Relaxed);

            i
        });

        handles.push(handle);
    }

    // 等待所有任务完成
    let mut results = Vec::new();
    for handle in handles {
        let result = handle.await.expect("任务执行失败");
        results.push(result);
    }

    // 验证结果
    assert_eq!(results.len(), concurrent_count, "任务数量必须匹配");
    assert_eq!(
        counter.load(Ordering::Relaxed),
        concurrent_count as u64,
        "计数器值必须匹配"
    );

    println!("✅ 并发操作模拟测试通过，完成{}个任务", concurrent_count);
}

#[tokio::test]
async fn test_timeout_handling() {
    // 测试超时处理机制

    use tokio::time::timeout;

    // 测试正常操作（不超时）
    let quick_operation = async {
        sleep(Duration::from_millis(10)).await;
        "success"
    };

    let result = timeout(Duration::from_millis(100), quick_operation).await;
    assert!(result.is_ok(), "快速操作不应该超时");
    assert_eq!(result.unwrap(), "success", "操作结果必须正确");

    // 测试超时操作
    let slow_operation = async {
        sleep(Duration::from_millis(200)).await;
        "should_timeout"
    };

    let result = timeout(Duration::from_millis(50), slow_operation).await;
    assert!(result.is_err(), "慢速操作应该超时");

    println!("✅ 超时处理测试通过");
}

// 辅助函数：计算平均值
fn calculate_average(latencies: &[u64]) -> f64 {
    if latencies.is_empty() {
        0.0
    } else {
        (latencies.iter().sum::<u64>() as f64) / (latencies.len() as f64)
    }
}

// 辅助函数：计算百分位数
fn calculate_percentile(latencies: &[u64], percentile: f64) -> u64 {
    if latencies.is_empty() {
        return 0;
    }

    let mut sorted = latencies.to_vec();
    sorted.sort_unstable();

    let index = ((((sorted.len() as f64) - 1.0) * percentile) / 100.0).round() as usize;
    sorted[index.min(sorted.len() - 1)]
}

// 辅助函数：创建默认配置
fn create_default_config() -> TestConfig {
    TestConfig {
        server_url: "ws://127.0.0.1:3000/ws".to_string(),
        auth_token: "test_token_for_latency_test".to_string(),
        connection_timeout: 10,
        message_timeout: 5,
        test_rounds: 10,
        concurrent_connections: 5,
        heartbeat_interval: 30,
    }
}

// 测试配置结构体
#[derive(Debug, Clone)]
struct TestConfig {
    pub server_url: String,
    pub auth_token: String,
    pub connection_timeout: u64,
    pub message_timeout: u64,
    pub test_rounds: usize,
    pub concurrent_connections: usize,
    pub heartbeat_interval: u64,
}

#[tokio::test]
async fn test_integration_websocket_latency_tool() {
    // 集成测试：验证WebSocket延迟测试工具的整体功能

    println!("🧪 开始WebSocket延迟测试工具集成测试");

    // 1. 配置验证
    let config = create_default_config();
    println!("  ✅ 配置创建成功");

    // 2. 统计计算验证
    let sample_latencies = vec![5, 10, 15, 20, 25, 30, 35, 40, 45, 50];
    let avg = calculate_average(&sample_latencies);
    let p95 = calculate_percentile(&sample_latencies, 95.0);

    assert!(avg > 0.0, "平均延迟必须大于0");
    assert!(p95 > 0, "P95延迟必须大于0");
    println!("  ✅ 统计计算验证通过 - 平均: {:.2}ms, P95: {}ms", avg, p95);

    // 3. 消息处理验证
    let test_message = serde_json::json!({
        "type": "ping",
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "test_id": uuid::Uuid::new_v4().to_string()
    });

    let serialized = test_message.to_string();
    assert!(!serialized.is_empty(), "消息序列化不能为空");
    println!("  ✅ 消息处理验证通过");

    // 4. 并发模拟验证
    let concurrent_tasks = 3;
    let mut handles = Vec::new();

    for i in 0..concurrent_tasks {
        let handle = tokio::spawn(async move {
            sleep(Duration::from_millis(5)).await;
            format!("task_{}", i)
        });
        handles.push(handle);
    }

    let mut results = Vec::new();
    for handle in handles {
        let result = handle.await.expect("并发任务失败");
        results.push(result);
    }

    assert_eq!(results.len(), concurrent_tasks, "并发任务数量必须匹配");
    println!("  ✅ 并发模拟验证通过 - 完成{}个任务", concurrent_tasks);

    println!("🎉 WebSocket延迟测试工具集成测试全部通过！");
}
