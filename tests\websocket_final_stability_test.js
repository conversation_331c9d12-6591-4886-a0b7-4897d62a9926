/**
 * WebSocket最终稳定性测试
 * 
 * 验证修复后的WebSocket连接稳定性问题
 */

const { chromium } = require('playwright');

async function runWebSocketFinalStabilityTest() {
    console.log('🎯 开始WebSocket最终稳定性测试...');
    
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // 监听控制台错误
    page.on('console', msg => {
        if (msg.type() === 'error') {
            console.log('❌ 浏览器错误:', msg.text());
        }
    });
    
    try {
        // 导航和登录
        await page.goto('http://127.0.0.1:3000');
        await page.waitForLoadState('networkidle');
        
        await page.click('#loginTab');
        await page.fill('#loginUsername', 'testuser456');
        await page.fill('#loginPassword', 'password123');
        await page.click('#loginForm button[type="submit"]');
        await page.waitForSelector('#currentUser:has-text("testuser456")', { timeout: 10000 });
        
        console.log('✅ 登录成功');
        
        const connectBtn = page.locator('#wsConnectBtn');
        const disconnectBtn = page.locator('#wsDisconnectBtn');
        const connectionStatus = page.locator('#connectionStatus');
        const messageInput = page.locator('#messageInput');
        const sendBtn = page.locator('#sendBtn');
        const chatMessages = page.locator('#chatMessages');
        
        // 执行原始问题重现序列
        console.log('🔄 执行原始问题重现序列...');
        
        // 连续点击"连接"按钮3次（只有第一次会真正连接）
        console.log('📡 连续点击"连接"按钮3次...');
        for (let i = 1; i <= 3; i++) {
            console.log(`  第${i}次点击连接按钮`);
            if (await connectBtn.isEnabled()) {
                await connectBtn.click();
                await page.waitForTimeout(500);
            } else {
                console.log(`    按钮已禁用，跳过点击`);
            }
            const status = await connectionStatus.textContent();
            console.log(`    状态: ${status}`);
        }
        
        // 连续点击"断开"按钮3次（只有第一次会真正断开）
        console.log('📡 连续点击"断开"按钮3次...');
        for (let i = 1; i <= 3; i++) {
            console.log(`  第${i}次点击断开按钮`);
            if (await disconnectBtn.isEnabled()) {
                await disconnectBtn.click();
                await page.waitForTimeout(500);
            } else {
                console.log(`    按钮已禁用，跳过点击`);
            }
            const status = await connectionStatus.textContent();
            console.log(`    状态: ${status}`);
        }
        
        // 最后点击"连接"按钮
        console.log('📡 最后点击"连接"按钮...');
        await connectBtn.click();
        await page.waitForTimeout(2000);
        
        const finalStatus = await connectionStatus.textContent();
        console.log(`最终连接状态: ${finalStatus}`);
        
        if (!finalStatus.includes('已连接')) {
            console.log('❌ 最终连接失败');
            return false;
        }
        
        // 测试消息发送功能
        console.log('📝 测试消息发送功能...');
        const testMessage = '稳定性测试消息';
        
        // 等待输入框启用
        await page.waitForFunction(() => {
            const input = document.getElementById('messageInput');
            return input && !input.disabled;
        }, { timeout: 5000 });
        
        await messageInput.fill(testMessage);
        await sendBtn.click();
        await page.waitForTimeout(1000);
        
        const messageExists = await chatMessages.locator(`:has-text("${testMessage}")`).count() > 0;
        if (messageExists) {
            console.log('✅ 消息发送成功');
        } else {
            console.log('❌ 消息发送失败');
        }
        
        // 监控连接稳定性30秒
        console.log('⏱️  监控连接稳定性30秒...');
        let connectionStable = true;
        
        for (let second = 1; second <= 30; second++) {
            await page.waitForTimeout(1000);
            const currentStatus = await connectionStatus.textContent();
            
            if (!currentStatus.includes('已连接')) {
                console.log(`❌ 连接在第${second}秒时断开！`);
                connectionStable = false;
                break;
            }
            
            // 每5秒报告一次
            if (second % 5 === 0) {
                console.log(`    第${second}秒: 连接稳定`);
            }
        }
        
        if (connectionStable) {
            console.log('✅ 连接在30秒内保持稳定');
        }
        
        // 最终功能验证
        console.log('🔍 最终功能验证...');
        const finalTestMessage = '最终验证消息';
        
        await messageInput.fill(finalTestMessage);
        await sendBtn.click();
        await page.waitForTimeout(1000);
        
        const finalMessageExists = await chatMessages.locator(`:has-text("${finalTestMessage}")`).count() > 0;
        if (finalMessageExists) {
            console.log('✅ 最终功能验证成功');
        } else {
            console.log('❌ 最终功能验证失败');
        }
        
        // 生成测试结果
        const testPassed = finalStatus.includes('已连接') && 
                          messageExists && 
                          connectionStable && 
                          finalMessageExists;
        
        console.log('\n📊 测试结果总结:');
        console.log('='.repeat(50));
        console.log(`最终连接状态: ${testPassed ? '✅ 通过' : '❌ 失败'}`);
        console.log(`消息发送功能: ${messageExists ? '✅ 通过' : '❌ 失败'}`);
        console.log(`连接稳定性: ${connectionStable ? '✅ 通过' : '❌ 失败'}`);
        console.log(`最终功能验证: ${finalMessageExists ? '✅ 通过' : '❌ 失败'}`);
        console.log(`总体结果: ${testPassed ? '🎉 测试通过' : '⚠️ 测试失败'}`);
        
        return testPassed;
        
    } catch (error) {
        console.error('❌ 测试执行失败:', error);
        return false;
    } finally {
        await browser.close();
        console.log('🏁 最终稳定性测试完成');
    }
}

// 运行测试
if (require.main === module) {
    runWebSocketFinalStabilityTest().then(success => {
        process.exit(success ? 0 : 1);
    }).catch(error => {
        console.error(error);
        process.exit(1);
    });
}

module.exports = { runWebSocketFinalStabilityTest };
