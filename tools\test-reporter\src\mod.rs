//! # 测试报告生成模块
//!
//! 提供全面的测试报告生成功能，包括HTML、JSON、图表可视化等
//!
//! ## 功能特性
//! - HTML测试报告模板
//! - JSON数据导出
//! - 图表可视化集成
//! - 历史趋势分析
//! - 自动化报告生成流程

pub mod html_template;
pub mod json_exporter;
pub mod chart_visualizer;
pub mod trend_analyzer;
pub mod report_generator;

pub use html_template::*;
pub use json_exporter::*;
pub use chart_visualizer::*;
pub use trend_analyzer::*;
pub use report_generator::*;

use serde::{ Deserialize, Serialize };
use std::collections::HashMap;
// use app_common::chrono::{ DateTime, Utc };

/// 测试结果状态枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TestStatus {
    /// 测试通过
    Passed,
    /// 测试失败
    Failed,
    /// 测试跳过
    Skipped,
    /// 测试错误
    Error,
}

impl std::fmt::Display for TestStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TestStatus::Passed => write!(f, "通过"),
            TestStatus::Failed => write!(f, "失败"),
            TestStatus::Skipped => write!(f, "跳过"),
            TestStatus::Error => write!(f, "错误"),
        }
    }
}

/// 单个测试用例结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestCase {
    /// 测试用例名称
    pub name: String,
    /// 测试状态
    pub status: TestStatus,
    /// 执行时间（毫秒）
    pub duration_ms: u64,
    /// 错误信息（如果有）
    pub error_message: Option<String>,
    /// 测试输出
    pub output: Option<String>,
    /// 测试文件路径
    pub file_path: String,
    /// 测试行号
    pub line_number: Option<u32>,
}

/// 测试套件结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestSuite {
    /// 套件名称
    pub name: String,
    /// 测试用例列表
    pub test_cases: Vec<TestCase>,
    /// 套件总执行时间（毫秒）
    pub total_duration_ms: u64,
    /// 通过的测试数量
    pub passed_count: u32,
    /// 失败的测试数量
    pub failed_count: u32,
    /// 跳过的测试数量
    pub skipped_count: u32,
    /// 错误的测试数量
    pub error_count: u32,
}

impl TestSuite {
    /// 计算测试套件的总数量
    pub fn total_count(&self) -> u32 {
        self.passed_count + self.failed_count + self.skipped_count + self.error_count
    }

    /// 计算成功率
    pub fn success_rate(&self) -> f64 {
        let total = self.total_count();
        if total == 0 {
            0.0
        } else {
            ((self.passed_count as f64) / (total as f64)) * 100.0
        }
    }
}

/// 完整的测试报告数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestReport {
    /// 报告生成时间
    pub timestamp: DateTime<Utc>,
    /// 项目名称
    pub project_name: String,
    /// 项目版本
    pub project_version: String,
    /// 测试套件列表
    pub test_suites: Vec<TestSuite>,
    /// 总体统计信息
    pub summary: TestSummary,
    /// 覆盖率信息
    pub coverage: Option<CoverageInfo>,
    /// 环境信息
    pub environment: EnvironmentInfo,
}

/// 测试总结统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TestSummary {
    /// 总测试数量
    pub total_tests: u32,
    /// 通过的测试数量
    pub passed_tests: u32,
    /// 失败的测试数量
    pub failed_tests: u32,
    /// 跳过的测试数量
    pub skipped_tests: u32,
    /// 错误的测试数量
    pub error_tests: u32,
    /// 总执行时间（毫秒）
    pub total_duration_ms: u64,
    /// 成功率
    pub success_rate: f64,
}

/// 覆盖率信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CoverageInfo {
    /// 行覆盖率
    pub line_coverage: f64,
    /// 分支覆盖率
    pub branch_coverage: f64,
    /// 函数覆盖率
    pub function_coverage: f64,
    /// 覆盖的行数
    pub lines_covered: u32,
    /// 总行数
    pub lines_total: u32,
    /// 覆盖的分支数
    pub branches_covered: u32,
    /// 总分支数
    pub branches_total: u32,
    /// 覆盖的函数数
    pub functions_covered: u32,
    /// 总函数数
    pub functions_total: u32,
}

/// 环境信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnvironmentInfo {
    /// Rust版本
    pub rust_version: String,
    /// 操作系统
    pub os: String,
    /// 架构
    pub arch: String,
    /// 主机名
    pub hostname: String,
    /// 环境变量
    pub env_vars: HashMap<String, String>,
}

/// 历史趋势数据点
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrendDataPoint {
    /// 时间戳
    pub timestamp: DateTime<Utc>,
    /// 成功率
    pub success_rate: f64,
    /// 总测试数量
    pub total_tests: u32,
    /// 执行时间
    pub duration_ms: u64,
    /// 覆盖率
    pub coverage_rate: Option<f64>,
}

/// 趋势分析结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrendAnalysis {
    /// 历史数据点
    pub data_points: Vec<TrendDataPoint>,
    /// 成功率趋势（正向/负向/稳定）
    pub success_rate_trend: TrendDirection,
    /// 测试数量趋势
    pub test_count_trend: TrendDirection,
    /// 执行时间趋势
    pub duration_trend: TrendDirection,
    /// 覆盖率趋势
    pub coverage_trend: Option<TrendDirection>,
}

/// 趋势方向枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TrendDirection {
    /// 上升趋势
    Increasing,
    /// 下降趋势
    Decreasing,
    /// 稳定趋势
    Stable,
    /// 数据不足
    Insufficient,
}

impl std::fmt::Display for TrendDirection {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TrendDirection::Increasing => write!(f, "上升"),
            TrendDirection::Decreasing => write!(f, "下降"),
            TrendDirection::Stable => write!(f, "稳定"),
            TrendDirection::Insufficient => write!(f, "数据不足"),
        }
    }
}

/// 报告配置选项
#[derive(Debug, Clone)]
pub struct ReportConfig {
    /// 输出目录
    pub output_dir: String,
    /// 项目名称
    pub project_name: String,
    /// 项目版本
    pub project_version: String,
    /// 是否生成HTML报告
    pub generate_html: bool,
    /// 是否生成JSON报告
    pub generate_json: bool,
    /// 是否包含图表
    pub include_charts: bool,
    /// 是否包含趋势分析
    pub include_trends: bool,
    /// 历史数据保留天数
    pub history_retention_days: u32,
}

impl Default for ReportConfig {
    fn default() -> Self {
        Self {
            output_dir: "target/test-reports".to_string(),
            project_name: "Axum项目".to_string(),
            project_version: "1.0.0".to_string(),
            generate_html: true,
            generate_json: true,
            include_charts: true,
            include_trends: true,
            history_retention_days: 30,
        }
    }
}
