//! # JSON数据导出器
//!
//! 将测试结果以结构化的JSON格式导出，供后续分析或其他系统集成使用
//!
//! ## 功能特性
//! - 标准JSON格式导出
//! - 支持多种导出模式
//! - 数据压缩和优化
//! - 兼容性验证

use super::*;
use anyhow::{Result, Context};
use serde_json;
use std::fs;
use std::path::Path;

/// JSON导出器
pub struct JsonExporter {
    /// 导出配置
    config: ReportConfig,
}

/// JSON导出选项
#[derive(Debug, Clone)]
pub struct JsonExportOptions {
    /// 是否美化输出
    pub pretty_print: bool,
    /// 是否包含详细信息
    pub include_details: bool,
    /// 是否压缩输出
    pub compress: bool,
    /// 导出模式
    pub export_mode: JsonExportMode,
}

/// JSON导出模式
#[derive(Debug, Clone)]
pub enum JsonExportMode {
    /// 完整模式 - 包含所有数据
    Full,
    /// 摘要模式 - 仅包含摘要信息
    Summary,
    /// 统计模式 - 仅包含统计数据
    Statistics,
    /// 自定义模式 - 根据字段选择器导出
    Custom(Vec<String>),
}

impl Default for JsonExportOptions {
    fn default() -> Self {
        Self {
            pretty_print: true,
            include_details: true,
            compress: false,
            export_mode: JsonExportMode::Full,
        }
    }
}

impl JsonExporter {
    /// 创建新的JSON导出器
    pub fn new(config: ReportConfig) -> Self {
        Self { config }
    }

    /// 导出测试报告为JSON格式
    pub fn export_json(&self, report: &TestReport, options: Option<JsonExportOptions>) -> Result<String> {
        let options = options.unwrap_or_default();
        
        // 根据导出模式处理数据
        let export_data = self.prepare_export_data(report, &options)?;
        
        // 序列化为JSON
        let json_string = if options.pretty_print {
            serde_json::to_string_pretty(&export_data)
        } else {
            serde_json::to_string(&export_data)
        }.with_context(|| "JSON序列化失败")?;

        // 保存到文件
        self.save_json_file(&json_string, &options)?;

        println!("✅ JSON测试报告已导出");
        Ok(json_string)
    }

    /// 准备导出数据
    fn prepare_export_data(&self, report: &TestReport, options: &JsonExportOptions) -> Result<serde_json::Value> {
        match &options.export_mode {
            JsonExportMode::Full => {
                // 完整模式 - 导出所有数据
                Ok(serde_json::to_value(report)?)
            },
            JsonExportMode::Summary => {
                // 摘要模式 - 仅导出摘要信息
                Ok(serde_json::json!({
                    "timestamp": report.timestamp,
                    "project_name": report.project_name,
                    "project_version": report.project_version,
                    "summary": report.summary,
                    "environment": {
                        "rust_version": report.environment.rust_version,
                        "os": report.environment.os,
                        "arch": report.environment.arch
                    }
                }))
            },
            JsonExportMode::Statistics => {
                // 统计模式 - 仅导出统计数据
                self.create_statistics_export(report)
            },
            JsonExportMode::Custom(fields) => {
                // 自定义模式 - 根据字段选择器导出
                self.create_custom_export(report, fields)
            }
        }
    }

    /// 创建统计数据导出
    fn create_statistics_export(&self, report: &TestReport) -> Result<serde_json::Value> {
        let mut suite_stats = Vec::new();
        
        for suite in &report.test_suites {
            suite_stats.push(serde_json::json!({
                "name": suite.name,
                "total_tests": suite.total_count(),
                "passed": suite.passed_count,
                "failed": suite.failed_count,
                "skipped": suite.skipped_count,
                "error": suite.error_count,
                "success_rate": suite.success_rate(),
                "duration_ms": suite.total_duration_ms
            }));
        }

        Ok(serde_json::json!({
            "timestamp": report.timestamp,
            "project_name": report.project_name,
            "overall_summary": report.summary,
            "suite_statistics": suite_stats,
            "coverage": report.coverage
        }))
    }

    /// 创建自定义字段导出
    fn create_custom_export(&self, report: &TestReport, fields: &[String]) -> Result<serde_json::Value> {
        let full_data = serde_json::to_value(report)?;
        let mut custom_data = serde_json::Map::new();

        for field in fields {
            if let Some(value) = self.extract_field_value(&full_data, field) {
                custom_data.insert(field.clone(), value);
            }
        }

        Ok(serde_json::Value::Object(custom_data))
    }

    /// 从JSON值中提取指定字段
    fn extract_field_value(&self, data: &serde_json::Value, field_path: &str) -> Option<serde_json::Value> {
        let parts: Vec<&str> = field_path.split('.').collect();
        let mut current = data;

        for part in parts {
            match current {
                serde_json::Value::Object(map) => {
                    current = map.get(part)?;
                },
                serde_json::Value::Array(arr) => {
                    if let Ok(index) = part.parse::<usize>() {
                        current = arr.get(index)?;
                    } else {
                        return None;
                    }
                },
                _ => return None,
            }
        }

        Some(current.clone())
    }

    /// 保存JSON文件
    fn save_json_file(&self, json_content: &str, options: &JsonExportOptions) -> Result<()> {
        // 确保输出目录存在
        let output_dir = Path::new(&self.config.output_dir);
        if !output_dir.exists() {
            fs::create_dir_all(output_dir)
                .with_context(|| format!("创建输出目录失败: {}", self.config.output_dir))?;
        }

        // 根据导出模式确定文件名
        let filename = match &options.export_mode {
            JsonExportMode::Full => "test_report_full.json",
            JsonExportMode::Summary => "test_report_summary.json",
            JsonExportMode::Statistics => "test_report_statistics.json",
            JsonExportMode::Custom(_) => "test_report_custom.json",
        };

        let json_path = output_dir.join(filename);
        
        // 如果启用压缩，可以在这里添加压缩逻辑
        let final_content = if options.compress {
            self.compress_json_content(json_content)?
        } else {
            json_content.to_string()
        };

        fs::write(&json_path, final_content)
            .with_context(|| format!("写入JSON文件失败: {:?}", json_path))?;

        println!("📄 JSON报告已保存: {:?}", json_path);
        Ok(())
    }

    /// 压缩JSON内容（简单的空白字符移除）
    fn compress_json_content(&self, content: &str) -> Result<String> {
        // 这里可以实现更复杂的压缩逻辑，比如使用gzip
        // 目前只是移除不必要的空白字符
        let compressed: String = content
            .lines()
            .map(|line| line.trim())
            .filter(|line| !line.is_empty())
            .collect::<Vec<_>>()
            .join("");
        
        Ok(compressed)
    }

    /// 验证JSON格式
    pub fn validate_json(&self, json_content: &str) -> Result<bool> {
        match serde_json::from_str::<serde_json::Value>(json_content) {
            Ok(_) => {
                println!("✅ JSON格式验证通过");
                Ok(true)
            },
            Err(e) => {
                println!("❌ JSON格式验证失败: {}", e);
                Ok(false)
            }
        }
    }

    /// 导出多种格式的JSON报告
    pub fn export_multiple_formats(&self, report: &TestReport) -> Result<Vec<String>> {
        let mut results = Vec::new();

        // 导出完整格式
        let full_json = self.export_json(report, Some(JsonExportOptions {
            export_mode: JsonExportMode::Full,
            ..Default::default()
        }))?;
        results.push(full_json);

        // 导出摘要格式
        let summary_json = self.export_json(report, Some(JsonExportOptions {
            export_mode: JsonExportMode::Summary,
            ..Default::default()
        }))?;
        results.push(summary_json);

        // 导出统计格式
        let stats_json = self.export_json(report, Some(JsonExportOptions {
            export_mode: JsonExportMode::Statistics,
            ..Default::default()
        }))?;
        results.push(stats_json);

        println!("✅ 多格式JSON报告导出完成");
        Ok(results)
    }

    /// 创建兼容性报告
    pub fn create_compatibility_report(&self, report: &TestReport) -> Result<String> {
        // 创建与常见测试框架兼容的JSON格式
        let compatible_format = serde_json::json!({
            "version": "1.0",
            "framework": "axum-test-reporter",
            "timestamp": report.timestamp.timestamp(),
            "results": {
                "total": report.summary.total_tests,
                "passed": report.summary.passed_tests,
                "failed": report.summary.failed_tests,
                "skipped": report.summary.skipped_tests,
                "duration": report.summary.total_duration_ms
            },
            "suites": report.test_suites.iter().map(|suite| {
                serde_json::json!({
                    "name": suite.name,
                    "tests": suite.test_cases.iter().map(|test| {
                        serde_json::json!({
                            "name": test.name,
                            "status": match test.status {
                                TestStatus::Passed => "pass",
                                TestStatus::Failed => "fail",
                                TestStatus::Skipped => "skip",
                                TestStatus::Error => "error"
                            },
                            "duration": test.duration_ms,
                            "error": test.error_message
                        })
                    }).collect::<Vec<_>>()
                })
            }).collect::<Vec<_>>()
        });

        let json_string = serde_json::to_string_pretty(&compatible_format)?;
        
        // 保存兼容性报告
        let output_dir = Path::new(&self.config.output_dir);
        let compat_path = output_dir.join("test_report_compatible.json");
        fs::write(&compat_path, &json_string)
            .with_context(|| format!("写入兼容性报告失败: {:?}", compat_path))?;

        println!("🔄 兼容性JSON报告已生成: {:?}", compat_path);
        Ok(json_string)
    }
}
