//! # 任务处理器
//!
//! 处理任务相关的HTTP请求，集成WebSocket实时更新功能

use super::*;
use crate::routes::AppState;
use app_common::middleware::AuthenticatedUser;
use tracing::{info, warn};

/// 获取所有任务处理器
///
/// 处理 GET /api/tasks 请求
pub async fn get_all_tasks(
    State(state): State<AppState>,
    user: AuthenticatedUser,
) -> std::result::Result<impl IntoResponse, axum::response::Response> {
    info!(user_id = %user.user_id, username = %user.username, "收到获取所有任务请求");

    let user_id = user.user_id;

    // 调用任务应用服务获取用户任务
    let tasks = match state.task_service.get_user_tasks(user_id).await {
        Ok(tasks) => tasks,
        Err(e) => {
            return Err(app_error_to_response(e));
        }
    };

    info!(task_count = tasks.len(), "成功获取任务列表");

    Ok(success_response(tasks, "获取任务列表成功").into_response())
}

/// 创建任务处理器
///
/// 处理 POST /api/tasks 请求
/// 遵循企业级API命名规范：以HTTP动词开头
pub async fn post_task(
    State(state): State<AppState>,
    user: AuthenticatedUser,
    Json(payload): Json<CreateTaskRequest>,
) -> Result<impl IntoResponse> {
    info!(user_id = %user.user_id, username = %user.username, title = %payload.title, "收到创建任务请求");

    let user_id = user.user_id;

    // 调用任务应用服务创建任务
    let task = state.task_service.create_task(payload, user_id).await?;

    info!(task_id = %task.id, title = %task.title, "任务创建成功");

    // 广播任务创建的实时更新
    let task_json = match serde_json::to_value(&task) {
        Ok(json) => json,
        Err(e) => {
            warn!("任务序列化失败: {}", e);
            serde_json::json!({
                "id": task.id.to_string(),
                "title": task.title,
                "description": task.description,
                "priority": task.priority,
                "completed": task.completed,
                "created_at": task.created_at,
                "updated_at": task.updated_at
            })
        }
    };

    // 通过WebSocket广播任务创建消息
    if let Err(e) = state
        .websocket_service
        .broadcast_task_created(task_json, Some(user_id))
        .await
    {
        warn!("广播任务创建消息失败: {}", e);
        // 不影响主要功能，只记录警告
    }

    Ok((StatusCode::CREATED, success_response(task, "任务创建成功")))
}

/// 根据ID获取任务处理器
///
/// 处理 GET /api/tasks/:id 请求
pub async fn get_task_by_id(
    State(state): State<AppState>,
    user: AuthenticatedUser,
    Path(task_id): Path<Uuid>,
) -> Result<impl IntoResponse> {
    info!(user_id = %user.user_id, task_id = %task_id, "收到获取任务详情请求");

    let user_id = user.user_id;

    // 调用任务应用服务查找任务
    let task = state
        .task_service
        .find_task_by_id(task_id, user_id)
        .await?
        .ok_or_else(|| AppError::NotFound("任务不存在".to_string()))?;

    info!(task_id = %task.id, "成功获取任务详情");

    Ok(success_response(task, "获取任务详情成功"))
}

/// 更新任务处理器
///
/// 处理 PUT /api/tasks/:id 请求
/// 遵循企业级API命名规范：以HTTP动词开头
pub async fn put_task(
    State(state): State<AppState>,
    user: AuthenticatedUser,
    Path(task_id): Path<Uuid>,
    Json(payload): Json<UpdateTaskRequest>,
) -> Result<impl IntoResponse> {
    info!(user_id = %user.user_id, task_id = %task_id, "收到更新任务请求");
    info!("更新任务请求数据: {:?}", payload);

    let user_id = user.user_id;

    // 调用任务应用服务更新任务
    let task = state
        .task_service
        .update_task(task_id, payload, user_id)
        .await?;

    info!(task_id = %task.id, "任务更新成功");

    // 广播任务更新的实时更新
    let task_json = match serde_json::to_value(&task) {
        Ok(json) => json,
        Err(e) => {
            warn!("任务序列化失败: {}", e);
            serde_json::json!({
                "id": task.id.to_string(),
                "title": task.title,
                "description": task.description,
                "priority": task.priority,
                "completed": task.completed,
                "created_at": task.created_at,
                "updated_at": task.updated_at
            })
        }
    };

    // 通过WebSocket广播任务更新消息
    if let Err(e) = state
        .websocket_service
        .broadcast_task_updated(task_json, Some(user_id))
        .await
    {
        warn!("广播任务更新消息失败: {}", e);
        // 不影响主要功能，只记录警告
    }

    Ok(success_response(task, "任务更新成功"))
}

/// 删除任务处理器
///
/// 处理 DELETE /api/tasks/:id 请求
pub async fn delete_task(
    State(state): State<AppState>,
    user: AuthenticatedUser,
    Path(task_id): Path<Uuid>,
) -> Result<impl IntoResponse> {
    info!(user_id = %user.user_id, task_id = %task_id, "收到删除任务请求");

    let user_id = user.user_id;

    // 调用任务应用服务删除任务
    state.task_service.delete_task(task_id, user_id).await?;

    info!(task_id = %task_id, "任务删除成功");

    // 广播任务删除的实时更新
    // 注意：这里需要将UUID转换为u64，如果不兼容则使用字符串形式
    let task_id_u64 = task_id.as_u128() as u64; // 简化转换，实际项目中可能需要更复杂的处理

    // 通过WebSocket广播任务删除消息
    if let Err(e) = state
        .websocket_service
        .broadcast_task_deleted(task_id_u64, Some(user_id))
        .await
    {
        warn!("广播任务删除消息失败: {}", e);
        // 不影响主要功能，只记录警告
    }

    Ok((StatusCode::NO_CONTENT, ()))
}

// 测试实现已移至集成测试目录
// 参考: tests/e2e_task_*.rs 文件中的完整测试实现
