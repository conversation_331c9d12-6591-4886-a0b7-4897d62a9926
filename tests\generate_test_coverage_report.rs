//! # 测试覆盖率报告生成器
//!
//! 生成详细的测试覆盖率报告，包括重构建议
//! 遵循Context7 MCP最佳实践

mod test_coverage_analysis;
mod test_refactoring_tool;

use std::fs;
use std::path::Path;
use test_coverage_analysis::TestCoverageAnalyzer;
use test_refactoring_tool::TestRefactoringTool;

/// 测试覆盖率报告生成器
pub struct TestCoverageReportGenerator {
    analyzer: TestCoverageAnalyzer,
    refactoring_tool: TestRefactoringTool,
}

impl TestCoverageReportGenerator {
    /// 创建新的报告生成器
    pub fn new() -> Self {
        Self {
            analyzer: TestCoverageAnalyzer::new(),
            refactoring_tool: TestRefactoringTool::new(),
        }
    }

    /// 生成完整的测试覆盖率报告
    pub fn generate_full_report(
        &mut self,
        project_root: &str,
    ) -> Result<String, Box<dyn std::error::Error>> {
        // 分析测试覆盖率
        self.analyzer.scan_test_files(project_root)?;

        // 分析重构建议
        self.refactoring_tool.analyze_and_suggest(project_root)?;

        // 生成综合报告
        let mut report = String::new();

        // 报告头部
        report.push_str("# Axum Tutorial 项目测试覆盖率与质量报告\n\n");
        report.push_str(&format!(
            "**生成时间**: {}\n",
            chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC")
        ));
        report.push_str(&format!("**项目路径**: {project_root}\n\n"));

        // 执行摘要
        report.push_str("## 📊 执行摘要\n\n");
        let stats = self.analyzer.get_coverage_stats();
        let suggestions = self.refactoring_tool.get_suggestions();

        report.push_str(&format!("- **总测试数量**: {} 个\n", stats.total_tests));
        report.push_str(&format!("- **单元测试**: {} 个\n", stats.unit_tests));
        report.push_str(&format!("- **集成测试**: {} 个\n", stats.integration_tests));
        report.push_str(&format!(
            "- **测试覆盖率**: {:.1}%\n",
            stats.coverage_percentage
        ));
        report.push_str(&format!(
            "- **重复模式**: {} 个\n",
            stats.duplicate_patterns
        ));
        report.push_str(&format!("- **重构建议**: {} 条\n\n", suggestions.len()));

        // 质量评估
        report.push_str("## 🎯 质量评估\n\n");
        let quality_score = self.calculate_quality_score();
        report.push_str(&format!("**整体质量评分**: {quality_score:.1}/10\n\n"));

        if quality_score >= 8.0 {
            report.push_str("✅ **优秀**: 测试质量很高，代码重复度低\n\n");
        } else if quality_score >= 6.0 {
            report.push_str("⚠️ **良好**: 测试质量不错，但有改进空间\n\n");
        } else if quality_score >= 4.0 {
            report.push_str("🔶 **一般**: 测试质量中等，建议重构\n\n");
        } else {
            report.push_str("🔴 **需要改进**: 测试质量较低，急需重构\n\n");
        }

        // 详细的覆盖率分析
        report.push_str("---\n\n");
        report.push_str(&self.analyzer.generate_report());

        // 重构建议
        report.push_str("---\n\n");
        report.push_str(&self.refactoring_tool.generate_refactoring_report());

        // 最佳实践建议
        report.push_str("---\n\n");
        report.push_str(&self.generate_best_practices_section());

        Ok(report)
    }

    /// 计算质量评分
    fn calculate_quality_score(&self) -> f64 {
        let stats = self.analyzer.get_coverage_stats();
        let suggestions = self.refactoring_tool.get_suggestions();

        // 基础分数（基于测试数量）
        let test_score = if stats.total_tests >= 100 {
            3.0
        } else if stats.total_tests >= 50 {
            2.5
        } else if stats.total_tests >= 20 {
            2.0
        } else {
            1.0
        };

        // 覆盖率分数
        let coverage_score = (stats.coverage_percentage / 100.0) * 3.0;

        // 重复度扣分
        let duplication_penalty = ((stats.duplicate_patterns as f64) * 0.1).min(2.0);

        // 重构建议扣分
        let refactoring_penalty = ((suggestions.len() as f64) * 0.05).min(1.0);

        // 计算最终分数
        let final_score = test_score + coverage_score - duplication_penalty - refactoring_penalty;
        final_score.clamp(0.0, 10.0)
    }

    /// 生成最佳实践建议部分
    fn generate_best_practices_section(&self) -> String {
        let mut section = String::new();

        section.push_str("# 🚀 测试最佳实践建议\n\n");

        section.push_str("## 基于Context7 MCP的Rust测试最佳实践\n\n");

        section.push_str("### 1. 测试文件组织\n");
        section.push_str("- ✅ 将单元测试放在 `#[cfg(test)]` 模块中\n");
        section.push_str("- ✅ 将集成测试放在 `tests/` 目录中\n");
        section.push_str("- ✅ 使用描述性的测试函数名称\n");
        section.push_str("- ✅ 按功能模块组织测试\n\n");

        section.push_str("### 2. 测试命名规范\n");
        section.push_str("- ✅ 使用 `test_` 前缀\n");
        section.push_str("- ✅ 描述测试的行为和期望结果\n");
        section.push_str("- ✅ 例如: `test_create_user_with_valid_data_should_succeed`\n\n");

        section.push_str("### 3. 测试数据管理\n");
        section.push_str("- ✅ 使用构建器模式创建测试数据\n");
        section.push_str("- ✅ 提供默认值，允许覆盖特定字段\n");
        section.push_str("- ✅ 使用工厂函数生成常用测试对象\n\n");

        section.push_str("### 4. 断言最佳实践\n");
        section.push_str("- ✅ 使用具体的断言而不是通用的 `assert!`\n");
        section.push_str("- ✅ 提供有意义的错误消息\n");
        section.push_str("- ✅ 一个测试只验证一个行为\n\n");

        section.push_str("### 5. 异步测试\n");
        section.push_str("- ✅ 使用 `#[tokio::test]` 进行异步测试\n");
        section.push_str("- ✅ 正确处理超时和错误情况\n");
        section.push_str("- ✅ 避免在测试中使用 `block_on`\n\n");

        section.push_str("### 6. 模拟和存根\n");
        section.push_str("- ✅ 使用依赖注入便于测试\n");
        section.push_str("- ✅ 创建测试专用的实现\n");
        section.push_str("- ✅ 避免过度模拟\n\n");

        section.push_str("### 7. 测试隔离\n");
        section.push_str("- ✅ 每个测试应该独立运行\n");
        section.push_str("- ✅ 使用内存数据库进行数据库测试\n");
        section.push_str("- ✅ 清理测试产生的副作用\n\n");

        section.push_str("### 8. 性能测试\n");
        section.push_str("- ✅ 使用 `#[bench]` 进行基准测试\n");
        section.push_str("- ✅ 测试关键路径的性能\n");
        section.push_str("- ✅ 设置合理的性能阈值\n\n");

        // 项目特定建议
        let stats = self.analyzer.get_coverage_stats();
        section.push_str("## 🎯 项目特定建议\n\n");

        if stats.total_tests < 50 {
            section.push_str("- 🔴 **增加测试覆盖率**: 当前测试数量较少，建议增加更多单元测试\n");
        }

        if stats.unit_tests < stats.integration_tests {
            section.push_str("- ⚠️ **平衡测试类型**: 单元测试数量应该多于集成测试\n");
        }

        if stats.duplicate_patterns > 10 {
            section.push_str("- 🔶 **减少代码重复**: 发现较多重复模式，建议重构\n");
        }

        section.push_str("\n### 🛠️ 推荐工具\n\n");
        section.push_str("- **cargo-tarpaulin**: 生成覆盖率报告\n");
        section.push_str("- **cargo-nextest**: 更快的测试运行器\n");
        section.push_str("- **proptest**: 属性测试框架\n");
        section.push_str("- **mockall**: 模拟框架\n");
        section.push_str("- **serial_test**: 串行测试执行\n\n");

        section
    }

    /// 保存报告到文件
    pub fn save_report_to_file(
        &self,
        report: &str,
        file_path: &str,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // 确保目录存在
        if let Some(parent) = Path::new(file_path).parent() {
            fs::create_dir_all(parent)?;
        }

        fs::write(file_path, report)?;
        Ok(())
    }
}

impl Default for TestCoverageReportGenerator {
    fn default() -> Self {
        Self::new()
    }
}

/// 主函数，用于生成测试覆盖率报告
#[allow(dead_code)]
fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 开始生成测试覆盖率报告...");

    let mut generator = TestCoverageReportGenerator::new();
    let report = generator.generate_full_report(".")?;

    let report_path = "target/test_coverage_report.md";
    generator.save_report_to_file(&report, report_path)?;

    println!("✅ 测试覆盖率报告已生成: {report_path}");
    println!("📊 报告长度: {} 字符", report.len());

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_report_generator_creation() {
        let generator = TestCoverageReportGenerator::new();
        // 基本创建测试
        assert_eq!(generator.analyzer.get_coverage_stats().total_tests, 0);
        assert_eq!(generator.refactoring_tool.get_suggestions().len(), 0);
    }

    #[test]
    fn test_quality_score_calculation() {
        let mut generator = TestCoverageReportGenerator::new();

        // 模拟一些统计数据
        {
            let stats = generator.analyzer.get_coverage_stats_mut();
            stats.total_tests = 50;
            stats.coverage_percentage = 80.0;
            stats.duplicate_patterns = 5;
        }

        let score = generator.calculate_quality_score();

        // 验证分数在合理范围内
        assert!((0.0..=10.0).contains(&score));
        println!("质量评分: {score:.1}");
    }

    #[test]
    fn test_best_practices_section_generation() {
        let generator = TestCoverageReportGenerator::new();
        let section = generator.generate_best_practices_section();

        // 验证包含关键内容
        assert!(section.contains("测试最佳实践建议"));
        assert!(section.contains("Context7 MCP"));
        assert!(section.contains("测试文件组织"));
        assert!(section.contains("断言最佳实践"));
        assert!(section.contains("异步测试"));
        assert!(section.contains("推荐工具"));
    }

    #[test]
    fn test_report_file_saving() {
        let generator = TestCoverageReportGenerator::new();
        let test_report = "# 测试报告\n\n这是一个测试报告。";
        let file_path = "target/test_report_save.md";

        let result = generator.save_report_to_file(test_report, file_path);
        assert!(result.is_ok());

        // 验证文件是否创建
        assert!(Path::new(file_path).exists());

        // 验证文件内容
        let content = fs::read_to_string(file_path).unwrap();
        assert_eq!(content, test_report);

        // 清理测试文件
        let _ = fs::remove_file(file_path);
    }

    #[test]
    fn test_full_report_structure() {
        let mut generator = TestCoverageReportGenerator::new();

        // 注意：这个测试可能会扫描实际的项目文件
        // 在CI环境中可能需要模拟数据
        let result = generator.generate_full_report(".");

        match result {
            Ok(report) => {
                // 验证报告结构
                assert!(report.contains("Axum Tutorial 项目测试覆盖率与质量报告"));
                assert!(report.contains("执行摘要"));
                assert!(report.contains("质量评估"));
                assert!(report.contains("测试最佳实践建议"));

                println!("报告长度: {} 字符", report.len());

                // 可选：保存报告以供查看
                let _ = generator.save_report_to_file(&report, "target/full_test_report.md");
            }
            Err(e) => {
                println!("生成报告时出错: {e}");
                // 在某些环境中可能无法访问文件系统，这是正常的
            }
        }
    }
}
