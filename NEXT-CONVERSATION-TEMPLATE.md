# Axum企业级应用前端API集成升级项目 - 新对话连续性模板

## 📋 项目当前状态（2025年7月23日）

### 项目基本信息
- **项目名称**: Axum企业级应用前端API集成升级计划
- **项目目标**: 将21个未集成API接口系统性集成到前端，提升API利用率从32%到90%+
- **技术栈**: Axum 0.8.4 + Tokio 1.45.1 + SeaORM + PostgreSQL 17 + DragonflyDB
- **架构**: 模块化领域驱动设计(Modular DDD) + 整洁架构
- **部署环境**: 混合架构（本机Windows 10运行Axum，WSL2容器运行数据库）

### 项目进度状态
- **总任务数**: 25个主要任务，已全部完成详细拆分
- **当前阶段**: 第1阶段 - 基础架构重构（第1-3周）
- **当前任务**: 任务ID 27 - 重构前端代码为ES6模块（复杂度8分，已拆分为8个子任务）
- **任务状态**: 准备开始实施
- **完成进度**: 0%（规划阶段完成100%）

### 技术规范要求
- **严格遵循**: rust_axum_Rules.md编码规范
- **数据库**: 使用SeaORM（不使用SQLx），维护API向后兼容性
- **编译检查**: 确保每步可编译 `cargo check --workspace`
- **开发模式**: 遵循DRY、SOLID原则、TDD开发模式（先写测试再写代码）
- **注释规范**: 所有代码注释必须使用中文
- **错误处理**: 遇到问题时必须先调用Context7 MCP工具查询2025年最新最佳实践

## 🎯 下一步行动指南

### 立即开始的任务
**任务ID 27: 重构前端代码为ES6模块**

#### 子任务清单（按执行顺序）
1. **子任务27.1**: 分析现有代码结构
   - 全面分析当前的单体JavaScript代码
   - 识别功能模块和依赖关系
   - 绘制模块依赖关系图
   - **预计时间**: 15-20分钟

2. **子任务27.2**: 设计模块化架构
   - 基于分析结果设计ES6模块化整体架构
   - 确定模块划分方案
   - 设计模块间依赖关系
   - **预计时间**: 15-20分钟

3. **子任务27.3**: 创建目录结构
   - 按照设计的模块化架构创建项目目录结构
   - 在src目录下创建js文件夹
   - 根据模块划分创建对应的子目录和文件结构
   - **预计时间**: 10-15分钟

4. **子任务27.4**: 拆分代码为功能模块
   - 将现有代码按照设计的模块化架构拆分
   - 将用户管理相关代码拆分到user.js
   - 将聊天功能相关代码拆分到chat.js
   - 将WebSocket通信相关代码拆分到websocket.js
   - **预计时间**: 20-25分钟

5. **子任务27.5**: 实现模块化语法
   - 使用ES6的export/import语法实现模块间通信
   - 在每个模块中使用export导出公共接口
   - 在需要的地方使用import引入模块
   - **预计时间**: 15-20分钟

6. **子任务27.6**: 配置模块加载器
   - 配置适合ES6模块的加载器
   - 选择合适的模块加载器（Webpack、Rollup或原生ES6）
   - 配置加载器的入口文件和输出路径
   - **预计时间**: 20-25分钟

7. **子任务27.7**: 测试模块间通信
   - 验证模块间的通信和数据传递
   - 编写测试用例验证模块间的调用
   - 检查模块导出和导入的接口
   - **预计时间**: 15-20分钟

8. **子任务27.8**: 优化和文档更新
   - 对重构后的代码进行优化
   - 更新模块化架构文档
   - 编写模块使用说明和示例
   - **预计时间**: 15-20分钟

### 验收标准
- ✅ 模块加载无错误
- ✅ 所有现有功能保持不变
- ✅ 通过 `cargo check --workspace` 检查
- ✅ 代码结构清晰，符合ES6模块规范
- ✅ 所有新增代码包含中文注释

## 🔧 开发环境准备

### 必要工具检查
- [ ] Rust 2024 Edition 环境
- [ ] Node.js 和 npm/yarn（用于前端模块管理）
- [ ] 代码编辑器（推荐VSCode with Rust插件）
- [ ] Git版本控制
- [ ] WSL2环境（用于数据库容器）

### 项目文件结构
```
d:\ceshi\ceshi\axum-tutorial\
├── .taskmaster/                 # Task-Master-AI项目管理
│   ├── tasks/                  # 任务文件
│   ├── docs/                   # 项目文档
│   └── reports/                # 复杂度分析报告
├── static/                     # 前端静态文件
│   └── index.html             # 当前单页面应用
├── src/                       # 后端Rust代码
├── README-API-Integration-Plan.md  # 项目规划文档
├── TASK-OPTIMIZATION-REPORT.md     # 任务优化报告
└── NEXT-CONVERSATION-TEMPLATE.md   # 本文件
```

## 📞 新对话开始提示词

### 复制以下内容到新对话：

```
我正在进行Axum企业级应用前端API集成升级项目，需要继续之前的工作，请按照我们一直遵循的5步工作流程（Task-Master-AI查询任务详情并分析 → Context7 MCP验证 → TDD实现 → 质量保证 → 任务完成记录）来组织回答。

**项目背景**：
- 基于Rust 2024 Edition的Axum企业级后端项目
- 目标：将21个未集成API接口系统性集成到前端，提升API利用率从32%到90%+
- 技术栈：Axum 0.8.4 + Tokio 1.45.1 + SeaORM + PostgreSQL 17 + DragonflyDB
- 架构：模块化领域驱动设计(Modular DDD) + 整洁架构

**当前状态**：
- 项目根目录：d:\ceshi\ceshi\axum-tutorial
- 已完成：项目规划和任务拆分（25个主要任务，已全部详细拆分）
- 当前任务：任务ID 27 - 重构前端代码为ES6模块（8个子任务）
- 任务状态：准备开始实施

当前时间是2025-7月，你的训练数据截止是2024年，确保我提供的建议是基于2025-7月最新的、前沿的最佳实践。记住：写代码或测试过程中遇到错误需要处理错误修复代码时，请必须先调用Context7的MCP核查最佳实践并继续未完成的任务。

**技术要求**：
- 严格遵循rust_axum_Rules.md编码规范
- 使用SeaORM（不使用SQLx），维护API向后兼容性
- 确保每步可编译：cargo check --workspace
- 遵循DRY、SOLID原则、TDD开发模式（先写测试再写代码）
- 所有代码注释必须使用中文
- 错误处理：遇到问题时必须先调用Context7 MCP工具查询2025年最新最佳实践



## 🎯 成功标准提醒

### 每个任务完成后的检查清单
- [ ] 运行 `cargo check --workspace` 确保编译通过
- [ ] 运行 `cargo clippy` 检查代码质量
- [ ] 运行 `cargo test` 确保测试通过
- [ ] 验证新功能不破坏现有功能
- [ ] 确保页面加载时间<2秒，API响应时间<500ms
- [ ] 所有新增代码包含详细中文注释
- [ ] 更新Task-Master-AI任务状态

### 项目最终目标
- **API利用率**: 从32%提升到90%+
- **功能模块**: 从5个增加到15个
- **测试覆盖率**: >80%
- **用户体验**: 支持4种角色权限，响应式设计，暗色主题
- **技术掌握**: Axum框架高级特性，企业级架构设计能力
**对话结束提醒**：完成任务后提醒我开启新对话以避免上下文过长。提供下一个任务的新对话框连续性提示词模版 
---

**模板创建时间**: 2025年7月23日
**项目阶段**: 第1阶段 - 基础架构重构
**准备状态**: ✅ 完成，可以开始实施
