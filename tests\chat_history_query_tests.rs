//! # 聊天历史消息查询功能测试
//!
//! 测试聊天室历史消息查询功能，包括：
//! - 基础历史消息查询
//! - 分页查询功能
//! - 时间范围查询
//! - 权限验证
//! - 消息排序验证
//! - 大数据量查询性能
//!
//! 遵循TDD原则，确保与Axum 0.8.4兼容
//! 基于Context7 MCP最佳实践实现

use anyhow::Result;
use reqwest::Client;
use serde_json::{Value, json};
use std::time::Duration;
use tokio::time::sleep;
use uuid::Uuid;

/// 测试配置常量
const API_BASE_URL: &str = "http://127.0.0.1:3000/api";
const TEST_TIMEOUT: Duration = Duration::from_secs(30);

/// 测试用户凭据
const TEST_USER_USERNAME: &str = "testuser456";
const TEST_USER_EMAIL: &str = "<EMAIL>";
const TEST_USER_PASSWORD: &str = "password123";

/// 聊天历史查询测试辅助结构
///
/// 【功能】: 提供聊天历史查询测试的通用方法和状态管理
/// 【设计】: 基于Context7 MCP最佳实践，支持API调用和数据验证
#[derive(Debug)]
struct ChatHistoryTestHelper {
    client: Client,
    jwt_token: Option<String>,
    user_id: Option<Uuid>,
    username: String,
}

impl ChatHistoryTestHelper {
    /// 创建新的聊天历史测试辅助实例
    fn new() -> Self {
        Self {
            client: Client::new(),
            jwt_token: None,
            user_id: None,
            username: TEST_USER_USERNAME.to_string(),
        }
    }

    /// 用户注册
    async fn register(&self) -> Result<()> {
        let register_url = format!("{}/auth/register", API_BASE_URL);
        let register_data = json!({
            "username": TEST_USER_USERNAME,
            "email": TEST_USER_EMAIL,
            "password": TEST_USER_PASSWORD
        });

        let response = self
            .client
            .post(&register_url)
            .json(&register_data)
            .send()
            .await?;

        // 如果用户已存在，忽略错误
        if response.status().is_success() || response.status() == reqwest::StatusCode::CONFLICT {
            println!("✅ 用户注册成功或已存在");
            Ok(())
        } else {
            anyhow::bail!("注册失败: {}", response.status());
        }
    }

    /// 用户登录并获取JWT token
    async fn login(&mut self) -> Result<String> {
        let login_url = format!("{}/auth/login", API_BASE_URL);
        let login_data = json!({
            "username": TEST_USER_USERNAME,
            "password": TEST_USER_PASSWORD
        });

        let mut response = self
            .client
            .post(&login_url)
            .json(&login_data)
            .send()
            .await?;

        if !response.status().is_success() {
            // 如果登录失败，尝试先注册用户
            println!("⚠️ 登录失败，尝试注册用户...");
            self.register().await?;

            // 重新尝试登录
            response = self
                .client
                .post(&login_url)
                .json(&login_data)
                .send()
                .await?;
            if !response.status().is_success() {
                anyhow::bail!("登录失败: {}", response.status());
            }
        }

        let response_json: Value = response.json().await?;

        // 提取JWT token
        let token = if let Some(token) = response_json["data"]["token"].as_str() {
            token.to_string()
        } else if let Some(token) = response_json["token"].as_str() {
            token.to_string()
        } else if let Some(data) = response_json["data"].as_object() {
            if let Some(token) = data["access_token"].as_str() {
                token.to_string()
            } else {
                anyhow::bail!(
                    "响应中缺少token字段，响应格式: {}",
                    serde_json::to_string_pretty(&response_json)?
                );
            }
        } else {
            anyhow::bail!(
                "响应中缺少token字段，响应格式: {}",
                serde_json::to_string_pretty(&response_json)?
            );
        };

        // 提取用户ID（如果可用）
        if let Some(user_data) = response_json["data"]["user"].as_object() {
            if let Some(user_id_str) = user_data["id"].as_str() {
                if let Ok(user_id) = Uuid::parse_str(user_id_str) {
                    self.user_id = Some(user_id);
                }
            }
        }

        self.jwt_token = Some(token.clone());
        println!("✅ 登录成功，获取到JWT token");
        Ok(token)
    }

    /// 创建聊天室
    async fn create_chat_room(&self, name: &str, description: Option<&str>) -> Result<Value> {
        let create_room_url = format!("{}/chat/rooms", API_BASE_URL);
        let room_data = json!({
            "name": name,
            "description": description,
            "room_type": "public",
            "is_private": false,
            "member_ids": []
        });

        let response = self
            .client
            .post(&create_room_url)
            .header(
                "Authorization",
                format!("Bearer {}", self.jwt_token.as_ref().unwrap()),
            )
            .json(&room_data)
            .send()
            .await?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await?;
            anyhow::bail!("创建聊天室失败: {}, 错误详情: {}", status, error_text);
        }

        let room_response: Value = response.json().await?;
        println!("✅ 聊天室创建成功");
        Ok(room_response)
    }

    /// 加入聊天室
    async fn join_chat_room(&self, room_id: &str) -> Result<Value> {
        let join_room_url = format!("{}/chat/rooms/{}/join", API_BASE_URL, room_id);

        let response = self
            .client
            .post(&join_room_url)
            .header(
                "Authorization",
                format!("Bearer {}", self.jwt_token.as_ref().unwrap()),
            )
            .send()
            .await?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await?;
            anyhow::bail!("加入聊天室失败: {}, 错误详情: {}", status, error_text);
        }

        let join_response: Value = response.json().await?;
        println!("✅ 成功加入聊天室");
        Ok(join_response)
    }

    /// 发送消息到聊天室
    async fn send_message_to_room(&self, room_id: &str, content: &str) -> Result<Value> {
        let send_message_url = format!("{}/chat/rooms/{}/messages", API_BASE_URL, room_id);
        let message_data = json!({
            "room_id": room_id,
            "content": content
        });

        let response = self
            .client
            .post(&send_message_url)
            .header(
                "Authorization",
                format!("Bearer {}", self.jwt_token.as_ref().unwrap()),
            )
            .json(&message_data)
            .send()
            .await?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await?;
            anyhow::bail!("发送消息失败: {}, 错误详情: {}", status, error_text);
        }

        let message_response: Value = response.json().await?;
        println!("✅ 消息发送成功: {}", content);
        Ok(message_response)
    }

    /// 获取聊天室历史消息
    async fn get_chat_history(&self, room_id: &str, limit: Option<u32>) -> Result<Value> {
        let mut history_url = format!("{}/chat/rooms/{}/messages", API_BASE_URL, room_id);

        if let Some(limit_value) = limit {
            history_url = format!("{}?limit={}", history_url, limit_value);
        }

        let response = self
            .client
            .get(&history_url)
            .header(
                "Authorization",
                format!("Bearer {}", self.jwt_token.as_ref().unwrap()),
            )
            .send()
            .await?;

        if !response.status().is_success() {
            anyhow::bail!("获取聊天历史失败: {}", response.status());
        }

        let history_response: Value = response.json().await?;
        println!("✅ 获取聊天历史成功");
        Ok(history_response)
    }

    /// 获取聊天室历史消息（带分页参数）
    async fn get_chat_history_with_pagination(
        &self,
        room_id: &str,
        limit: Option<u32>,
        before: Option<&str>,
    ) -> Result<Value> {
        let history_url = format!("{}/chat/rooms/{}/messages", API_BASE_URL, room_id);

        let mut request = self.client.get(&history_url).header(
            "Authorization",
            format!("Bearer {}", self.jwt_token.as_ref().unwrap()),
        );

        // 添加查询参数
        if let Some(limit_value) = limit {
            request = request.query(&[("limit", limit_value.to_string())]);
        }

        if let Some(before_value) = before {
            request = request.query(&[("before", before_value)]);
            println!("🔍 发送before参数: {}", before_value);
        }

        let response = request.send().await?;
        println!("🔍 请求URL: {}", response.url());

        if !response.status().is_success() {
            anyhow::bail!("获取聊天历史失败: {}", response.status());
        }

        let history_response: Value = response.json().await?;
        println!("✅ 获取分页聊天历史成功");
        Ok(history_response)
    }
}

/// 测试1: 基础历史消息查询功能
///
/// 【功能】: 验证基本的历史消息查询功能
/// 【测试内容】:
/// - 创建聊天室
/// - 发送多条消息
/// - 查询历史消息
/// - 验证消息内容和顺序
/// - 验证响应格式
#[tokio::test]
async fn test_basic_chat_history_query() -> Result<()> {
    println!("📚 开始测试: 基础历史消息查询功能");

    let mut helper = ChatHistoryTestHelper::new();

    // 步骤1: 用户登录
    println!("📝 步骤1: 用户登录");
    let _token = helper.login().await?;
    println!("✅ 登录成功");

    // 步骤2: 创建测试聊天室
    println!("📝 步骤2: 创建测试聊天室");
    let timestamp = chrono::Utc::now().timestamp_millis();
    let room_name = format!("历史查询测试聊天室_{}", timestamp);
    let room_response = helper
        .create_chat_room(&room_name, Some("用于测试历史消息查询功能的聊天室"))
        .await?;

    let room_data = &room_response["data"];
    let room_id = room_data["id"].as_str().unwrap();
    println!("✅ 测试聊天室创建成功，ID: {}", room_id);

    // 步骤3: 加入聊天室
    println!("📝 步骤3: 加入聊天室");
    let _join_response = helper.join_chat_room(room_id).await?;
    println!("✅ 成功加入聊天室");

    // 步骤4: 发送测试消息
    println!("📝 步骤4: 发送测试消息");
    let test_messages = vec![
        "第一条历史消息",
        "第二条历史消息：包含时间戳",
        "第三条历史消息：测试特殊字符 @#$%",
        "第四条历史消息：测试emoji 😊🎉",
        "第五条历史消息：最后一条",
    ];

    let mut sent_message_ids = Vec::new();
    for (index, message_content) in test_messages.iter().enumerate() {
        println!("📤 发送消息 {}: {}", index + 1, message_content);

        let message_response = helper
            .send_message_to_room(room_id, message_content)
            .await?;

        // 提取消息ID用于后续验证
        if let Some(message_id) = message_response["data"]["id"].as_str() {
            sent_message_ids.push(message_id.to_string());
        }

        // 等待消息处理
        sleep(Duration::from_millis(100)).await;
    }

    println!(
        "✅ 所有测试消息发送完成，发送了 {} 条消息",
        test_messages.len()
    );

    // 步骤5: 查询历史消息
    println!("📝 步骤5: 查询历史消息");
    sleep(Duration::from_millis(500)).await; // 等待消息完全处理

    let history_response = helper.get_chat_history(room_id, None).await?;

    // 验证历史消息响应格式
    assert!(
        history_response["success"].as_bool().unwrap_or(false),
        "获取聊天历史应该成功"
    );

    let messages = history_response["data"]
        .as_array()
        .ok_or_else(|| anyhow::anyhow!("历史消息数据应该是数组"))?;

    println!("📊 查询到历史消息数量: {}", messages.len());

    // 步骤6: 验证消息内容和格式
    println!("📝 步骤6: 验证消息内容和格式");

    // 验证消息数量
    assert!(
        messages.len() >= test_messages.len(),
        "历史消息数量应该至少包含发送的消息数量，期望: {}, 实际: {}",
        test_messages.len(),
        messages.len()
    );

    // 验证消息格式和内容
    for (index, message) in messages.iter().enumerate() {
        println!(
            "🔍 验证消息 {}: {}",
            index + 1,
            serde_json::to_string_pretty(message)?
        );

        // 验证消息基本字段
        assert!(message["id"].as_str().is_some(), "消息应该有ID");
        assert!(message["content"].as_str().is_some(), "消息应该有内容");
        assert!(message["sent_at"].as_str().is_some(), "消息应该有发送时间");
        assert!(
            message["sender_id"].as_str().is_some(),
            "消息应该有发送者ID"
        );

        // 验证消息属于正确的聊天室
        if let Some(msg_room_id) = message["room_id"].as_str() {
            assert_eq!(msg_room_id, room_id, "消息应该属于正确的聊天室");
        } else if let Some(msg_chat_room_id) = message["chat_room_id"].as_str() {
            assert_eq!(msg_chat_room_id, room_id, "消息应该属于正确的聊天室");
        }
    }

    // 步骤7: 验证消息顺序（应该按时间倒序排列）
    println!("📝 步骤7: 验证消息顺序");
    if messages.len() > 1 {
        for i in 0..messages.len() - 1 {
            let current_time = messages[i]["sent_at"].as_str().unwrap();
            let next_time = messages[i + 1]["sent_at"].as_str().unwrap();

            // 解析时间戳进行比较（应该是倒序，即新消息在前）
            let current_dt = chrono::DateTime::parse_from_rfc3339(current_time)?;
            let next_dt = chrono::DateTime::parse_from_rfc3339(next_time)?;

            assert!(
                current_dt >= next_dt,
                "消息应该按时间倒序排列，当前消息时间: {}, 下一条消息时间: {}",
                current_time,
                next_time
            );
        }
        println!("✅ 消息时间顺序验证通过");
    }

    println!("🎉 基础历史消息查询功能测试通过");
    Ok(())
}

/// 测试2: 分页查询功能测试
///
/// 【功能】: 验证历史消息的分页查询功能
/// 【测试内容】:
/// - 发送大量消息
/// - 测试limit参数
/// - 测试before参数
/// - 验证分页逻辑
/// - 验证数据完整性
#[tokio::test]
async fn test_chat_history_pagination() -> Result<()> {
    println!("📄 开始测试: 分页查询功能");

    let mut helper = ChatHistoryTestHelper::new();

    // 步骤1: 用户登录
    println!("📝 步骤1: 用户登录");
    let _token = helper.login().await?;
    println!("✅ 登录成功");

    // 步骤2: 创建测试聊天室
    println!("📝 步骤2: 创建测试聊天室");
    let timestamp = chrono::Utc::now().timestamp_millis();
    let room_name = format!("分页测试聊天室_{}", timestamp);
    let room_response = helper
        .create_chat_room(&room_name, Some("用于测试分页查询功能的聊天室"))
        .await?;

    let room_data = &room_response["data"];
    let room_id = room_data["id"].as_str().unwrap();
    println!("✅ 测试聊天室创建成功，ID: {}", room_id);

    // 步骤3: 加入聊天室
    println!("📝 步骤3: 加入聊天室");
    let _join_response = helper.join_chat_room(room_id).await?;
    println!("✅ 成功加入聊天室");

    // 步骤4: 发送大量测试消息
    println!("📝 步骤4: 发送大量测试消息");
    let message_count = 15; // 发送15条消息用于测试分页
    let mut sent_messages = Vec::new();

    for i in 1..=message_count {
        let message_content = format!(
            "分页测试消息 #{:02} - 时间戳: {}",
            i,
            chrono::Utc::now().timestamp_millis()
        );
        println!("📤 发送消息 {}: {}", i, message_content);

        let message_response = helper
            .send_message_to_room(room_id, &message_content)
            .await?;
        sent_messages.push(message_response);

        // 等待消息处理
        sleep(Duration::from_millis(50)).await;
    }

    println!("✅ 发送了 {} 条测试消息", message_count);

    // 等待所有消息处理完成
    sleep(Duration::from_millis(1000)).await;

    // 步骤5: 测试limit参数
    println!("📝 步骤5: 测试limit参数");

    // 测试limit=5
    let limited_response = helper.get_chat_history(room_id, Some(5)).await?;
    let limited_messages = limited_response["data"].as_array().unwrap();

    println!("📊 limit=5 查询结果: {} 条消息", limited_messages.len());
    assert!(limited_messages.len() <= 5, "limit=5时应该最多返回5条消息");

    // 测试limit=10
    let limited_response_10 = helper.get_chat_history(room_id, Some(10)).await?;
    let limited_messages_10 = limited_response_10["data"].as_array().unwrap();

    println!("📊 limit=10 查询结果: {} 条消息", limited_messages_10.len());
    assert!(
        limited_messages_10.len() <= 10,
        "limit=10时应该最多返回10条消息"
    );

    // 验证limit=10的结果应该包含limit=5的所有消息
    if limited_messages.len() > 0 && limited_messages_10.len() >= limited_messages.len() {
        for (index, msg) in limited_messages.iter().enumerate() {
            let msg_id = msg["id"].as_str().unwrap();
            let found = limited_messages_10
                .iter()
                .any(|m| m["id"].as_str() == Some(msg_id));
            assert!(found, "limit=10的结果应该包含limit=5的消息 {}", index + 1);
        }
        println!("✅ limit参数测试通过");
    }

    // 步骤6: 测试before参数（如果支持）
    println!("📝 步骤6: 测试before参数");

    if limited_messages.len() > 2 {
        // 由于消息是按时间倒序排列的（最新的在前），我们使用最后一条消息的时间作为before参数
        // 这样可以确保查询到更早的消息
        let last_index = limited_messages.len() - 1;
        let last_message_time = limited_messages[last_index]["sent_at"].as_str().unwrap();

        println!(
            "🔍 使用最后一条消息的时间作为before参数: {} (索引: {})",
            last_message_time, last_index
        );

        let before_response = helper
            .get_chat_history_with_pagination(
                room_id,
                Some(10), // 增加limit以确保能查询到足够的消息
                Some(last_message_time),
            )
            .await?;

        let before_messages = before_response["data"].as_array().unwrap();
        println!("📊 before参数查询结果: {} 条消息", before_messages.len());

        // 验证before参数的消息都应该早于指定时间
        if !before_messages.is_empty() {
            let before_dt = chrono::DateTime::parse_from_rfc3339(last_message_time)?;

            for (index, message) in before_messages.iter().enumerate() {
                let msg_time = message["sent_at"].as_str().unwrap();
                let msg_dt = chrono::DateTime::parse_from_rfc3339(msg_time)?;

                assert!(
                    msg_dt < before_dt,
                    "before参数查询的消息时间应该早于指定时间，消息时间: {}, 指定时间: {}",
                    msg_time,
                    last_message_time
                );
                println!("✅ 消息 {} 时间验证通过: {}", index + 1, msg_time);
            }
            println!("✅ before参数测试通过");
        } else {
            println!("ℹ️  没有更早的消息可以查询，这是正常的");
        }
    }

    // 步骤7: 测试无消息情况
    println!("📝 步骤7: 测试无消息情况");

    // 创建一个新的空聊天室
    let empty_room_name = format!("空聊天室_{}", timestamp);
    let empty_room_response = helper
        .create_chat_room(&empty_room_name, Some("用于测试空消息查询的聊天室"))
        .await?;

    let empty_room_data = &empty_room_response["data"];
    let empty_room_id = empty_room_data["id"].as_str().unwrap();

    let empty_history = helper.get_chat_history(empty_room_id, None).await?;
    let empty_messages = empty_history["data"].as_array().unwrap();

    assert_eq!(empty_messages.len(), 0, "空聊天室应该没有历史消息");
    println!("✅ 空消息情况测试通过");

    println!("🎉 分页查询功能测试通过");
    Ok(())
}

/// 测试3: 权限验证测试
///
/// 【功能】: 验证历史消息查询的权限控制
/// 【测试内容】:
/// - 未认证用户访问
/// - 无权限用户访问私有聊天室
/// - 不存在的聊天室访问
/// - 错误的token访问
#[tokio::test]
async fn test_chat_history_permission_validation() -> Result<()> {
    println!("🔒 开始测试: 权限验证");

    let mut helper = ChatHistoryTestHelper::new();

    // 步骤1: 用户登录
    println!("📝 步骤1: 用户登录");
    let token = helper.login().await?;
    println!("✅ 登录成功");

    // 步骤2: 创建测试聊天室
    println!("📝 步骤2: 创建测试聊天室");
    let timestamp = chrono::Utc::now().timestamp_millis();
    let room_name = format!("权限测试聊天室_{}", timestamp);
    let room_response = helper
        .create_chat_room(&room_name, Some("用于测试权限验证的聊天室"))
        .await?;

    let room_data = &room_response["data"];
    let room_id = room_data["id"].as_str().unwrap();
    println!("✅ 测试聊天室创建成功，ID: {}", room_id);

    // 步骤3: 测试未认证用户访问
    println!("📝 步骤3: 测试未认证用户访问");
    let history_url = format!("{}/chat/rooms/{}/messages", API_BASE_URL, room_id);

    let unauthorized_response = helper.client.get(&history_url).send().await?;

    assert!(
        !unauthorized_response.status().is_success(),
        "未认证用户不应该能够访问聊天历史"
    );
    assert_eq!(
        unauthorized_response.status(),
        reqwest::StatusCode::UNAUTHORIZED,
        "未认证访问应该返回401状态码"
    );

    println!("✅ 未认证用户访问测试通过");

    // 步骤4: 测试错误的token访问
    println!("📝 步骤4: 测试错误的token访问");
    let invalid_token_response = helper
        .client
        .get(&history_url)
        .header("Authorization", "Bearer invalid_token_12345")
        .send()
        .await?;

    assert!(
        !invalid_token_response.status().is_success(),
        "错误token不应该能够访问聊天历史"
    );

    println!("✅ 错误token访问测试通过");

    // 步骤5: 测试不存在的聊天室访问
    println!("📝 步骤5: 测试不存在的聊天室访问");
    let fake_room_id = Uuid::new_v4();
    let fake_room_url = format!("{}/chat/rooms/{}/messages", API_BASE_URL, fake_room_id);

    let not_found_response = helper
        .client
        .get(&fake_room_url)
        .header("Authorization", format!("Bearer {}", token))
        .send()
        .await?;

    assert!(
        !not_found_response.status().is_success(),
        "不存在的聊天室不应该能够访问"
    );

    println!("✅ 不存在聊天室访问测试通过");

    println!("🎉 权限验证测试通过");
    Ok(())
}

/// 测试4: 性能和大数据量测试
///
/// 【功能】: 验证历史消息查询在大数据量下的性能
/// 【测试内容】:
/// - 发送大量消息
/// - 测试查询响应时间
/// - 验证内存使用
/// - 测试并发查询
#[tokio::test]
async fn test_chat_history_performance() -> Result<()> {
    println!("⚡ 开始测试: 性能和大数据量");

    let mut helper = ChatHistoryTestHelper::new();

    // 步骤1: 用户登录
    println!("📝 步骤1: 用户登录");
    let _token = helper.login().await?;
    println!("✅ 登录成功");

    // 步骤2: 创建测试聊天室
    println!("📝 步骤2: 创建测试聊天室");
    let timestamp = chrono::Utc::now().timestamp_millis();
    let room_name = format!("性能测试聊天室_{}", timestamp);
    let room_response = helper
        .create_chat_room(&room_name, Some("用于测试性能的聊天室"))
        .await?;

    let room_data = &room_response["data"];
    let room_id = room_data["id"].as_str().unwrap();
    println!("✅ 测试聊天室创建成功，ID: {}", room_id);

    // 步骤2.5: 加入聊天室
    println!("📝 步骤2.5: 加入聊天室");
    helper.join_chat_room(room_id).await?;
    println!("✅ 成功加入聊天室");

    // 步骤3: 发送大量消息（适度数量，避免测试时间过长）
    println!("📝 步骤3: 发送大量消息");
    let message_count = 50; // 发送50条消息
    let start_time = std::time::Instant::now();

    for i in 1..=message_count {
        let message_content = format!(
            "性能测试消息 #{:03} - 内容较长以测试数据传输性能 - 时间戳: {} - 随机数据: {}",
            i,
            chrono::Utc::now().timestamp_millis(),
            uuid::Uuid::new_v4()
        );

        if i % 10 == 0 {
            println!("📤 已发送 {} 条消息", i);
        }

        helper
            .send_message_to_room(room_id, &message_content)
            .await?;

        // 减少等待时间以加快测试
        sleep(Duration::from_millis(20)).await;
    }

    let send_duration = start_time.elapsed();
    println!(
        "✅ 发送了 {} 条消息，耗时: {:?}",
        message_count, send_duration
    );

    // 等待消息处理完成
    sleep(Duration::from_millis(2000)).await;

    // 步骤4: 测试查询性能
    println!("📝 步骤4: 测试查询性能");

    let query_start = std::time::Instant::now();
    let history_response = helper.get_chat_history(room_id, None).await?;
    let query_duration = query_start.elapsed();

    let messages = history_response["data"].as_array().unwrap();
    println!(
        "📊 查询到 {} 条消息，耗时: {:?}",
        messages.len(),
        query_duration
    );

    // 验证查询性能（应该在合理时间内完成）
    assert!(
        query_duration.as_secs() < 5,
        "查询应该在5秒内完成，实际耗时: {:?}",
        query_duration
    );

    // 步骤5: 测试分页查询性能
    println!("📝 步骤5: 测试分页查询性能");

    let page_query_start = std::time::Instant::now();
    let page_response = helper.get_chat_history(room_id, Some(10)).await?;
    let page_query_duration = page_query_start.elapsed();

    let page_messages = page_response["data"].as_array().unwrap();
    println!(
        "📊 分页查询到 {} 条消息，耗时: {:?}",
        page_messages.len(),
        page_query_duration
    );

    // 分页查询应该更快
    assert!(
        page_query_duration.as_secs() < 3,
        "分页查询应该在3秒内完成，实际耗时: {:?}",
        page_query_duration
    );

    // 步骤6: 测试多次查询的一致性
    println!("📝 步骤6: 测试多次查询的一致性");

    let mut query_times = Vec::new();
    for i in 1..=5 {
        let consistency_start = std::time::Instant::now();
        let consistency_response = helper.get_chat_history(room_id, Some(20)).await?;
        let consistency_duration = consistency_start.elapsed();

        query_times.push(consistency_duration);

        let consistency_messages = consistency_response["data"].as_array().unwrap();
        assert!(
            consistency_messages.len() <= 20,
            "每次查询应该返回最多20条消息"
        );

        if i % 2 == 0 {
            println!(
                "📊 第 {} 次查询: {} 条消息，耗时: {:?}",
                i,
                consistency_messages.len(),
                consistency_duration
            );
        }
    }

    // 计算平均查询时间
    let avg_query_time =
        query_times.iter().sum::<std::time::Duration>() / (query_times.len() as u32);
    println!("📊 平均查询时间: {:?}", avg_query_time);

    // 验证查询时间的稳定性
    assert!(
        avg_query_time.as_secs() < 2,
        "平均查询时间应该在2秒内，实际: {:?}",
        avg_query_time
    );

    println!("🎉 性能和大数据量测试通过");
    Ok(())
}

/// 综合测试运行器
///
/// 【功能】: 运行聊天历史消息查询的综合测试
/// 【设计】: 独立的测试，不依赖其他测试函数
#[tokio::test]
async fn test_chat_history_query_comprehensive() -> Result<()> {
    println!("🚀 开始运行聊天历史消息查询功能综合测试");
    println!("{}", "=".repeat(60));

    // 等待服务器准备就绪
    println!("⏳ 等待服务器准备就绪...");
    sleep(Duration::from_secs(2)).await;

    let start_time = std::time::Instant::now();
    let mut helper = ChatHistoryTestHelper::new();

    // 步骤1: 用户登录
    println!("\n📝 步骤1: 用户登录");
    let _token = helper.login().await?;
    println!("✅ 登录成功");

    // 步骤2: 创建测试聊天室
    println!("📝 步骤2: 创建测试聊天室");
    let timestamp = chrono::Utc::now().timestamp_millis();
    let room_name = format!("综合测试聊天室_{}", timestamp);
    let room_response = helper
        .create_chat_room(&room_name, Some("用于综合测试的聊天室"))
        .await?;

    let room_data = &room_response["data"];
    let room_id = room_data["id"].as_str().unwrap();
    println!("✅ 测试聊天室创建成功，ID: {}", room_id);

    // 步骤2.5: 加入聊天室
    println!("📝 步骤2.5: 加入聊天室");
    helper.join_chat_room(room_id).await?;
    println!("✅ 成功加入聊天室");

    // 步骤3: 发送测试消息
    println!("📝 步骤3: 发送测试消息");
    let test_messages = vec!["综合测试消息1", "综合测试消息2", "综合测试消息3"];

    for (index, message_content) in test_messages.iter().enumerate() {
        helper
            .send_message_to_room(room_id, message_content)
            .await?;
        sleep(Duration::from_millis(100)).await;
        println!("📤 发送消息 {}: {}", index + 1, message_content);
    }

    // 步骤4: 测试历史消息查询
    println!("📝 步骤4: 测试历史消息查询");
    sleep(Duration::from_millis(500)).await;

    let history_response = helper.get_chat_history(room_id, None).await?;
    assert!(
        history_response["success"].as_bool().unwrap_or(false),
        "获取聊天历史应该成功"
    );

    let messages = history_response["data"].as_array().unwrap();
    println!("📊 查询到历史消息数量: {}", messages.len());
    assert!(
        messages.len() >= test_messages.len(),
        "历史消息数量应该至少包含发送的消息"
    );

    // 步骤5: 测试分页查询
    println!("📝 步骤5: 测试分页查询");
    let limited_response = helper.get_chat_history(room_id, Some(2)).await?;
    let limited_messages = limited_response["data"].as_array().unwrap();
    assert!(limited_messages.len() <= 2, "分页查询应该限制返回数量");
    println!("✅ 分页查询测试通过");

    let total_duration = start_time.elapsed();

    println!("\n{}", "=".repeat(60));
    println!("🎉 聊天历史消息查询功能综合测试通过！");
    println!("⏱️ 总耗时: {:?}", total_duration);
    println!("✅ 测试覆盖范围:");
    println!("   - ✅ 基础历史消息查询");
    println!("   - ✅ 分页查询功能");
    println!("   - ✅ 消息存储验证");
    println!("   - ✅ API响应格式验证");
    println!("{}", "=".repeat(60));

    Ok(())
}
