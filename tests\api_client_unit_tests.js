/**
 * API客户端单元测试
 * 测试统一API客户端的所有功能和边缘情况
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-23
 */

// 模拟fetch API
global.fetch = jest.fn();

// 模拟console方法以避免测试输出污染
global.console = {
    log: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
};

// 模拟localStorage
const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn()
};
global.localStorage = localStorageMock;

// 模拟auth模块
jest.mock('../static/js/modules/auth.js', () => ({
    getAuthToken: jest.fn(),
    isAuthenticated: jest.fn()
}));

// 导入要测试的模块
const {
    get,
    post,
    put,
    deleteRequest,
    patch,
    authAPI,
    taskAPI,
    APIError,
    HTTP_STATUS
} = require('../static/js/modules/api.js');

const { getAuthToken, isAuthenticated } = require('../static/js/modules/auth.js');

describe('API客户端单元测试', () => {
    beforeEach(() => {
        // 重置所有mock
        fetch.mockClear();
        console.log.mockClear();
        console.warn.mockClear();
        console.error.mockClear();
        localStorageMock.getItem.mockClear();
        localStorageMock.setItem.mockClear();
        localStorageMock.removeItem.mockClear();
    });

    describe('APIError类测试', () => {
        test('应该正确创建APIError实例', () => {
            const error = new APIError('测试错误', 400, { detail: '详细信息' });
            
            expect(error.name).toBe('APIError');
            expect(error.message).toBe('测试错误');
            expect(error.status).toBe(400);
            expect(error.response).toEqual({ detail: '详细信息' });
            expect(error instanceof Error).toBe(true);
        });
    });

    describe('HTTP状态码常量测试', () => {
        test('应该包含所有必要的HTTP状态码', () => {
            expect(HTTP_STATUS.OK).toBe(200);
            expect(HTTP_STATUS.CREATED).toBe(201);
            expect(HTTP_STATUS.NO_CONTENT).toBe(204);
            expect(HTTP_STATUS.BAD_REQUEST).toBe(400);
            expect(HTTP_STATUS.UNAUTHORIZED).toBe(401);
            expect(HTTP_STATUS.FORBIDDEN).toBe(403);
            expect(HTTP_STATUS.NOT_FOUND).toBe(404);
            expect(HTTP_STATUS.INTERNAL_SERVER_ERROR).toBe(500);
        });
    });

    describe('GET请求测试', () => {
        test('应该成功发送GET请求', async () => {
            const mockResponse = { id: 1, name: '测试用户' };
            fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                headers: new Map([['content-type', 'application/json']]),
                json: async () => mockResponse
            });

            const result = await get('/users/1');

            expect(fetch).toHaveBeenCalledWith('/api/users/1', expect.objectContaining({
                method: 'GET',
                headers: expect.objectContaining({
                    'Content-Type': 'application/json'
                })
            }));
            expect(result).toEqual(mockResponse);
        });

        test('应该在GET请求失败时抛出APIError', async () => {
            const errorResponse = { message: '用户不存在' };
            fetch.mockResolvedValueOnce({
                ok: false,
                status: 404,
                headers: new Map([['content-type', 'application/json']]),
                json: async () => errorResponse
            });

            await expect(get('/users/999')).rejects.toThrow(APIError);
            await expect(get('/users/999')).rejects.toThrow('用户不存在');
        });
    });

    describe('POST请求测试', () => {
        test('应该成功发送POST请求', async () => {
            const requestData = { name: '新用户', email: '<EMAIL>' };
            const mockResponse = { id: 2, ...requestData };
            
            fetch.mockResolvedValueOnce({
                ok: true,
                status: 201,
                headers: new Map([['content-type', 'application/json']]),
                json: async () => mockResponse
            });

            const result = await post('/users', requestData);

            expect(fetch).toHaveBeenCalledWith('/api/users', expect.objectContaining({
                method: 'POST',
                headers: expect.objectContaining({
                    'Content-Type': 'application/json'
                }),
                body: JSON.stringify(requestData)
            }));
            expect(result).toEqual(mockResponse);
        });

        test('应该支持不带数据的POST请求', async () => {
            fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                headers: new Map([['content-type', 'application/json']]),
                json: async () => ({ success: true })
            });

            await post('/auth/logout');

            expect(fetch).toHaveBeenCalledWith('/api/auth/logout', expect.objectContaining({
                method: 'POST',
                body: null
            }));
        });
    });

    describe('PUT请求测试', () => {
        test('应该成功发送PUT请求', async () => {
            const updateData = { name: '更新的用户名' };
            const mockResponse = { id: 1, ...updateData };
            
            fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                headers: new Map([['content-type', 'application/json']]),
                json: async () => mockResponse
            });

            const result = await put('/users/1', updateData);

            expect(fetch).toHaveBeenCalledWith('/api/users/1', expect.objectContaining({
                method: 'PUT',
                body: JSON.stringify(updateData)
            }));
            expect(result).toEqual(mockResponse);
        });
    });

    describe('DELETE请求测试', () => {
        test('应该成功发送DELETE请求', async () => {
            fetch.mockResolvedValueOnce({
                ok: true,
                status: 204,
                headers: new Map(),
                text: async () => ''
            });

            const result = await deleteRequest('/users/1');

            expect(fetch).toHaveBeenCalledWith('/api/users/1', expect.objectContaining({
                method: 'DELETE'
            }));
            expect(result).toBe('');
        });
    });

    describe('PATCH请求测试', () => {
        test('应该成功发送PATCH请求', async () => {
            const patchData = { status: 'active' };
            const mockResponse = { id: 1, status: 'active' };
            
            fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                headers: new Map([['content-type', 'application/json']]),
                json: async () => mockResponse
            });

            const result = await patch('/users/1', patchData);

            expect(fetch).toHaveBeenCalledWith('/api/users/1', expect.objectContaining({
                method: 'PATCH',
                body: JSON.stringify(patchData)
            }));
            expect(result).toEqual(mockResponse);
        });
    });

    describe('认证头测试', () => {
        test('应该在有token时添加Authorization头', async () => {
            // 模拟已登录状态
            localStorageMock.getItem.mockReturnValue('test-jwt-token');
            
            fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                headers: new Map([['content-type', 'application/json']]),
                json: async () => ({ success: true })
            });

            await get('/protected');

            expect(fetch).toHaveBeenCalledWith('/api/protected', expect.objectContaining({
                headers: expect.objectContaining({
                    'Authorization': 'Bearer test-jwt-token'
                })
            }));
        });

        test('应该在没有token时不添加Authorization头', async () => {
            localStorageMock.getItem.mockReturnValue(null);
            
            fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                headers: new Map([['content-type', 'application/json']]),
                json: async () => ({ success: true })
            });

            await get('/public');

            expect(fetch).toHaveBeenCalledWith('/api/public', expect.objectContaining({
                headers: expect.not.objectContaining({
                    'Authorization': expect.any(String)
                })
            }));
        });
    });

    describe('重试机制测试', () => {
        test('应该在网络错误时重试请求', async () => {
            // 前两次失败，第三次成功
            fetch
                .mockRejectedValueOnce(new TypeError('网络错误'))
                .mockRejectedValueOnce(new TypeError('网络错误'))
                .mockResolvedValueOnce({
                    ok: true,
                    status: 200,
                    headers: new Map([['content-type', 'application/json']]),
                    json: async () => ({ success: true })
                });

            const result = await get('/retry-test');

            expect(fetch).toHaveBeenCalledTimes(3);
            expect(result).toEqual({ success: true });
            expect(console.warn).toHaveBeenCalledTimes(2);
        });

        test('应该在达到最大重试次数后抛出错误', async () => {
            fetch.mockRejectedValue(new TypeError('持续网络错误'));

            await expect(get('/fail-test')).rejects.toThrow('持续网络错误');
            expect(fetch).toHaveBeenCalledTimes(4); // 1次原始请求 + 3次重试
        });

        test('应该不重试HTTP错误响应', async () => {
            fetch.mockResolvedValueOnce({
                ok: false,
                status: 400,
                headers: new Map([['content-type', 'application/json']]),
                json: async () => ({ error: '请求错误' })
            });

            await expect(get('/bad-request')).rejects.toThrow(APIError);
            expect(fetch).toHaveBeenCalledTimes(1); // 不应该重试HTTP错误
        });
    });

    describe('超时处理测试', () => {
        test('应该在请求超时时取消请求', async () => {
            // 模拟超时
            fetch.mockImplementation(() => 
                new Promise((resolve) => {
                    setTimeout(() => resolve({
                        ok: true,
                        status: 200,
                        headers: new Map([['content-type', 'application/json']]),
                        json: async () => ({ data: 'delayed' })
                    }), 15000); // 15秒延迟，超过10秒超时
                })
            );

            await expect(get('/slow-endpoint')).rejects.toThrow();
        });
    });

    describe('authAPI测试', () => {
        test('login方法应该发送正确的请求', async () => {
            const loginResponse = { token: 'jwt-token', user: { id: 1, username: 'testuser' } };
            
            fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                headers: new Map([['content-type', 'application/json']]),
                json: async () => loginResponse
            });

            const result = await authAPI.login('testuser', 'password123');

            expect(fetch).toHaveBeenCalledWith('/api/auth/login', expect.objectContaining({
                method: 'POST',
                body: JSON.stringify({ username: 'testuser', password: 'password123' })
            }));
            expect(result).toEqual(loginResponse);
        });

        test('register方法应该发送正确的请求', async () => {
            const registerResponse = { success: true, message: '注册成功' };
            
            fetch.mockResolvedValueOnce({
                ok: true,
                status: 201,
                headers: new Map([['content-type', 'application/json']]),
                json: async () => registerResponse
            });

            const result = await authAPI.register('newuser', 'password123');

            expect(fetch).toHaveBeenCalledWith('/api/auth/register', expect.objectContaining({
                method: 'POST',
                body: JSON.stringify({ username: 'newuser', password: 'password123' })
            }));
            expect(result).toEqual(registerResponse);
        });

        test('logout方法应该发送正确的请求', async () => {
            fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                headers: new Map([['content-type', 'application/json']]),
                json: async () => ({ success: true })
            });

            await authAPI.logout();

            expect(fetch).toHaveBeenCalledWith('/api/auth/logout', expect.objectContaining({
                method: 'POST',
                body: null
            }));
        });

        test('refresh方法应该发送正确的请求', async () => {
            const refreshResponse = { token: 'new-jwt-token' };
            
            fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                headers: new Map([['content-type', 'application/json']]),
                json: async () => refreshResponse
            });

            const result = await authAPI.refresh();

            expect(fetch).toHaveBeenCalledWith('/api/auth/refresh', expect.objectContaining({
                method: 'POST',
                body: null
            }));
            expect(result).toEqual(refreshResponse);
        });
    });

    describe('taskAPI测试', () => {
        test('fetchAll方法应该获取所有任务', async () => {
            const tasksResponse = [
                { id: 1, title: '任务1', completed: false },
                { id: 2, title: '任务2', completed: true }
            ];
            
            fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                headers: new Map([['content-type', 'application/json']]),
                json: async () => tasksResponse
            });

            const result = await taskAPI.fetchAll();

            expect(fetch).toHaveBeenCalledWith('/api/tasks', expect.objectContaining({
                method: 'GET'
            }));
            expect(result).toEqual(tasksResponse);
        });

        test('fetchById方法应该获取指定任务', async () => {
            const taskResponse = { id: 1, title: '任务1', completed: false };
            
            fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                headers: new Map([['content-type', 'application/json']]),
                json: async () => taskResponse
            });

            const result = await taskAPI.fetchById(1);

            expect(fetch).toHaveBeenCalledWith('/api/tasks/1', expect.objectContaining({
                method: 'GET'
            }));
            expect(result).toEqual(taskResponse);
        });

        test('create方法应该创建新任务', async () => {
            const newTask = { title: '新任务', description: '任务描述' };
            const createdTask = { id: 3, ...newTask, completed: false };
            
            fetch.mockResolvedValueOnce({
                ok: true,
                status: 201,
                headers: new Map([['content-type', 'application/json']]),
                json: async () => createdTask
            });

            const result = await taskAPI.create(newTask);

            expect(fetch).toHaveBeenCalledWith('/api/tasks', expect.objectContaining({
                method: 'POST',
                body: JSON.stringify(newTask)
            }));
            expect(result).toEqual(createdTask);
        });

        test('update方法应该更新任务', async () => {
            const updateData = { title: '更新的任务标题' };
            const updatedTask = { id: 1, title: '更新的任务标题', completed: false };
            
            fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                headers: new Map([['content-type', 'application/json']]),
                json: async () => updatedTask
            });

            const result = await taskAPI.update(1, updateData);

            expect(fetch).toHaveBeenCalledWith('/api/tasks/1', expect.objectContaining({
                method: 'PUT',
                body: JSON.stringify(updateData)
            }));
            expect(result).toEqual(updatedTask);
        });

        test('delete方法应该删除任务', async () => {
            fetch.mockResolvedValueOnce({
                ok: true,
                status: 204,
                headers: new Map(),
                text: async () => ''
            });

            const result = await taskAPI.delete(1);

            expect(fetch).toHaveBeenCalledWith('/api/tasks/1', expect.objectContaining({
                method: 'DELETE'
            }));
            expect(result).toBe('');
        });
    });

    describe('错误处理测试', () => {
        test('应该正确处理JSON响应错误', async () => {
            const errorResponse = { 
                error: '验证失败', 
                details: { username: '用户名已存在' } 
            };
            
            fetch.mockResolvedValueOnce({
                ok: false,
                status: 400,
                statusText: 'Bad Request',
                headers: new Map([['content-type', 'application/json']]),
                json: async () => errorResponse
            });

            try {
                await post('/users', { username: 'existing' });
            } catch (error) {
                expect(error).toBeInstanceOf(APIError);
                expect(error.message).toBe('验证失败');
                expect(error.status).toBe(400);
                expect(error.response).toEqual(errorResponse);
            }
        });

        test('应该正确处理文本响应错误', async () => {
            fetch.mockResolvedValueOnce({
                ok: false,
                status: 500,
                statusText: 'Internal Server Error',
                headers: new Map([['content-type', 'text/plain']]),
                text: async () => '服务器内部错误'
            });

            try {
                await get('/server-error');
            } catch (error) {
                expect(error).toBeInstanceOf(APIError);
                expect(error.message).toBe('服务器内部错误');
                expect(error.status).toBe(500);
            }
        });

        test('应该处理没有错误消息的响应', async () => {
            fetch.mockResolvedValueOnce({
                ok: false,
                status: 404,
                statusText: 'Not Found',
                headers: new Map([['content-type', 'application/json']]),
                json: async () => ({})
            });

            try {
                await get('/not-found');
            } catch (error) {
                expect(error).toBeInstanceOf(APIError);
                expect(error.message).toBe('HTTP 404: Not Found');
                expect(error.status).toBe(404);
            }
        });
    });

    describe('日志记录测试', () => {
        test('应该记录成功的请求日志', async () => {
            fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                headers: new Map([['content-type', 'application/json']]),
                json: async () => ({ success: true })
            });

            await get('/test');

            expect(console.log).toHaveBeenCalledWith('API请求: GET /api/test');
            expect(console.log).toHaveBeenCalledWith('API响应成功: GET /api/test');
        });

        test('应该记录失败的请求日志', async () => {
            fetch.mockResolvedValueOnce({
                ok: false,
                status: 400,
                headers: new Map([['content-type', 'application/json']]),
                json: async () => ({ error: '请求错误' })
            });

            try {
                await get('/error-test');
            } catch (error) {
                expect(console.log).toHaveBeenCalledWith('API请求: GET /api/error-test');
                expect(console.error).toHaveBeenCalledWith(
                    'API请求失败: GET /api/error-test', 
                    expect.any(APIError)
                );
            }
        });
    });
});
