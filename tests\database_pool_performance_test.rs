//! # 数据库连接池性能测试
//!
//! 测试数据库连接池在高并发场景下的性能和稳定性

use anyhow::Result;
use app_infrastructure::database::{DatabaseConfig, DatabasePoolManager};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::task::JoinSet;
use tracing::{info, warn};

/// 连接池性能测试配置
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct PerformanceTestConfig {
    /// 并发连接数
    pub concurrent_connections: usize,
    /// 每个连接的操作次数
    pub operations_per_connection: usize,
    /// 测试持续时间
    pub test_duration: Duration,
    /// 是否启用详细日志
    pub verbose_logging: bool,
}

impl Default for PerformanceTestConfig {
    fn default() -> Self {
        Self {
            concurrent_connections: 50,
            operations_per_connection: 100,
            test_duration: Duration::from_secs(30),
            verbose_logging: false,
        }
    }
}

/// 连接池性能测试结果
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct PerformanceTestResult {
    /// 总操作数
    pub total_operations: u64,
    /// 成功操作数
    pub successful_operations: u64,
    /// 失败操作数
    pub failed_operations: u64,
    /// 平均响应时间（毫秒）
    pub avg_response_time_ms: f64,
    /// 最大响应时间（毫秒）
    pub max_response_time_ms: u64,
    /// 最小响应时间（毫秒）
    pub min_response_time_ms: u64,
    /// 吞吐量（操作/秒）
    pub throughput_ops_per_sec: f64,
    /// 测试持续时间
    pub test_duration: Duration,
    /// 连接池健康状态
    pub pool_health: bool,
}

/// 数据库连接池性能测试器
pub struct DatabasePoolPerformanceTester {
    pool_manager: Arc<DatabasePoolManager>,
    config: PerformanceTestConfig,
}

impl DatabasePoolPerformanceTester {
    /// 创建新的性能测试器
    pub async fn new(config: PerformanceTestConfig) -> Result<Self> {
        // 创建数据库配置
        let database_config =
            DatabaseConfig::from_env().map_err(|e| anyhow::anyhow!("数据库配置加载失败: {}", e))?;

        // 创建连接池管理器
        let pool_manager = Arc::new(DatabasePoolManager::new(database_config).await?);

        // 启动监控
        pool_manager.start_monitoring();

        Ok(Self {
            pool_manager,
            config,
        })
    }

    /// 执行连接池性能测试
    pub async fn run_performance_test(&self) -> Result<PerformanceTestResult> {
        info!("🚀 开始数据库连接池性能测试");
        info!("📊 测试配置: {:?}", self.config);

        let start_time = Instant::now();
        let mut join_set = JoinSet::new();

        // 统计变量
        let total_operations = Arc::new(std::sync::atomic::AtomicU64::new(0));
        let successful_operations = Arc::new(std::sync::atomic::AtomicU64::new(0));
        let failed_operations = Arc::new(std::sync::atomic::AtomicU64::new(0));
        let response_times = Arc::new(std::sync::Mutex::new(Vec::new()));

        // 启动并发任务
        for task_id in 0..self.config.concurrent_connections {
            let pool_manager = self.pool_manager.clone();
            let operations_count = self.config.operations_per_connection;
            let total_ops = total_operations.clone();
            let success_ops = successful_operations.clone();
            let failed_ops = failed_operations.clone();
            let response_times_clone = response_times.clone();
            let verbose = self.config.verbose_logging;

            join_set.spawn(async move {
                Self::run_connection_task(
                    task_id,
                    pool_manager,
                    operations_count,
                    total_ops,
                    success_ops,
                    failed_ops,
                    response_times_clone,
                    verbose,
                )
                .await
            });
        }

        // 等待所有任务完成或超时
        let timeout_future = tokio::time::sleep(self.config.test_duration);
        tokio::pin!(timeout_future);

        tokio::select! {
            _ = async {
                while join_set.join_next().await.is_some() {}
            } => {
                info!("✅ 所有并发任务完成");
            }
            _ = &mut timeout_future => {
                warn!("⏰ 测试超时，终止剩余任务");
                join_set.abort_all();
            }
        }

        let test_duration = start_time.elapsed();

        // 收集测试结果
        let total_ops = total_operations.load(std::sync::atomic::Ordering::Relaxed);
        let success_ops = successful_operations.load(std::sync::atomic::Ordering::Relaxed);
        let failed_ops = failed_operations.load(std::sync::atomic::Ordering::Relaxed);

        let response_times_vec = response_times.lock().unwrap().clone();
        let (avg_time, max_time, min_time) = if !response_times_vec.is_empty() {
            let sum: u64 = response_times_vec.iter().sum();
            let avg = (sum as f64) / (response_times_vec.len() as f64);
            let max = *response_times_vec.iter().max().unwrap_or(&0);
            let min = *response_times_vec.iter().min().unwrap_or(&0);
            (avg, max, min)
        } else {
            (0.0, 0, 0)
        };

        let throughput = if test_duration.as_secs_f64() > 0.0 {
            (success_ops as f64) / test_duration.as_secs_f64()
        } else {
            0.0
        };

        // 检查连接池健康状态
        let pool_health = self.pool_manager.health_check().await;

        let result = PerformanceTestResult {
            total_operations: total_ops,
            successful_operations: success_ops,
            failed_operations: failed_ops,
            avg_response_time_ms: avg_time,
            max_response_time_ms: max_time,
            min_response_time_ms: min_time,
            throughput_ops_per_sec: throughput,
            test_duration,
            pool_health,
        };

        info!("📈 性能测试结果: {:?}", result);
        Ok(result)
    }

    /// 运行单个连接任务
    async fn run_connection_task(
        task_id: usize,
        pool_manager: Arc<DatabasePoolManager>,
        operations_count: usize,
        total_operations: Arc<std::sync::atomic::AtomicU64>,
        successful_operations: Arc<std::sync::atomic::AtomicU64>,
        failed_operations: Arc<std::sync::atomic::AtomicU64>,
        response_times: Arc<std::sync::Mutex<Vec<u64>>>,
        verbose: bool,
    ) {
        if verbose {
            info!("🔄 任务 {} 开始执行 {} 次操作", task_id, operations_count);
        }

        for op_id in 0..operations_count {
            let op_start = Instant::now();
            total_operations.fetch_add(1, std::sync::atomic::Ordering::Relaxed);

            // 获取数据库连接并执行简单查询
            match Self::execute_database_operation(&pool_manager).await {
                Ok(_) => {
                    successful_operations.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
                    let response_time = op_start.elapsed().as_millis() as u64;
                    response_times.lock().unwrap().push(response_time);

                    if verbose && op_id % 10 == 0 {
                        info!(
                            "✅ 任务 {} 操作 {} 成功，响应时间: {}ms",
                            task_id, op_id, response_time
                        );
                    }
                }
                Err(e) => {
                    failed_operations.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
                    if verbose {
                        warn!("❌ 任务 {} 操作 {} 失败: {}", task_id, op_id, e);
                    }
                }
            }

            // 短暂延迟以模拟真实负载
            tokio::time::sleep(Duration::from_millis(10)).await;
        }

        if verbose {
            info!("✅ 任务 {} 完成所有操作", task_id);
        }
    }

    /// 执行数据库操作
    async fn execute_database_operation(pool_manager: &DatabasePoolManager) -> Result<()> {
        let connection = pool_manager.get_connection();

        // 执行简单的健康检查查询
        sea_orm::DatabaseConnection::ping(connection.as_ref()).await?;

        Ok(())
    }

    /// 获取连接池指标
    pub fn get_pool_metrics(&self) -> Arc<app_infrastructure::database::pool_manager::PoolMetrics> {
        self.pool_manager.get_metrics()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tracing_test::traced_test;

    #[tokio::test]
    #[traced_test]
    async fn test_database_pool_basic_performance() {
        let config = PerformanceTestConfig {
            concurrent_connections: 10,
            operations_per_connection: 20,
            test_duration: Duration::from_secs(10),
            verbose_logging: true,
        };

        let tester = DatabasePoolPerformanceTester::new(config)
            .await
            .expect("创建性能测试器失败");

        let result = tester
            .run_performance_test()
            .await
            .expect("性能测试执行失败");

        // 验证测试结果
        assert!(result.pool_health, "连接池应该保持健康状态");
        assert!(result.successful_operations > 0, "应该有成功的操作");
        assert!(result.throughput_ops_per_sec > 0.0, "吞吐量应该大于0");

        println!("🎯 基础性能测试通过: {:?}", result);
    }

    #[tokio::test]
    #[traced_test]
    async fn test_database_pool_high_concurrency() {
        let config = PerformanceTestConfig {
            concurrent_connections: 100,
            operations_per_connection: 50,
            test_duration: Duration::from_secs(30),
            verbose_logging: false,
        };

        let tester = DatabasePoolPerformanceTester::new(config)
            .await
            .expect("创建高并发测试器失败");

        let result = tester
            .run_performance_test()
            .await
            .expect("高并发测试执行失败");

        // 验证高并发场景下的稳定性
        assert!(result.pool_health, "高并发下连接池应该保持健康");
        assert!(
            result.failed_operations < result.total_operations / 10,
            "失败率应该低于10%"
        );

        println!("🚀 高并发测试通过: {:?}", result);
    }
}
