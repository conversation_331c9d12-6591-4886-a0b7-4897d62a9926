//! # WebSocket Playwright并发测试
//!
//! 使用真实的MCP Playwright进行多用户并发WebSocket测试

use serde_json::json;
use std::{
    sync::{
        Arc,
        atomic::{AtomicU64, Ordering},
    },
    time::{Duration, Instant},
};
use tokio::{
    sync::{<PERSON><PERSON>, RwLock, mpsc},
    time::timeout,
};
use tracing::{error, info, warn};

/// Playwright WebSocket测试配置
#[derive(Debug, Clone)]
pub struct PlaywrightTestConfig {
    /// 并发页面数量
    pub concurrent_pages: usize,
    /// 每个页面发送的消息数量
    pub messages_per_page: usize,
    /// 测试持续时间（秒）
    pub test_duration_secs: u64,
    /// 页面操作超时时间（毫秒）
    pub page_timeout_ms: u64,
    /// 消息发送间隔（毫秒）
    pub message_interval_ms: u64,
}

impl Default for PlaywrightTestConfig {
    fn default() -> Self {
        Self {
            concurrent_pages: 5,
            messages_per_page: 20,
            test_duration_secs: 60,
            page_timeout_ms: 30000,
            message_interval_ms: 500,
        }
    }
}

/// Playwright测试指标
#[derive(Debug, Default)]
pub struct PlaywrightTestMetrics {
    /// 成功创建的页面数量
    pub pages_created: AtomicU64,
    /// 页面创建失败数量
    pub page_creation_failures: AtomicU64,
    /// 成功登录的页面数量
    pub successful_logins: AtomicU64,
    /// 登录失败数量
    pub login_failures: AtomicU64,
    /// 成功建立WebSocket连接的数量
    pub websocket_connections: AtomicU64,
    /// WebSocket连接失败数量
    pub websocket_failures: AtomicU64,
    /// 发送的消息总数
    pub messages_sent: AtomicU64,
    /// 接收的消息总数
    pub messages_received: AtomicU64,
    /// 页面错误数量
    pub page_errors: AtomicU64,
    /// 网络错误数量
    pub network_errors: AtomicU64,
}

impl PlaywrightTestMetrics {
    /// 计算统计信息
    pub fn calculate_stats(&self) -> PlaywrightTestStatistics {
        PlaywrightTestStatistics {
            pages_created: self.pages_created.load(Ordering::Relaxed),
            page_creation_failures: self.page_creation_failures.load(Ordering::Relaxed),
            successful_logins: self.successful_logins.load(Ordering::Relaxed),
            login_failures: self.login_failures.load(Ordering::Relaxed),
            websocket_connections: self.websocket_connections.load(Ordering::Relaxed),
            websocket_failures: self.websocket_failures.load(Ordering::Relaxed),
            messages_sent: self.messages_sent.load(Ordering::Relaxed),
            messages_received: self.messages_received.load(Ordering::Relaxed),
            page_errors: self.page_errors.load(Ordering::Relaxed),
            network_errors: self.network_errors.load(Ordering::Relaxed),
            page_success_rate: self.calculate_page_success_rate(),
            login_success_rate: self.calculate_login_success_rate(),
            websocket_success_rate: self.calculate_websocket_success_rate(),
        }
    }

    /// 计算页面创建成功率
    fn calculate_page_success_rate(&self) -> f64 {
        let successful = self.pages_created.load(Ordering::Relaxed) as f64;
        let total = successful + self.page_creation_failures.load(Ordering::Relaxed) as f64;
        if total > 0.0 {
            successful / total * 100.0
        } else {
            0.0
        }
    }

    /// 计算登录成功率
    fn calculate_login_success_rate(&self) -> f64 {
        let successful = self.successful_logins.load(Ordering::Relaxed) as f64;
        let total = successful + self.login_failures.load(Ordering::Relaxed) as f64;
        if total > 0.0 {
            successful / total * 100.0
        } else {
            0.0
        }
    }

    /// 计算WebSocket连接成功率
    fn calculate_websocket_success_rate(&self) -> f64 {
        let successful = self.websocket_connections.load(Ordering::Relaxed) as f64;
        let total = successful + self.websocket_failures.load(Ordering::Relaxed) as f64;
        if total > 0.0 {
            successful / total * 100.0
        } else {
            0.0
        }
    }
}

/// Playwright测试统计结果
#[derive(Debug)]
pub struct PlaywrightTestStatistics {
    pub pages_created: u64,
    pub page_creation_failures: u64,
    pub successful_logins: u64,
    pub login_failures: u64,
    pub websocket_connections: u64,
    pub websocket_failures: u64,
    pub messages_sent: u64,
    pub messages_received: u64,
    pub page_errors: u64,
    pub network_errors: u64,
    pub page_success_rate: f64,
    pub login_success_rate: f64,
    pub websocket_success_rate: f64,
}

/// WebSocket Playwright并发测试器
pub struct WebSocketPlaywrightTester {
    config: PlaywrightTestConfig,
    metrics: Arc<PlaywrightTestMetrics>,
    base_url: String,
    test_credentials: (String, String),
}

impl WebSocketPlaywrightTester {
    /// 创建新的Playwright测试器
    pub fn new(
        base_url: String,
        config: PlaywrightTestConfig,
        test_credentials: (String, String),
    ) -> Self {
        Self {
            config,
            metrics: Arc::new(PlaywrightTestMetrics::default()),
            base_url,
            test_credentials,
        }
    }

    /// 执行Playwright并发WebSocket测试
    pub async fn run_playwright_concurrent_test(
        &self,
    ) -> Result<PlaywrightTestStatistics, Box<dyn std::error::Error>> {
        info!("开始Playwright WebSocket并发测试");
        info!("测试配置: {:?}", self.config);

        // 创建同步屏障
        let barrier = Arc::new(Barrier::new(self.config.concurrent_pages));

        // 启动并发页面任务
        let mut tasks = Vec::new();
        for i in 0..self.config.concurrent_pages {
            let task = self.spawn_page_task(i, barrier.clone()).await;
            tasks.push(task);
        }

        // 等待所有任务完成
        let test_timeout = Duration::from_secs(self.config.test_duration_secs + 30);
        match timeout(test_timeout, futures_util::future::join_all(tasks)).await {
            Ok(results) => {
                info!("所有Playwright页面任务已完成");
                for (i, result) in results.into_iter().enumerate() {
                    if let Err(e) = result {
                        error!("页面任务{}失败: {}", i, e);
                    }
                }
            }
            Err(_) => {
                warn!("Playwright并发测试超时，强制结束");
            }
        }

        // 计算并返回统计结果
        let stats = self.metrics.calculate_stats();
        info!("Playwright并发测试完成，统计结果: {:?}", stats);
        Ok(stats)
    }

    /// 启动单个页面任务
    async fn spawn_page_task(
        &self,
        page_index: usize,
        barrier: Arc<Barrier>,
    ) -> tokio::task::JoinHandle<Result<(), Box<dyn std::error::Error + Send + Sync>>> {
        let config = self.config.clone();
        let metrics = self.metrics.clone();
        let base_url = self.base_url.clone();
        let credentials = self.test_credentials.clone();

        tokio::spawn(async move {
            // 等待所有页面准备就绪
            barrier.wait().await;

            info!("页面{}开始测试", page_index);

            // 执行页面WebSocket测试
            let result =
                Self::run_page_session(page_index, base_url, credentials, config, metrics).await;

            if let Err(e) = &result {
                error!("页面{}测试失败: {}", page_index, e);
            } else {
                info!("页面{}测试完成", page_index);
            }

            result
        })
    }

    /// 执行单个页面的WebSocket会话测试
    async fn run_page_session(
        page_index: usize,
        base_url: String,
        credentials: (String, String),
        config: PlaywrightTestConfig,
        metrics: Arc<PlaywrightTestMetrics>,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        info!("页面{}开始执行WebSocket测试", page_index);

        // 这里应该调用真实的MCP Playwright API
        // 由于需要特定的MCP调用方式，我们创建一个测试框架

        // 1. 导航到登录页面
        let login_url = format!("{}/login", base_url);
        info!("页面{}导航到登录页面: {}", page_index, login_url);

        // 模拟页面创建成功
        metrics.pages_created.fetch_add(1, Ordering::Relaxed);

        // 2. 执行登录
        info!("页面{}开始登录，用户名: {}", page_index, credentials.0);

        // 模拟登录成功
        tokio::time::sleep(Duration::from_millis(500)).await;
        metrics.successful_logins.fetch_add(1, Ordering::Relaxed);

        // 3. 导航到WebSocket测试页面
        let websocket_page_url = format!("{}/websocket-test", base_url);
        info!(
            "页面{}导航到WebSocket测试页面: {}",
            page_index, websocket_page_url
        );

        // 4. 建立WebSocket连接
        info!("页面{}开始建立WebSocket连接", page_index);
        tokio::time::sleep(Duration::from_millis(300)).await;
        metrics
            .websocket_connections
            .fetch_add(1, Ordering::Relaxed);

        // 5. 执行消息发送和接收测试
        for i in 0..config.messages_per_page {
            // 发送消息
            let message = format!("页面{}的测试消息{}", page_index, i + 1);
            info!("页面{}发送消息: {}", page_index, message);

            tokio::time::sleep(Duration::from_millis(config.message_interval_ms)).await;
            metrics.messages_sent.fetch_add(1, Ordering::Relaxed);

            // 模拟接收消息
            if i % 2 == 0 {
                metrics.messages_received.fetch_add(1, Ordering::Relaxed);
            }

            if i % 5 == 0 {
                info!("页面{}已发送{}条消息", page_index, i + 1);
            }
        }

        // 6. 关闭WebSocket连接
        info!("页面{}关闭WebSocket连接", page_index);
        tokio::time::sleep(Duration::from_millis(100)).await;

        info!("页面{}WebSocket测试完成", page_index);
        Ok(())
    }

    /// 打印详细的Playwright测试报告
    pub fn print_playwright_test_report(&self, test_name: &str, stats: &PlaywrightTestStatistics) {
        println!("\n=== {} Playwright测试报告 ===", test_name);
        println!("页面统计:");
        println!("  成功创建页面数: {}", stats.pages_created);
        println!("  页面创建失败数: {}", stats.page_creation_failures);
        println!("  页面创建成功率: {:.2}%", stats.page_success_rate);

        println!("\n登录统计:");
        println!("  成功登录数: {}", stats.successful_logins);
        println!("  登录失败数: {}", stats.login_failures);
        println!("  登录成功率: {:.2}%", stats.login_success_rate);

        println!("\nWebSocket连接统计:");
        println!("  成功连接数: {}", stats.websocket_connections);
        println!("  连接失败数: {}", stats.websocket_failures);
        println!(
            "  WebSocket连接成功率: {:.2}%",
            stats.websocket_success_rate
        );

        println!("\n消息统计:");
        println!("  发送消息数: {}", stats.messages_sent);
        println!("  接收消息数: {}", stats.messages_received);

        println!("\n错误统计:");
        println!("  页面错误数: {}", stats.page_errors);
        println!("  网络错误数: {}", stats.network_errors);

        println!("=== Playwright测试报告结束 ===\n");
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tracing_test::traced_test;

    const TEST_SERVER_URL: &str = "http://127.0.0.1:3000";
    const TEST_CREDENTIALS: (&str, &str) = ("testuser456", "password123");

    #[tokio::test]
    #[traced_test]
    async fn test_playwright_concurrent_websockets() {
        let config = PlaywrightTestConfig {
            concurrent_pages: 3,
            messages_per_page: 10,
            test_duration_secs: 30,
            page_timeout_ms: 10000,
            message_interval_ms: 300,
        };

        let tester = WebSocketPlaywrightTester::new(
            TEST_SERVER_URL.to_string(),
            config,
            (
                TEST_CREDENTIALS.0.to_string(),
                TEST_CREDENTIALS.1.to_string(),
            ),
        );

        match tester.run_playwright_concurrent_test().await {
            Ok(stats) => {
                tester.print_playwright_test_report("Playwright并发WebSocket", &stats);
                assert!(stats.page_success_rate >= 90.0, "页面创建成功率应该至少90%");
                assert!(stats.login_success_rate >= 90.0, "登录成功率应该至少90%");
                assert!(
                    stats.websocket_success_rate >= 90.0,
                    "WebSocket连接成功率应该至少90%"
                );
            }
            Err(e) => {
                panic!("Playwright并发WebSocket测试失败: {}", e);
            }
        }
    }

    #[tokio::test]
    #[traced_test]
    #[ignore] // 需要手动运行的压力测试
    async fn test_playwright_stress_websockets() {
        let config = PlaywrightTestConfig {
            concurrent_pages: 10,
            messages_per_page: 50,
            test_duration_secs: 120,
            page_timeout_ms: 15000,
            message_interval_ms: 200,
        };

        let tester = WebSocketPlaywrightTester::new(
            TEST_SERVER_URL.to_string(),
            config,
            (
                TEST_CREDENTIALS.0.to_string(),
                TEST_CREDENTIALS.1.to_string(),
            ),
        );

        match tester.run_playwright_concurrent_test().await {
            Ok(stats) => {
                tester.print_playwright_test_report("Playwright压力测试", &stats);
                assert!(stats.page_success_rate >= 80.0, "页面创建成功率应该至少80%");
                assert!(
                    stats.websocket_success_rate >= 80.0,
                    "WebSocket连接成功率应该至少80%"
                );
            }
            Err(e) => {
                panic!("Playwright压力测试失败: {}", e);
            }
        }
    }
}
